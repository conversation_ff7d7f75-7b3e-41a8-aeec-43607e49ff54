import Vue from 'vue'
import Cookies from 'js-cookie'
import 'normalize.css/normalize.css' // A modern alternative to CSS resets

import ElementUI from 'element-ui'
import '@/styles/theme/dark/index.css'
import 'element-ui/lib/theme-chalk/index.css'
import 'element-ui/lib/theme-chalk/display.css'
import locale from 'element-ui/lib/locale/lang/zh-CN' // lang i18n

import '@/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'
import '@/directive'
import '@/components-register'
import '@/permission' // permission control
import * as filters from './filters'
import { isMock } from '@/utils'
import { checkSwitchTheme } from '@/utils/theme'
import '@/plugin'
import { simpleUploadFlie, radmonFilename, getAccessImgUrl } from '@/api/oss'
import { application } from '@/settings'
import { sysOriginPlatforms } from '@/constant/origin'
import * as opsMessage from '@/utils/ops-message'
import videojs from 'video.js'

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online ! ! !
 */
if (isMock()) {
  const { mockXHR } = require('../mock')
  mockXHR()
}

// set ElementUI lang to zh-CN
Vue.use(ElementUI, { locale, size: Cookies.get('size') || 'medium' })
// 如果想要中文版 element-ui，按如下方式声明
// Vue.use(ElementUI)

Vue.config.productionTip = false

// 加载过滤器
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})

Object.keys(opsMessage).forEach(key => {
  Vue.prototype['$' + key] = opsMessage[key]
})

// 适配单位换算
function resizeScreenFontSize() {
  const clientWidth = document.documentElement.clientWidth
  document.documentElement.style.margin = 'auto'
  document.documentElement.style.position = 'relative'
  document.documentElement.style.fontSize = (clientWidth / 10) + 'px'
  if (clientWidth >= 540) {
    document.documentElement.style.fontSize = '54px'
    return
  }
}
resizeScreenFontSize()
window.onresize = () => resizeScreenFontSize()

Vue.prototype.$simpleUploadFlie = simpleUploadFlie
Vue.prototype.$radmonFilename = radmonFilename
Vue.prototype.$getAccessImgUrl = getAccessImgUrl
Vue.prototype.$application = application
Vue.prototype.$sysOriginMap = toSysOriginMap()
Vue.prototype.$videoJS = videojs

function toSysOriginMap() {
  const sysOriginMap = {}
  sysOriginPlatforms.forEach(item => {
    sysOriginMap[item.value] = item.label
  })
  return sysOriginMap
}
checkSwitchTheme()
