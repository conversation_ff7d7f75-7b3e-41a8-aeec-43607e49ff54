
import request from '@/utils/request'

// 礼包列表
export function giftPackTable(params) {
  return request({
    url: '/sys/gift/pack',
    method: 'get',
    params
  })
}

// 修改礼包信息
export function updateGiftPack(data) {
  return request({
    url: '/sys/gift/pack',
    method: 'put',
    data
  })
}

// 删除礼包信息
export function deleteGiftPack(id) {
  return request({
    url: `/sys/gift/pack/delete/${id}`,
    method: 'get'
  })
}

// 新增礼包信息
export function addGiftPack(data) {
  return request({
    url: '/sys/gift/pack',
    method: 'post',
    data
  })
}

// 获取礼包详情
export function getGiftPackInfo(giftPackId) {
  return request({
    url: `/sys/gift/pack/config/${giftPackId}`,
    method: 'get'
  })
}

// 礼包配置
export function giftPackConfig(data) {
  return request({
    url: '/sys/gift/pack/config',
    method: 'post',
    data
  })
}

// 新增礼包配置
export function addGiftPackConfig(data) {
  return request({
    url: '/sys/gift/pack/config/add',
    method: 'post',
    data
  })
}

// 获取礼包赠送金币
export function getGiftPackGold(giftPackId) {
  return request({
    url: `/sys/gift/pack/config/gold/${giftPackId}`,
    method: 'get'
  })
}

export function getGiftPackConfigInfo(params) {
  return request({
    url: '/sys/gift/pack/config/info',
    method: 'get',
    params
  })
}

export function getGiftPackGrade() {
  return request({
    url: '/sys/product/config/pack/grade',
    method: 'get'
  })
}

