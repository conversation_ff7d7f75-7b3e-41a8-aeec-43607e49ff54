import request from '@/utils/request'

export function login(data) {
  return request({
    url: '/account/token',
    method: 'post',
    data
  })
}

export function getInfo() {
  return request({
    url: '/account/info',
    method: 'get'
  })
}

export function logout() {
  return request({
    url: '/mock/user/logout',
    method: 'post'
  })
}

// 获取账号菜单
export function getAccountMenus() {
  return request({
    url: '/account/menus',
    method: 'get'
  })
}

// 获取按钮权限
export function getButtonPermissions() {
  return request({
    url: '/account/buttons/aliases',
    method: 'get'
  })
}

// 用户道具流水列表
export function userPropsTable(params) {
  return request({
    url: '/running/water/user/props/page',
    method: 'get',
    params
  })
}

// 用户购买麦位类型流水列表
export function userPropsMikeType(params) {
  return request({
    url: '/room-mike-type/page',
    method: 'get',
    params
  })
}

// 用户靓号申请列表
export function userBeautifulNumberApplyTable(params) {
  return request({
    url: '/beautiful/number/apply/page',
    method: 'get',
    params
  })
}

// 处理靓号申请状态
export function handleBeautifulNumberApplyState(id, state) {
  return request({
    url: `/beautiful/number/apply/handle/${id}/${state}`,
    method: 'get'
  })
}

// ////////////////////////////////////////////////////// 用户银行卡 start ////////////////////////////////////////////////////////////////

// 根据团队id获得银行卡列表.
export function listBankCardTable(userId) {
  return request({
    url: '/user/bank-card/list/user-id/' + userId,
    method: 'get'
  })
}

// 团队通过银行卡.
export function listUserPassBankCards(userId) {
  return request({
    url: '/user/bank-card/pass-list',
    method: 'get',
    params: { userId }
  })
}

// 修改银行卡资料
export function updateBankCard(data) {
  return request({
    url: '/user/bank-card/update',
    method: 'post',
    data
  })
}

// 添加银行卡资料
export function addBankCard(data) {
  return request({
    url: '/user/bank-card/add',
    method: 'post',
    data
  })
}

// 根据银行卡id删除.
export function deleteBankCard(bankCardId) {
  return request({
    url: '/user/bank-card/delete/' + bankCardId,
    method: 'get'
  })
}

// 切换使用中的银行卡.
export function useBankCard(bankCardId) {
  return request({
    url: '/user/bank-card/use/' + bankCardId,
    method: 'get'
  })
}

// ////////////////////////////////////////////////////// 用户银行卡 end ////////////////////////////////////////////////////////////////

// 用户密码日志列表
export function userPasswordLog(params) {
  return request({
    url: '/user-password-log/page',
    method: 'get',
    params
  })
}
// 用户上传专属礼物分页列表
export function pageUserCustomGift(params) {
  return request({
    url: '/user/custom-gift',
    method: 'get',
    params
  })
}

// 切换用户上传专属礼物状态
export function switchStatus(id, status) {
  return request({
    url: `/user/custom-gift/${id}/${status}`,
    method: 'get'
  })
}
