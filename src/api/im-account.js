import request from '@/utils/request'

// 系统im账号分页列表
export function pageSysImAccount(params) {
  return request({
    url: '/sys/im/account/page',
    method: 'get',
    params
  })
}

// 添加im账号
export function addSysImAccount(data) {
  return request({
    url: '/sys/im/account',
    method: 'post',
    data
  })
}

// 重置密码
export function resetSysImAccountPassword(params) {
  return request({
    url: '/sys/im/account/reset',
    method: 'put',
    params
  })
}

// 删除
export function delSysImAccountPassword(params) {
  return request({
    url: '/sys/im/account',
    method: 'delete',
    params
  })
}
