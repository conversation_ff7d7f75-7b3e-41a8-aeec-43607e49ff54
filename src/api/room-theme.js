
import request from '@/utils/request'

// 主题列表
export function themeTable(params) {
  return request({
    url: '/room/theme/list',
    method: 'get',
    params
  })
}

// 新增房间主题信息
export function addRoomTheme(data) {
  return request({
    url: '/room/theme',
    method: 'post',
    data
  })
}

// 修改房间主题信息
export function updateRoomTheme(data) {
  return request({
    url: '/room/theme',
    method: 'put',
    data
  })
}

// 删除主题信息
export function deleteRoomTheme(id) {
  return request({
    url: `/room/theme/${id}`,
    method: 'delete'
  })
}

