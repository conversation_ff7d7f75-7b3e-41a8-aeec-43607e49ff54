
import request from '@/utils/request'

// 歌曲列表
export function songTable(params) {
  return request({
    url: '/game-ktv/config',
    method: 'get',
    params
  })
}

// 修改歌曲信息
export function updateSong(data) {
  return request({
    url: '/game-ktv/config',
    method: 'put',
    data
  })
}

// 切换歌曲上下架状态
export function switchShelfStatus(id, status) {
  return request({
    url: `/game-ktv/config/switch/${id}/${status}`,
    method: 'put'
  })
}

// 新增歌曲信息
export function addSong(data) {
  return request({
    url: '/game-ktv/config',
    method: 'post',
    data
  })
}

// 删除歌曲信息
export function deleteSong(data) {
  return request({
    url: `/game-ktv/config/delete`,
    method: 'put',
    data
  })
}

