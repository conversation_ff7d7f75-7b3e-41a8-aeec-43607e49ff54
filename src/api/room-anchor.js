import request from '@/utils/request'

// BD列表
export function pageRoomBDInfo(params) {
  return request({
    url: '/team/bd/page',
    method: 'get',
    params
  })
}
// BD代理列表
export function pageRoomTeamBDInfo(params) {
  return request({
    url: '/team/bd/member/page',
    method: 'get',
    params
  })
}

// 删除BD
export function deleteBD(id) {
  return request({
    url: '/team/bd/del',
    method: 'get',
    params: { id }
  })
}

// 删除BD中的代理成员
export function deleteBDMember(id) {
  return request({
    url: '/team/bd/member/del',
    method: 'get',
    params: { id }
  })
}

// 添加BD
export function addBD(data) {
  return request({
    url: '/team/bd/add',
    method: 'post',
    data
  })
}

// 修改BD
export function updateBD(data) {
  return request({
    url: '/team/bd/update',
    method: 'post',
    data
  })
}

// 根据bdID获得bd信息
export function getBdById(id) {
  return request({
    url: '/bd/' + id,
    method: 'get'
  })
}

// BD Lead 列表
export function pageBdLead(params) {
  return request({
    url: '/team/bd/leader/page',
    method: 'get',
    params
  })
}

// 删除BD Lead
export function deleteBdLead(id) {
  return request({
    url: '/team/bd/leader/del',
    method: 'get',
    params: { id }
  })
}

// 添加BD Lead
export function addBdLead(data) {
  return request({
    url: '/team/bd/leader/add',
    method: 'post',
    data
  })
}

// 编辑BD Lead
export function updateBDLead(data) {
  return request({
    url: '/team/bd/leader/update',
    method: 'post',
    data
  })
}

// BD Lead添加子级BD
export function leadBindBd(data) {
  return request({
    url: '/team/bd/leader/bind/bd',
    method: 'post',
    data
  })
}

// BD工作统计
export function pageWorkStatistics(params) {
  return request({
    url: '/team/bd/work/statistics',
    method: 'get',
    params
  })
}
