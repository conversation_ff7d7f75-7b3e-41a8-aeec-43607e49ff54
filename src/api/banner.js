
import request from '@/utils/request'

// banner列表
export function bannerTable(params) {
  return request({
    url: '/sys/banner/config',
    method: 'get',
    params
  })
}

// 修改banner信息
export function updateBanner(data) {
  return request({
    url: '/sys/banner/config',
    method: 'put',
    data
  })
}

// 删除banner信息
export function deleteBanner(id, sysOrigin) {
  return request({
    url: `/sys/banner/config/${id}/${sysOrigin}`,
    method: 'delete'
  })
}

// 新增banner信息
export function addBanner(data) {
  return request({
    url: '/sys/banner/config',
    method: 'post',
    data
  })
}

