import request from '@/utils/request'

// 支付渠道-分页
export function pagePayChannel(params) {
  return request({
    url: '/sys-pay-channel/page',
    method: 'get',
    params
  })
}

// 支付渠道-列表
export function listPayChannel(params) {
  return request({
    url: '/sys-pay-channel/list',
    method: 'get',
    params
  })
}

// 支付渠道-添加
export function addPayChannel(data) {
  return request({
    url: '/sys-pay-channel/add',
    method: 'post',
    data
  })
}

// 支付渠道-修改
export function updatePayChannel(data) {
  return request({
    url: '/sys-pay-channel/update',
    method: 'post',
    data
  })
}

// 支付厂商-分页
export function pagePayFactory(params) {
  return request({
    url: '/sys-pay-factory/page',
    method: 'get',
    params
  })
}

// 支付厂商-列表a d s
export function listPayFactory(params) {
  return request({
    url: '/sys-pay-factory/list',
    method: 'get',
    params
  })
}

// 支付厂商-关联渠道-添加
export function addPayFactoryAssociatedChannels(data) {
  return request({
    url: '/sys-pay-channel-factory/add-batch',
    method: 'post',
    data
  })
}

// 支付厂商-关联渠道-删除
export function delPayFactoryAssociatedChannels(data) {
  return request({
    url: '/sys-pay-channel-factory/del-batch',
    method: 'post',
    data
  })
}

// 支付厂商-关联渠道-修改
export function updateFactoryAssociatedChannels(data) {
  return request({
    url: '/sys-pay-channel-factory/update',
    method: 'post',
    data
  })
}

// 支付厂商-关联渠道列表
export function listFactoryAssociatedChannels(factoryCode) {
  return request({
    url: '/sys-pay-channel-factory/channels',
    method: 'get',
    params: { factoryCode }
  })
}

// 根据限额获取支付的付款渠道-列表
export function listCountrytSupportAmountChannels(payCountryId, amountUsd) {
  return request({
    url: '/sys-pay-channel-factory/support-amount',
    method: 'get',
    params: { payCountryId, amountUsd }
  })
}

// 计算汇率
export function listCountrytSupportAmountChannelsRate(payCountryId, amount, exchangeRate) {
  return request({
    url: '/sys-pay-channel-factory/support-amount-rate',
    method: 'get',
    params: { payCountryId, amount, exchangeRate }
  })
}

// 支付厂商-添加
export function addPayFactory(data) {
  return request({
    url: '/sys-pay-factory/add',
    method: 'post',
    data
  })
}

// 支付厂商-修改
export function updatePayFactory(data) {
  return request({
    url: '/sys-pay-factory/update',
    method: 'post',
    data
  })
}

// 开通应用列表-修改
export function listPayApplication() {
  return request({
    url: '/sys-pay-application/list',
    method: 'get'
  })
}

// 开通应用-添加
export function addPayApplication(data) {
  return request({
    url: '/sys-pay-application/add',
    method: 'post',
    data
  })
}

// 开通应用-修改
export function updatePayApplication(data) {
  return request({
    url: '/sys-pay-application/update',
    method: 'post',
    data
  })
}

// 开通支付国家-分页
export function pagePayOpenCounty(params) {
  return request({
    url: '/sys-pay-open-country/page',
    method: 'get',
    params
  })
}

// 开通支付国家-列表
export function listPayOpenCountry() {
  return request({
    url: '/sys-pay-open-country/list',
    method: 'get'
  })
}

// 开通支付国家-列表
export function listPayOpenCountryByRegionId(regionId) {
  return request({
    url: '/sys-pay-open-country/list/region?regionId=' + regionId,
    method: 'get'
  })
}

// 开通支付国家-添加
export function addPayOpenCountyBatch(data) {
  return request({
    url: '/sys-pay-open-country/add-batch',
    method: 'post',
    data
  })
}

// 开通支付国家-删除
export function delPayOpenCountyBatch(data) {
  return request({
    url: '/sys-pay-open-country/del-batch',
    method: 'post',
    data
  })
}

// 开通支付国家-修改国家汇率
export function updatePayOpenCountryExchangeRate(id, rate) {
  return request({
    url: '/sys-pay-open-country/update-exchange-rate',
    method: 'post',
    params: { id, rate }
  })
}

// 开通支付国家-修改国家货币
export function updatePayOpenCountryCurrent(id, currency) {
  return request({
    url: '/sys-pay-open-country/update-currency',
    method: 'post',
    params: { id, currency }
  })
}

// 开通支付国家-修改国家权重
export function updatePayOpenCountrySort(id, sort) {
  return request({
    url: '/sys-pay-open-country/update-sort',
    method: 'post',
    params: { id, sort }
  })
}

// 开通支付国家-上下架
export function updatePayOpenCountyShelf(id, shelf) {
  return request({
    url: '/sys-pay-open-country/shelf',
    method: 'get',
    params: { id, shelf }
  })
}

// 开通支付国家-删除关联渠道
export function deletePayCountyAssociateChannel(id) {
  return request({
    url: '/sys-pay-open-country/channel-del',
    method: 'post',
    params: { id }
  })
}

// 开放支付国家关联渠道-列表
export function listPayCountryChannel(payCountryId) {
  return request({
    url: '/sys-pay-channel-factory/country',
    method: 'get',
    params: { payCountryId }
  })
}

// 渠道关联厂商列表-列表
export function listChannelFactoryAll() {
  return request({
    url: '/sys-pay-channel-factory/associate',
    method: 'get'
  })
}

// 开放支付国家关联渠道-上下架
export function updatePayCountryChannelShelf(id, shelf) {
  return request({
    url: '/sys-pay-country-channel/switch-shelf',
    method: 'get',
    params: { id, shelf }
  })
}

// 开放支付国家关联渠道-删除
export function delBatchPayCountryChannel(data) {
  return request({
    url: '/sys-pay-country-channel/del-batch',
    method: 'post',
    data
  })
}

// 开通支付国家-保存渠道关联
export function addBatchCountyAssociateChannel(data) {
  return request({
    url: '/sys-pay-country-channel/add-batch',
    method: 'post',
    data
  })
}

// 国家渠道关联详情-添加或修改
export function addOrUpdateCountyChannelDetails(data) {
  return request({
    url: '/sys-pay-country-channel/add-or-update-details',
    method: 'post',
    data
  })
}

// 商品管理-分页列表
export function pagePayCommodity(params) {
  return request({
    url: '/sys-pay-commodity/page',
    method: 'get',
    params
  })
}

// 商品管理-添加或修改
export function addOrUpdatePayCommodity(data) {
  return request({
    url: '/sys-pay-commodity/add-or-update',
    method: 'post',
    data
  })
}

// 商品管理-修改上下架状态
export function switchShelfCommodity(id, shelf) {
  return request({
    url: '/sys-pay-commodity/switch-shelf',
    method: 'post',
    params: { id, shelf }
  })
}
