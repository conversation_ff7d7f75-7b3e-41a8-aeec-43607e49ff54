
import request from '@/utils/request'

// 房间基本信息
export function getRoomProfileByRoomId(roomId) {
  return request({
    url: '/room-profile-manager',
    method: 'get',
    params: { roomId }
  })
}

// 房间基本详细信息
export function getRoomProfileDetailsByRoomId(roomId) {
  return request({
    url: '/room/profile-manager/details',
    method: 'get',
    params: { roomId }
  })
}

// 房间基本信息
export function getRoomProfileBySysOriginAccount(sysOrigin, account) {
  return request({
    url: '/room/profile-manager/account/by-account',
    method: 'get',
    params: { sysOrigin, account }
  })
}

// 房间基本信息
export function listRoomProfileByAccount(account) {
  return request({
    url: '/room/profile-manager/account/list/by-account',
    method: 'get',
    params: { account }
  })
}

// 房间贡献余额列表
export function contributionBalanceTable(params) {
  return request({
    url: '/room/contribution/balance/page',
    method: 'get',
    params
  })
}

// 查询时间段内的房间贡献余额列表
export function roomTimeContributionBalanceTable(params) {
  return request({
    url: '/room/contribution/balance/time/page',
    method: 'get',
    params
  })
}

// 房间资料列表
export function pofileTable(params) {
  return request({
    url: '/room/profile-manager/flow',
    method: 'get',
    params
  })
}

// 修改房间资料信息
export function updateProfile(data) {
  return request({
    url: '/room/profile-manager/update-profile',
    method: 'post',
    data
  })
}

// 房间用户信息
export function getRoomUserInfo(params) {
  return request({
    url: `/room/member/page`,
    method: 'get',
    params
  })
}

// 房间权限变更
export function changeRole(data) {
  return request({
    url: `/room/member/change/role`,
    method: 'post',
    data
  })
}

// 最近房间访客列表
export function listRecentVisitors(params) {
  return request({
    url: '/room/profile-manager/visitors',
    method: 'get',
    params
  })
}

// 房间红包发送记录
export function flowGameRedPacket(params) {
  return request({
    url: '/game-red-packet/flow',
    method: 'get',
    params
  })
}

// 移除或拉黑房间用户
export function removeOrPullBlackRoomUser(data) {
  return request({
    url: '/room/profile-manager/blacklist/remove-or-pull',
    method: 'post',
    data
  })
}

// 房间黑名单列表
export function getRoomBlacklist(params) {
  return request({
    url: '/room/profile-manager/blacklist',
    method: 'get',
    params
  })
}
