
import request from '@/utils/request'

// 根据条件获得礼物列表
export function listByTab(sysOrigin, giftTab) {
  return request({
    url: `/sys/gift/config/listByTab/${sysOrigin}/${giftTab}`,
    method: 'get'
  })
}

// 礼物列表
export function giftTable(params) {
  return request({
    url: '/sys/gift/config',
    method: 'get',
    params
  })
}

// 修改礼物信息
export function updateGift(data) {
  return request({
    url: '/sys/gift/config',
    method: 'put',
    data
  })
}

// 切换礼物上下架状态
export function switchDelStatus(id, status) {
  return request({
    url: `/sys/gift/config/switch/${id}/${status}`,
    method: 'get'
  })
}

// 新增礼物信息
export function addGift(data) {
  return request({
    url: '/sys/gift/config',
    method: 'post',
    data
  })
}

// 获取指定平台礼物
export function listGiftBySysOrigin(sysOrigin) {
  return request({
    url: `/sys/gift/config/sys-origin/${sysOrigin}`,
    method: 'get'
  })
}

// 礼物赠送列表
export function listGiftGiveAwayRunningWater(params) {
  return request({
    url: '/running-water-log/gift-give',
    method: 'get',
    params
  })
}

// 用户房间赠送流水
export function listUserRoomGiftGiveAwayRunningWater(params) {
  return request({
    url: '/running-water-log/gift-give-room',
    method: 'get',
    params
  })
}

// 礼物赠送列表-总价值
export function countGiftAmount(params) {
  return request({
    url: '/running-water-log/total-count',
    method: 'get',
    params
  })
}

// 礼物赠送列表-接收礼物数量
export function countGiftAcceptQuantity(params) {
  return request({
    url: '/running-water-log/accept-gift-quantity',
    method: 'get',
    params
  })
}

// 获取赠送Cp组合礼物ID
export function getPairCpGiveGiftId(sysOrigin) {
  return request({
    url: `/sys/gift/config/pair/cp/give/gift/${sysOrigin}`,
    method: 'get'
  })
}

// 保存赠送Cp组合礼物ID
export function pushPairCpGiveGiftId(giftId, sysOrigin) {
  return request({
    url: `/sys/gift/config/pair/cp/give/gift/${giftId}/${sysOrigin}`,
    method: 'get'
  })
}

