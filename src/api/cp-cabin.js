
import request from '@/utils/request'

// cpCabin列表
export function cpCabinTable(params) {
  return request({
    url: '/sys/cp-cabin/config',
    method: 'get',
    params
  })
}

// 修改cpCabin信息
export function updateCpCabin(data) {
  return request({
    url: '/sys/cp-cabin/config',
    method: 'put',
    data
  })
}

// 删除cpCabin信息
export function deleteCpCabin(id) {
  return request({
    url: `/sys/cp-cabin/config/${id}`,
    method: 'delete'
  })
}

// 新增cpCabin信息
export function addCpCabin(data) {
  return request({
    url: '/sys/cp-cabin/config',
    method: 'post',
    data
  })
}
// cp告白信息
export function userConfessionRecordPage(params) {
  return request({
    url: '/user/confession/record/page',
    method: 'get',
    params
  })
}
// cp告白审核
export function auditUserConfession(data) {
  return request({
    url: '/user/confession/record',
    method: 'put',
    data
  })
}

