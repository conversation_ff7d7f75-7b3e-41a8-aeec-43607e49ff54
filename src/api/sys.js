/**
 * app管理相关
 */
import request from '@/utils/request'

// 系统用户友谊卡列表.
export function userFriendshipCardConfigTable(params) {
  return request({
    url: '/sys/user/friendship/card/list',
    method: 'get',
    params
  })
}

// 新增用户友谊卡配置.
export function addUserFriendshipCardConfig(data) {
  return request({
    url: '/sys/user/friendship/card',
    method: 'post',
    data
  })
}

// 修改用户友谊卡配置
export function updateUserFriendshipCardConfig(data) {
  return request({
    url: '/sys/user/friendship/card',
    method: 'put',
    data
  })
}

// 获取指定平台配置信息
export function getLotteryConf(params) {
  return request({
    url: '/lottery/conf',
    method: 'get',
    params
  })
}

// 获取指定平台配置信息
export function addLotteryConf(data) {
  return request({
    url: '/lottery/conf',
    method: 'post',
    data
  })
}

// 国家基本信息
export function getCountryAlls() {
  return request({
    url: '/sys/country/code',
    method: 'get'
  })
}

// 用户登陆日志
export function loginLoggerPage(params) {
  return request({
    url: '/user/login/logger/page',
    method: 'get',
    params
  })
}

// 黑名单列表
export function requestBlackList() {
  return request({
    url: '/request-black-list',
    method: 'get'
  })
}

// 黑名单列表-机型
export function addBlackListPhoneModel(phoneModel) {
  return request({
    url: '/request-black-list/phone-model',
    method: 'post',
    params: { phoneModel }
  })
}

// 黑名单列表-ip
export function addBlackListIP(ip) {
  return request({
    url: '/request-black-list/ip',
    method: 'post',
    params: { ip }
  })
}

// 删除黑名单列表
export function delBlackList(content) {
  return request({
    url: '/request-black-list/del',
    method: 'delete',
    params: { content }
  })
}

// 分页列表
export function refundAnchorTrackRecordPage(params) {
  return request({
    url: '/refund-anchor-track-record/page',
    method: 'get',
    params
  })
}

// ///////////////////////////////// 置顶房间 //////////////////////////////////
// 置顶房间-添加或修改
export function addOrUpdateSetTopRoom(data) {
  return request({
    url: '/sys-set-top/add-or-update',
    method: 'post',
    data
  })
}

// 置顶房间-移除
export function removeSetTopRoom(roomId) {
  return request({
    url: '/sys-set-top/remove',
    method: 'get',
    params: { roomId }
  })
}

// 置顶房间-分页列表
export function pageSetTopRoom(params) {
  return request({
    url: '/sys-set-top/page',
    method: 'get',
    params
  })
}

// ///////////////////////////////// 热门房间 //////////////////////////////////
// 热门房间-添加或修改
export function addOrUpdateSetHotRoom(data) {
  return request({
    url: '/sys-set-hot/add-or-update',
    method: 'post',
    data
  })
}

// 热门房间-移除
export function removeSetHotRoom(roomId) {
  return request({
    url: '/sys-set-hot/remove',
    method: 'get',
    params: { roomId }
  })
}

// 热门房间-分页列表h
export function pageSetHotRoom(params) {
  return request({
    url: '/sys-set-hot/page',
    method: 'get',
    params
  })
}

// 游戏列表配置-分页列表
export function pageGameConfig(params) {
  return request({
    url: '/sys-game-list-config/page',
    method: 'get',
    params
  })
}

// 游戏列表配置-添加或修改
export function saveOrUpdateGameConfig(data) {
  return request({
    url: '/sys-game-list-config/save-or-update',
    method: 'post',
    data
  })
}

// 游戏列表配置-删除
export function deleteGameConfig(id, sysOrigin) {
  return request({
    url: '/sys-game-list-config/delete',
    method: 'get',
    params: { id, sysOrigin }
  })
}

// 匹配游戏列表
export function flowGameMatchConfig(params) {
  return request({
    url: '/sys-game-list-config/match-conf',
    method: 'get',
    params
  })
}

// 添加或修改匹配游戏配置
export function upsertGameMatchConfig(data) {
  return request({
    url: '/sys-game-list-config/match-conf',
    method: 'post',
    data
  })
}

// 添加或修改匹配游戏配置
export function delGameMatchConfig(id) {
  return request({
    url: '/sys-game-list-config/match-conf',
    method: 'delete',
    params: { id }
  })
}

// ///////////////////////////////// 邀请用户规则配置 ////////////////////////////////////
// 修改配置
export function updateSysInviteUserConfig(data) {
  return request({
    url: '/sys/invite/user/config',
    method: 'post',
    data
  })
}
// 查询配置
export function querySysInviteUserConfig(sysOrigin) {
  return request({
    url: '/sys/invite/user/config?sysOrigin=' + sysOrigin,
    method: 'get'
  })
}

export function updateSysUserAdvertisingConfigDTO(data) {
  return request({
    url: '/sys/user/advertising/save',
    method: 'post',
    data
  })
}
export function querySysUserAdvertisingConfigDTO(sysOrigin) {
  return request({
    url: '/sys/user/advertising/config?sysOrigin=' + sysOrigin,
    method: 'get'
  })
}

// -----------活动模版start------------------

/**
 * 活动模版-列表.
 */
export function listTemplates(params) {
  return request({
    url: '/activity-conf/template-list',
    method: 'get',
    params
  })
}

/**
 * 活动模版-列表.
 */
export function listTemplateValues() {
  return request({
    url: '/activity-conf/template-values',
    method: 'get'
  })
}

/**
 * 活动模版-添加.
 */
export function addTemplate(data) {
  return request({
    url: '/activity-conf/template-add',
    method: 'post',
    data
  })
}

/**
 * 活动模版-修改.
 */
export function updateTemplate(data) {
  return request({
    url: '/activity-conf/template-update',
    method: 'post',
    data
  })
}

/**
 * 活动模版-修改名称.
 */
export function updateTemplateName(id, name) {
  return request({
    url: '/activity-conf/template-update-name',
    method: 'post',
    data: { id, name }
  })
}

/**
 * 根据id获得活动模版.
 */
export function getTemplateById(id) {
  return request({
    url: '/activity-conf/template',
    method: 'get',
    params: { id }
  })
}

/**
 * 活动配置-列表.
 */
export function listActivityConf(params) {
  return request({
    url: '/activity-conf/conf-list',
    method: 'get',
    params
  })
}

/**
 * 活动配置-添加.
 */
export function addActivityConf(data) {
  return request({
    url: '/activity-conf/conf-add',
    method: 'post',
    data
  })
}

/**
 * 活动配置-修改.
 */
export function updateActivityConf(data) {
  return request({
    url: '/activity-conf/conf-update',
    method: 'post',
    data
  })
}

/**
 * 活动奖品-排名.
 */
export function listActivityRewardRank(activityId) {
  return request({
    url: '/activity-conf/rank-reward-preview',
    method: 'get',
    params: { activityId }
  })
}

/**
 * 活动-排名.
 */
export function listActivityRank(activityId) {
  return request({
    url: '/activity-conf/rank',
    method: 'get',
    params: { activityId }
  })
}

/**
 * 活动-发送奖励.
 */
export function sendActivityReward(activityId) {
  return request({
    url: '/activity-conf/send-reward',
    method: 'get',
    params: { activityId }
  })
}
// -----------活动模版end------------------

// 用户邀请分页列表
export function pageUserInviteRegister(params) {
  return request({
    url: '/user-invite-register/page',
    method: 'get',
    params
  })
}

// ////////////////////////////////////////////////////// 客服管理 start ////////////////////////////////////////////////////////////////
// 客服列表.
export function listCustomerService(sysOrigin, region) {
  return request({
    url: '/sys/customer-service/list/' + sysOrigin + '?region=' + region,
    method: 'get'
  })
}

// 添加客服.
export function addCustomerService(data) {
  return request({
    url: '/sys/customer-service/add',
    method: 'post',
    data
  })
}

// 修改客服.
export function updateCustomerService(data) {
  return request({
    url: '/sys/customer-service/update',
    method: 'post',
    data
  })
}

// 删除客服.
export function deleteCustomerService(id) {
  return request({
    url: '/sys/customer-service/delete/' + id,
    method: 'get'
  })
}

// ////////////////////////////////////////////////////// 区域管理 start ////////////////////////////////////////////////////////////////
// 区域列表.
export function regionConfigTable(params) {
  return request({
    url: '/region/config/list',
    method: 'get',
    params
  })
}

// 修改区域.
export function updateRegionConfig(data) {
  return request({
    url: '/region/config/update',
    method: 'post',
    data
  })
}

// 添加区域.
export function addRegionConfig(data) {
  return request({
    url: '/region/config/add',
    method: 'post',
    data
  })
}

// 删除区域.
export function deleteRegionConfig(id) {
  return request({
    url: '/region/config/delete/' + id,
    method: 'get'
  })
}

// 清理区域提现.
export function resetRegionWithdrawal(regionId) {
  return request({
    url: '/region/config/reset-withdrawal',
    method: 'get',
    params: { regionId }
  })
}

// ////////////////////////////////////////////////////// 区域辅助数据管理 ////////////////////////////////////////////////////////////////
// 区域辅助数据列表.
export function regionAssistConfigTable(params) {
  return request({
    url: '/region/assist/config/list',
    method: 'get',
    params
  })
}

// 修改区域辅助数据.
export function updateRegionAssistConfig(data) {
  return request({
    url: '/region/assist/config/update',
    method: 'post',
    data
  })
}

// 添加区域辅助数据.
export function addRegionAssistConfig(data) {
  return request({
    url: '/region/assist/config/add',
    method: 'post',
    data
  })
}

// 删除区域辅助数据.
export function deleteRegionAssistConfig(id) {
  return request({
    url: '/region/assist/config/delete/' + id,
    method: 'get'
  })
}

// ////////////////////////////////////////////////////// 区域关系业务编织 start ////////////////////////////////////////////////////////////////
// 区域关系业务列表.
export function regionRelationTable(params) {
  return request({
    url: '/sys/region/relation/page',
    method: 'get',
    params
  })
}

// 修改区域关系业务.
export function updateRegionRelation(data) {
  return request({
    url: '/sys/region/relation/update',
    method: 'post',
    data
  })
}

// 添加区域关系业务.
export function addRegionRelation(data) {
  return request({
    url: '/sys/region/relation/add',
    method: 'post',
    data
  })
}

// ////////////////////////////////////////////////////// 用户充值黑名单 start ////////////////////////////////////////////////////////////////

// 用户充值黑名单列表.
export function userRechargeBlackList() {
  return request({
    url: '/recharge-black-list',
    method: 'get'
  })
}

// 新增用户充值黑名单.
export function addUserRechargeBlackList(userId) {
  return request({
    url: '/recharge-black-list',
    method: 'post',
    params: { userId }
  })
}

// 删除用户充值黑名单.
export function delUserRechargeBlackList(content) {
  return request({
    url: '/recharge-black-list',
    method: 'delete',
    params: { content }
  })
}

// ////////////////////////////////////////////////////// 用户充值黑名单 end ////////////////////////////////////////////////////////////////

// ////////////////////////////////////////////////////// 用户注销 start ////////////////////////////////////////////////////////////////
// 用户注销日志
export function logoutLogPage(params) {
  return request({
    url: '/user-logout-log/page',
    method: 'get',
    params
  })
}
// 用户注销申请
export function logoutApplyPage(params) {
  return request({
    url: '/user-logout-apply/page',
    method: 'get',
    params
  })
}

// ////////////////////////////////////////////////////// 用户注销 end ////////////////////////////////////////////////////////////////
