import request from '@/utils/request'
import {uuid} from '@/utils'

import COS from 'cos-js-sdk-v5'

/**
 * 随机创建文件名称.
 */
const radmonFilename = (filename) => {
  const suffix = filename ? filename.substring(filename.lastIndexOf('.')) : ''
  return `manager-${uuid()}${suffix}`
}

/**
 * 获取url.
 */
const getAccessImgUrl = (key) => {
  if (!key) {
    return ''
  }
  if (key.startsWith('/')) {
    return `${process.env.VUE_APP_OSS_URL}${key}`
  }
  return `${process.env.VUE_APP_OSS_URL}/${key}`
}

/**
 * 简单上传
 * @param {*} fileObj 文件对象
 */
const simpleUploadFlie = (fileObj, dir) => {
  return new Promise((resolve, reject) => {
    request({
      url: '/ali-yun/oss/sts',
      method: 'get'
    }).then(res => {
      const stsResult = res.body
      if (stsResult) {

        const cos = new COS({
          SecretId: stsResult['accessKeyId'], // sts服务下发的临时 secretId
          SecretKey: stsResult['accessKeySecret'], // sts服务下发的临时 secretKey
          SecurityToken: stsResult['securityToken'] // sts服务下发的临时 SessionToken

        })
        const filename = '/console/' +  radmonFilename(fileObj.file.name)
        // 上传文件
        //打印
        // console.log('Bucket', process.env.VUE_APP_OSS_BUCKET)
        // console.log('fileObj', fileObj)
        // console.log('filename', filename)
        // console.log('AccessKeyId', stsResult['AccessKeyId'])
        // console.log('AccessKeySecret', stsResult['AccessKeySecret'])
        // console.log('SecurityToken', stsResult['SecurityToken'])

        cos.uploadFile({
          Bucket: process.env.VUE_APP_OSS_BUCKET,
          Region: "ap-beijing",
          Key: filename,
          Body: fileObj.file, // 要上传的文件对象。
          onProgress: function (progressData) {
            console.log('上传进度：', progressData);
          }
        }, function (err, data) {
          console.log('上传结束', err || data);
          if (err) {
            reject(err)
          } else {
            // 打印返回的 URL
            console.log('返回的图片 URL:', data.Location);
            // 使用 getAccessImgUrl 生成正确的 URL
            const imgUrl = getAccessImgUrl(filename);
            console.log('生成的图片 URL:', imgUrl);
            resolve({name:imgUrl});
          }
        })
      }
    }).catch(er => {
      console.error(er)
      reject(er)
    })
  })
}
// /**
//  * 简单上传
//  * @param {*} fileObj 文件对象
//  */
// const simpleUploadFlie = (fileObj, dir) => {
//   return new Promise((resolve, reject) => {
//     request({
//       url: '/ali-yun/oss/sts',
//       method: 'get'
//     }).then(res => {
//       const stsResult = res.body
//       if (stsResult) {
//         const client = new OSS({
//           endpoint: 'oss-accelerate.aliyuncs.com',
//           accessKeyId: stsResult['AccessKeyId'],
//           accessKeySecret: stsResult['AccessKeySecret'],
//           stsToken: stsResult['SecurityToken'],
//           bucket: process.env.VUE_APP_OSS_BUCKET
//         })
//         try {
//           const filename = fileObj.customFilename || radmonFilename(fileObj.file.name)
//           client.put(`${dir || 'other'}/${filename}`, fileObj.file).then(ossRes => {
//             resolve(ossRes)
//           }).catch(er => {
//             console.error('error:', er)
//             reject(er)
//           })
//         } catch (er) {
//           console.error('error:', er)
//           reject(er)
//         }
//       }
//     }).catch(er => {
//       console.error(er)
//       reject(er)
//     })
//   })
// }

export {
  radmonFilename,
  simpleUploadFlie,
  getAccessImgUrl
}
