import request from '@/utils/request'

// 批量删除
export function batchRemoves(data) {
  return request({
    url: '/sys/video/stream/batch/delete',
    method: 'post',
    data
  })
}

// 批量收藏
export function batchSaves(data) {
  return request({
    url: '/sys/video/stream/batch/save',
    method: 'post',
    data
  })
}

// 视频通话录像
export function getVideoCallStream(params) {
  return request({
    url: '/sys/video/stream/page',
    method: 'get',
    params
  })
}

// 删除视频会话
export function deleteStream(id) {
  return request({
    url: `/sys/video/stream/delete/${id}`,
    method: 'get'
  })
}

// 收藏视频会话
export function saveVideoStreamCollection(id) {
  return request({
    url: `/sys/video/stream/save/${id}`,
    method: 'get'
  })
}

export function addAdministrator(data) {
  return request({
    url: `/sys/administrator/add`,
    method: 'post',
    data
  })
}

export function deleteAdministrator(id) {
  return request({
    url: `/sys/administrator/delete/${id}`,
    method: 'get'
  })
}

