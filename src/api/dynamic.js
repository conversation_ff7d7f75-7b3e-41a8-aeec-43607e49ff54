// 动态
import request from '@/utils/request'

// 黑名单列表
export function blacklistTable(params) {
  return request({
    url: '/dynamic/blacklist/page',
    method: 'get',
    params
  })
}

// 新增黑名单
export function addBlacklist(data) {
  return request({
    url: '/dynamic/blacklist/add',
    method: 'post',
    data
  })
}

// 删除黑名单
export function deleteBlacklist(userId) {
  return request({
    url: '/dynamic/blacklist/delete/' + userId,
    method: 'get'
  })
}

// Tag列表
export function tagTable(params) {
  return request({
    url: '/dynamic/tag/page',
    method: 'get',
    params
  })
}

// 修改或新增Tag信息
export function addOrUpdate(data) {
  return request({
    url: '/dynamic/tag/add-or-update',
    method: 'post',
    data
  })
}

// 投诉动态列表
export function reportTable(params) {
  return request({
    url: '/dynamic/report/page',
    method: 'get',
    params
  })
}

// 设置置顶
export function setUpTop(data) {
  return request({
    url: '/user/dynamic/setUpTop',
    method: 'post',
    data
  })
}

// 取消置顶
export function closeTop(dynamicId) {
  return request({
    url: '/user/dynamic/closeTop/' + dynamicId,
    method: 'get'
  })
}

// 处理投诉
export function report(data) {
  return request({
    url: '/dynamic/report',
    method: 'post',
    data
  })
}

// 动态内容
export function getContentTable(params) {
  return request({
    url: '/approval/dynamic/content/page',
    method: 'get',
    params
  })
}

// 动态内容审批-通过
export function approvalContentPass(data) {
  return request({
    url: '/approval/dynamic/content/pass',
    method: 'post',
    data
  })
}

// 动态内容审批-不通过
export function approvalContentNotPass(data) {
  return request({
    url: '/approval/dynamic/content/not/pass',
    method: 'post',
    data
  })
}

// 热门权重配置
export function getPopularConfig() {
  return request({
    url: '/dynamic/popular/config',
    method: 'get'
  })
}

// 热门权重配置
export function savePopularConfig(data) {
  return request({
    url: '/dynamic/popular/config',
    method: 'post',
    data
  })
}

// 用户动态列表
export function userDynamicTable(params) {
  return request({
    url: '/user/dynamic/page',
    method: 'get',
    params
  })
}

// 删除用户动态
export function deleteUserDynamic(data) {
  return request({
    url: '/user/dynamic/delete',
    method: 'post',
    data
  })
}
