
import request from '@/utils/request'

// 新建push
export function newPush(data) {
  return request({
    url: '/push',
    method: 'post',
    data
  })
}

// push文案库
export function messageCopywritingPage(params) {
  return request({
    url: '/message/copywriting/page',
    method: 'get',
    params
  })
}

// 定时推送消息列表
export function pushTaskPage(params) {
  return request({
    url: '/push/task/page',
    method: 'get',
    params
  })
}

// 保存定时推送任务
export function savePushTask(data) {
  return request({
    url: '/push/task/save',
    method: 'post',
    data
  })
}

// 删除推送任务
export function deletePushTask(id) {
  return request({
    url: '/push/task/delete/' + id,
    method: 'get'
  })
}

// push日志
export function pushLogTable(params) {
  return request({
    url: '/push/page',
    method: 'get',
    params
  })
}

// 添加push文案
export function addMessageCopywriting(data) {
  return request({
    url: '/message/copywriting',
    method: 'post',
    data
  })
}

// 删除push文案
export function removeMessageCopywriting(id) {
  return request({
    url: `/message/copywriting/${id}`,
    method: 'delete'
  })
}

// 已录入文案
export function getPushTextHistory(id) {
  return request({
    url: `/sys/push/text/content/${id}`,
    method: 'get'
  })
}

// 消息同步
export function synchronPushText() {
  return request({
    url: '/sys/push/text/content/synchronize',
    method: 'get'
  })
}
