
import request from '@/utils/request'

// 赏金任务配置
export function getGameFruitBountyConfigTable(params) {
  return request({
    url: '/game/fruit/bounty-config',
    method: 'get',
    params
  })
}

// 新增/修改赏金任务配置
export function addOrUpdateGameFruitBountyConfig(data) {
  return request({
    url: '/game/fruit/bounty-config',
    method: 'post',
    data
  })
}

// 删除赏金任务配置信息
export function deleteGameFruitBountyConfig(id) {
  return request({
    url: `/game/fruit/bounty-config/${id}`,
    method: 'delete'
  })
}

// 任务配置
export function getGameFruitTaskConfig(params) {
  return request({
    url: '/game/fruit/task-config',
    method: 'get',
    params
  })
}

// 新增/修改任务配置
export function addGameFruitTaskConfig(data) {
  return request({
    url: '/game/fruit/task-config',
    method: 'post',
    data
  })
}

// 删除任务配置信息
export function deleteGameFruitTaskConfig(id) {
  return request({
    url: `/game/fruit/task-config/${id}`,
    method: 'delete'
  })
}

// 获取游戏奖项图片信息
export function getGameFruitImages(sysOrigin) {
  return request({
    url: `/game/fruit/images/${sysOrigin}`,
    method: 'get'
  })
}
