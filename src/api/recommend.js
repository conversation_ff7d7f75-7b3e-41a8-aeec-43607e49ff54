
import request from '@/utils/request'

// 推荐人列表
export function recommendTable(params) {
  return request({
    url: '/cacoo/recommend',
    method: 'get',
    params
  })
}

// 删除推荐人信息
export function deleteRecommend(userId) {
  return request({
    url: `/cacoo/recommend/delete/${userId}`,
    method: 'get'
  })
}

// 新增推荐人
export function addRecommend(data) {
  return request({
    url: '/cacoo/recommend',
    method: 'post',
    data
  })
}

// 获取归属系统
export function getRecommendAttribution(userId) {
  return request({
    url: `/cacoo/recommend/sysOrigin/${userId}`,
    method: 'get'
  })
}

// 修改归属系统
export function deleteAndAddRecommendAttribution(data) {
  return request({
    url: '/cacoo/recommend',
    method: 'put',
    data
  })
}

