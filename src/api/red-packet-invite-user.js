import request from '@/utils/request'

// 邀请新用户-目标中转账户-分页.
export function pageTargetTransitBalance(params) {
  return request({
    url: '/user/invite-new/target/transit/balance/page',
    method: 'get',
    params
  })
}

// 邀请新用户-兑换目标.
export function reduceTarget(data) {
  return request({
    url: '/user/invite-new/target/transit/reduce',
    method: 'post',
    data
  })
}

// 邀请新用户-美元中转账户-分页.
export function pageWithdrawTransitBalance(params) {
  return request({
    url: '/user/invite-new/withdraw/transit/balance/page',
    method: 'get',
    params
  })
}

// 邀请新用户-红包抽奖记录-分页.
export function pageRedPacketDrawLog(params) {
  return request({
    url: '/user/invite-new/red/packet/draw/log/page',
    method: 'get',
    params
  })
}

// 邀请新用户-助力记录-分页.
export function pageRedPacketHelpLog(params) {
  return request({
    url: '/user/invite-new/red/packet/help/log/page',
    method: 'get',
    params
  })
}

// 邀请新用户-红包记录-分页.
export function pageRedPacket(params) {
  return request({
    url: '/user/invite-new/red/packet/log/page',
    method: 'get',
    params
  })
}

// 邀请新用户-转盘奖项列表.
export function listCarouselAwards(sysOrigin) {
  return request({
    url: '/user/invite-new/red/packet/carousel/awards/list?sysOrigin=' + sysOrigin,
    method: 'get'
  })
}

// 邀请新用户-转盘奖项-保存.
export function saveCarouselAwards(data) {
  return request({
    url: '/user/invite-new/red/packet/carousel/awards/save',
    method: 'post',
    data
  })
}

// 邀请新用户-删除转盘奖项.
export function deleteCarouselAwards(id) {
  return request({
    url: '/user/invite-new/red/packet/carousel/awards/delete?id=' + id,
    method: 'get'
  })
}

// 邀请新用户-删除转盘奖项.
export function getConfigBySysOrigin(sysOrigin) {
  return request({
    url: '/user/invite-new/red/packet/config?sysOrigin=' + sysOrigin,
    method: 'get'
  })
}

// 邀请新用户-配置-保存.
export function saveConfig(data) {
  return request({
    url: '/user/invite-new/red/packet/config/save',
    method: 'post',
    data
  })
}
