
import request from '@/utils/request'

// 徽章规则列表
export function badgeTable(params) {
  return request({
    url: '/sys/badge/config',
    method: 'get',
    params
  })
}

// 修改徽章规则信息
export function updateBadge(data) {
  return request({
    url: '/sys/badge/config',
    method: 'put',
    data
  })
}

// 删除徽章规则信息
export function deleteBadge(id) {
  return request({
    url: `/sys/badge/config/delete/${id}`,
    method: 'get'
  })
}

// 新增徽章规则信息
export function addBadge(data) {
  return request({
    url: '/sys/badge/config',
    method: 'post',
    data
  })
}

// 徽章赠送 - 非成就徽章
export function giveBadgeTable(params) {
  return request({
    url: '/sys/badge/config/getGiveBadgePage',
    method: 'get',
    params
  })
}

// 发送管理员徽章
export function giveBadge(badgeId, userId) {
  return request({
    url: `/sys/badge/config/give/badge/${badgeId}/${userId}`,
    method: 'get'
  })
}

// 收回管理员徽章
export function retrieveBadge(badgeId, userId) {
  return request({
    url: `/sys/badge/config/retrieve/badge/${badgeId}/${userId}`,
    method: 'get'
  })
}

/**
 * 获取活动徽章列表.
 */
export function listBadgeByType(type) {
  return request({
    url: '/sys/badge/config/list/badge-by-type',
    method: 'get',
    params: { type }
  })
}

/**
 * 添加或修改资源.
 */
export function saveOrUpdateBadgePicture(data) {
  return request({
    url: '/sys/badge/picture/config/add-or-update',
    method: 'post',
    data
  })
}

/**
 * 获取平台徽章信息.
 */
export function listBadgePictureBySysOrigin(sysOrigin, type) {
  return request({
    url: '/sys/badge/config/sys-origin',
    method: 'post',
    params: { sysOrigin, type }
  })
}

// 查询用户非成就徽章
export function listUserNotAchieveBadge(userId) {
  return request({
    url: '/user/badge/backpack/not-achieve/' + userId,
    method: 'get'
  })
}
