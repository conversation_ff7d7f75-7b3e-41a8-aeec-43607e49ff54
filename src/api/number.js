
import request from '@/utils/request'

// 靓号列表
export function numberTable(params) {
  return request({
    url: '/user/beautiful/number',
    method: 'get',
    params
  })
}

// 修改靓号信息
export function updateNumber(data) {
  return request({
    url: '/user/beautiful/number',
    method: 'put',
    data
  })
}

// 删除靓号信息
export function deleteNumber(id) {
  return request({
    url: `/user/beautiful/number/delete/${id}`,
    method: 'get'
  })
}

// 新增靓号信息
export function addNumber(data) {
  return request({
    url: '/user/beautiful/number',
    method: 'post',
    data
  })
}

