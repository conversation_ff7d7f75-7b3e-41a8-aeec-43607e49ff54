
import request from '@/utils/request'

// 字典列表
export function pageDictionary(params) {
  return request({
    url: '/sys/dictionary/page',
    method: 'get',
    params
  })
}

// 修改字典主题信息
export function updateSysDictionary(data) {
  return request({
    url: '/sys/dictionary',
    method: 'put',
    data
  })
}

// 删除字典信息
export function deleteSysDictionary(id) {
  return request({
    url: `/sys/dictionary/${id}`,
    method: 'delete'
  })
}

// 添加字典信息
export function addSysDictionary(data) {
  return request({
    url: '/sys/dictionary',
    method: 'post',
    data
  })
}

