
import request from '@/utils/request'
import { httpGetExport } from '@/utils/export-excel'

// 今日内购总额
export function purchaseTodayTotal(params) {
  return request({
    url: '/order/purchase/history/purchase/today/total',
    method: 'get',
    params
  })
}

// 今日内购总额
export function purchaseTodayTotalBySysOrigin(sysOrigin) {
  return request({
    url: `/order/purchase/history/${sysOrigin}/purchase/today/total`,
    method: 'get'
  })
}

// 金币收支流水
export function listGoldRunningWater(params) {
  return request({
    url: '/user-wallet/gold/running-water/list',
    method: 'get',
    params
  })
}

// 金币收支流水cls
export function getClsGoldRunningWater(params) {
  return request({
    url: '/user-wallet/gold/running-water/cls',
    method: 'get',
    params
  })
}

// 用户购买总额
export function getBuyTotalByUserId(userId) {
  return request({
    url: `/order/purchase/history/buy/total/${userId}`,
    method: 'get'
  })
}

// 购买记录
export function getPurchaseTable(params) {
  return request({
    url: '/order/purchase/history/page',
    method: 'get',
    params
  })
}

// 购买消耗类型列表 - 导出
export function exportPurchaseConsumeTable(params, excelName) {
  return httpGetExport('/order/purchase/history/consume/export', params, excelName)
}

// 内购订单详情
export function getInAppPurchase(id) {
  return request({
    url: '/order/in-app-purchase/details',
    method: 'get',
    params: { id }
  })
}

// 内购订单详情- list
export function listInAppPurchase(params) {
  return request({
    url: '/order/in-app-purchase/details/list',
    method: 'get',
    params
  })
}

// 获取异常订单日志列表
export function getOrderAbnormalPage(params) {
  return request({
    url: '/order/abnormal/log/page',
    method: 'get',
    params
  })
}

// 获取异常订单日志,凭证
export function getOrderAbnormalCertificate(id) {
  return request({
    url: '/order/abnormal/log/certificate',
    method: 'get',
    params: { id }
  })
}

// 修改异常订单日志,装态
export function updateOrderAbnormalStatus(params) {
  return request({
    url: '/order/abnormal/log/status',
    method: 'put',
    params
  })
}

// 订单补偿
export function orderPurchaseHistoryCompensate(data) {
  return request({
    url: '/order/purchase/history/compensate',
    method: 'post',
    data
  })
}

// 补偿糖果
export function compensateCandy(params) {
  return request({
    url: '/order/purchase/history/compensate/candy',
    method: 'post',
    params
  })
}

// 其他支付订单分页
export function orderOtherRechargePage(params) {
  return request({
    url: '/order/other-recharge/page',
    method: 'get',
    params
  })
}

// 处理订单信息-添加
export function orderOtherRechargeProcess(data) {
  return request({
    url: '/order/other-recharge/process',
    method: 'post',
    data
  })
}

