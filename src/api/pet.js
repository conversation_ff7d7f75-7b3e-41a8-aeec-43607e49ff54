
import request from '@/utils/request'

// 宠物配置分页列表
export function pagePetPool(params) {
  return request({
    url: '/pet_pool/page',
    method: 'get',
    params
  })
}

// 添加宠物信息
export function addPet(data) {
  return request({
    url: '/pet_pool/add',
    method: 'post',
    data
  })
}

// 上下架
export function offShelf(id, status) {
  return request({
    url: '/pet_pool/off_shelf',
    method: 'get',
    params: { id, status }
  })
}

// 宠物喂养记录
export function pagePetFeeding(params) {
  return request({
    url: '/pet_pool/feeding/page',
    method: 'get',
    params
  })
}
