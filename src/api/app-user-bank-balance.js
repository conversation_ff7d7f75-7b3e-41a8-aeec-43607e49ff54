import request from '@/utils/request'
import { httpGetExport } from '@/utils/export-excel'

// 用户银行账户分页列表
export function pageBank(params) {
  return request({
    url: '/user-bank-balance/page',
    method: 'get',
    params
  })
}

// 用户银行账户-导出
export function exprotBank(params) {
  return httpGetExport(
    '/user-bank-balance/export',
    params,
    'ExportUserBankAccount'
  )
}

// 用户银行账户-导入
export function confirmImport(form) {
  return request({
    url: '/user-bank-balance/import',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: form
  })
}

// 用户银行流水列表
export function pageRunningWater(params) {
  return request({
    url: '/user-bank-balance/running-water-page',
    method: 'get',
    params
  })
}

// 用户银行流水列表
export function calculateTotalAmount(params) {
  return request({
    url: '/user-bank-balance/calculate/total',
    method: 'get',
    params
  })
}

// 发送用户银行现金
export function sendMoney(data) {
  return request({
    url: '/user-bank-balance/send-money',
    method: 'post',
    data
  })
}

// 创建用户银行现金账户
export function createBankBalance(data) {
  return request({
    url: '/user-bank-balance/create-bank-balance',
    method: 'post',
    data
  })
}

// 扣除用户银行现金
export function deductMoney(data) {
  return request({
    url: '/user-bank-balance/deduct-money',
    method: 'post',
    data
  })
}

// 转账用户银行现金
export function transferMoney(data) {
  return request({
    url: '/user-bank-balance/transfer-money',
    method: 'post',
    data
  })
}

// 用户银行卡金币兑换申请列表
export function pageBankWithdrawGoldApply(params) {
  return request({
    url: '/user-bank-balance/withdraw-gold-apply-page',
    method: 'get',
    params
  })
}

// 用户银行卡提现现金申请
export function pageBankWithdrawMoneyApply(params) {
  return request({
    url: '/user-bank-balance/withdraw-money-apply-page',
    method: 'get',
    params
  })
}

// 用户银行卡提现现金申请-导出
export function exportBankWithdrawMoneyApply(params) {
  return httpGetExport(
    '/user-bank-balance/withdraw-money-apply/export',
    params,
    'ExportWithdrawMoneyApply'
  )
}

// 用户银行卡提现现金申请
export function getBankWithdrawMoneyApply(id) {
  return request({
    url: '/user-bank-balance/withdraw-money-apply/info',
    method: 'get',
    params: { id }
  })
}

// 审核用户银行卡提现现金申请
export function approvalMoneyApply(data) {
  return request({
    url: '/user-bank-balance/approval-money-apply',
    method: 'post',
    data
  })
}

// //////////////////////// 自动化支付工资凭证 ///////////////////////////////////
// 给用户自动发送工资凭证记录
export function pageSalary(params) {
  return request({
    url: '/team/salary/list',
    method: 'get',
    params
  })
}

// //////////////////////// 将主播剩余目标兑换成美元记录 ///////////////////////////////////
// 将主播剩余目标兑换成美元记录
export function pageTeamExchangeTarget(params) {
  return request({
    url: '/team/exchange/target/list',
    method: 'get',
    params
  })
}

// 将主播剩余目标兑换成美元总额
export function calculateExchangeUsdTotalAmount(params) {
  return request({
    url: '/team/exchange/target/usd-total',
    method: 'get',
    params
  })
}
