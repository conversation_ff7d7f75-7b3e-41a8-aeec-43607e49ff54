
import request from '@/utils/request'

// ktv礼物列表
export function songGiftTable(params) {
  return request({
    url: '/game-ktv-gift-config',
    method: 'get',
    params
  })
}

// 修改ktv礼物信息
export function updateSongGift(data) {
  return request({
    url: '/game-ktv-gift-config',
    method: 'put',
    data
  })
}

// 新增ktv礼物信息
export function addSongGift(data) {
  return request({
    url: '/game-ktv-gift-config',
    method: 'post',
    data
  })
}

// 删除ktv礼物信息
export function deleteSongGift(id) {
  return request({
    url: `/game-ktv-gift-config/delete/${id}`,
    method: 'put'
  })
}

// 查询ktv礼物和表情包信息
export function listGiftOrEmoji(params) {
  return request({
    url: '/game-ktv-gift-config/list',
    method: 'get',
    params
  })
}

