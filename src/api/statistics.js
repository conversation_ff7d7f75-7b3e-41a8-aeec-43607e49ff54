
import request from '@/utils/request'

// 每日糖果收入
export function getStatisticsRateIncome(params) {
  return request({
    url: '/statistics/candy/incomeRate',
    method: 'get',
    params
  })
}

// 每日收入总共糖果
export function getStatisticsRateSumIncome(params) {
  return request({
    url: '/statistics/candy/incomeRate/sum',
    method: 'get',
    params
  })
}

// 获取最新的注册注销人数
export function latestRegisterLogout() {
  return request({
    url: '/count/user/latest/register/logout',
    method: 'get'
  })
}

// 获取最近30天最订单购买退款统计数据
export function getDailyPurchaseAmountThirtyDays(areaCode) {
  return request({
    url: '/count/purchase/latest/order',
    method: 'get',
    params: { areaCode }
  })
}

// 获取最新货币收支情况
export function latestStatisticsCurrency() {
  return request({
    url: '/count/currency/daily/platform',
    method: 'get'
  })
}

// 获取最新金币收支详情
export function latestStatisticsGoldOriginDetails(origin) {
  return request({
    url: '/count/currency/daily/gold-details',
    method: 'get',
    params: { origin }
  })
}

// 获取每日金币来源top
export function latestStatisticsGoldOriginDetailsAll(dateNumber) {
  return request({
    url: '/count/currency/daily/gold-details/all',
    method: 'get',
    params: { dateNumber }
  })
}

// 每日用户金币top榜
export function listDailyUserGoldTop(dateNumber) {
  return request({
    url: '/count/currency/daily-user-gold-top',
    method: 'get',
    params: { dateNumber }
  })
}

// 每日用户充值top榜
export function listDailyUserRechargeTop(dateNumber) {
  return request({
    url: '/count/currency/daily-user-recharge-top',
    method: 'get',
    params: { dateNumber }
  })
}

// 最新平台内购情况
export function latestPurchaseAmountPlatform(areaCode) {
  return request({
    url: '/count/purchase/daily/list/sys-origin',
    method: 'get',
    params: { areaCode }
  })
}

// 最新平台每月内购情况
export function latestMonthlyPurchaseAmountPlatform() {
  return request({
    url: '/count/purchase/monthly/list/sys-origin',
    method: 'get'
  })
}

// 高产用户列表
export function pageQualityUsers(params) {
  return request({
    url: '/statistics/quality/users/page',
    method: 'get',
    params
  })
}

// 高产用户列表-每月
export function pageMonthlyQualityUsers(params) {
  return request({
    url: '/statistics/quality/users/page/monthly',
    method: 'get',
    params
  })
}

// 高产用户明细列表
export function pageQualityUsersDetails(params) {
  return request({
    url: '/statistics/quality/users/details/page',
    method: 'get',
    params
  })
}

// 高产用户备注
export function qualityUserRemarkSave(data) {
  return request({
    url: '/statistics/quality/users/add-or-update',
    method: 'post',
    data
  })
}

// 房间指标图
export function listRoomActiveIndex(params) {
  return request({
    url: '/count/operation/active-index/room',
    method: 'get',
    params
  })
}

// 用户活跃指标
export function listUserActiveIndex(params) {
  return request({
    url: '/count/operation/active-index/user',
    method: 'get',
    params
  })
}

// 道具销售情况-指定道具
export function getPropsSaleSinge(params) {
  return request({
    url: '/count/operation/active-index/props-sale-singe',
    method: 'get',
    params
  })
}

// 道具销售情况-列表
export function getPropsSale(params) {
  return request({
    url: '/count/operation/active-index/props-sale',
    method: 'get',
    params
  })
}

// 用户本月充值
export function getThisMonthRechargeByUserId(userId) {
  return request({
    url: '/statistics/quality/users/this-month/recharge',
    method: 'get',
    params: { userId }
  })
}

// 系统用户注册数量
export function getSysOriginByUser(params) {
  return request({
    url: '/count/operation/active-index/sys-user-count',
    method: 'get',
    params
  })
}
