/**
 * 团队管理API.
 */
import request from '@/utils/request'
import { httpGetExport } from '@/utils/export-excel'

// 团队列表.
export function listTeamTable(params) {
  return request({
    url: '/team/list',
    method: 'get',
    params
  })
}

// 团队详情.
export function getTeamDetails(teamId) {
  return request({
    url: '/team/details',
    method: 'get',
    params: { teamId }
  })
}

// 创建团队
export function createTeam(data) {
  return request({
    url: '/team/create',
    method: 'post',
    data
  })
}

// 修改团队资料
export function updateTeamProfile(data) {
  return request({
    url: '/team/update-profile',
    method: 'post',
    data
  })
}

// 修改团队状态
export function updateTeamStatus(data) {
  return request({
    url: '/team/update-status',
    method: 'post',
    data
  })
}

// 添加团队-备注
export function addTeamRemarks(data) {
  return request({
    url: '/team/add-remarks',
    method: 'post',
    data
  })
}

// 移除团队-备注
export function delTeamRemarks(data) {
  return request({
    url: '/team/del-remarks',
    method: 'post',
    data
  })
}

// 删除团队
export function delTeamBatch(teamIds) {
  return request({
    url: '/team/del',
    method: 'post',
    data: teamIds
  })
}

// 添加团队-联系方式
export function addTeamContact(data) {
  return request({
    url: '/team/add-contact',
    method: 'post',
    data
  })
}

// 移除团队-联系方式
export function delTeamContact(data) {
  return request({
    url: '/team/del-contact',
    method: 'post',
    data
  })
}
// 获得成员列表
export function listMembers() {
  return request({
    url: '/users/list/combo',
    method: 'get'
  })
}

// 团队成员列表.
export function listMemberTable(params) {
  return request({
    url: '/team/member/list',
    method: 'get',
    params
  })
}

// 添加成员列表.
export function addTeamMember(data) {
  return request({
    url: '/team/member/add',
    method: 'post',
    data
  })
}

// 移除成员列表.
export function removeTeamMember(data) {
  return request({
    url: '/team/member/remove',
    method: 'post',
    data
  })
}

// 修改成员备注.
export function updateMemberRemark(data) {
  return request({
    url: '/team/member/update-remark',
    method: 'post',
    data
  })
}

// 修改成员归属团队.
export function updateMemberTeam(data) {
  return request({
    url: '/team/member/team-change',
    method: 'post',
    data
  })
}

// ////////////////////////////////////////////////////// 团队政策管理 ////////////////////////////////////////////////////////////////

// 获得当前对外发布策略.
export function teamPolicyReleases(params) {
  return request({
    url: '/team/policy/manager/releases',
    method: 'get',
    params
  })
}

// 对外发布过的策略历史列表.
export function teamPolicyHistoryReleases(params) {
  return request({
    url: '/team/policy/manager/history/releases',
    method: 'get',
    params
  })
}

// 添加策略.
export function teamPolicyAdd(data) {
  return request({
    url: '/team/policy/manager/add',
    method: 'post',
    data
  })
}

// 根据id删除策略.
export function deleteTeamPolicy(id) {
  return request({
    url: '/team/policy/manager/delete/' + id,
    method: 'get'
  })
}

/**
 * 获取系统区域列表.
 */
export function listSysRegionPolicy(sysOrigin, region) {
  return request({
    url: '/team/policy/manager/sys-region',
    method: 'get',
    params: { sysOrigin, region }
  })
}

// 团队账单计算.
export function getTeamBillCalculator(params) {
  return request({
    url: '/team/bill/calculator',
    method: 'get',
    params
  })
}

// 团队账单.
export function listTeamBillTable(params) {
  return request({
    url: '/team/bill/list',
    method: 'get',
    params
  })
}

// 团队账单-内部备注添加.
export function addBillInternalRemarks(data) {
  return request({
    url: '/team/bill/add/internal-remarks',
    method: 'post',
    data
  })
}

// 团队账单-内部备注删除.
export function delBillInternalRemarks(data) {
  return request({
    url: '/team/bill/del/internal-remarks',
    method: 'post',
    data
  })
}

// 团队账单-备注.
export function updateBillRemarks(data) {
  return request({
    url: '/team/bill/remarks',
    method: 'post',
    data
  })
}

// 团队账单-结算.
export function teamBillSettle(data) {
  return request({
    url: '/team/bill/settle',
    method: 'post',
    data
  })
}

// 团队账单-账单状态.
export function updateBillStatus(data) {
  return request({
    url: '/team/bill/update-status',
    method: 'post',
    data
  })
}

// ////////////////////////////////////////////////////// 团队通知 ////////////////////////////////////////////////////////////////

// 发送通知
export function sendTeamNotice(data) {
  return request({
    url: '/team/notice/send',
    method: 'post',
    data
  })
}

// 根据团队id获得通知列表.
export function listTeamNoticeByTeamId(teamId) {
  return request({
    url: '/team/notice/list/' + teamId,
    method: 'get'
  })
}

// 根据通知id删除团队通知.
export function deleteTeamNoticeById(id) {
  return request({
    url: '/team/notice/delete/' + id,
    method: 'get'
  })
}

// ////////////////////////////////////////////////////// 团队成员申请 ////////////////////////////////////////////////////////////////

// 修改团队成员申请状态
export function teamProcessChangeStatus(data) {
  return request({
    url: '/team/application/process/change/status',
    method: 'post',
    data
  })
}

// 团队申请列表.
export function teamProcessTable(params) {
  return request({
    url: '/team/application/process/list',
    method: 'get',
    params
  })
}

// ////////////////////////////////////////////////////// 团队成员申请日志 ////////////////////////////////////////////////////////////////

// 团队申请成员列表.
export function teamProcessApprovalTable(params) {
  return request({
    url: '/team/application/process/approval/list',
    method: 'get',
    params
  })
}

// 团队成员工作.
export function listTeamMemberWork(params) {
  return request({
    url: '/team/member/work',
    method: 'get',
    params
  })
}

// 切换团队, 团长.
export function switchTeamOwn(data) {
  return request({
    url: '/team/switch/own',
    method: 'post',
    data
  })
}

// 检查当前成员目标情况.
export function checkTarget(params) {
  return request({
    url: '/team/check-target',
    method: 'get',
    params
  })
}

// 查询当前成员修正幸运礼物后的目标情况.
export function checkTargetLuckyGift(params) {
  return request({
    url: '/team/check-target-lucky-gift',
    method: 'get',
    params
  })
}

// 重置用户目标.
export function resetUnpaidBillMemberTarget(data) {
  return request({
    url: '/team/reset-target',
    method: 'post',
    data
  })
}

// 重置用户目标.
export function resetUnpaidBillMemberTargetTmp(data) {
  return request({
    url: '/team/reset-target-psg',
    method: 'post',
    data
  })
}

// ////////////// 导出 /////////////////////////////

// 导出主播账单数据
export function excelAnchorBill(params, excelName) {
  return httpGetExport('/team/bill/excel', params, excelName)
}

// ////////////// 将代理绑定到指定BD名下 /////////////////////////////
export function teamBindBd(data) {
  return request({
    url: '/team/bind/bd',
    method: 'post',
    data
  })
}

