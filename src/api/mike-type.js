
import request from '@/utils/request'

// mikeType列表
export function mikeTypeTable(params) {
  return request({
    url: '/sys/mike-type/config',
    method: 'get',
    params
  })
}

// 修改mikeType信息
export function updateMikeType(data) {
  return request({
    url: '/sys/mike-type/config',
    method: 'put',
    data
  })
}

// 删除mikeType信息
export function deleteMikeType(id) {
  return request({
    url: `/sys/mike-type/config/delete/${id}`,
    method: 'delete'
  })
}

// 新增mikeType信息
export function addMikeType(data) {
  return request({
    url: '/sys/mike-type/config',
    method: 'post',
    data
  })
}

