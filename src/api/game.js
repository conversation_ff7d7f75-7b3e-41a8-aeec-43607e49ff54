
import request from '@/utils/request'

// /////////////////////////////////////////////////////// 2种水果游戏 ///////////////////////////////////////////////////////////
// 水果游戏记录 50s
export function pageFruitMachine(params) {
  return request({
    url: '/game/fruit/machine/page',
    method: 'get',
    params
  })
}

// 水果游戏用户记录 50s
export function pageFruitMachineUserBet(params) {
  return request({
    url: '/game/fruit/machine/user/page',
    method: 'get',
    params
  })
}

// /////////////////////////////////////////////////////// lucky box 游戏 ///////////////////////////////////////////////////////////
// lucky box 抽奖列表.
export function listGameLuckyBox(params) {
  return request({
    url: '/game/lucky/box/list',
    method: 'get',
    params
  })
}

// 获得系统盈亏数量.
export function getProfitLossAmount(params) {
  return request({
    url: '/game/lucky/box/profit-loss',
    method: 'get',
    params
  })
}

// 奖金池.
export function getPrizePoolAmount(sysOrigin) {
  return request({
    url: '/game/lucky/box/prize-pool',
    method: 'get',
    params: { sysOrigin }
  })
}

// 幸运奖金池.
export function getLuckyPrizePoolAmount(sysOrigin) {
  return request({
    url: '/game/lucky/box/lucky-prize-pool',
    method: 'get',
    params: { sysOrigin }
  })
}

// 获取设置抽成比率-幸运.
export function getLuckyDrawRatio(sysOrigin) {
  return request({
    url: '/game/lucky/box/lucky-draw-ratio',
    method: 'get',
    params: { sysOrigin }
  })
}

// 设置抽成比率-幸运.
export function setLuckyDrawRatio(sysOrigin, ratio) {
  return request({
    url: '/game/lucky/box/lucky-draw-ratio',
    method: 'post',
    params: { sysOrigin, ratio }
  })
}

// 获取设置抽成比率-投入奖金.
export function getPoolPutRatio(sysOrigin) {
  return request({
    url: '/game/lucky/box/pool-put-ratio',
    method: 'get',
    params: { sysOrigin }
  })
}

// 设置抽成比率-投入奖金.
export function setPoolPutRatio(sysOrigin, ratio) {
  return request({
    url: '/game/lucky/box/pool-put-ratio',
    method: 'post',
    params: { sysOrigin, ratio }
  })
}

// /////////////////////////////////////////////////////// 砸金蛋游戏 ///////////////////////////////////////////////////////////
// 碎片兑换记录
export function pageEggExchangeRecord(params) {
  return request({
    url: '/game/egg/exchange/page',
    method: 'get',
    params
  })
}

// 抽奖记录
export function pageEggLotteryRecord(params) {
  return request({
    url: '/game/egg/lottery/record/page',
    method: 'get',
    params
  })
}

// 配置信息-获取
export function getEggConfig(params) {
  return request({
    url: '/game/egg/config',
    method: 'get',
    params
  })
}

// 配置信息-编辑
export function updateEggConfig(data) {
  return request({
    url: '/game/egg/config',
    method: 'post',
    data
  })
}

// 配置信息-编辑
export function resetEggConsumeStockQuantity(id) {
  return request({
    url: '/game/egg/config/reset-consume-stock',
    method: 'get',
    params: { id }
  })
}

// 抽取概率-获取
export function getEggExtractRatio(sysOrigin) {
  return request({
    url: '/game/egg/config/extract-pool',
    method: 'get',
    params: { sysOrigin }
  })
}

// 抽取概率-编辑
export function setEggExtractRatio(sysOrigin, ratio) {
  return request({
    url: '/game/egg/config/set-extract-pool',
    method: 'get',
    params: { sysOrigin, ratio }
  })
}

// 抽取概率-编辑
export function getEggPrizePool(sysOrigin) {
  return request({
    url: '/game/egg/config/prize-pool',
    method: 'get',
    params: { sysOrigin }
  })
}

// /////////////////////////////////////////////////////// 抽奖游戏 ///////////////////////////////////////////////////////////
// 抽奖奖励配置-分页
export function pageGameLotteryGroup(params) {
  return request({
    url: '/game/lottery/reward/group/page',
    method: 'get',
    params
  })
}

// 抽奖奖励配置-修改/添加
export function saveOrUpdateGameLotteryGroup(data) {
  return request({
    url: '/game/lottery/reward/group/save_or_update',
    method: 'post',
    data
  })
}

// 抽奖奖励配置-上下架
export function offGameLotteryGroup(id, offShelf) {
  return request({
    url: `/game/lottery/reward/group/off/shelf/${id}/${offShelf}`,
    method: 'get'
  })
}

// /////////////////////////////////////////////////////// 果游戏 ///////////////////////////////////////////////////////////

// 双层糖果总额By条件
export function totalDoubleLayerFruitUserBet(params) {
  return request({
    url: '/game/double/layer/fruit/total',
    method: 'get',
    params
  })
}
// /////////////////////////////////////////////////////// Ludo飞行棋游戏 ///////////////////////////////////////////////////////////

// 飞行棋游戏-列表
export function flowLudoGame(params) {
  return request({
    url: '/game-ludo/flow',
    method: 'get',
    params
  })
}

// 飞行棋游戏-解散
export function dismissLudoGame(roomId) {
  return request({
    url: '/game-ludo/dismiss',
    method: 'get',
    params: { roomId }
  })
}

// /////////////////////////////////////////////////////// 炸金花游戏 ///////////////////////////////////////////////////////////

// 游戏记录分页列表-炸金花
export function pageTableTeenPatti(params) {
  return request({
    url: '/game/teen-patti/page',
    method: 'get',
    params
  })
}

// 游戏记录分页列表,用户押注-炸金花
export function pageTableUserBetTeenPatti(params) {
  return request({
    url: '/game/teen-patti/user-bet/page',
    method: 'get',
    params
  })
}

// 收支情况-炸金花
export function getIncomeAndExpenditureTeenPatti(params) {
  return request({
    url: '/game/teen-patti/total',
    method: 'get',
    params
  })
}

// 配置信息-炸金花
export function getConfigTeenPatti() {
  return request({
    url: '/game/teen-patti/config',
    method: 'get'
  })
}

// 配置信息,修改-炸金花
export function updateConfigTeenPatti(data) {
  return request({
    url: '/game/teen-patti/config',
    method: 'post',
    data
  })
}

// 奖金池余额-炸金花
export function getBonusAmountBalanceTeenPatti() {
  return request({
    url: '/game/teen-patti/bonus-balance',
    method: 'get'
  })
}

// /////////////////////////////////////////////////////// 喇叭 ///////////////////////////////////////////////////////////

// 获得前50个喇叭列表.
export function listTrumpet(params) {
  return request({
    url: '/game/trumpet/list',
    method: 'get',
    params
  })
}

// 根根据id删除喇叭.
export function deleteTrumpet(id) {
  return request({
    url: `/game/trumpet/delete/${id}`,
    method: 'get'
  })
}

// 黑名单列表
export function trumpetBlacklistTable(params) {
  return request({
    url: '/game-trumpet-blacklist/page',
    method: 'get',
    params
  })
}

// 新增黑名单
export function addTrumpetBlacklist(data) {
  return request({
    url: '/game-trumpet-blacklist/add',
    method: 'post',
    data
  })
}

// 删除黑名单
export function deleteTrumpetBlacklist(userId) {
  return request({
    url: '/game-trumpet-blacklist/delete/' + userId,
    method: 'get'
  })
}

// /////////////////////////////////////////////////////// PK记录 /////////////////////////////
// 房间PK列表.
export function listRoomPk(params) {
  return request({
    url: '/game/pk/list-room-pk',
    method: 'get',
    params
  })
}

// 团队PK列表.
export function listTeamPk(params) {
  return request({
    url: '/game/pk/list-team-pk',
    method: 'get',
    params
  })
}

// /////////////////////////////////////////////////////// 摩天轮游戏 /////////////////////////////
// 游戏奖项.
export function prizeList(params) {
  return request({
    url: '/game/fruit/config/list/prize',
    method: 'get',
    params
  })
}

// 编辑游戏奖项.
export function updatePrize(data) {
  return request({
    url: '/game/fruit/config/update/prize',
    method: 'post',
    data
  })
}

// 新增游戏奖项.
export function addPrize(data) {
  return request({
    url: '/game/fruit/config/add/prize',
    method: 'post',
    data
  })
}

// 编辑游戏规则配置.
export function updateGameFruitRule(data) {
  return request({
    url: '/game/fruit/config/update/rule',
    method: 'post',
    data
  })
}

// 新增游戏规则配置.
export function addGameFruitRule(data) {
  return request({
    url: '/game/fruit/config/add/rule',
    method: 'post',
    data
  })
}

// 查询游戏规则配置.
export function getGameFruitRule(params) {
  return request({
    url: '/game/fruit/config/get/rule',
    method: 'get',
    params
  })
}

// 查询游戏历史记录.
export function getGameFruitDaysRecord(params) {
  return request({
    url: '/game/fruit/history/pageGameFruitDaysRecord',
    method: 'get',
    params
  })
}

// 查询游戏下注记录.
export function pageUserBetRecord(params) {
  return request({
    url: '/game/fruit/history/pageUserBetRecord',
    method: 'get',
    params
  })
}

// 查询用户每日记录.
export function pageDaysUserData(params) {
  return request({
    url: '/game/fruit/history/pageDaysUserData',
    method: 'get',
    params
  })
}
