import request from '@/utils/request'

// 表情列表-分页
export function pageTable(params) {
  return request({
    url: '/emoji/config/page',
    method: 'get',
    params
  })
}

// 表情-添加或修改
export function addOrUpdate(data) {
  return request({
    url: '/emoji/config/add-or-update',
    method: 'post',
    data
  })
}

// 表情-添加或修改
export function offShelfById(id) {
  return request({
    url: '/emoji/config/off-shelf/' + id,
    method: 'post'
  })
}

// 表情分组列表-分页
export function pageGroupTable(params) {
  return request({
    url: '/emoji/config/group/page',
    method: 'get',
    params
  })
}

// 表情分组列表-添加或修改
export function addOrUpdateGroup(data) {
  return request({
    url: '/emoji/config/group/add-or-update',
    method: 'post',
    data
  })
}

// 表情分组列表-根据系统获取
export function listGroupBySysOrigin(sysOrigin) {
  return request({
    url: '/emoji/config/group/sys-origin-list',
    method: 'get',
    params: { sysOrigin }
  })
}

// 表情分组列表-上下架
export function switchGroupShelfStatus(id, status) {
  return request({
    url: '/emoji/config/group/shelf-status-switch',
    method: 'get',
    params: { id, status }
  })
}
