
import request from '@/utils/request'

// 系统公告通知消息列表
export function noticeMessageTable(params) {
  return request({
    url: '/sys/notice/message',
    method: 'get',
    params
  })
}

// 修改系统公告通知消息信息
export function updateNoticeMessage(data) {
  return request({
    url: '/sys/notice/message',
    method: 'put',
    data
  })
}

// 删除系统公告通知消息信息
export function deleteNoticeMessage(id) {
  return request({
    url: `/sys/notice/message/${id}`,
    method: 'delete'
  })
}

// 新增系统公告通知消息信息
export function addNoticeMessage(data) {
  return request({
    url: '/sys/notice/message',
    method: 'post',
    data
  })
}

// 发布系统公告
export function changeAnnouncementStatus(id) {
  return request({
    url: `/sys/notice/message/announcement/${id}`,
    method: 'get'
  })
}

// 上下架系统公告
export function changeShelfStatus(id, offShelf) {
  return request({
    url: `/sys/notice/message/off/shelf/${id}/${offShelf}`,
    method: 'get'
  })
}
