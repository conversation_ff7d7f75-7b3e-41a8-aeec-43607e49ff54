
import request from '@/utils/request'

// 内购产品配置列表
export function getProductConfigList(params) {
  return request({
    url: '/sys/product/config/list',
    method: 'get',
    params
  })
}

// 内购产品礼包配置列表
export function getProductConfigPackList(params) {
  return request({
    url: '/sys/product/config/pack/list',
    method: 'get',
    params
  })
}

// 获取商品包名信息
export function listConfigPackageByCnfId(productCnfId) {
  return request({
    url: '/sys/product/config/config_package',
    method: 'get',
    params: { productCnfId }
  })
}

// 添加/修改包名
export function addOrUpdatePackageBatch(data) {
  return request({
    url: '/sys/product/config/add-or-update/package',
    method: 'post',
    data
  })
}

// 内购产品配置信息
export function getProductConfigInfo(id) {
  return request({
    url: `/sys/product/config/${id}`,
    method: 'get'
  })
}

// 添加内购产品配置信息
export function addBatchProductConfigInfo(data) {
  return request({
    url: '/sys/product/config',
    method: 'post',
    data
  })
}

// 修改内购产品配置信息
export function updateProductConfigInfo(data) {
  return request({
    url: '/sys/product/config',
    method: 'put',
    data
  })
}

// 删除内购产品配置信息
export function delProductConfig(data) {
  return request({
    url: '/sys/product/config/del',
    method: 'delete',
    data
  })
}

// 同步删除内购产品配置信息
export function delByProductId(sysOrigin, productId) {
  return request({
    url: `/sys/product/config/delete/${sysOrigin}/${productId}`,
    method: 'delete'
  })
}

// 推荐商品
export function singleRecommend(params) {
  return request({
    url: '/sys/product/config/single/recommend',
    method: 'post',
    params
  })
}

// 推荐商品,同步所有平台
export function syncAllRecommend(params) {
  return request({
    url: '/sys/product/config/sync/all/recommend',
    method: 'post',
    params
  })
}

// 取消推荐
export function cancelRecommend(params) {
  return request({
    url: '/sys/product/config/cancel/recommend',
    method: 'delete',
    params
  })
}

// 修改排序
export function updateSort(data) {
  return request({
    url: '/sys/product/config/sort',
    method: 'post',
    data
  })
}

// 获得金币产品列表
export function listFirstChargeRewards(sysOrigin) {
  return request({
    url: `/sys/product/config/list_candy_group/${sysOrigin}`,
    method: 'get'
  })
}

// -----------------------产品池配置-----------------------

// 产品池列表
export function listProductPool(params) {
  return request({
    url: '/sys/product/pool',
    method: 'get',
    params
  })
}

// 添加指定产品信息到池子
export function addProductPool(data) {
  return request({
    url: '/sys/product/pool',
    method: 'post',
    data
  })
}

// 删除产品池中指定信息
export function delProductPool(productId) {
  return request({
    url: `/sys/product/pool/${productId}`,
    method: 'delete'
  })
}

// ///////////////////////////// V2 ////////////////////////////////////////
// 内购产品配置列表
export function listProduct(params) {
  return request({
    url: '/sys/product/config/list',
    method: 'get',
    params
  })
}

// 添加内购产品配置
export function addProduct(data) {
  return request({
    url: '/sys/product/config',
    method: 'post',
    data
  })
}

// 修改内购产品配置
export function updateProduct(data) {
  return request({
    url: '/sys/product/config',
    method: 'put',
    data
  })
}
