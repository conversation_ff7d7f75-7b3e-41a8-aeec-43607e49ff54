import request from '@/utils/request'

// 工会成员列表
export function pageFamilyMember(params) {
  return request({
    url: '/family/member/page',
    method: 'get',
    params
  })
}

// 工会列表
export function pageFamily(params) {
  return request({
    url: '/family/page',
    method: 'get',
    params
  })
}

// 工会等级列表
export function pageLevel(params) {
  return request({
    url: '/family/level/config/page',
    method: 'get',
    params
  })
}

// 修改或新增工会等级
export function addOrUpdateLevel(data) {
  return request({
    url: `/family/level/config/add-or-update`,
    method: 'post',
    data
  })
}

// 工会等级配置list
export function pageCreateRule(params) {
  return request({
    url: '/family/create/rule/page',
    method: 'get',
    params
  })
}

// 修改工会等级配置
export function addOrUpdateCreateRule(data) {
  return request({
    url: '/family/create/rule/add-or-update',
    method: 'post',
    data
  })
}

// 工会每周奖励配置列表
export function pageFamilyRewardRule(params) {
  return request({
    url: '/family/reward/rule/page',
    method: 'get',
    params
  })
}

// 修改工会每周奖励配置
export function addOrUpdateFamilyRewardRule(data) {
  return request({
    url: '/family/reward/rule/add-or-update',
    method: 'post',
    data
  })
}

// 解散工会
export function delFamily(familyId) {
  return request({
    url: `/family/del/${familyId}`,
    method: 'get'
  })
}

// 移出工会成员
export function removeFamilyMember(familyId, memberId) {
  return request({
    url: `/family/member/del/${familyId}/${memberId}`,
    method: 'get'
  })
}
