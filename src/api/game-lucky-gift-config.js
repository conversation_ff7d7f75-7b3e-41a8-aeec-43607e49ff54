
import request from '@/utils/request'

// 获取幸运礼物规则配置
export function getLuckyGiftRuleConfig(params) {
  return request({
    url: '/game/lucky-gift/rule-config',
    method: 'get',
    params
  })
}

// 新增/修改幸运礼物规则配置
export function addLuckyGiftRuleConfig(data) {
  return request({
    url: '/game/lucky-gift/rule-config',
    method: 'post',
    data
  })
}

// 幸运礼物规格列表
export function standardConfigTable(params) {
  return request({
    url: '/game/lucky-gift/standard-config',
    method: 'get',
    params
  })
}

// 新增/修改幸运礼物规格配置
export function addOrUpdateStandardConfig(data) {
  return request({
    url: '/game/lucky-gift/standard-config',
    method: 'post',
    data
  })
}

// 删除幸运礼物规格信息
export function deleteStandardConfig(id) {
  return request({
    url: `/game/lucky-gift/standard-config/${id}`,
    method: 'delete'
  })
}

// 获取幸运礼物规格配置Map
export function mapLuckyGiftStandard(sysOrigin) {
  return request({
    url: '/game/lucky-gift/map/probability?sysOrigin=' + sysOrigin,
    method: 'get'
  })
}

// 新增/修改幸运礼物规格概率配置
export function addOrUpdateProbabilityConfig(data) {
  return request({
    url: '/game/lucky-gift/probability-config',
    method: 'post',
    data
  })
}
// 获取幸运礼物规格概率以及详情配置信息
export function gameLuckyGiftProbabilityInfoConfig(params) {
  return request({
    url: '/game/lucky-gift/probability-info-config',
    method: 'get',
    params
  })
}

// 新增/修改礼物规格概率基础配置和详情配置.
export function addLuckyGiftProbabilityInfoConfig(data) {
  return request({
    url: '/game/lucky-gift/probability-info-config',
    method: 'post',
    data
  })
}

// 获取幸运礼物送礼记录
export function pageLuckyGiftGameRecord(params) {
  return request({
    url: '/game/lucky-gift/page/game/record',
    method: 'get',
    params
  })
}

// 根据条件获取幸运礼物统计信息
export function countLuckyGiftGame(params) {
  return request({
    url: '/game/lucky-gift/count',
    method: 'get',
    params
  })
}
