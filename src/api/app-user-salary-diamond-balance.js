import request from '@/utils/request'

// 用户银行账户分页列表
export function pageSalaryDiamond(params) {
  return request({
    url: '/user-salary-diamond/page',
    method: 'get',
    params
  })
}

// 用户银行流水列表
export function pageRunningWater(params) {
  return request({
    url: '/user-salary-diamond/running-water-page',
    method: 'get',
    params
  })
}

// 发送用户银行现金
export function sendSalaryDiamond(data) {
  return request({
    url: '/user-salary-diamond/send',
    method: 'post',
    data
  })
}

// 创建用户银行现金账户
export function createSalaryDiamondBalance(data) {
  return request({
    url: '/user-salary-diamond/create',
    method: 'post',
    data
  })
}

// 扣除用户银行现金
export function deductSalaryDiamond(data) {
  return request({
    url: '/user-salary-diamond/deduct',
    method: 'post',
    data
  })
}

// 转账用户银行现金
export function transferSalaryDiamond(data) {
  return request({
    url: '/user-salary-diamond/transfer',
    method: 'post',
    data
  })
}

