
import request from '@/utils/request'

// 列表
export function pageData(params) {
  return request({
    url: '/sys/start/page/plan/page',
    method: 'get',
    params
  })
}

// 修改信息
export function updateData(data) {
  return request({
    url: '/sys/start/page/plan',
    method: 'put',
    data
  })
}

// 删除信息
export function deleteData(id) {
  return request({
    url: `/sys/start/page/plan/${id}`,
    method: 'post'
  })
}

// 新增信息
export function addData(data) {
  return request({
    url: '/sys/start/page/plan',
    method: 'post',
    data
  })
}

