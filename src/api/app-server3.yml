apiVersion: v1
items:
- apiVersion: apps/v1
  kind: Deployment
  metadata:
    annotations:
      deployment.kubernetes.io/revision: "13"
      kubectl.kubernetes.io/last-applied-configuration: |
        {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{},"labels":{"app":"app-service"},"name":"app-service-deployment","namespace":"rc-service-api"},"spec":{"replicas":6,"revisionHistoryLimit":3,"selector":{"matchLabels":{"app":"app-service"}},"template":{"metadata":{"labels":{"app":"app-service"}},"spec":{"containers":[{"image":"registry.ap-southeast-1.aliyuncs.com/rc1304/appservice:20230413tmpv1","livenessProbe":{"failureThreshold":2,"httpGet":{"path":"/probe/healthy","port":9000},"periodSeconds":5,"timeoutSeconds":2},"name":"app-service","ports":[{"containerPort":9000}],"readinessProbe":{"failureThreshold":2,"httpGet":{"path":"/probe/healthy","port":9000},"periodSeconds":5,"timeoutSeconds":2},"resources":{"limits":{"cpu":"2","memory":"2Gi"},"requests":{"cpu":"1","memory":"1Gi"}},"startupProbe":{"failureThreshold":60,"httpGet":{"path":"/probe/healthy","port":9000},"periodSeconds":5,"timeoutSeconds":2}}],"imagePullSecrets":[{"name":"rc-aliyun-secret"}],"terminationGracePeriodSeconds":30}}}}
    creationTimestamp: "2023-04-13T14:02:33Z"
    generation: 13
    labels:
      app: app-service
    name: app-service-deployment
    namespace: rc-service-api
    resourceVersion: "2895428"
    uid: 039244b7-8b7d-419f-9340-8072ac05527a
  spec:
    progressDeadlineSeconds: 600
    replicas: 6
    revisionHistoryLimit: 3
    selector:
      matchLabels:
        app: app-service
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
      type: RollingUpdate
    template:
      metadata:
        annotations:
          kubectl.kubernetes.io/restartedAt: "2023-04-14T02:36:07Z"
        creationTimestamp: null
        labels:
          app: app-service
      spec:
        containers:
        - image: registry.ap-southeast-1.aliyuncs.com/rc1304/appservice:20230413tmpv3
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 2
            httpGet:
              path: /probe/healthy
              port: 9000
              scheme: HTTP
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 2
          name: app-service
          ports:
          - containerPort: 9000
            protocol: TCP
          readinessProbe:
            failureThreshold: 2
            httpGet:
              path: /probe/healthy
              port: 9000
              scheme: HTTP
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 2
          resources:
            limits:
              cpu: "2"
              memory: 2Gi
            requests:
              cpu: "1"
              memory: 1Gi
          startupProbe:
            failureThreshold: 60
            httpGet:
              path: /probe/healthy
              port: 9000
              scheme: HTTP
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 2
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
        dnsPolicy: ClusterFirst
        imagePullSecrets:
        - name: rc-aliyun-secret
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 30
  status:
    availableReplicas: 6
    conditions:
    - lastTransitionTime: "2023-04-13T14:02:44Z"
      lastUpdateTime: "2023-04-13T14:02:44Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
    - lastTransitionTime: "2023-04-13T14:02:33Z"
      lastUpdateTime: "2023-04-14T02:40:39Z"
      message: ReplicaSet "app-service-deployment-7559b95b9" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    observedGeneration: 13
    readyReplicas: 6
    replicas: 6
    updatedReplicas: 6
kind: List
metadata:
  resourceVersion: ""