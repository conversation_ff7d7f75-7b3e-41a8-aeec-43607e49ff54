import request from '@/utils/request'

// 获取枚举配置列表
export function getAppEnumConfigCache() {
  return request({
    url: '/tools/redis/enum/configs',
    method: 'post'
  })
}

// 获取产品配置列表
export function getPlatformProductConfigCache(sysOrigin) {
  return request({
    url: `/tools/redis/product/configs/${sysOrigin}`,
    method: 'post'
  })
}

// 获取礼物配置列表
export function getGiftConfigCache(userId) {
  return request({
    url: '/tools/redis/gift/configs',
    method: 'post'
  })
}

// 获取用户打卡天数
export function getCheckInDaysCache(userId) {
  return request({
    url: `/tools/redis/${userId}/check_in/days`,
    method: 'post'
  })
}

// 获取api签名
export function requestApiSing(data) {
  return request({
    url: '/tools/api_sing',
    method: 'post',
    data
  })
}

// 分析金币来源
export function goldAnalyze(params) {
  return request({
    url: '/tools/gold_analyze',
    method: 'get',
    params
  })
}

// 别名算法分析
export function alisMethod(data) {
  return request({
    url: '/tools/alis_method',
    method: 'post',
    data
  })
}

// 用户账户调换-检测
export function checkUserSwapUser(params) {
  return request({
    url: '/tools/user-swap-user',
    method: 'get',
    params
  })
}

// 用户账户调换-执行
export function executeUserSwapUser(params) {
  return request({
    url: '/tools/user-swap-user-execute',
    method: 'get',
    params
  })
}

// 用户房间区域变动-执行
export function updateUserRoomRegion(params) {
  return request({
    url: '/tools/user-room-region-update',
    method: 'post',
    params
  })
}

// 注销用户恢复-执行
export function executeUserCancelAndRestore(params) {
  return request({
    url: '/tools/user-cancel-and-restore-execute',
    method: 'post',
    params
  })
}

// api请求日志
export function listApiOperationLog(params) {
  return request({
    url: '/tools/api-operation-log',
    method: 'get',
    params
  })
}

// 获取token信息
export function getAccessTokenByUserId(token) {
  return request({
    url: '/tools/token-user',
    method: 'get',
    params: { token }
  })
}

// 获取用户token
export function getUserToken(userId) {
  return request({
    url: '/tools/user-token',
    method: 'get',
    params: { userId }
  })
}

// 清理redis key.
export function clearRedisKey(key) {
  return request({
    url: '/tools/clean-redis-key',
    method: 'get',
    params: { key }
  })
}

