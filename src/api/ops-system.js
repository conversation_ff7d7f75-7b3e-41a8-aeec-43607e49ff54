import request from '@/utils/request'

/**
 * 获取登录令牌.
 */
export function login(data) {
  return request({
    url: '/account/login',
    method: 'post',
    data
  })
}

/**
 * 退出登录.
 */
export function logout() {
  return request({
    url: '/logout',
    method: 'post'
  })
}

/**
 * 获取当前登录用户信息.
 */
export function getInfo() {
  return request({
    url: '/account/info',
    method: 'get'
  })
}

// 获取账号菜单
export function getAccountMenus() {
  return request({
    url: '/account/menus',
    method: 'get'
  })
}

// 获取按钮权限
export function getButtonPermissions() {
  return request({
    url: '/account/buttons/aliases',
    method: 'get'
  })
}

// ///////////////////////////////// 用户管理 ////////////////////////////////////
// 用户管理列表
export function pageUsers(params) {
  return request({
    url: '/users',
    method: 'get',
    params
  })
}

// 修改用户状态
export function switchUserStatus(id, status) {
  return request({
    url: '/users/update/status',
    method: 'put',
    data: { id, status }
  })
}

// 重置密码
export function restPassword(id) {
  return request({
    url: '/users/reset/password',
    method: 'put',
    params: { id }
  })
}

// 角色管理列表
export function pageRoleTable(params) {
  return request({
    url: '/roles',
    method: 'get',
    params
  })
}

// 添加角色
export function addRole(data) {
  return request({
    url: '/roles',
    method: 'post',
    data
  })
}

// 修改角色
export function updateRole(data) {
  return request({
    url: '/roles/update',
    method: 'put',
    data
  })
}

// 删除角色
export function delRole(id) {
  return request({
    url: '/roles/remove',
    method: 'delete',
    params: { id }
  })
}

// 角色列表
export function getRoles() {
  return request({
    url: '/roles/list',
    method: 'get'
  })
}

// 添加用户
export function addUser(data) {
  return request({
    url: '/users',
    method: 'post',
    data
  })
}

// 修改用户
export function updateUser(data) {
  return request({
    url: '/users/update',
    method: 'put',
    data
  })
}

// ///////////////////////////////// 资源信息 ////////////////////////////////////

// 获取可授权资源
export function getResources() {
  return request({
    url: '/resources/list',
    method: 'get'
  })
}

// 资源管理列表
export function pageResources(params) {
  return request({
    url: '/resources',
    method: 'get',
    params
  })
}

// 刷新资源列表
export function resetResources() {
  return request({
    url: '/resources',
    method: 'PUT'
  })
}

// 角色设置权限菜单
export function updateRoleMenus(data) {
  return request({
    url: '/roles/update/menus',
    method: 'put',
    data
  })
}

// 重置密码
export function resetPassword(data) {
  return request({
    url: '/account/password',
    method: 'put',
    data
  })
}

// ///////////////////////////////// dashboard ////////////////////////////////////
/**
 * 获取dashboard.
 */
export function getDashboard() {
  return request({
    url: '/dashboard',
    method: 'get'
  })
}

/**
   * 添加预览 dashboard 信息.
   */
export function addDashboardPreviews(data) {
  return request({
    url: '/dashboard/previews',
    method: 'post',
    data,
    messageAlert: false
  })
}

/**
   * 移除预览 dashboard 信息.
   */
export function removeDashboardPreviews(id) {
  return request({
    url: '/dashboard/previews',
    method: 'delete',
    params: { id }
  })
}

/**
   * 添加自定义 dashboard 信息.
   */
export function addDashboardCustom(data) {
  return request({
    url: '/dashboard/custom',
    method: 'post',
    data
  })
}

/**
   * 移除自定义 dashboard 信息.
   */
export function removeDashboardCustom(id) {
  return request({
    url: '/dashboard/custom',
    method: 'delete',
    params: { id }
  })
}

/**
   * 修改主题.
   */
export function updateTheme(theme) {
  return request({
    url: '/dashboard/theme',
    method: 'post',
    params: { theme }
  })
}

// ///////////////////////////////// 菜单 ////////////////////////////////////

// 用户管理列表
export function allMenus() {
  return request({
    url: '/menus',
    method: 'get'
  })
}

// 添加菜单
export function addMenu(data) {
  return request({
    url: '/menus/create',
    method: 'post',
    data
  })
}

// 获取菜单
export function getMenu(id) {
  return request({
    url: '/menus/get',
    method: 'get',
    params: { id }
  })
}

// 修改菜单
export function updateMenu(data) {
  return request({
    url: '/menus/update',
    method: 'put',
    data
  })
}

// 删除菜单
export function delMenu(id) {
  return request({
    url: '/menus/remove',
    method: 'delete',
    params: { id }
  })
}

