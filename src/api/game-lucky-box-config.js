
import request from '@/utils/request'

// 获取LuckyBox抽奖记录
export function pageLuckyBoxGameRecord(params) {
  return request({
    url: '/game/lucky-box/page/record',
    method: 'get',
    params
  })
}

// 根据条件获取LuckyBox抽奖统计信息
export function countLuckyBoxGame(params) {
  return request({
    url: '/game/lucky-box/count',
    method: 'get',
    params
  })
}

// 获取幸运值规则配置
export function getLuckyBoxFortuneConfig(params) {
  return request({
    url: '/game/lucky-box/fortune-config',
    method: 'get',
    params
  })
}

// 新增/修改幸运值规则配置
export function addLuckyBoxFortuneConfig(data) {
  return request({
    url: '/game/lucky-box/fortune-config',
    method: 'post',
    data
  })
}

// 赏金任务配置
export function getBountyConfigTable(params) {
  return request({
    url: '/game/lucky-box/bounty-config',
    method: 'get',
    params
  })
}

// 新增/修改赏金任务配置
export function addOrUpdateBountyConfig(data) {
  return request({
    url: '/game/lucky-box/bounty-config',
    method: 'post',
    data
  })
}

// 删除赏金任务配置信息
export function deleteBountyConfig(id) {
  return request({
    url: `/game/lucky-box/bounty-config/${id}`,
    method: 'delete'
  })
}

// 获取赏金任务配置详情
export function getBountyDetialsConfigTable(params) {
  return request({
    url: '/game/lucky-box/bounty-details-config',
    method: 'get',
    params
  })
}

// 新增/修改赏金任务配置详情.
export function addOrUpdateBountyDetialsConfig(data) {
  return request({
    url: '/game/lucky-box/bounty-details-config',
    method: 'post',
    data
  })
}

//  删除赏金任务配置详情信息
export function deleteBountyDetailsConfig(id) {
  return request({
    url: `/game/lucky-box/bounty-details-config/${id}`,
    method: 'delete'
  })
}

// 累计抽奖奖励配置列表.
export function getAwardConfig(params) {
  return request({
    url: '/game/lucky-box/award-config',
    method: 'get',
    params
  })
}

// 新增/修改累计抽奖奖励配置.
export function addGameLuckyBoxAwardConfig(data) {
  return request({
    url: '/game/lucky-box/award-config',
    method: 'post',
    data
  })
}

// 删除累计抽奖奖励配置
export function deleteAwardConfig(id) {
  return request({
    url: `/game/lucky-box/award-config/${id}`,
    method: 'delete'
  })
}

// 获取累计抽奖奖励配置详情
export function getGameLuckyBoxAwardDetailsConfig(params) {
  return request({
    url: '/game/lucky-box/award-details-config',
    method: 'get',
    params
  })
}

// 新增/修改累计抽奖奖励配置详情.
export function addGameLuckyBoxAwardDetailsConfig(data) {
  return request({
    url: '/game/lucky-box/award-details-config',
    method: 'post',
    data
  })
}

//  删除赏金任务配置详情信息
export function deleteAwardDetailsConfig(id) {
  return request({
    url: `/game/lucky-box/award-details-config/${id}`,
    method: 'delete'
  })
}

// 获取抽奖规格配置信息.
export function getStandardConfig(params) {
  return request({
    url: '/game/lucky-box/standard-config',
    method: 'get',
    params
  })
}

// 新增/修改抽奖规格配置信息.
export function addStandardConfig(data) {
  return request({
    url: '/game/lucky-box/standard-config',
    method: 'post',
    data
  })
}

// 切换抽奖规格状态
export function switchStatus(id, status) {
  return request({
    url: `/game/lucky-box/switch_status/${id}/${status}`,
    method: 'post'
  })
}

// 删除抽奖规格配置
export function deleteStandardConfig(id) {
  return request({
    url: `/game/lucky-box/standard-config/${id}`,
    method: 'delete'
  })
}

// 获取抽奖礼物配置
export function getGameLuckyBoxGiftConfig(params) {
  return request({
    url: '/game/lucky-box/gift-config',
    method: 'get',
    params
  })
}

// 新增/修改抽奖礼物配置
export function addOrUpdateGiftConfig(data) {
  return request({
    url: '/game/lucky-box/gift-config',
    method: 'post',
    data
  })
}

// 获取luckyBox礼物规格配置Map
export function mapLuckyBoxGiftMap(params) {
  return request({
    url: '/game/lucky-box/map/gift',
    method: 'get',
    params
  })
}

// 获取抽奖规格配置详情
export function getGameLuckyBoxStandardDetailsConfig(params) {
  return request({
    url: '/game/lucky-box/standard-details-config',
    method: 'get',
    params
  })
}

// 新增/修改抽奖规格配置详情.
export function addGameLuckyBoxStandardDetailsConfig(data) {
  return request({
    url: '/game/lucky-box/standard-details-config',
    method: 'post',
    data
  })
}

