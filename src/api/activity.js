/**
 * 活动相关.
 */
import request from '@/utils/request'

// //////////////////// 名人堂 ///////////////////////////////

// 名人堂列表
export function hallFameTable(params) {
  return request({
    url: '/activity/hall-fame/page',
    method: 'get',
    params
  })
}

// 修改或新增名人堂
export function addOrUpdateHallFame(data) {
  return request({
    url: '/activity/hall-fame/add-or-update',
    method: 'post',
    data
  })
}

// ////////////////////// 国际化描述 //////////////////////////////////
// 删除
export function globalizationDelete(id) {
  return request({
    url: '/activity/globalization/description/del',
    method: 'get',
    params: { id }
  })
}

// 国际化描述列表
export function globalizationTable(id) {
  return request({
    url: '/activity/globalization/description/list',
    method: 'get',
    params: { id }
  })
}

// 修改或新增国际化描述
export function addOrUpdateGlobalization(data) {
  return request({
    url: '/activity/globalization/description/add-or-update',
    method: 'post',
    data
  })
}

// ////////////////////// 房间支持活动 //////////////////////////////////
// 房间支持活动金币领取记录
export function pageRoomContribution(params) {
  return request({
    url: '/activity/room-contribution/page',
    method: 'get',
    params
  })
}

// ////////////////////// 代理活动 //////////////////////////////////
// 代理活动历史记录
export function pageAgentActivity(params) {
  return request({
    url: '/activity/agent-count/page',
    method: 'get',
    params
  })
}
// ////////////////////// 活动图片配置 //////////////////////////////////
// 活动图片列表
export function activityPicture(params) {
  return request({
    url: '/activity-picture/config',
    method: 'get',
    params
  })
}

// 修改活动图片信息
export function updateActivityPicture(data) {
  return request({
    url: '/activity-picture/config',
    method: 'put',
    data
  })
}

// 删除活动图片
export function deleteActivityPicture(id) {
  return request({
    url: `/activity-picture/config/delete/${id}`,
    method: 'delete'
  })
}

// 新增活动图片
export function addActivityPicture(data) {
  return request({
    url: '/activity-picture/config',
    method: 'post',
    data
  })
}
