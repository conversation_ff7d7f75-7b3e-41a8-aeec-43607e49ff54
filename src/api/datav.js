
import request from '@/utils/request'

// 活跃用户分布国家
export function latestActiveUserCountryCode(params) {
  return request({
    url: '/datav/active/user-country-code',
    method: 'get',
    params
  })
}

// 活跃用户分布国家
export function onlineUserCount(params) {
  return request({
    url: '/datav/online/user/count',
    method: 'get',
    params
  })
}

// TimChat在线房间数
export function onlineRoomCount(params) {
  return request({
    url: '/datav/online/room/count',
    method: 'get',
    params
  })
}

