apiVersion: v1
kind: Namespace
metadata:
  name: rc-service-api
---
apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: rc-service-api
  name: app-service-deployment
  labels:
    app: app-service
spec:
  replicas: 6
  revisionHistoryLimit:  3
  selector:
    matchLabels:
      app: app-service
  template:
    metadata:
      labels:
        app: app-service
    spec:
      containers:
        - name: app-service
          image: registry.ap-southeast-1.aliyuncs.com/rc1304/appservice:20230413tmpv3
          ports:
            - containerPort: 9000
          resources:
            requests:
              cpu: "1"
              memory: "1Gi"
            limits:
              cpu: "2"
              memory: "2Gi"
          livenessProbe:
            httpGet:
              path: /probe/healthy
              port: 9000
            failureThreshold: 2
            periodSeconds: 5
            timeoutSeconds: 2
            # initialDelaySeconds: 180
          readinessProbe:
            httpGet:
              path:  /probe/healthy
              port: 9000
            failureThreshold: 2
            periodSeconds: 5
            timeoutSeconds: 2
            # initialDelaySeconds: 180
          startupProbe:
            httpGet:
              path: /probe/healthy
              port: 9000
            timeoutSeconds: 2
            failureThreshold: 60
            periodSeconds: 5
#          lifecycle:
#            preStop:
#              httpGet:
#                path: /probe/shutdown?key=ZDk1p0M36tpxKobK
#                port: 9000
      terminationGracePeriodSeconds: 30
      imagePullSecrets:
        - name: rc-aliyun-secret
---
apiVersion: v1
kind: Service
metadata:
  namespace: rc-service-api
  name: app-service-service
spec:
  selector:
    app: app-service
  ports:
    - port: 80
      targetPort: 9000

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  namespace: rc-service-api
  name: app-service-ingress
  annotations:
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/target-group-attributes: deregistration_delay.timeout_seconds=30
spec:
  ingressClassName: alb
  rules:
    - http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: app-service-service
                port:
                  number: 80

