/**
 * 系统配置-字典相关
 */
import request from '@/utils/request'

// 国家列表
export function pageSysCountryCode(params) {
  return request({
    url: '/sys/country/code/page',
    method: 'get',
    params
  })
}

// 国家修改
export function updateSysCountryCode(data) {
  return request({
    url: '/sys/country/code',
    method: 'put',
    data
  })
}

// 周星礼物分组列表
export function getWeekStarGroupPage(params) {
  return request({
    url: '/sys-week-star-group/page',
    method: 'get',
    params
  })
}

// 添加分组
export function addWeekStarGroup(data) {
  return request({
    url: '/sys-week-star-group',
    method: 'post',
    data
  })
}

// 添加分组
export function delWeekStarGroup(id) {
  return request({
    url: '/sys-week-star-group',
    method: 'delete',
    params: { id }
  })
}
