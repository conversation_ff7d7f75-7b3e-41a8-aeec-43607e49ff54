import request from '@/utils/request'

/**
 * 数据查询.
 */
export function flow(params) {
  return request({
    url: '/user-special-id/flow',
    method: 'get',
    params
  })
}

/**
 * 添加或修改.
 */
export function saveOrUpdate(data) {
  return request({
    url: '/user-special-id/add-or-update',
    method: 'post',
    data
  })
}

/**
 * 修改有效时间靓号.
 */
export function updateExpiredTime(data) {
  return request({
    url: '/user-special-id/expired-time',
    method: 'post',
    data
  })
}

/**
 * 移除.
 */
export function remove(data) {
  return request({
    url: '/user-special-id/remove',
    method: 'post',
    data
  })
}

/**
 * 获取账号最新日志.
 */
export function listLatestLog(account) {
  return request({
    url: '/user-special-id/latest-log',
    method: 'get',
    params: { account }
  })
}

/**
 * 修改靓号.
 */
export function updateAccount(data) {
  return request({
    url: '/user-special-id/update-account',
    method: 'post',
    data
  })
}

// 账号操作记录-分页查询.
export function specialIdLogTable(params) {
  return request({
    url: '/user-special-id/page/latest-log',
    method: 'get',
    params
  })
}

