/**
 * 审批相关
 */
import request from '@/utils/request'

// 获取工会审批资料列表
export function getFamilyApprovalPage(params) {
  return request({
    url: '/family/approval/page',
    method: 'get',
    params
  })
}

// 修改不通过审批的工会头像,公告,昵称
export function notPassFamilyApproval(data) {
  return request({
    url: `/family/approval/not-pass`,
    method: 'post',
    data
  })
}

// 用户管理列表
export function getUserVideoCensorApprvalTable(params) {
  return request({
    url: '/sys/video/call/censor/approval/page',
    method: 'get',
    params
  })
}

// 账号处理
export function accountHandle(data) {
  return request({
    url: '/user/data/violation/expand/approval/account',
    method: 'post',
    data
  })
}

// 视频违规记录
export function getVideoCallCensorApproval(params) {
  return request({
    url: '/sys/video/call/censor/approval/page',
    method: 'get',
    params
  })
}

// 审批违规审批图片
export function approvalVideoCallCensor(data) {
  return request({
    url: '/sys/video/call/censor/approval',
    method: 'post',
    data
  })
}

// 获取用户审批资料列表
export function getUserApprovalData(params) {
  return request({
    url: '/user/data/violation/expand',
    method: 'get',
    params
  })
}

// 用户资料审批
export function approvalUserInfo(data) {
  return request({
    url: '/user/data/violation/expand/approval',
    method: 'post',
    data
  })
}

// 获取违规照片墙信息
export function getViolationLatestPhotowall(userId) {
  return request({
    url: `/user/data/violation/latest/photo/wall/${userId}`,
    method: 'get'
  })
}

// 审批违规历史记录列表
export function pageViolationHistory(params) {
  return request({
    url: '/approval/history/page',
    method: 'get',
    params
  })
}

// 用户积分详情列表
export function pageUserIntegralOrigin(params) {
  return request({
    url: '/user/integral/origin/page',
    method: 'get',
    params
  })
}

// 用户积分流水列表
export function pageUserIntegralOriginStream(params) {
  return request({
    url: '/user/integral/origin/stream/page',
    method: 'get',
    params
  })
}

// 用户积分历史流水列表
export function pageUserIntegralOriginHistoryStream(params) {
  return request({
    url: '/user/integral/origin/history/stream/page',
    method: 'get',
    params
  })
}

// 用户积分兑换账单列表
export function pageTeamBillingIntegralRecord(params) {
  return request({
    url: '/team/billing/integral/record/page',
    method: 'get',
    params
  })
}

// 用户账户审核历史记录
export function getUserStatusLogTable(params) {
  return request({
    url: '/approval/user/account/status/log/page',
    method: 'get',
    params
  })
}

// 最近审批列表
export function getUserStatusLogLatestList(params) {
  return request({
    url: '/approval/user/account/status/log/latest',
    method: 'get',
    params
  })
}

// 用户积分详情列表导出
export function integralExport(params) {
  return request({
    url: '/user/integral/origin/excel',
    method: 'get',
    responseType: 'blob',
    params
  })
}

// 用户积分兑换记录导出
export function integralRecodeExport(params) {
  return request({
    url: '/team/billing/integral/record/excel',
    method: 'get',
    responseType: 'blob',
    params
  })
}

// 用户签到日志
export function pageUserCheckLog(params) {
  return request({
    url: '/user/check/inLog',
    method: 'get',
    params
  })
}

// 照片墙审批-通过
export function approvalPhotoWallPass(data) {
  return request({
    url: '/approval/photo/wall/pass',
    method: 'post',
    data
  })
}

// 照片墙审批-不通过
export function approvalPhotoWallNotPass(data) {
  return request({
    url: '/approval/photo/wall/not/pass',
    method: 'post',
    data
  })
}

// 举报审批-通过
export function approvalReportedPass(data) {
  return request({
    url: '/approval/reported/pass',
    method: 'post',
    data
  })
}

// 举报审批-不通过
export function approvalReportedNotPass(data) {
  return request({
    url: '/approval/reported/not/pass',
    method: 'post',
    data
  })
}

// 房间资料审批信息分页列表
export function pageRoomApproval(params) {
  return request({
    url: '/room/profile-manager/approval/page',
    method: 'get',
    params
  })
}

// 审批违规资料
export function approvalData(data) {
  return request({
    url: '/data/approval',
    method: 'post',
    data
  })
}

// 用户个性签名审批列表
export function pageUserProfileDescApproval(params) {
  return request({
    url: '/data/approval/user-profile-desc/page',
    method: 'get',
    params
  })
}

// 用户头像昵称审批列表
export function pageUserProfileApproval(params) {
  return request({
    url: '/data/approval/user-profile/page',
    method: 'get',
    params
  })
}

// 房间主题审批信息分页列表
export function pageRoomThemeApproval(params) {
  return request({
    url: '/room/user/theme/page',
    method: 'get',
    params
  })
}

// 审批房间主题
export function themeApprove(data) {
  return request({
    url: '/room/user/theme/approve',
    method: 'post',
    data
  })
}

// 用户银行卡列表
export function pageUserBankCard(params) {
  return request({
    url: '/user/bank-card/page',
    method: 'get',
    params
  })
}

// 用户银行卡审核通过
export function userBankCardPass(data) {
  return request({
    url: '/user/bank-card/pass',
    method: 'post',
    data
  })
}

// 用户银行卡审核驳回
export function userBankCardNotPass(data) {
  return request({
    url: '/user/bank-card/not-pass',
    method: 'post',
    data
  })
}
