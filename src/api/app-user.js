import request from '@/utils/request'
import { httpGetExport } from '@/utils/export-excel'

// 获取用户认证信息
export function getNotExpiredSubscriptionName(userId) {
  return request({
    url: `/user/subscription/balance/${userId}/name`,
    method: 'get'
  })
}

// 获取用户认证信息
export function getUserAuthType(userId) {
  return request({
    url: `/user/auth/type/${userId}`,
    method: 'get'
  })
}

// 登录令牌查看
export function getLoginToken(userId) {
  return request({
    url: '/user/auth/type/longin/token',
    method: 'get',
    params: { userId }
  })
}

// 删除登录令牌
export function delLoginToken(userId) {
  return request({
    url: '/user/auth/type/longin/token',
    method: 'delete',
    params: { userId }
  })
}

// 获取用户糖果
export function getUserCandyBalance(userId) {
  return request({
    url: `/user/candy/balance/${userId}`,
    method: 'get'
  })
}

// 获取用户余额top表
export function listUserCandyTop(params) {
  return request({
    url: `/user/candy/balance/top`,
    method: 'get',
    params
  })
}

// 获取用户游戏券
export function getUserGameCouponBalance(userId) {
  return request({
    url: `/user/game/coupon/balance/${userId}`,
    method: 'get'
  })
}

// 获取用户注册信息
export function getUserRegister(userId) {
  return request({
    url: `/user/register/info/${userId}`,
    method: 'get'
  })
}

// 获取用户正常照片墙
export function getUserPhotoWallNormal(userId) {
  return request({
    url: `/user/photo/wall/normal/${userId}`,
    method: 'get'
  })
}

// 获取用户所有照片墙
export function getUserPhotoWallAll(userId) {
  return request({
    url: `/user/photo/wall/all/${userId}`,
    method: 'get'
  })
}

// app用户列表
export function getUserTable(data) {
  return request({
    url: '/user/base/info/page',
    method: 'post',
    data
  })
}

// 修改用户基本信息
export function updateBaseInfo(data) {
  return request({
    url: '/user/base/info',
    method: 'put',
    data
  })
}

// 用户基本信息
export function getUserBaseInfo(id) {
  return request({
    url: `/user/base/info/${id}`,
    method: 'get'
  })
}

// 用户基本信息
export function getUserBaseInfoByAccount(id) {
  return request({
    url: `/user/base/info/account/${id}`,
    method: 'get'
  })
}

// 用户基本信息
export function getUserBaseInfoBySysOriginAccount(sysOrigin, id) {
  return request({
    url: `/user/base/info/${sysOrigin}/account/${id}`,
    method: 'get'
  })
}

// 所有照片墙
export function getPotoWallTable(params) {
  return request({
    url: '/user/photo/wall/page',
    method: 'get',
    params
  })
}

// 删除照片墙
export function deletePhotoWall(id) {
  return request({
    url: `/user/photo/wall/del/${id}`,
    method: 'get'
  })
}

// 所有照片墙按条件筛选
export function getPotoWallTableSeach(params) {
  return request({
    url: '/user/photo/wall/page/all',
    method: 'get',
    params
  })
}

// 修改用户等级
export function updateLevelScore(data) {
  return request({
    url: '/user/level/score',
    method: 'post',
    data
  })
}

// 用户积分
export function getUserLevel(id) {
  return request({
    url: `/user/base/info/level/${id}`,
    method: 'get'
  })
}

// 在线女性用户分页列表
export function getOnlineUserTable(params) {
  return request({
    url: '/user/real/time/status/online/female/page',
    method: 'get',
    params
  })
}

// 在线女性用户总数
export function getOnlineTotal() {
  return request({
    url: '/user/real/time/status/online/female/total',
    method: 'get'
  })
}

// 在线男性用户分页列表
export function getOnlineMaleUserTable(params) {
  return request({
    url: '/user/real/time/status/online/male/page',
    method: 'get',
    params
  })
}

// 在线男性用户总数
export function getOnlineMaleTotal() {
  return request({
    url: '/user/real/time/status/online/male/total',
    method: 'get'
  })
}

// 获取男性用户等级
export function getUserMaleLevel(id) {
  return request({
    url: `/user/base/info/male/level/${id}`,
    method: 'get'
  })
}

// 修改男性用户等级
export function updateMaleLevelScore(data) {
  return request({
    url: '/user/male/level/score',
    method: 'post',
    data
  })
}

// 修改ai用户基本信息
export function updateVestBaseInfo(data) {
  return request({
    url: '/user/base/info/vest',
    method: 'put',
    data
  })
}

// 获取用户当前账号状态
export function getAccountStatus(userId) {
  return request({
    url: `/user/base/info/account/status/${userId}`,
    method: 'get'
  })
}

// 获取礼物墙
export function getGiftWall(userId) {
  return request({
    url: `/user/gift/wall/${userId}`,
    method: 'get'
  })
}

// 用户游戏券奖励
export function rewardGameCoupon(userId, coupon, remarks, rewardType) {
  return request({
    url: `/user/game/coupon/income/expenditure/reward/coupon`,
    method: 'post',
    params: { userId, coupon, remarks, rewardType }
  })
}

// 用户游戏券扣除
export function deductGameCoupon(userId, coupon, remarks) {
  return request({
    url: `/user/game/coupon/income/expenditure/deduct/coupon`,
    method: 'post',
    params: { userId, coupon, remarks }
  })
}

// 发送金币
export function sendGold(data) {
  return request({
    url: '/user-wallet/send-gold',
    method: 'post',
    data
  })
}

// 用户金币扣除
export function deductGold(data) {
  return request({
    url: '/user-wallet/deduct-gold',
    method: 'post',
    data
  })
}
// 发送钻石
export function sendDiamond(data) {
  return request({
    url: '/user-wallet/send-diamond',
    method: 'post',
    data
  })
}

// 用户钻石扣除
export function deductDiamond(data) {
  return request({
    url: '/user-wallet/deduct-diamond',
    method: 'post',
    data
  })
}

// APP管理员分页列表
export function getAdministratorTable(params) {
  return request({
    url: '/sys/administrator/page',
    method: 'get',
    params
  })
}

// 禁用/启用APP管理员
export function changeStatusAdministrator(data) {
  return request({
    url: '/sys/administrator',
    method: 'put',
    data
  })
}

// 改变角色
export function changeSysAdministratorRoles(params) {
  return request({
    url: `/sys/administrator/change_roles/${params.id}/${params.roles}`,
    method: 'get'
  })
}

// APP权限列表
export function getAdministratorAuthTable() {
  return request({
    url: '/sys/administrator/auth/resource',
    method: 'get'
  })
}

export function deleteAdministratorAuth(id) {
  return request({
    url: `/sys/administrator/auth/resource/delete/${id}`,
    method: 'get'
  })
}

export function updateAdministratorAuth(data) {
  return request({
    url: '/sys/administrator/auth/resource',
    method: 'put',
    data
  })
}

export function addAdministratorAuth(data) {
  return request({
    url: '/sys/administrator/auth/resource',
    method: 'post',
    data
  })
}

// 获取用户选中的权限信息
export function getSysAdministratorAuthResourceByUserId(userId) {
  return request({
    url: `/sys/administrator/auth/resource/select/${userId}`,
    method: 'get'
  })
}

// 修改权限
export function deleteAndAddAdministratorAuth(data) {
  return request({
    url: '/sys/administrator/auth',
    method: 'put',
    data
  })
}

// 获取用户的设备信息
export function getUserImei(id) {
  return request({
    url: `/user/base/info/user/imei/${id}`,
    method: 'get'
  })
}

// 获取用户的最新设备信息
export function getDeviceByUserId(userId) {
  return request({
    url: `/user/base/info/device/${userId}`,
    method: 'get'
  })
}

// 用户最新设备列表
export function getUserDeviceTable(params) {
  return request({
    url: `/user/latest/mobile/device/page`,
    method: 'get',
    params
  })
}

// 货运代理账户分页列表
export function pageFreight(params) {
  return request({
    url: '/freight/page',
    method: 'get',
    params
  })
}

// 发货给代理账户
export function shipFreight(data) {
  return request({
    url: '/freight/ship',
    method: 'post',
    data
  })
}

// 代理账户扣除货款
export function deductionFreight(data) {
  return request({
    url: '/freight/deduction',
    method: 'post',
    data
  })
}

// 货运流水列表
export function pageRunningWater(params) {
  return request({
    url: '/freight/running-water-page',
    method: 'get',
    params
  })
}

// 货运流水列表-导出
export function exportFreightWaters(params, excelName) {
  return httpGetExport('/freight/running-water/export', params, excelName)
}

// 货运流水列表
export function pageFreightSellerRunningWater(params) {
  return request({
    url: '/freight/freight-seller-running-water-page',
    method: 'get',
    params
  })
}

// 切换关闭账号状态
export function switchStatusFreight(id, status) {
  return request({
    url: `/freight/switch-status/${id}/${status}`,
    method: 'post'
  })
}

// 切换显示/不显示，在H5页面显示账号信息
export function showStatusFreight(id, status) {
  return request({
    url: `/freight/show-status/${id}/${status}`,
    method: 'post'
  })
}

// 是否经销商切换
export function switchStatusDealer(id, status) {
  return request({
    url: `/freight/switch-dealer-status/${id}/${status}`,
    method: 'post'
  })
}

// 经销商卖家列表
export function pageFreightSeller(params) {
  return request({
    url: '/freight-seller/page',
    method: 'get',
    params
  })
}

// 修改经销商卖家数量
export function updateSellerQuantity(data) {
  return request({
    url: '/freight/update-seller-quantity',
    method: 'post',
    data
  })
}

// 删除经销商卖家
export function removeFreightSeller(id) {
  return request({
    url: `/freight-seller/delete/${id}`,
    method: 'delete'
  })
}

// 获取指定用户运行资料
export function getUserRunProfileById(userId) {
  return request({
    url: `/user-run-profile/${userId}`,
    method: 'get'
  })
}

// 移除用户运行资料
export function removeUserRunProfileById(userId) {
  return request({
    url: `/user-run-profile/${userId}`,
    method: 'delete'
  })
}

// 获取扩展信息
export function getUserExpend(userId) {
  return request({
    url: '/user/expand',
    method: 'get',
    params: { userId }
  })
}

// 获得认证详情
export function getUserAuthTypeDetails(userId) {
  return request({
    url: '/user/auth/type/details/' + userId,
    method: 'get',
    params: { userId }
  })
}

// 修改绑定手机认证信息
export function editUserMobileAuth(data) {
  return request({
    url: '/user/auth/type/edit/mobile-auth',
    method: 'post',
    data
  })
}

// 删除绑定手机认证信息
export function deleteUserMobileAuth(userId) {
  return request({
    url: '/user/auth/type/delete/mobile-auth/' + userId,
    method: 'get'
  })
}

// 重置用户账号密码登录信息
export function initUserAccountLogin(userId) {
  return request({
    url: '/user/auth/type/init/account-auth/' + userId,
    method: 'get'
  })
}
// 删除用户账号密码登录信息
export function deleteUserAccountLogin(userId) {
  return request({
    url: '/user/auth/type/delete/account-auth/' + userId,
    method: 'get'
  })
}

// 重置用户支付密码
export function initUserPayAuth(userId) {
  return request({
    url: '/user/auth/type/init/pay-auth/' + userId,
    method: 'get'
  })
}
// 删除用户支付密码
export function deleteUserPayAuth(userId) {
  return request({
    url: '/user/auth/type/delete/pay-auth/' + userId,
    method: 'get'
  })
}
// 获取用户vip实际权益信息
export function getUserVipEquity(userId) {
  return request({
    url: '/user/base/info/user/vip/equity/' + userId,
    method: 'get'
  })
}

// 用户身份信息
export function getUserIdentity(userId) {
  return request({
    url: '/user/base/info/' + userId + '/identity',
    method: 'get'
  })
}

// 用户认证身份信息
export function getUserBankIdentityInfo(params) {
  return request({
    url: '/user/bank-identity-info/page',
    method: 'get',
    params
  })
}
// 用户认证身份信息审核
export function auditUserBankIdentityInfo(data) {
  return request({
    url: '/user/bank-identity-info',
    method: 'put',
    data
  })
}

// 用户认证身份信息-导出
export function exportUserBankIdentityInfo(params, excelName) {
  return httpGetExport('/user/bank-identity-info/export', params, excelName)
}

// 用户钻石流水列表
export function pageUserDiamondRunWater(params) {
  return request({
    url: '/user/diamond/run/water/page',
    method: 'get',
    params
  })
}

// 用户钻石余额列表
export function pageUserDiamondBalance(params) {
  return request({
    url: '/user/diamond/balance/page',
    method: 'get',
    params
  })
}
