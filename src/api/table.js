
import request from '@/utils/request'

// 意见反馈列表
export function getFeedbackTable(params) {
  return request({
    url: '/sys/feedback/page',
    method: 'get',
    params
  })
}

// 意见反馈处理
export function processFeedback(params) {
  return request({
    url: '/sys/feedback/process',
    method: 'get',
    params
  })
}

// 意见反馈批量处理
export function batchProcessFeedback(data) {
  return request({
    url: '/sys/feedback/batchProcess',
    method: 'post',
    data
  })
}

// 举报列表
export function getReportedTable(params) {
  return request({
    url: '/sys/reported/user/page',
    method: 'get',
    params
  })
}

// 参数配置列表
export function getConfigInfo(params) {
  return request({
    url: '/sys/enum/config/page',
    method: 'get',
    params
  })
}

// 添加配置信息
export function addConfigInfo(data) {
  return request({
    url: '/sys/enum/config',
    method: 'post',
    data
  })
}

// 修改配置信息
export function updateConfigInfo(data) {
  return request({
    url: '/sys/enum/config',
    method: 'put',
    data
  })
}

// 删除配置信息
export function delConfig(id) {
  return request({
    url: `/sys/enum/config/${id}`,
    method: 'delete'
  })
}

// 获取系统枚举分组列表
export function getEnumConfigByGroup(group) {
  return request({
    url: `/sys/enum/config/list/${group}`,
    method: 'get'
  })
}

// 保存配置序号
export function updateSort(data) {
  return request({
    url: '/sys/enum/config/sort',
    method: 'post',
    data
  })
}

// 获取app崩溃版本信息
export function breakdownFlow(params) {
  return request({
    url: '/sys/breakdown/info/flow',
    method: 'get',
    params
  })
}

// 清空指定平台日期崩溃日志
export function deleteByPlatform(platform) {
  return request({
    url: `/sys/breakdown/info/${platform}/delete`,
    method: 'delete'
  })
}

// 清空指定平台日期崩溃日志
export function deleteByPlatformDate(platform, startTime, endTime) {
  return request({
    url: `/sys/breakdown/info/${platform}/${startTime}/${endTime}/delete`,
    method: 'delete'
  })
}

// 系统评分列表
export function appScorePage(params) {
  return request({
    url: '/sys/app/score/page',
    method: 'get',
    params
  })
}

// 封禁设备列表
export function archiveDevicePage(params) {
  return request({
    url: '/sys/archive/device/page',
    method: 'get',
    params
  })
}

// 删除封禁设备
export function delDevice(deviceNo) {
  return request({
    url: `/sys/archive/device/del/${deviceNo}`,
    method: 'get'
  })
}

// cp申请分页列表
export function pageCpApply(params) {
  return request({
    url: '/user-cp-apply/page',
    method: 'get',
    params
  })
}

