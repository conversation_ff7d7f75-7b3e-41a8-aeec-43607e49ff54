
import request from '@/utils/request'

// 道具资源-分页列表
export function pagePropsSource(params) {
  return request({
    url: '/props/source/record/page',
    method: 'get',
    params
  })
}

// 道具资源-添加
export function addPropsSource(data) {
  return request({
    url: '/props/source/record/add',
    method: 'post',
    data
  })
}

// 道具资源-修改
export function updatePropsSource(data) {
  return request({
    url: '/props/source/record/update',
    method: 'post',
    data
  })
}

// 道具资源-上下架
export function offShelfPropsSource(id, offShelf) {
  return request({
    url: `/props/source/record/off/shelf`,
    method: 'get',
    params: { id, offShelf }
  })
}

// 获取资源列表-平台+类型获取
export function listSysOriginTypeList(sysOrigin, type) {
  return request({
    url: '/props/source/record/sys-origin/type/list',
    method: 'get',
    params: { sysOrigin, type }
  })
}

// 获取资源列表-类型获取-排除家族资源-根据平台
export function listNotFamilyBySysOriginType(sysOrigin, type) {
  return request({
    url: '/props/source/record/sys-origin/type/list/exclude-family',
    method: 'get',
    params: { sysOrigin, type }
  })
}

// 活动道具奖励配置-分页
export function pagePropsActivityRewardGroup(params) {
  return request({
    url: '/props/activity/reward/group/page',
    method: 'get',
    params
  })
}

// 根据资源组ID获得活动道具奖励配置
export function getByGroupId(id) {
  return request({
    url: '/props/activity/reward/group/' + id,
    method: 'get'
  })
}

// 活动道具奖励配置-修改/添加
export function saveOrUpdatePropsActivityRewardGroup(data) {
  return request({
    url: '/props/activity/reward/group/save-or-update',
    method: 'post',
    data
  })
}

// 活动道具奖励配置-上下架
export function offPropsActivityRewardGroup(id, offShelf) {
  return request({
    url: `/props/activity/reward/group/off/shelf/${id}/${offShelf}`,
    method: 'get'
  })
}

// 活动道具规则-分页
export function pagePropsActivityRuleConfig(params) {
  return request({
    url: '/props/activity/rule/config/page',
    method: 'get',
    params
  })
}

// 活动道具规则-添加或修改
export function savePropsActivityRuleConfig(data) {
  return request({
    url: '/props/activity/rule/config/save-or-update',
    method: 'post',
    data
  })
}

// 活动道具规则-删除
export function delPropsActivityRuleConfig(id) {
  return request({
    url: `/props/activity/rule/config/del/${id}`,
    method: 'get'
  })
}

// 上架中的活动道具组
export function getGroupActivityList() {
  return request({
    url: `/props/activity/reward/group/get/activity/list`,
    method: 'get'
  })
}

// 赠送道具给用户
export function sendPropsGiveUser(data) {
  return request({
    url: '/props/give/send',
    method: 'post',
    data
  })
}

// 道具商店-分页
export function pagePropsStore(params) {
  return request({
    url: '/props/store/page',
    method: 'get',
    params
  })
}

// 道具商店-添加或修改
export function addOrUpdatePropsStore(data) {
  return request({
    url: '/props/store/add-or-update',
    method: 'post',
    data
  })
}
