/**
 * app管理相关
 */
import request from '@/utils/request'

// 获取版本信息列表
export function getAppVersionTable(params) {
  return request({
    url: '/sys/version/manage',
    method: 'get',
    params
  })
}

// 添加版本信息
export function addAppVersion(data) {
  return request({
    url: '/sys/version/manage',
    method: 'post',
    data
  })
}

// 修改版本信息
export function updateAppVersion(data) {
  return request({
    url: '/sys/version/manage/update',
    method: 'post',
    data
  })
}

// 删除版本信息
export function delAppVersion(id) {
  return request({
    url: `/sys/version/manage/${id}`,
    method: 'get'
  })
}

// 添加buildVersion
export function addServerBuildVersion(params) {
  return request({
    url: '/sys/version/manage/add/build-version',
    method: 'get',
    params
  })
}

// 获取当前添加buildVersion
export function getServerBuildVersion(params) {
  return request({
    url: '/sys/version/manage/get/build-version',
    method: 'get',
    params
  })
}

// 版本更新描述

// 获取版本更新描述列表
export function getAppVersionDescriptionTable(params) {
  return request({
    url: '/sys/version/update/description',
    method: 'get',
    params
  })
}

// 添加版本更新描述列表
export function addAppVersionDescription(data) {
  return request({
    url: '/sys/version/update/description',
    method: 'post',
    data
  })
}

// 修改版本更新描述列表
export function updateAppVersionDescription(data) {
  return request({
    url: '/sys/version/update/description/update',
    method: 'post',
    data
  })
}

// 删除版本更新描述列表
export function delAppVersionDescription(id) {
  return request({
    url: `/sys/version/update/description/${id}`,
    method: 'get'
  })
}
