import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'
import { isMock } from '@/utils'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const endRouter = [{ path: '*', redirect: '/401', hidden: true }]

/**
 * 测试路由
 */
export const mockRouters = [
  {
    path: '/team',
    component: Layout,
    redirect: '/team',
    name: 'TeamManager',
    meta: { title: '团队管理', icon: 'example' },
    children: [
      {
        path: 'team-policy',
        name: 'TeamPolicyManager',
        component: () => import('@/views/team/policy/index'),
        meta: { title: '团队政策', icon: 'form' }
      },
      {
        path: 'team-diamond-policy',
        name: 'TeamPolicyManager',
        component: () => import('@/views/team/diamond-policy/index'),
        meta: { title: '团队钻石政策', icon: 'form' }
      },
      {
        path: 'team-list',
        name: 'TeamList',
        component: () => import('@/views/team/team-list/index'),
        meta: { title: '团队列表', icon: 'form' }
      },
      {
        path: 'team/member',
        name: 'TeamMember',
        component: () => import('@/views/team/team-member/index'),
        meta: { title: '成员列表', icon: 'form' }
      },
      {
        path: 'team/bill',
        name: 'TeamBill',
        component: () => import('@/views/team/team-bill/index'),
        meta: { title: '账单列表', icon: 'form' }
      },
      {
        path: 'team/member-work',
        name: 'TeamMemberWork',
        component: () => import('@/views/team/team-member-work/index'),
        meta: { title: '成员工作', icon: 'form' }
      },
      {
        path: 'team/application-process',
        name: 'TeamApplicationProcessList',
        component: () => import('@/views/team/application-process/index'),
        meta: { title: '成员审核日志', icon: 'form' }
      },
      {
        path: 'team/bd-lead',
        name: 'BdLead',
        component: () => import('@/views/team/bd-lead/index'),
        meta: { title: 'BD Lead', icon: 'form' }
      }
    ]
  },
  {
    path: '/dynamic/manager',
    component: Layout,
    redirect: '/dynamic/manager',
    name: 'DynamicManager',
    meta: { title: '动态管理', icon: 'example' },
    children: [
      {
        path: 'dynamic/tag',
        name: 'DynamicTag',
        component: () => import('@/views/dynamic/tag/index'),
        meta: { title: '标签列表', icon: 'form' }
      },
      {
        path: 'dynamic/popular/config',
        name: 'DynamicPopularConfig',
        component: () => import('@/views/dynamic/config/index'),
        meta: { title: '权重配置', icon: 'form' }
      },
      {
        path: 'user/dynamic/list',
        name: 'UserDynamicList',
        component: () => import('@/views/dynamic/user-dynamic/index'),
        meta: { title: '用户动态', icon: 'form' }
      },
      {
        path: 'dynamic/blacklist',
        name: 'DynamicBlacklist',
        component: () => import('@/views/dynamic/blacklist/index'),
        meta: { title: '黑名单', icon: 'form' }
      }
    ]
  },
  {
    path: '/family/manager',
    component: Layout,
    name: 'FamilyManager',
    meta: { title: '家族管理', icon: 'example' },
    children: [
      {
        path: 'family_config',
        name: 'FamilyConfig',
        component: () => import('@/views/family/config/config-level/index'),
        meta: { title: '家族配置', icon: 'form' }
      },
      {
        path: 'family_list',
        name: 'FamilyList',
        component: () => import('@/views/family/list/index'),
        meta: { title: '家族列表', icon: 'form' }
      }
    ]
  },
  {
    path: '/pet/manager',
    component: Layout,
    name: 'PetManager',
    meta: { title: '宠物管理', icon: 'example' },
    children: [
      {
        path: 'feeding',
        name: 'PetFeeding',
        component: () => import('@/views/pet/feeding/index'),
        meta: { title: '宠物喂养记录', icon: 'form' }
      },
      {
        path: 'beans',
        name: 'Beas',
        component: () => import('@/views/order/beans/balance/index'),
        meta: { title: '豆子账户', icon: 'form' }
      },
      {
        path: 'beans/running-water',
        name: 'BeasRunningWater',
        component: () => import('@/views/order/beans/running-water/index'),
        meta: { title: '豆子流水', icon: 'form' }
      },
      {
        path: 'pool',
        name: 'PetPool',
        component: () => import('@/views/pet/cnf/index'),
        meta: { title: '宠物池', icon: 'form' }
      }
    ]
  },
  {
    path: '/props/manager',
    component: Layout,
    name: 'PropsManager',
    meta: { title: '道具管理', icon: 'example' },
    children: [
      {
        path: 'props/send_tool',
        name: 'PropsSendTool',
        component: () => import('@/views/props/props-send-tool/index'),
        meta: { title: '道具赠送', icon: 'form' }
      },
      {
        path: 'props/config',
        name: 'PropsConfig',
        component: () => import('@/views/props/props-resource-config/index'),
        meta: { title: '资源配置', icon: 'form' }
      },
      {
        path: 'props/props_source_group',
        name: 'PropsActivityRewardCnf',
        component: () => import('@/views/props/props-source-group/index'),
        meta: { title: '道具资源组配置', icon: 'form' }
      },
      {
        path: 'props/props-activity-rule-config',
        name: 'PropsActivityRuleConfig',
        component: () =>
          import('@/views/props/props-activity-rule-config/index'),
        meta: { title: '活动道具规则配置', icon: 'form' }
      },
      {
        path: 'props/props_store',
        name: 'PropsStore',
        component: () => import('@/views/props/props-store/index'),
        meta: { title: '道具商店', icon: 'form' }
      }
    ]
  },
  {
    path: '/app/tools',
    component: Layout,
    name: 'AppTools',
    meta: { title: '工具管理', icon: 'example' },
    children: [
      {
        path: 'request-blacklist',
        name: 'requestBlacklist',
        component: () => import('@/views/sys/request-blacklist/index'),
        meta: { title: '请求黑名单', icon: 'form' }
      },
      {
        path: 'gold-analyze',
        name: 'MatchAnalysis',
        component: () => import('@/views/tools/gold-analyze/index'),
        meta: { title: '金币分析', icon: 'form' }
      },
      {
        path: '/redis',
        name: 'RedisManager',
        component: () => import('@/views/tools/redis/index'),
        meta: { title: 'Redis缓存管理', icon: 'form' }
      },
      {
        path: '/upload/file',
        name: 'UploadFile',
        component: () => import('@/views/tools/upload/index'),
        meta: { title: '文件上传', icon: 'form' }
      },
      {
        path: '/socket',
        name: 'ToolsSocket',
        component: () => import('@/views/tools/websocket/index'),
        meta: { title: 'Socket测试', icon: 'form' }
      }
    ]
  },
  {
    path: '/app/sys/mamange',
    component: Layout,
    name: 'AppSysManager',
    meta: { title: 'App系统管理', icon: 'example' },
    children: [
      {
        path: 'login/logger',
        name: 'loginLogger',
        component: () => import('@/views/log/login-logger/index'),
        meta: { title: '登陆日志', icon: 'form' }
      },
      {
        path: 'log/blacklist',
        name: 'LogBlacklist',
        component: () => import('@/views/log/blacklist/index'),
        meta: { title: '房间黑名单', icon: 'form' }
      },
      {
        path: 'log/api-request-log',
        name: 'ApiRequestLog',
        component: () => import('@/views/log/request-log/index'),
        meta: { title: '请求日志', icon: 'form' }
      },
      {
        path: 'userSpecialId',
        name: 'UserSpecialId',
        component: () => import('@/views/user/special-id/index'),
        meta: { title: '靓号管理', icon: 'form' }
      },
      {
        path: 'userSpecialId',
        name: 'UserSpecialId',
        component: () => import('@/views/user/special-id-role/index'),
        meta: { title: '靓号管理', icon: 'form' }
      },
      {
        path: 'external/app_doc_interface',
        name: 'ExternalAppDocInterface',
        component: () => import('@/views/external/app_doc_interface'),
        meta: { title: '文档管理', icon: 'form' }
      },
      {
        path: 'external/im',
        name: 'ExternalIm',
        component: () => import('@/views/external/im'),
        meta: { title: 'IM通讯', icon: 'form' }
      },
      {
        path: 'version',
        name: 'SugartimeAppVersion',
        component: () => import('@/views/version/app/index'),
        meta: { title: '版本管理', icon: 'form' }
      },
      {
        path: 'enum/config',
        name: 'EnumConfig',
        component: () => import('@/views/cnf/enums/manager/index'),
        meta: { title: '参数配置管理', icon: 'form' }
      },
      {
        path: 'enum/setting',
        name: 'EnumConfigSetting',
        component: () => import('@/views/cnf/enums/setting/index'),
        meta: { title: '参数配置', icon: 'form' }
      },
      {
        path: 'config/product/v2',
        name: 'ProductConfigV2',
        component: () => import('@/views/cnf/product-v2/index'),
        meta: { title: '内购产品配置V2', icon: 'form' }
      },
      {
        path: 'archive/device',
        name: 'ArchiveDevice',
        component: () => import('@/views/log/archive-device/index'),
        meta: { title: '封禁设备', icon: 'form' }
      },
      {
        path: 'emoji/config',
        name: 'EmojiConfig',
        component: () => import('@/views/sys/emoji-config/index'),
        meta: { title: '表情管理', icon: 'form' }
      }
    ]
  },
  {
    path: '/sys/manager',
    component: Layout,
    redirect: '/sys/manager',
    name: 'sysManager',
    meta: { title: '系统管理', icon: 'example' },
    children: [
      {
        path: 'region/config',
        name: 'RegionConfig',
        component: () => import('@/views/sys/region-config/index'),
        meta: { title: '区域配置', icon: 'form' }
      },
      {
        path: 'user',
        name: 'UserManager',
        component: () => import('@/views/sys/user-manager/index'),
        meta: { title: '用户管理', icon: 'form' }
      },
      {
        path: 'roles',
        name: 'RolesManager',
        component: () => import('@/views/sys/role-manager/index'),
        meta: { title: '角色管理', icon: 'form' }
      },
      {
        path: 'menus',
        name: 'MenusManager',
        component: () => import('@/views/sys/menu-manager/index'),
        meta: { title: '菜单管理', icon: 'form' }
      },
      {
        path: 'resource',
        name: 'ResourceManager',
        component: () => import('@/views/sys/resources-manager/index'),
        meta: { title: '资源管理', icon: 'form' }
      }
    ]
  },
  {
    path: '/statistics/manager',
    component: Layout,
    redirect: '/statistics/manager',
    name: 'StatisticsManager',
    meta: { title: '统计管理', icon: 'example' },
    children: [
      {
        path: 'datav',
        name: 'Datav',
        component: () => import('@/views/datav/index'),
        meta: { title: '数据大屏', icon: 'form' }
      }
    ]
  },
  {
    path: '/operate/manager',
    component: Layout,
    redirect: '/operate/manager',
    name: 'OperateManager',
    meta: { title: '运营管理', icon: 'example' },
    children: [
      {
        path: 'agentActivity',
        name: 'AgentActivity',
        component: () => import('@/views/activity/agent-activity/index'),
        meta: { title: '代理活动', icon: 'form' }
      },
      {
        path: 'gold-balance',
        name: 'GoldBalance',
        component: () => import('@/views/user/gold-balance/index'),
        meta: { title: '金币余额', icon: 'form' }
      },
      {
        path: '/auto-salary-pay-record',
        name: 'AutoSalaryPayRecord',
        component: () =>
          import('@/views/user/bank-balance/auto-salary-pay-record/index'),
        meta: { title: '自动发放工资凭据', icon: 'form' }
      },
      {
        path: '/withdraw-money-apply-page',
        name: 'UserBankWithdrawMoneyApply',
        component: () =>
          import('@/views/user/bank-balance/withdraw-money-apply/index'),
        meta: { title: '用户银行卡提现现金申请', icon: 'form' }
      },
      {
        path: '/user-bank-withdraw-gold-apply',
        name: 'UserBankWithdrawGoldApply',
        component: () =>
          import('@/views/user/bank-balance/withdraw-gold-apply/index'),
        meta: { title: '用户银行金币兑换申请', icon: 'form' }
      },
      {
        path: '/user-bank-balance',
        name: 'UserBankBalance',
        component: () => import('@/views/user/bank-balance/table/index'),
        meta: { title: '用户银行账户', icon: 'form' }
      },
      {
        path: '/user-running-water',
        name: 'UserBankRunningWater',
        component: () =>
          import('@/views/user/bank-balance/running-water/index'),
        meta: { title: '用户银行流水', icon: 'form' }
      },
      {
        path: '/customer-service',
        name: 'CustomerService',
        component: () => import('@/views/sys/customer-service/index'),
        meta: { title: '客服列表', icon: 'form' }
      },
      {
        path: 'cp',
        name: 'cp',
        component: () => import('@/views/log/cp/index'),
        meta: { title: 'CP申请', icon: 'form' }
      },
      {
        path: '/game/pk',
        name: 'GamePK',
        component: () => import('@/views/game/pk/index'),
        meta: { title: 'PK记录', icon: 'form' }
      },
      {
        path: 'invite-user-log',
        name: 'InviteUserLog',
        component: () => import('@/views/log/invite-user-log/index'),
        meta: { title: '邀请用户日志', icon: 'form' }
      },
      {
        path: 'activity/cnf',
        name: 'ActivityTemplate',
        component: () => import('@/views/activity/template-cnf/index'),
        meta: { title: '活动配置', icon: 'form' }
      },
      {
        path: 'activity/template',
        name: 'ActivityTemplate',
        component: () =>
          import('@/views/activity/template-cnf/template-form/index'),
        meta: { title: '活动模版', icon: 'form' }
      },
      {
        path: '/sys/friendship-card',
        name: 'SysUserFriendshipCard',
        component: () => import('@/views/sys/friendship-card/index'),
        meta: { title: '用户友谊卡配置', icon: 'form' }
      },
      {
        path: '/sys/invite-user-rule',
        name: 'AswatInviteUserRule',
        component: () => import('@/views/sys/invite-user-rule/index'),
        meta: { title: '邀请用户配置', icon: 'form' }
      },
      {
        path: '/sys/user-advertising',
        name: 'AswatUserAdvertising',
        component: () => import('@/views/sys/user-advertising/index'),
        meta: { title: '用户观看广告配置', icon: 'form' }
      },
      {
        path: '/game-ludo',
        name: 'GameLudo',
        component: () => import('@/views/game/ludo/index'),
        meta: { title: 'Ludo飞行棋', icon: 'form' }
      },
      {
        path: 'set-top-room',
        name: 'SetTop',
        component: () => import('@/views/sys/set-top/index'),
        meta: { title: '置顶房间', icon: 'form' }
      },
      {
        path: 'set-hot-room',
        name: 'SetHot',
        component: () => import('@/views/sys/set-hot/index'),
        meta: { title: '热门房间', icon: 'form' }
      },
      {
        path: 'refund-deduct-tag-log',
        name: 'RefundDeductTagLog',
        component: () => import('@/views/room/refund-deduct-tag-log/index'),
        meta: { title: '退款扣除目标', icon: 'form' }
      },
      {
        path: 'pay/country',
        name: 'PayCountry',
        component: () => import('@/views/cnf/pay/country/index'),
        meta: { title: '开通支付国家', icon: 'form' }
      },
      {
        path: 'region/relation',
        name: 'RegionRelation',
        component: () => import('@/views/cnf/pay/region-relation/index'),
        meta: { title: '支付区域关系', icon: 'form' }
      },
      {
        path: 'pay/application',
        name: 'PayApplication',
        component: () => import('@/views/cnf/pay/application/index'),
        meta: { title: '应用管理', icon: 'form' }
      },
      {
        path: 'pay/factory',
        name: 'PayFactory',
        component: () => import('@/views/cnf/pay/factory/index'),
        meta: { title: '支付厂商', icon: 'form' }
      },
      {
        path: 'pay/channel',
        name: 'PayChannel',
        component: () => import('@/views/cnf/pay/channel/index'),
        meta: { title: '支付渠道', icon: 'form' }
      },
      {
        path: 'open_pay/country',
        name: 'openPayCountry',
        component: () => import('@/views/cnf/pay/country/index'),
        meta: { title: '开通支付国家', icon: 'form' }
      },
      {
        path: 'cnf/lottery',
        name: 'CnfLottery',
        component: () => import('@/views/cnf/lottery/index'),
        meta: { title: '抽奖品配置', icon: 'form' }
      },
      {
        path: 'user_freight',
        name: 'UserFreight',
        component: () => import('@/views/user/freight/index'),
        meta: { title: '货运代理', icon: 'form' }
      },
      {
        path: 'room/business-development',
        name: 'BusinessDevelopment',
        component: () => import('@/views/team/business-development/index'),
        meta: { title: 'BD列表', icon: 'form' }
      },
      {
        path: 'user/invite-reward',
        name: 'InviteRewardRecord',
        component: () => import('@/views/user/invite-reward/index'),
        meta: { title: '邀请用户奖励记录', icon: 'form' }
      },
      {
        path: 'im/account/manager',
        name: 'ImAccountManager',
        component: () => import('@/views/user/im-account-manager/index'),
        meta: { title: 'IM账号管理', icon: 'form' }
      },
      {
        path: 'check/in/log',
        name: 'CheckInLog',
        component: () => import('@/views/user/check-in-log/index'),
        meta: { title: '用户签到', icon: 'form' }
      },
      {
        path: 'user',
        name: 'UserTable',
        component: () => import('@/views/user/user-table/index'),
        meta: { title: '用户列表', icon: 'form' }
      },
      {
        path: 'user',
        name: 'UserTable',
        component: () => import('@/views/user/user-table-role/index'),
        meta: { title: '用户列表', icon: 'form' }
      },
      {
        path: 'user/user-device',
        name: 'UserDevice',
        component: () => import('@/views/user/user-device/index'),
        meta: { title: '用户最新设备列表', icon: 'form' }
      },

      {
        path: 'administrator',
        name: 'AdministratorAuthTable',
        component: () => import('@/views/user/app-manager/index'),
        meta: { title: 'APP管理员', icon: 'form' }
      },
      {
        path: 'compensate',
        name: 'Compensate',
        component: () => import('@/views/tools/compensate-order/index'),
        meta: { title: '订单补偿', icon: 'form' }
      },
      {
        path: 'tools/other',
        name: 'ToolsOther',
        component: () => import('@/views/tools/other/index'),
        meta: { title: '小工具', icon: 'form' }
      },
      {
        path: 'abnormal/log',
        name: 'AbnormalLog',
        component: () => import('@/views/order/abnormal/index'),
        meta: { title: '异常订单', icon: 'form' }
      },
      {
        path: 'user_freight_info_list',
        name: 'UserFreightInfoList',
        component: () => import('@/views/user/freight-info-list/index'),
        meta: { title: '货运代理流水', icon: 'form' }
      },
      {
        path: 'quality_users_list',
        name: 'QualityUsersList',
        component: () => import('@/views/statistics/quality-users/index'),
        meta: { title: '高产用户列表', icon: 'form' }
      },
      {
        path: 'running/water',
        name: 'CandyRunningWater',
        component: () => import('@/views/order/candy/running-water/index'),
        meta: { title: '金币收支记录', icon: 'form' }
      },
      {
        path: 'inside-operation-gold',
        name: 'InsideOperationGoldog',
        component: () =>
          import('@/views/order/candy/inside-operation-gold-log/index'),
        meta: { title: '内部操作金币', icon: 'form' }
      },
      {
        path: '/game/coupon/running/water',
        name: 'GameCouponRunningWater',
        component: () =>
          import('@/views/order/game-coupon/running-water/index'),
        meta: { title: '游戏券收支记录', icon: 'form' }
      },
      {
        path: 'integral/stream',
        name: 'IntegralStream',
        component: () => import('@/views/user/integral-stream/index'),
        meta: { title: '积分收支记录', icon: 'form' }
      },

      {
        path: 'order/details',
        name: 'OrderDetails',
        component: () => import('@/views/order/order-details/index'),
        meta: { title: '订单详情', icon: 'form' }
      },
      {
        path: 'reimburse',
        name: 'OrderReimburse',
        component: () => import('@/views/order/reimburse/index'),
        meta: { title: '退款记录', icon: 'form' }
      },
      {
        path: 'gift',
        name: 'ContentManagerGift',
        component: () => import('@/views/cnf/gift/index'),
        meta: { title: '礼物管理', icon: 'form' }
      },
      {
        path: '/game/lottery-config',
        name: 'GameLottery',
        component: () => import('@/views/game/lottery-config/index'),
        meta: { title: '抽奖概率配置', icon: 'form' }
      },
      {
        path: 'badge',
        name: 'ContentManagerBadge',
        component: () => import('@/views/user/badge/index'),
        meta: { title: '徽章配置管理', icon: 'form' }
      },
      {
        path: 'beautiful-number',
        name: 'ContentManagerNumber',
        component: () => import('@/views/user/beautiful-number/index'),
        meta: { title: '靓号池管理', icon: 'form' }
      },
      {
        path: 'gift/history',
        name: 'ContentManagerGiftHistory',
        component: () => import('@/views/user/gift-history/index'),
        meta: { title: '礼物赠送记录', icon: 'form' }
      },
      {
        path: 'contribution/balance',
        name: 'RoomManagerContributionBalance',
        component: () => import('@/views/room/contribution-balance/index'),
        meta: { title: '房间贡献余额', icon: 'form' }
      },
      {
        path: 'push',
        name: 'MessageManagerPush',
        component: () => import('@/views/message/push/index'),
        meta: { title: '消息推送', icon: 'form' }
      },
      {
        path: 'online/room',
        name: 'OnlineRoom',
        component: () => import('@/views/room/online/index'),
        meta: { title: '在线房间', icon: 'form' }
      },
      {
        path: 'profile/room',
        name: 'ProfileRoom',
        component: () => import('@/views/room/profile/index'),
        meta: { title: '房间资料信息', icon: 'form' }
      },
      {
        path: 'sys/banner',
        name: 'SysBanner',
        component: () => import('@/views/sys/banner/index'),
        meta: { title: '系统banner管理', icon: 'form' }
      },
      {
        path: 'mike/type',
        name: 'mikeType',
        component: () => import('@/views/mike/type/index'),
        meta: { title: '麦位类型管理', icon: 'form' }
      },
      {
        path: 'game-config',
        name: 'GameConfig',
        component: () => import('@/views/sys/game-config/index'),
        meta: { title: '游戏列表', icon: 'form' }
      },
      {
        path: 'sys/start/page',
        name: 'StartPage',
        component: () => import('@/views/sys/start-page/index'),
        meta: { title: 'App启动页', icon: 'form' }
      },
      {
        path: 'notice/message',
        name: 'NoticeMessage',
        component: () => import('@/views/sys/notice-message/index'),
        meta: { title: '系统公告管理', icon: 'form' }
      },
      {
        path: 'sys/country',
        name: 'SysCountryCode',
        component: () => import('@/views/sys/country-code/index'),
        meta: { title: '国家管理', icon: 'form' }
      },

      // {
      //   path: 'user/photo/wall',
      //   name: 'PhotoWall',
      //   component: () => import('@/views/user/photo-wall/index'),
      //   meta: { title: '照片墙', icon: 'form' }
      // },
      {
        path: '/running/water/user/props',
        name: 'RunningWaterUserProps',
        component: () => import('@/views/user/user-prop-list/index'),
        meta: { title: '用户道具流水', icon: 'form' }
      },
      {
        path: '/user/diamond-balance',
        name: 'GameDoubleLayerFruit',
        component: () => import('@/views/user/diamond-balance/index'),
        meta: { title: '用户钻石余额列表', icon: 'form' }
      },
      {
        path: '/user/diamond-run-water',
        name: 'GameDoubleLayerFruit',
        component: () => import('@/views/user/diamond-run-water/index'),
        meta: { title: '用户钻石流水列表', icon: 'form' }
      },
      {
        path: 'mike/type/list',
        name: 'mikeTypeList',
        component: () => import('@/views/mike/list/index'),
        meta: { title: '购买麦位类型流水', icon: 'form' }
      },

      {
        path: '/game/red/packet/record',
        name: 'GameRedPacketRecord',
        component: () => import('@/views/game/red-packet/index'),
        meta: { title: '红包发送列表', icon: 'form' }
      },
      {
        path: '/user/user-activity-receive',
        name: 'UserActivityReceive',
        component: () => import('@/views/user/user-activity-receive/index'),
        meta: { title: '用户活动领奖记录', icon: 'form' }
      },
      {
        path: '/game/lucky-gift/rule-config/config',
        name: 'GameDoubleLayerFruit',
        component: () => import('@/views/game/lucky-gift/rule-config/index'),
        meta: { title: '幸运礼物规则配置', icon: 'form' }
      },
      {
        path: '/game/lucky-gift/standard-config/config',
        name: 'GameDoubleLayerFruit',
        component: () => import('@/views/game/lucky-gift/standard-config/index'),
        meta: { title: '幸运礼物规格配置', icon: 'form' }
      },
      {
        path: '/game/lucky-gift/give-record',
        name: 'GameDoubleLayerFruit',
        component: () => import('@/views/game/lucky-gift/give-record/index'),
        meta: { title: '幸运礼物送礼记录', icon: 'form' }
      },
      {
        path: '/game/lucky-box',
        name: 'GameLuckyBox',
        component: () => import('@/views/game/lucky-box/index'),
        meta: { title: 'LuckyBox抽奖记录', icon: 'form' }
      },
      {
        path: '/game/fruit/log/fruit-days-log',
        name: 'fruit:days:log',
        component: () => import('@/views/game/fruit/log/fruit-days-log/index'),
        meta: { title: '摩天轮游戏每日数据', icon: 'form' }
      },
      {
        path: 'game/fruit/task-config',
        name: 'GameLuckyBox',
        component: () => import('@/views/game/fruit/task-config/index'),
        meta: { title: '摩天轮任务配置', icon: 'form' }
      },
      {
        path: '/game/egg',
        name: 'GameEgg',
        component: () => import('@/views/game/egg/index'),
        meta: { title: '砸金蛋记录', icon: 'form' }
      },
      {
        path: '/game/teen-patti',
        name: 'GameDoubleLayerFruit',
        component: () => import('@/views/game/teen-patti/index'),
        meta: { title: '炸金花', icon: 'form' }
      },
      {
        path: '/user/beautiful-number-apply',
        name: 'BeautifulNumberApply',
        component: () => import('@/views/user/beautiful-number-apply/index'),
        meta: { title: '用户申请靓号记录', icon: 'form' }
      },
      {
        path: '/activity/hall-fame',
        name: 'ActivityHallFame',
        component: () => import('@/views/activity/hall-fame/index'),
        meta: { title: '自定义名人堂', icon: 'form' }
      },
      {
        path: '/game/trumpet',
        name: 'GameTrumpet',
        component: () => import('@/views/game/trumpet/index'),
        meta: { title: '喇叭列表', icon: 'form' }
      },
      {
        path: '/activity/room/contribution',
        name: 'ActivityRoomContribution',
        component: () =>
          import('@/views/activity/room-contribution-list/index'),
        meta: { title: '房间支持活动', icon: 'form' }
      }
    ]
  },
  {
    path: '/approval/manager',
    component: Layout,
    redirect: '/approval/manager',
    name: 'ApprovalManager',
    meta: { title: '审批管理', icon: 'example' },
    children: [
      {
        path: 'user/avatatphoto/approval',
        name: 'AvatatPhotoApproval',
        component: () => import('@/views/approval/avatar-approval/index'),
        meta: { title: '头像审核', icon: 'form' }
      },
      {
        path: 'user/nickname/approval',
        name: 'NickNameApproval',
        component: () => import('@/views/approval/nickname-approval/index'),
        meta: { title: '昵称审核', icon: 'form' }
      },
      {
        path: 'user/photowall/approval',
        name: 'PhotoWallApproval',
        component: () => import('@/views/approval/photo-wall-approval/index'),
        meta: { title: '照片墙审核', icon: 'form' }
      },
      {
        path: 'room/cover/approval',
        name: 'RoomCoverWallApproval',
        component: () =>
          import('@/views/approval/room-cover-wall-approval/index'),
        meta: { title: '房间封面审批', icon: 'form' }
      },
      {
        path: 'user/profile_desc/approval',
        name: 'UserProfileDescApproval',
        component: () =>
          import('@/views/approval/user-profile-desc-approval/index'),
        meta: { title: '用户个性签名审批', icon: 'form' }
      },
      {
        path: 'room/notice/approval',
        name: 'RoomNoticeApproval',
        component: () => import('@/views/approval/room-notice-approval/index'),
        meta: { title: '房间通知审批', icon: 'form' }
      },
      {
        path: 'room/nickanme/approval',
        name: 'RoomNicknameApproval',
        component: () =>
          import('@/views/approval/room-nickname-approval/index'),
        meta: { title: '房间名称审批', icon: 'form' }
      },
      {
        path: 'room/theme/approval',
        name: 'RoomThemeApproval',
        component: () => import('@/views/approval/room-theme-approval/index'),
        meta: { title: '房间主题审批', icon: 'form' }
      },
      {
        path: 'report',
        name: 'ReportManagerReport',
        component: () => import('@/views/approval/report/index'),
        meta: { title: '举报管理', icon: 'form' }
      },
      {
        path: 'feedback',
        name: 'ReportManagerFeedback',
        component: () => import('@/views/feedback/index'),
        meta: { title: '意见反馈', icon: 'form' }
      },
      {
        path: 'not_pass_history/table',
        name: 'NotPassHistory',
        component: () => import('@/views/approval/not-pass-history/index'),
        meta: { title: '违规历史记录', icon: 'form' }
      },
      {
        path: 'family/approval',
        name: 'FamilyApproval',
        component: () => import('@/views/approval/family-approval/index'),
        meta: { title: '家族头像审批', icon: 'form' }
      },
      {
        path: 'dynamic/content/approval',
        name: 'DynamicContentApproval',
        component: () =>
          import('@/views/approval/dynamic-content-approval/index'),
        meta: { title: '动态内容审批', icon: 'form' }
      },
      {
        path: 'dynamic/report',
        name: 'DynamicReport',
        component: () =>
          import('@/views/approval/dynamic-report-approval/index'),
        meta: { title: '动态举报审批', icon: 'form' }
      },
      {
        path: 'user/bank-card/approval',
        name: 'UserBankCardApproval',
        component: () => import('@/views/approval/user-bank-card/index'),
        meta: { title: '用户银行卡审批', icon: 'form' }
      }
    ]
  }

  // 404 page must be placed at the end !!!
  // { path: '*', redirect: '/404', hidden: true }
]

let systemRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/401'),
    hidden: true
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index'),
        meta: { title: '首页', icon: 'dashboard' }
      }
    ]
  },
  {
    path: '/common',
    component: Layout,
    redirect: '/common',
    hidden: true,
    children: [
      {
        path: 'user/deatils/:id',
        name: 'UserDeatils',
        component: () => import('@/views/common/user_deatils'),
        meta: { title: '用户详情', icon: 'dashboard', keepAlive: true }
      }
    ]
  }
]

if (isMock()) {
  systemRoutes = systemRoutes.concat(mockRouters)
}

/**
 * 默认路由
 */
export const defaultRoutes = systemRoutes

const createRouter = () =>
  new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: defaultRoutes
  })

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

router.addRoutes(defaultRoutes)

export default router
