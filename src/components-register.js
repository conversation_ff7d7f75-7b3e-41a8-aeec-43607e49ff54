import Vue from 'vue'
import '@/icons' // icon
import '@/flag-icons'
import Avatar from '@/components/data/Avatar'
import Gender from '@/components/data/Gender'
import SysOriginIcon from '@/components/SysOriginIcon'
import UserDeatilsDrawer from '@/components/data/UserDeatilsDrawer'
import UserTableExhibit from '@/components/data/UserInfo/UserTableExhibit'
import Svgaplayer from '@/components/svgaplayer'
import AccountInput from '@/components/data/AccountInput'
import SearchRoomInput from '@/components/data/SearchRoomInput'
import UploadImage from '@/components/upload-image'
import SysOriginPermissionSelect from '@/components/data/SysOriginPermissionSelect'
import SelectSystemRegion from '@/components/data/SelectSystemRegion'
import VueLuckyCanvas from '@lucky-canvas/vue'
Vue.use(VueLuckyCanvas)
Vue.component('sys-origin-icon', SysOriginIcon)
Vue.component('avatar', Avatar)
Vue.component('gender', Gender)
Vue.component('user-deatils-drawer', UserDeatilsDrawer)
Vue.component('svgaplayer', Svgaplayer)
Vue.component('account-input', AccountInput)
Vue.component('search-room-input', SearchRoomInput)
Vue.component('user-table-exhibit', UserTableExhibit)
Vue.component('upload-image', UploadImage)
Vue.component('sys-origin-permission-select', SysOriginPermissionSelect)
Vue.component('select-system-region', SelectSystemRegion)
