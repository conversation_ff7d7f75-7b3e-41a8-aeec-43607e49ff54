import Sortable from 'sortablejs'

/**
 * 表格排序
 * @param {*} endCall 拖拽结束回调 value=upper/under
 */
export function simpleElDropTable(endCall) {
  const tbody = document.querySelector('.el-table__body-wrapper tbody')
  Sortable.create(tbody, {
    ghostClass: 'simple-el-drop-table',
    onEnd: function(evt) {
      const oldIndex = evt.oldIndex
      const newIndex = evt.newIndex
      if (newIndex > oldIndex) {
        endCall(evt, 'under')
        return
      }
      endCall(evt, 'upper')
    }
  })
}
