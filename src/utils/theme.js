import Cookies from 'js-cookie'

const adminThemeKey = 'admin-theme'

export function getTheme() {
  return Cookies.get(adminThemeKey)
}

export function setTheme(theme) {
  return Cookies.set(adminThemeKey, theme)
}

export function removeTheme() {
  return Cookies.remove(adminThemeKey)
}

/**
 * 检查并切换主题.
 */
export function checkSwitchTheme() {
  try {
    const theme = getTheme()
    if (theme === 'normal') {
      document.documentElement.setAttribute('class', '')
      return
    }

    if (theme === 'dark') {
      document.documentElement.setAttribute('class', 'dark-theme')
      return
    }

    const timeNow = new Date()
    const hours = timeNow.getHours()
    document.documentElement.setAttribute('class', (hours > 18 && hours <= 24) || (hours >= 0 && hours <= 6) ? 'dark-theme' : '')
  } catch (er) {
    console.log('checkSwitchTheme', er)
  }
}

