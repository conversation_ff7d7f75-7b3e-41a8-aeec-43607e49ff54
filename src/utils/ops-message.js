/**
 * 系统消息统一.
 * 统一弹出层的使用, 根据业务在包装:
 * 1. 消息文案的统一, 操作成功/失败 不用传递消息给出默认的提示文案
 * 2. 能快速统一的更管提示框样式
 */
import { Message } from 'element-ui'

/**
 * 提示消息.
 */
const opsMessage = {
  success: (text) => {
    Message({
      message: text || 'Successful',
      type: 'success',
      duration: 3 * 1000
    })
  },
  fail: (text) => {
    Message({
      message: text || 'Failed',
      type: 'error',
      duration: 3 * 1000
    })
  },
  warn: (text) => {
    Message({
      message: text,
      type: 'warning',
      duration: 3 * 1000
    })
  },
  info: (text) => {
    Message({
      message: text,
      type: 'info',
      duration: 3 * 1000
    })
  }
}

export {
  opsMessage
}

