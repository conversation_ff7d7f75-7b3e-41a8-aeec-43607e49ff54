/**
 * Created by PanJiaChen on 16/11/18.
 * pengliang on 2021-12-16
 */

/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
  return str && str.trim().length > 0
}

/**
 * 是否是正整数
 * @param {Object} num
 * @returns {Boolean}
 */
export function validPositiveNumber(num) {
  return /^\d+$/.test(num)
}

/**
 * 是否是小数保留两位
 * @param {Object} num
 * @returns {Boolean}
 */
export function validPositiveNumberPointTwo(num) {
  return /^\d{1,}(\.\d{0,2})?$/.test(num)
}
