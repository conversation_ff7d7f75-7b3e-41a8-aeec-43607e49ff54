
import request from '@/utils/request'

export function httpGetExport(url, params, excelName) {
  return new Promise((resolve, reject) => {
    request({
      url: url,
      method: 'get',
      responseType: 'blob',
      params
    }).then(res => {
      resolve(res)
      // type这里表示xlsx类型
      var blob = new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8' })
      var downloadElement = document.createElement('a')
      // 创建下载的链接
      var href = window.URL.createObjectURL(blob)
      downloadElement.href = href
      // 下载后文件名
      downloadElement.download = `${excelName}.xlsx`
      document.body.appendChild(downloadElement)
      // 点击下载
      downloadElement.click()
      // 下载完成移除元素
      document.body.removeChild(downloadElement)
      // 释放掉blob对象
      window.URL.revokeObjectURL(href)
    }).catch(er => {
      reject(er)
    })
  })
}
