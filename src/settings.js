module.exports = {

  title: '<PERSON><PERSON> yin <PERSON>',
  /**
   * @type {boolean} true | false
   * @description Whether show the settings right-panel
   */
  showSettings: true,
  /**
   * @type {boolean} true | false
   * @description Whether fix the header
   */
  fixedHeader: true,

  /**
   * @type {boolean} true | false
   * @description Whether show the logo in sidebar
   */
  sidebarLogo: true,

  /**
   * 全局配置.
   */
  application: {
    fileBucket: {
      avatar: 'avatar',
      other: 'other',
      svgasource: 'svgasource',
      svgaCover: 'svga_cover',
      back: 'back',
      startPage: 'start_page',
      feedback: 'feedback',
      apk: 'apk'
    },
    goldIconUrl: 'http://img.sugartimeapp.com/web/gold_icon_doller.png',
    diamondIconUrl: 'http://img.sugartimeapp.com/web/timchat_diamond.png',
    defaultImgUrl: 'http://img.sugartimeapp.com/back/default_avatar_20221102.png',
    imgBaseUrl: 'http://img.sugartimeapp.com'
  }
}
