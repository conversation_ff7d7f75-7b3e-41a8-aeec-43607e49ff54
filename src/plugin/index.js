import Vue from 'vue'
// ECharts 主模块
import * as echarts from 'echarts/lib/echarts'

// 图表组件
import 'echarts/lib/chart/line'
import 'echarts/lib/chart/bar'
import 'echarts/lib/chart/pie'
import 'echarts/lib/chart/map'
import 'echarts/lib/component/visualMap'

// 引入提示框和标题组件
import 'echarts/lib/component/tooltip'
import 'echarts/lib/component/title'
import 'echarts/lib/component/legend'
Vue.prototype.$echarts = echarts
import JsonViewer from 'vue-json-viewer'
Vue.use(JsonViewer)
import scroll from 'vue-seamless-scroll'
Vue.use(scroll)
import countTo from 'vue-count-to'
Vue.component('countTo', countTo)

