import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getToken } from '@/utils/auth' // get token from cookie
import { checkSwitchTheme } from '@/utils/theme'
import getPageTitle from '@/utils/get-page-title'
NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ['/login'] // no redirect whitelist
const stackRouter = []
router.beforeEach(async(to, from, next) => {
  // start progress bar
  NProgress.start()
  stackRouter.push()
  // set page title
  document.title = getPageTitle(to.meta.title)

  // determine whether the user has logged in
  const hasToken = getToken()

  if (hasToken) {
    if (to.path === '/login') {
      // if is logged in, redirect to the home page
      next({ path: '/' })
      NProgress.done()
    } else {
      const hasGetUserInfo = store.getters.name
      if (hasGetUserInfo) {
        if (to.fullPath !== '/dashboard') {
          store.dispatch('user/addNavigationRecentPreviews', {
            id: to.name || 'none',
            type: 'SYSTEM',
            name: to.meta.title || '未知',
            link: to.fullPath
          })
        }
        next()
      } else {
        try {
          // get user info
          await store.dispatch('user/getInfo').then(res => {
            store.dispatch('user/getMenus').then(res => {
              // store.getters.routers
              // router.addRoutes(store.getters.routers)
              if (res && res.length > 0) {
                router.addRoutes(res)
              }
            })
            // load dashboard
            store.dispatch('user/getUserDashboard')
          }).catch(err => {
            console.error(err)
          })
          store.dispatch('user/buttonPermissions')
          next()
        } catch (error) {
          // remove token and go to login page to re-login
          await store.dispatch('user/resetToken')
          Message.error(error || 'Has Error')
          next(`/login?redirect=${to.path}`)
          NProgress.done()
        }
      }
    }
  } else {
    /* has no token*/
    if (whiteList.indexOf(to.path) !== -1) {
      // in the free login whitelist, go directly
      next()
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      next(`/login?redirect=${to.path}`)
      NProgress.done()
    }
  }
  checkSwitchTheme()
})

router.afterEach(() => {
  // finish progress bar
  NProgress.done()
})
