import { mergeObject } from '@/utils'
/**
  * 创建编辑表单参数.
  * @param options 覆盖默认参数
  */
const createEditorForm = (options) => {
  const editorForm = {
    attr: {
    },
    style: {
      hide: false,
      background: {
        img: {
          url: '',
          repeat: 'no-repeat',
          size: '100% 100%'
        },
        color: {
          start: '',
          direction: 'NONE',
          end: ''
        }
      },
      font: {
        color: '',
        size: '',
        weight: ''
      },
      border: {
        width: '',
        color: '',
        style: '',
        radius: ''
      }
    }
  }
  if (!options) {
    return editorForm
  }
  const { attr, style } = options
  editorForm.attr = mergeObject(editorForm.attr, attr)
  editorForm.style = mergeObject(editorForm.style, style)
  return editorForm
}

/**
 * 模版支持语言.
 */
const langs = [
  { name: '中文', value: 'zh' },
  { name: '英语', value: 'en' },
  { name: '阿拉伯语', value: 'ar' },
  { name: '土耳其语', value: 'tr' },
  { name: '印尼语', value: 'id' }
]

/**
 * 模版默认树结构-礼物排名.
 */
const giftRankingTreeIndexs = [{
  id: 'INDEX',
  label: 'Index',
  editor: createEditorForm({
    attr: {
      copywriting: {
        label: '页面标题',
        type: 'text',
        i18n: [{ name: '中文', lang: 'zh', value: 'Index' }]
      }
    },
    style: {
      background: {
        color: {
          start: '#392110',
          direction: 'NONE',
          end: ''
        },
        font: {
          size: '.3rem'
        }
      }
    }
  }),
  children: [
    {
      id: 'TOP',
      label: '顶部',
      editor: createEditorForm({
        style: {
          background: {
            img: {
              url: 'https://img.sugartimeapp.com/default_img_max.png',
              repeat: 'no-repeat',
              size: '100% 100%'
            }
          }
        }
      })
    },
    {
      id: 'RULE',
      label: '规则',
      editor: createEditorForm({
        attr: {
          copywriting: {
            label: '标题',
            type: 'text',
            i18n: [
              { name: '中文', lang: 'zh', value: '规则' },
              { name: '英语', lang: 'en', value: 'Rule' },
              { name: '阿拉伯语', lang: 'ar', value: 'القواعد' },
              { name: '土耳其语', lang: 'tr', value: 'Kural' },
              { name: '印尼语', lang: 'id', value: 'Aturan' }
            ]
          }
        },
        style: {
          background: {
            color: {
              start: '#E22123',
              direction: 'to right',
              end: '#F98F48'
            }
          },
          font: {
            color: '#FFFFFF'
          },
          border: {
            radius: '1rem'
          }
        }
      }),
      children: [
        {
          id: 'RULE_CONTENT',
          label: '内容',
          editor: createEditorForm({
            attr: {
              copywriting: {
                label: '规则',
                type: 'textarea',
                i18n: [
                  { name: '中文', lang: 'zh', value: '1.活动规则内容\n<br>\n2.通过上面的符号换行' },
                  { name: '英语', lang: 'en', value: '1.en\n<br>\n2.en' },
                  { name: '阿拉伯语', lang: 'ar', value: '1.ar\n<br>\n2.ar' },
                  { name: '土耳其语', lang: 'tr', value: '1.tr\n<br>\n2.tr' },
                  { name: '印尼语', lang: 'id', value: '1.id\n<br>\n2.id' }
                ]
              }
            },
            style: {
              background: {
                color: {
                  start: '#1f1309',
                  direction: 'NONE',
                  end: ''
                }
              },
              font: {
                color: '#FFFFFF'
              }
            }
          })
        },
        {
          id: 'RULE_BUTTON',
          label: '按钮',
          editor: createEditorForm({
            style: {
              background: {
                color: {
                  start: '#FFFFFF',
                  direction: 'NONE',
                  end: ''
                }
              },
              font: {
                color: '#1f1309'
              }
            }
          })
        }]
    },
    {
      id: 'COUNT_DOWN',
      label: '倒计时',
      editor: createEditorForm({
        style: {
          background: {
            color: {
              start: '#8D0502',
              direction: 'NONE',
              end: ''
            }
          },
          font: {
            color: '#FFFFFF',
            size: '.45rem',
            weight: 'bold'
          },
          border: {
            width: '.02rem',
            color: '#FFFFFF',
            style: 'solid',
            radius: '.1rem'
          }
        }
      }),
      children: [{
        id: 'COUNT_DOWN_NUMBER',
        label: '计数',
        editor: createEditorForm({
          style: {
            background: {
              color: {
                start: '#FDCC4C',
                direction: 'to left',
                end: '#F86711'
              }
            },
            font: {
              color: '#FFFFFF',
              size: '.5rem',
              weight: 'bold'
            },
            border: {
              radius: '.1rem'
            }
          }
        })
      },
      {
        id: 'COUNT_DOWN_TEXT_DAY',
        label: '天',
        editor: createEditorForm({
          attr: {
            copywriting: {
              label: '文本',
              type: 'type',
              i18n: [
                { name: '中文', lang: 'zh', value: '天' },
                { name: '英语', lang: 'en', value: 'Days' },
                { name: '阿拉伯语', lang: 'ar', value: 'أيام' },
                { name: '土耳其语', lang: 'tr', value: 'gün' },
                { name: '印尼语', lang: 'id', value: 'langit' }
              ]
            }
          },
          style: {
            font: {
              color: '#fff8db'
            }
          }
        })
      },
      {
        id: 'COUNT_DOWN_TEXT_HOURS',
        label: '时',
        editor: createEditorForm({
          attr: {
            copywriting: {
              label: '文本',
              type: 'type',
              i18n: [
                { name: '中文', lang: 'zh', value: '时' },
                { name: '英语', lang: 'en', value: 'Hours' },
                { name: '阿拉伯语', lang: 'ar', value: 'ساعة' },
                { name: '土耳其语', lang: 'tr', value: 'Zaman' },
                { name: '印尼语', lang: 'id', value: 'Waktu' }
              ]
            }
          },
          style: {
            font: {
              color: '#fff8db'
            }
          }
        })
      },
      {
        id: 'COUNT_DOWN_TEXT_MINUTES',
        label: '分',
        editor: createEditorForm({
          attr: {
            copywriting: {
              label: '文本',
              type: 'type',
              i18n: [
                { name: '中文', lang: 'zh', value: '分' },
                { name: '英语', lang: 'en', value: 'minutes' },
                { name: '阿拉伯语', lang: 'ar', value: 'دقيقة' },
                { name: '土耳其语', lang: 'tr', value: 'Dakika' },
                { name: '印尼语', lang: 'id', value: 'Menit' }
              ]
            }
          },
          style: {
            font: {
              color: '#fff8db'
            }
          }
        })
      },
      {
        id: 'COUNT_DOWN_TEXT_SECONDS',
        label: '秒',
        editor: createEditorForm({
          attr: {
            copywriting: {
              label: '文本',
              type: 'type',
              i18n: [
                { name: '中文', lang: 'zh', value: '秒' },
                { name: '英语', lang: 'en', value: 'Seconds' },
                { name: '阿拉伯语', lang: 'ar', value: 'ثانية' },
                { name: '土耳其语', lang: 'tr', value: 'ikinci' },
                { name: '印尼语', lang: 'id', value: 'kedua' }
              ]
            }
          },
          style: {
            font: {
              color: '#fff8db'
            }
          }
        })
      }]
    },
    {
      id: 'ACTIVIY_GIFT',
      label: '活动礼物',
      editor: createEditorForm({
        style: {
          border: {
            width: '.02rem ',
            color: '#d3be87',
            style: 'solid',
            radius: '.1rem'
          },
          background: {
            color: {
              start: '#392110',
              direction: 'NONE',
              end: ''
            }
          }
        }
      }),
      children: [{
        id: 'ACTIVIY_GIFT_TITLE',
        label: '标题',
        editor: createEditorForm({
          attr: {
            copywriting: {
              label: '文本',
              type: 'type',
              i18n: [
                { name: '中文', lang: 'zh', value: '活动礼物' },
                { name: '英语', lang: 'en', value: 'Event gift' },
                { name: '阿拉伯语', lang: 'ar', value: 'هدية الحدث' },
                { name: '土耳其语', lang: 'tr', value: 'etkinlik hediyesi' },
                { name: '印尼语', lang: 'id', value: 'hadiah acara' }
              ]
            }
          },
          style: {
            background: {
              color: {
                start: '#392110',
                direction: 'NONE',
                end: ''
              }
            },
            border: {
              width: '.02rem ',
              color: '#d3be87',
              style: 'solid',
              radius: '.1rem'
            }
          }
        })
      },
      {
        id: 'ACTIVIY_GIFT_BODY_GIFT_1',
        label: '礼物1',
        editor: createEditorForm({
          style: {
            border: {
              width: '.02rem ',
              color: '#d3be87',
              style: 'solid',
              radius: '.1rem'
            }
          }
        }),
        children: [
          {
            id: 'ACTIVIY_GIFT_BODY_GIFT_1_ICON',
            label: '图标',
            editor: createEditorForm({
              attr: {
                imgUrls: [{ id: '1', label: '图片', url: 'http://img.sugartimeapp.com/web/timchat_gold.png' }]
              },
              style: {
                hide: true
              }
            })
          },
          {
            id: 'ACTIVIY_GIFT_BODY_GIFT_1_TEXT',
            label: '文案',
            editor: createEditorForm()
          }
        ]
      },
      {
        id: 'ACTIVIY_GIFT_BODY_GIFT_2',
        label: '礼物2',
        editor: createEditorForm({
          style: {
            border: {
              width: '.02rem ',
              color: '#d3be87',
              style: 'solid',
              radius: '.1rem'
            }
          }
        }),
        children: [
          {
            id: 'ACTIVIY_GIFT_BODY_GIFT_2_ICON',
            label: '图标',
            editor: createEditorForm({
              attr: {
                imgUrls: [{ id: '1', label: '图片', url: 'http://img.sugartimeapp.com/web/timchat_gold.png' }]
              },
              style: {
                hide: true
              }
            })
          },
          {
            id: 'ACTIVIY_GIFT_BODY_GIFT_2_TEXT',
            label: '文案',
            editor: createEditorForm()
          }
        ]
      },
      {
        id: 'ACTIVIY_GIFT_BODY_GIFT_3',
        label: '礼物3',
        editor: createEditorForm({
          style: {
            border: {
              width: '.02rem ',
              color: '#d3be87',
              style: 'solid',
              radius: '.1rem'
            }
          }
        }),
        children: [
          {
            id: 'ACTIVIY_GIFT_BODY_GIFT_3_ICON',
            label: '图标',
            editor: createEditorForm({
              attr: {
                imgUrls: [{ id: '1', label: '图片', url: 'http://img.sugartimeapp.com/web/timchat_gold.png' }]
              },
              style: {
                hide: true
              }
            })
          },
          {
            id: 'ACTIVIY_GIFT_BODY_GIFT_3_TEXT',
            label: '文案',
            editor: createEditorForm()
          }
        ]
      }]
    },
    {
      id: 'ACTIVITY_BUT',
      label: '活动按钮',
      editor: createEditorForm({
        style: {
          tips: '按钮选中效果设置',
          background: {
            color: {
              start: '#FFFFFF',
              direction: 'NONE',
              end: ''
            }
          },
          font: {
            color: '#8D0502'
          },
          border: {
            width: '.05rem',
            color: '#FFFFFF',
            style: 'solid',
            radius: '.4rem'
          }
        }
      }),
      children: [{
        id: 'ACTIVITY_BUT_1',
        label: '按钮1',
        editor: createEditorForm({
          attr: {
            copywriting: {
              label: '文本',
              type: 'type',
              i18n: [
                { name: '中文', lang: 'zh', value: '财富' },
                { name: '英语', lang: 'en', value: 'Wealth' },
                { name: '阿拉伯语', lang: 'ar', value: 'ثروة' },
                { name: '土耳其语', lang: 'tr', value: 'varlık' },
                { name: '印尼语', lang: 'id', value: 'kekayaan' }
              ]
            }
          },
          style: {
            font: {
              color: '#FFFFFF',
              size: '',
              weight: ''
            },
            border: {
              width: '.05rem',
              color: '#FFFFFF',
              style: 'solid',
              radius: '.4rem'
            }
          }
        })
      },
      {
        id: 'ACTIVITY_BUT_2',
        label: '按钮2',
        editor: createEditorForm({
          attr: {
            copywriting: {
              label: '文本',
              type: 'type',
              i18n: [
                { name: '中文', lang: 'zh', value: '魅力' },
                { name: '英语', lang: 'en', value: 'Charm' },
                { name: '阿拉伯语', lang: 'ar', value: 'سحر' },
                { name: '土耳其语', lang: 'tr', value: 'cazibe' },
                { name: '印尼语', lang: 'id', value: 'pesona' }
              ]
            }
          },
          style: {
            font: {
              color: '#FFFFFF',
              size: '',
              weight: ''
            },
            border: {
              width: '.05rem',
              color: '#FFFFFF',
              style: 'solid',
              radius: '.4rem'
            }
          }
        })
      }]
    },
    {
      id: 'ACTIVITY_BODY',
      label: '内容',
      editor: createEditorForm({
        style: {
          background: {
            color: {
              start: '#1F1209',
              direction: 'NONE',
              end: ''
            }
          },
          border: {
            radius: '.5rem .5rem .5rem .5rem'
          }
        }
      }),
      children: [{
        id: 'ACTIVITY_BODY_BUT',
        label: '活动按钮',
        editor: createEditorForm({
          style: {
            tips: '按钮选中效果设置',
            background: {
              color: {
                start: '#FFFFFF',
                direction: 'NONE',
                end: ''
              }
            },
            font: {
              color: '#8D0502'
            },
            border: {
              width: '.04rem',
              color: '#FFFFFF',
              style: 'solid'
            }
          }
        }),
        children: [{
          id: 'ACTIVITY_BODY_BUT_1',
          label: '按钮1',
          editor: createEditorForm({
            attr: {
              copywriting: {
                label: '文本',
                type: 'type',
                i18n: [
                  { name: '中文', lang: 'zh', value: '排行榜' },
                  { name: '英语', lang: 'en', value: 'Rank' },
                  { name: '阿拉伯语', lang: 'ar', value: 'مرتبة' },
                  { name: '土耳其语', lang: 'tr', value: 'rütbe' },
                  { name: '印尼语', lang: 'id', value: 'pangkat' }
                ]
              }
            },
            style: {
              font: {
                color: '#FFFFFF'
              },
              border: {
                width: '.04rem',
                color: '#FFFFFF',
                style: 'solid'
              }
            }
          })
        },
        {
          id: 'ACTIVITY_BODY_BUT_2',
          label: '按钮2',
          editor: createEditorForm({
            attr: {
              copywriting: {
                label: '文本',
                type: 'type',
                i18n: [
                  { name: '中文', lang: 'zh', value: '奖励' },
                  { name: '英语', lang: 'en', value: 'Award' },
                  { name: '阿拉伯语', lang: 'ar', value: 'جائزة' },
                  { name: '土耳其语', lang: 'tr', value: 'ödül' },
                  { name: '印尼语', lang: 'id', value: 'hadiah' }
                ]
              }
            },
            style: {
              font: {
                color: '#FFFFFF'
              },
              border: {
                width: '.04rem',
                color: '#FFFFFF',
                style: 'solid'
              }
            }
          })
        }]
      },
      {
        id: 'ACTIVITY_BODY_RING',
        label: '活动排行榜',
        editor: createEditorForm({
          style: {
            background: {
              color: {
                start: '#392110',
                direction: 'NONE',
                end: ''
              }
            },
            border: {
              width: '.02rem',
              color: '#D3BE87',
              style: 'solid',
              radius: '.2rem'
            }
          }
        }),
        children: [
          {
            id: 'ACTIVITY_BODY_RING_BAG',
            label: 'TOP徽章',
            editor: createEditorForm({
              attr: {
                imgUrls: [
                  { id: 'TOP1', label: '1徽章图', url: 'http://img.sugartimeapp.com/back_teamplate_top1.png' },
                  { id: 'TOP2', label: '2徽章图', url: 'http://img.sugartimeapp.com/back_teamplate_top2.png' },
                  { id: 'TOP3', label: '3徽章图', url: 'http://img.sugartimeapp.com/back_teamplate_top3.png' }
                ]
              }
            })
          },
          {
            id: 'ACTIVITY_BODY_RING_AVATAR',
            label: '用户头像',
            editor: createEditorForm({
              style: {
                hide: true
              }
            })
          },
          {
            id: 'ACTIVITY_BODY_RING_NICKNAME',
            label: '用户昵称',
            editor: createEditorForm({
              style: {
                font: {
                  color: '#FFFFFF'
                }
              }
            })
          },
          {
            id: 'ACTIVITY_BODY_RING_AMOUNT',
            label: '贡献金额',
            editor: createEditorForm({
              style: {
                font: {
                  color: '#DCC79C'
                }
              }
            })
          },
          {
            id: 'ACTIVITY_BODY_RING_ICON',
            label: '贡献icon',
            editor: createEditorForm({
              attr: {
                imgUrls: [
                  { id: '1', label: 'icon', url: 'http://img.sugartimeapp.com/web/week_star_gift_pack_2.png' }
                ]
              },
              style: {
                hide: true
              }
            })
          }
        ]
      },
      {
        id: 'ACTIVITY_BODY_REWARD',
        label: '活动奖品',
        editor: createEditorForm({
          attr: {
            // rewards: ['1', '2', '3', '4', '5~10', '11~20']
          },
          style: {
            background: {
              color: {
                start: '#2b221b',
                direction: 'NONE',
                end: ''
              }
            },
            border: {
              radius: '.1rem'
            }
          }
        }),
        children: [
          {
            id: 'ACTIVITY_BODY_REWARD_TITLE',
            label: '标题',
            editor: createEditorForm({
              style: {
                background: {
                  color: {
                    start: '#392110',
                    direction: 'NONE',
                    end: ''
                  }
                },
                font: {
                  color: '#FFFFFF'
                },
                border: {
                  width: '.02rem ',
                  color: '#D3BE87',
                  style: 'solid',
                  radius: '.1rem'
                }
              }
            })
          },
          {
            id: 'ACTIVITY_BODY_REWARD_CARD',
            label: '奖励品卡',
            editor: createEditorForm({
              style: {
                background: {
                  color: {
                    start: '#ef8827',
                    direction: 'to bottom',
                    end: '#86a38a'
                  }
                }
              }
            }),
            children: [
              {
                id: 'ACTIVITY_BODY_REWARD_CARD_TOP',
                label: '道具名称',
                editor: createEditorForm({
                  style: {
                    font: {
                      color: '#333'
                    }
                  }
                })
              },
              {
                id: 'ACTIVITY_BODY_REWARD_CARD_CENTER',
                label: '道具预览',
                editor: createEditorForm({
                  style: {
                    background: {
                      color: {
                        start: '#818a75',
                        direction: 'NONE',
                        end: ''
                      }
                    }
                  }
                })
              },
              {
                id: 'ACTIVITY_BODY_REWARD_CARD_BOTTOM',
                label: '道具数额',
                editor: createEditorForm({
                  style: {
                    font: {
                      color: '#FFFFFF'
                    }
                  }
                })
              }
            ]
          }
        ]
      }]
    },
    {
      id: 'COPYRIGHT',
      label: '解释权',
      editor: createEditorForm({
        attr: {
          copywriting: {
            label: '文案',
            type: 'text',
            i18n: [
              { name: '中文', lang: 'zh', value: '最终解释权归 {} 所有' },
              { name: '英语', lang: 'en', value: 'The final interpretation right belongs to {}' },
              { name: '阿拉伯语', lang: 'ar', value: 'يعود حق التفسير النهائي إلى {}' },
              { name: '土耳其语', lang: 'tr', value: 'Nihai yorum hakkı {}\'a aittir.' },
              { name: '印尼语', lang: 'id', value: 'Hak interpretasi akhir milik {}' }
            ]
          }
        },
        style: {
          font: {
            color: '#DCC79C',
            weight: 'bold'
          }
        }
      })
    },
    {
      id: 'ACTIVITY_MY',
      label: '我的贡献',
      editor: createEditorForm({
        style: {
          background: {
            color: {
              start: '#967245',
              direction: 'to bottom right',
              end: '#1f1309'
            }
          },
          font: {
            color: '#FFFFFF'
          },
          border: {
            radius: '.3rem .3rem 0rem 0rem'
          }
        }
      }),
      children: [
        {
          id: 'ACTIVITY_MY_AVATAR',
          label: '用户头像',
          editor: createEditorForm({
            style: {
              hide: true
            }
          })
        },
        {
          id: 'ACTIVITY_MY_NICKNAME',
          label: '用户昵称',
          editor: createEditorForm({
            style: {
              font: {
                color: '#FFFFFF'
              }
            }
          })
        },
        {
          id: 'ACTIVITY_MY_TEXT',
          label: '文本',
          editor: createEditorForm({
            style: {
              font: {
                color: '#FFFFFF'
              }
            }
          })
        },
        {
          id: 'ACTIVITY_MY_ICON',
          label: '礼物icon',
          editor: createEditorForm({
            attr: {
              imgUrls: [
                { id: '1', label: 'icon', url: 'http://img.sugartimeapp.com/web/week_star_gift_pack_2.png' }
              ]
            }
          })
        }
      ]
    }]
}]

export { giftRankingTreeIndexs, langs, createEditorForm }

