// 性别
const genders = [
  { value: 0, name: '女' },
  { value: 1, name: '男' }
]

// 用户类型
const userTypes = [
  { value: 0, name: '真实' },
  // { value: 1, name: '普通马甲' },
  // { value: 2, name: 'VIP马甲' }
  { value: 3, name: '视频马甲' }
]

// 账号类型
const accountStatus = [
  { value: 'NORMAL', name: '正常' },
  { value: 'FREEZE', name: '冻结' },
  { value: 'ARCHIVE', name: '封禁' }
]

// 身份状态
const vipStatus = [
  { value: 'true', name: '会员' },
  { value: 'false', name: '非会员' }
]

// 年龄段
const ageRang = [
  { value: '18,23', name: '18-23岁' },
  { value: '23,28', name: '23-28岁' },
  { value: '28,33', name: '28-33岁' },
  { value: '33,38', name: '33-38岁' },
  { value: '38,43', name: '38-43岁' },
  { value: '43,48', name: '43-48岁' },
  { value: '48,88', name: '48-88岁' }
]

// 照片墙审核状态
const approvalStatus = [
  { value: 'NORMAL', name: '正常的' },
  { value: 'VIOLATION', name: '违规的' },
  { value: 'SUSPECTED', name: '等待人工复审的' }
]

// 审核状态
const commonApprovalStatus = [
  { value: 'PASS', name: '通过' },
  { value: 'NOT_PASS', name: '不通过' },
  { value: 'PENDING', name: '等待审核' }
]

// 动态类型
const commonDynamicType = [
  { value: 0, name: '图文' },
  { value: 1, name: '视频' }
]

// 等级-废弃
const levels = [
  { value: '-1', name: 'L-1' },
  { value: '0', name: 'L0' },
  { value: '1', name: 'L1' },
  { value: '2', name: 'L2' },
  { value: '3', name: 'L3' }
]

// 积分
const scores = [
  { value: '0,1000', name: '0-1000' },
  { value: '1000,3000', name: '1000-3000' },
  { value: '3000,5000', name: '3000-5000' },
  { value: '5000,8000', name: '5000-8000' },
  { value: '8000,10000', name: '8000-10000' },
  { value: '10000,9999999', name: '10000以上' }
]

// 视频通话来源
const videoOrigin = [
  { value: 'CLUB', name: 'Club聊天' },
  { value: 'VIDEO_ACTIVE_CALL', name: '视频电话' },
  { value: 'VIDEO_MATCH_ALL', name: '匹配所有' },
  { value: 'VIDEO_CONDITION_FEMALE', name: '匹配女生' },
  { value: 'VIDEO_CONDITION_GODDESS', name: '匹配女神' },
  { value: 'CHAT_CONSUME_CANDY', name: '文本聊天' },
  { value: 'GIFT', name: '赠送礼物' },
  { value: 'SHORT_VIDEO_WATCH_AD_SETTLE_ACCOUNTS', name: '短视频广告结算' }
]

// 视频会话收藏
const collects = [
  { value: 0, name: '未收藏' },
  { value: 1, name: '已收藏' }
]

// 模板类型
const modeTypes = [
  { value: 'CHAT_MESSAGE', name: '聊天消息' },
  { value: 'SYSTEM_CALL', name: '系统招呼' }
]

// 模板用户类型
const userModeTypes = [
  { value: 'MAN', name: '男' },
  { value: 'WOMAN', name: '女' },
  { value: 'ALL', name: '全部' }
]

// 模板状态
const modeStatus = [
  { value: 'PUBLISHED', name: '已发布' },
  { value: 'UNPUBLISHED', name: '未发布' },
  { value: 'REMOVE', name: '已下架' }
]

// 视频会话来源
const videoObtainOrigin = [
  { value: 'VIDEO_MATCH_ALL', name: '免费池' },
  { value: 'VIDEO_CONDITION_FEMALE', name: '女生池' },
  { value: 'VIDEO_CONDITION_GODDESS', name: '女神池' }
]

// 选择偏好
const videoObtainHobby = [
  { value: '0', name: '免费池' },
  { value: '1', name: '女生池' },
  { value: '2', name: '女神池' }
]

// 视频来源
const streamOrigins = [
  { value: '2', name: '主动拨打' },
  { value: '3', name: '匹配所有' },
  { value: '4', name: '匹配女生' },
  { value: '5', name: '匹配女神' },
  { value: '6', name: '匹配马甲' }
]

// 女性用户身份
const userIdentitys = [
  { value: 'PRIORITY', name: '优质女性' },
  { value: 'ANCHOR', name: '主播' },
  { value: 'ORDINARY', name: '普通女性' },
  { value: 'PRIORITY_AND_ANCHOR', name: '主播与优质女性' }
]

// 男性用户身份
const userMaleIdentitys = [
  { value: 'BUY_VIP', name: '男性会员' },
  { value: 'BUY_CANDY', name: '购买过糖果的男性' },
  { value: 'ORDINARY', name: '普通男性' },
  { value: 'BUY_CANDY_AND_VIP', name: '会员与购买过糖果男性' }
]

// 男性等级-废弃
const maleLevels = [
  { value: '0', name: 'T0' },
  { value: '1', name: 'T1' },
  { value: '2', name: 'T2' },
  { value: '3', name: 'T3' }
]

// 提成积分来源
const integralCommissionOrigin = [
  { value: 'EXCHANGE', name: '积分兑换' },
  { value: 'REMOVE', name: '违规扣除' },
  { value: 'COMPENSATE', name: '系统补偿' },
  { value: 'COMMISSION', name: '提成' }
]

const idCardApprovalStatus = [
  { value: 'NOT', name: '等待认证' },
  { value: 'VERIFIED', name: '通过' },
  { value: 'NOT_CERTIFIED', name: '不通过' }
]

// 银行流水事件
const userBankWaterEvent = [
  { value: 'WITHDRAW', name: '提现' },
  { value: 'TRANSFER', name: '转账' },
  { value: 'RECEIVE_TRANSFER', name: '接收转账' },
  { value: 'SYSTEM_SETTLEMENT_WAGES_AGENT', name: '结算代理工资' },
  { value: 'SYSTEM_SETTLEMENT_WAGES_MEMBER', name: '结算代理代收工资' },
  { value: 'EXCHANGE_GOLD_COINS', name: '兑换金币' },
  { value: 'BILL_EXCHANGE_GOLD_COINS', name: '账单兑换金币' },
  { value: 'SALARY_EXCHANGE_GOLD_COINS', name: '工资兑换金币' },
  { value: 'SYSTEM_SETTLEMENT_WAGES', name: '结算成员工资' },
  { value: 'SEND_SALARY', name: '后台导入' },
  { value: 'SYSTEM_DEDUCT', name: '系统扣除' },
  { value: 'SYSTEM_COMPENSATE', name: '系统补偿' },
  { value: 'CREATE_ACCOUNT', name: '创建银行账户' },
  { value: 'SYSTEM_AUTOMATIC_DEDUCT_SALARY', name: 'AUTO-扣除主播' },
  { value: 'SYSTEM_AUTOMATIC_DEDUCT_SALARY_AGENT', name: 'AUTO-扣除代理' },
  { value: 'SYSTEM_AUTOMATIC_DEDUCT_SALARY_AGENT_MEMBER', name: 'AUTO-扣除代理代收' },
  { value: 'SYSTEM_AUTOMATIC_SETTLEMENT_WAGES', name: 'AUTO-主播结算' },
  { value: 'SYSTEM_AUTOMATIC_SETTLEMENT_WAGES_AGENT', name: 'AUTO-代理结算' },
  { value: 'SYSTEM_AUTOMATIC_SETTLEMENT_WAGES_MEMBER', name: 'AUTO-代理代收' }
]

// 工资钻石流水事件
const userSalaryDiamondWaterEvent = [
  { value: 'WITHDRAW', name: '提现' },
  { value: 'TRANSFER', name: '转账' },
  { value: 'RECEIVE_TRANSFER', name: '接收转账' },
  { value: 'SYSTEM_SETTLEMENT_WAGES_AGENT', name: '结算代理工资' },
  { value: 'SYSTEM_SETTLEMENT_WAGES_MEMBER', name: '结算代理代收工资' },
  { value: 'EXCHANGE_GOLD_COINS', name: '兑换金币' },
  { value: 'BILL_EXCHANGE_GOLD_COINS', name: '账单兑换金币' },
  { value: 'SALARY_EXCHANGE_GOLD_COINS', name: '工资兑换金币' },
  { value: 'SYSTEM_SETTLEMENT_WAGES', name: '结算成员工资' },
  { value: 'SYSTEM_DEDUCT', name: '系统扣除' },
  { value: 'SYSTEM_COMPENSATE', name: '系统补偿' },
  { value: 'CREATE_ACCOUNT', name: '创建银行账户' },
  { value: 'SYSTEM_AUTOMATIC_DEDUCT_SALARY', name: 'AUTO-扣除主播' },
  { value: 'SYSTEM_AUTOMATIC_DEDUCT_SALARY_AGENT', name: 'AUTO-扣除代理' },
  { value: 'SYSTEM_AUTOMATIC_DEDUCT_SALARY_AGENT_MEMBER', name: 'AUTO-扣除代理代收' },
  { value: 'SYSTEM_AUTOMATIC_SETTLEMENT_WAGES', name: 'AUTO-主播结算' },
  { value: 'SYSTEM_AUTOMATIC_SETTLEMENT_WAGES_AGENT', name: 'AUTO-代理结算' },
  { value: 'SYSTEM_AUTOMATIC_SETTLEMENT_WAGES_MEMBER', name: 'AUTO-代理代收' }
]

export { userBankWaterEvent, genders, userTypes, accountStatus, vipStatus, ageRang, approvalStatus, commonApprovalStatus, commonDynamicType, levels, scores, videoOrigin, collects, modeTypes, userModeTypes, modeStatus, videoObtainOrigin, videoObtainHobby, streamOrigins, userIdentitys, userMaleIdentitys, maleLevels, integralCommissionOrigin, idCardApprovalStatus, userSalaryDiamondWaterEvent }

