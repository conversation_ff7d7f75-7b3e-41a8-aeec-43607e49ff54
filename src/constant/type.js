/**
 * App启动页活动类型
 */
const appStartPagePlanTypes = [
  { value: "WEEK_STAR", name: "周星" },
  { value: "WEEK_CP", name: "每周CP" },
  { value: "WEEK_KING", name: "每周国王/King" },
  { value: "NON_FIXED_ACTIVITY", name: "非固定活动" }
];

/**
 * 抽奖游戏类型
 */
const lotteryGameTypes = [{ value: "EGG", name: "砸金蛋" }];

/**
 * 货运账单余额来源
 */
const freightBalanceOrigin = [
  { value: "SHIPMENT", name: "出货" },
  { value: "PURCHASE", name: "进货" },
  { value: "DEDUCTION", name: "扣除" }
];

/**
 * 表情类型
 */
const emojiType = [
  { value: "VIP", name: "会员" },
  { value: "NORMAL", name: "正常" }
];

/**
 * 表情资源类型
 */
const emojiSourceType = [{ value: 1, name: "svga" }, { value: 2, name: "gif" }];

/**
 * 房间状态
 */
const roomEvents = [
  { value: "AVAILABLE", name: "正常" },
  { value: "CLOSE", name: "关闭" },
  { value: "WAITING_CONFIRMED", name: "等待确定" },
  { value: "ID_CHANGE", name: "ID变更" }
];

/**
 * 房间角色
 */
const roomRoles = [
  { value: "HOMEOWNER", name: "房主" },
  { value: "ADMIN", name: "管理员" },
  { value: "MEMBER", name: "成员" }
];

/**
 * 活动规则类型
 */
const propActivityTypes = [
  { value: "THE_BIG_WHEEL", name: "大转盘" },
  { value: "INDEPENDENCE_DAY_OF_INDIA", name: "印度独立日" },
  { value: "ACTIVITY_FRIENDSHIP_CARD_REWARDS", name: "好友关系卡榜单活动" },
  { value: "INVITE_USER_REWARDS", name: "邀请用户数量奖励" },
  { value: "STAR", name: "周星" },
  { value: "WEEK_STAR", name: "周星奖励" },
  { value: "CUMULATIVE_RECHARGE", name: "累积充值" },
  { value: "CRYSTAL", name: "爆水晶" },
  { value: "CRYSTAL_TOP", name: "爆水晶突破奖励" },
  { value: "CRYSTAL_LUCKY_BOX", name: "爆水晶宝箱奖励" },
  { value: "GAME_EXPLOSION_CRYSTAL", name: "爆水晶奖励" },
  { value: "ROOM_REWARD", name: "房间奖励" },
  { value: "CP_REWARD", name: "CP奖励" },
  { value: "FIRST_CHARGE_REWARD", name: "首充奖励" },
  { value: "WEEK_CP_GIFT", name: "每周CP礼物互送榜" },
  { value: "SVIP_REWARD", name: "SVIP" },
  { value: "WEEK_KING", name: "每周国王" },
  { value: "WEEK_QUEEN", name: "每周皇后" },
  { value: "ROOM_PK", name: "房间PK" },
  { value: "GAME_KING", name: "游戏之王" },
  { value: "DAILY_REGISTER", name: "每天签到" },
  { value: "LUCKY_GIFT_REWARD", name: "幸运礼物奖励" },
  { value: "APRIL_ACTIVITY_FRAGMENT", name: "五福碎片" },
  { value: "CUMULATIVE_RECHARGE_LOTTERY", name: "累计充值抽奖奖品" },
  { value: "WEEKLY_GAME_TASKS", name: "每周游戏任务" },
  { value: "ACTIVE_AGENT_ANCHOR_COUNT_REWARD", name: "代理的主播数量满足奖励" },
  {
    value: "ACTIVE_AGENT_MONTH_TARGET_REWARD",
    name: "代理名下主播累计月积分奖励"
  },
  { value: "ACTIVE_ANCHOR_MONTH_TARGET_REWARD", name: "主播月积分奖励" },
  { value: "ACTIVE_ANCHOR_DAY_TARGET_REWARD", name: "主播日积分奖励" },
  { value: "GAME_KTV_WEEK_RANK_REWARD", name: "KTV游戏每周榜单奖励" },
  { value: "AGENT_ACTIVE_WEEK_REWARD", name: "代理活动奖励-周" },
  { value: "AGENT_ACTIVE_MONTH_REWARD", name: "代理活动奖励-月" },
  { value: "CONSUMPTION_ACTIVITY", name: "消耗活动" },
  { value: "ROOM_FAN_VOTES_ACTIVITY", name: "房间粉丝人气票" },
  { value: "ACTIVITY_COUNTRY_PK", name: "区域国旗PK活动" },
  { value: "USER_RECHARGE_DRAW", name: "充值活动抽奖" },
  { value: "USER_RECHARGE_DRAW_REWARD", name: "用户充值抽奖活动奖励" },
  { value: "NEWCOMER_GIFT_PACKAGE", name: "新人大礼包" }
];

/**
 * 时间段
 */
const weekTypes = [
  { value: "THIS_WEEK", name: "这周" },
  { value: "LAST_WEEK", name: "上周" }
];

/**
 * 活动-数据缓存刷新
 */
const activityRedisTypes = [{ value: "STAR", name: "周星" }];

/**
 * 活动规则类型-帮助
 */
const propActivityTypesHelp = {
  STAR: {
    quantity: "赠送周星礼物最低限额"
  },
  CUMULATIVE_RECHARGE: {
    quantity: "累积充值价格里程碑"
  },
  ROOM_REWARD: {
    quantity: "房间奖励贡献里程碑"
  },
  CUMULATIVE_RECHARGE_LOTTERY: {
    quantity: "累积充值抽奖要求充值金额"
  },
  CRYSTAL: {
    level: "游戏等级",
    milestone: "游戏里程碑",
    smallIcon: "小号图标",
    mediumIcon: "中号图标",
    sourceUrl: "动画资源图"
  }
};

/**
 * 徽章赠送使用类型
 */
const badgeInvalidTypes = [
  { value: 0, name: "使用中" },
  { value: 1, name: "已收回" }
];
/**
 * 扶持房间奖励发放状态
 */
const sendStatusArray = [
  { value: 0, name: "待发放" },
  { value: 1, name: "已发放" }
];

/**
 * 糖果购买收支类型
 */
const candyPurchasingTypes = [
  { value: 0, name: "收入" },
  { value: 1, name: "支出" }
];

/**
 * 反馈意见审核类型
 */
const approvalStatusArray = [
  { value: 0, name: "未处理" },
  { value: 1, name: "已处理" }
];

/**
 * 发布
 */
const announcementArray = [
  { value: true, name: "已发布" },
  { value: false, name: "未发布" }
];

/**
 * 货币奖励原因
 */
const currencyRewardReasons = [
  { value: 1, name: "奖励" },
  { value: 2, name: "内部" },
  { value: 3, name: "工资" },
  { value: 4, name: "其他" }
];

/**
 * 货币扣除原因
 */
const currencyDeductReasons = [
  { value: 1, name: "违规" },
  { value: 2, name: "多发" },
  { value: 3, name: "操作错误" },
  { value: 4, name: "其他" }
];

/**
 * 邀请奖励领取状态
 */
const inviteRewardTypes = [
  { value: "true", name: "已领取" },
  { value: "false", name: "未领取" }
];

/**
 * 消息文案类型
 */
const msgCopywritingTypes = [
  {
    value: "PUSH",
    name: "推送",
    children: [
      { value: "NONE", name: "无" },
      { value: "CARD_LIKE", name: "谁右滑了你" },
      { value: "CARD_PAIR", name: "卡片配对成功" },
      { value: "IM_MSG", name: "谁给你发送了私信" },
      { value: "VIDEO_MATCH_SUCCESS", name: "视频匹配成功" }
    ]
  },
  { value: "GREET", name: "打招呼", children: [{ value: "NONE", name: "无" }] }
];

/**
 * 语言类型
 */
const language = [
  { value: "en", name: "英语" },
  { value: "ar", name: "阿拉伯语" },
  { value: "id", name: "印尼语" },
  { value: "tr", name: "土耳其语" },
  { value: "ru", name: "俄语" },
  { value: "pt", name: "葡萄牙语" },
  { value: "ko", name: "韩语" },
  { value: "ja", name: "日语" },
  { value: "hi", name: "印地语" },
  { value: "fr", name: "法语" },
  { value: "es", name: "西班牙语" },
  { value: "de", name: "德语" },
  { value: "zh_CN", name: "中文" },
  { value: "zh_TW", name: "繁体" }
];

/**
 * banner，动态tag等语言类型
 */
const specifyLanguage = [
  { value: "en", name: "英语" },
  { value: "ar", name: "阿拉伯语" },
  { value: "tr", name: "土耳其语" },
  { value: "id", name: "印尼语" },
  { value: "hi", name: "印地语" },
  { value: "ur", name: "巴基斯坦乌尔都语" },
  { value: "vi", name: "越南语" },
  { value: "bn", name: "孟加拉语" },
  { value: "tl", name: "菲律宾他加禄语" }
];

/**
 * 审批类型
 */
const approvalType = [
  { value: "PORN", name: "涉黄", disabled: false },
  { value: "VIOLENCE", name: "涉爆", disabled: false },
  { value: "POLITICS", name: "涉政", disabled: false },
  { value: "ADVERTISING", name: "涉广告", disabled: false },
  { value: "NORMAL", name: "正常", disabled: false }
];

/**
 * 音频类型
 */
const audioType = [{ value: 0, name: "唱歌" }, { value: 1, name: "说说" }];

const orderStatus = [
  { value: "COMPLETE", name: "已完成" },
  { value: "REIMBURSE", name: "已退款" }
];

/**
 * 货币来源类型
 */
const currencyOrigins = [
  { value: "ROOM_SEND_TRUMPET", name: "发送喇叭" },
  { value: "USER_REFUND", name: "用户退款" },
  { value: "ACTIVITY_FRIENDSHIP_CARD_REWARDS", name: "每周特殊关系卡片奖励" },
  { value: "WEEKLY_GAME_TASKS", name: "每周游戏任务奖励" },
  { value: "SEND_DYNAMIC_PAY_GOLD", name: "超限制发动态支付金币" },
  { value: "BUY_EMOJI", name: "购买表情包" },
  { value: "BET_TEEN_PATTI", name: "炸金花" },
  { value: "TEEN_PATTI", name: "炸金花游戏 " },
  { value: "USD_GAME_REFUND_DRAW", name: "sud游戏退款平局" },
  { value: "USD_GAME_REFUND", name: "sud游戏退款" },
  { value: "USD_GAME_WIN", name: "sud游戏获胜奖励" },
  { value: "USER_FRIENDSHIP_CARD_REFUND", name: "用户特殊关系卡退款" },
  { value: "USER_FRIENDSHIP_CARD_GIVE_AWAY", name: "赠送用户特殊关系卡" },
  { value: "LUCKY_GIFT_GOLD_REWARD", name: "幸运礼物金币奖励" },
  { value: "INVITE_USER_REWARDS", name: "邀请用户奖励" },
  { value: "GAME_LUDO_REFUND", name: "LUDO游戏退款" },
  { value: "LOTTERY_NUMBER_BET", name: "幸运彩票押注" },
  { value: "CUMULATIVE_RECHARGE_LOTTERY", name: "累计充值抽奖" },
  { value: "LUCKY_GIFT_REWARD", name: "幸运礼物抽奖" },
  { value: "DAILY_REGISTER", name: "每日签到抽奖" },
  { value: "GAME_KING_AWARD", name: "游戏王奖励" },
  { value: "WEEK_QUEEN", name: "每周王后" },
  { value: "WEEK_KING", name: "每周国王" },
  { value: "DOUBLE_LAYER_BARBECUE", name: "烧烤机游戏" },
  { value: "DOUBLE_LAYER_FRUIT", name: "水果机游戏" },
  { value: "EGG", name: "砸金蛋" },
  { value: "PET_TURNTABLE_LOTTERY", name: "宠物转盘抽奖" },
  { value: "PET_REWARD", name: "宠物奖励" },
  { value: "ACTIVATE_PET", name: "激活宠物" },
  { value: "FEEDING_PETS_HELP_OTHERS", name: "帮助他人喂养" },
  { value: "FEEDING_PETS", name: "喂养宠物" },
  { value: "ROOM_PK", name: "房间PK奖励" },
  { value: "WEEK_CP_GIFT", name: "cp礼物互赠消耗" },
  { value: "PURCHASING_LAYOUT_GIVEAWAY", name: "赠送道具主题-金币" },
  { value: "PURCHASING_THEME_GIVEAWAY", name: "购买主题-赠送" },
  { value: "PURCHASING_THEME", name: "购买主题-金币" },
  { value: "OPEN_VIP", name: "开通VIP" },
  { value: "BUILD_HOUSE", name: "盖房子消费" },
  { value: "DIAMOND_EXCHANGE_GOLD", name: "钻石兑换金币" },
  { value: "FIRST_CHARGE_REWARD", name: "首次充值" },
  { value: "FAMILY_AWARD_RECEIVE", name: "家族每周宝箱奖励领取" },
  { value: "GOLD_CREATE_FAMILY", name: "使用金币创建家族" },
  { value: "USER_FIRST_RECHARGE_REWARD", name: "用户首次充值奖励" },
  { value: "ACCEPT_GIFT", name: "接收礼物" },
  { value: "RECEIVE_RED_PACKET_REFUND", name: "红包退款" },
  { value: "RECEIVE_RED_PACKET", name: "领取红包奖励" },
  { value: "SEND_RED_PACKET", name: "发送红包" },
  { value: "RECEIVE_USER_RED_PACKET_REFUND", name: "红包用户个人退款" },
  { value: "RECEIVE_USER_RED_PACKET", name: "领取用户个人红包奖励" },
  { value: "SEND_USER_RED_PACKET", name: "发送用户个人红包" },
  {
    value: "INVITED_USER_FIRST_RECHARGE_REWARD",
    name: "邀请的用户首次充值奖励"
  },
  { value: "INVITED_NEW_USER_REWARD", name: "邀请用户注册奖励" },
  { value: "FRIENDS_GIVE_GOLD_COINS", name: "朋友赠送金币" },
  { value: "BUY_GOLD", name: "购买金币" },
  { value: "GIFT_PACK", name: "礼包" },
  { value: "GAME_RAKE", name: "游戏抽成" },
  { value: "GAME_WINNING", name: "游戏胜利" },
  { value: "GAME_BACK", name: "游戏退还" },
  { value: "SEVEN_CHECK_IN", name: "签到获取奖励" },
  { value: "ROOM_REWARD_EXTRACT", name: "房间奖励提取" },
  { value: "WEB_BUY", name: "WEB端购买" },
  { value: "WEEK_STAR", name: "周星" },
  { value: "ROOM_REWARD", name: "房间奖励" },
  { value: "CUMULATIVE_RECHARGE_REWARDS", name: "累计充值奖励" },
  { value: "GAME_BURST_CRYSTAL", name: "爆水晶游戏" },
  { value: "SHIPPING_AGENT", name: "货运代理" },
  { value: "CP_REWARD", name: "CP奖励" },
  { value: "DAILY_TASK", name: "每日任务" },
  { value: "LUDO_REFUND", name: "LUDO退款" },
  { value: "LUDO_VICTORY", name: "LUDO胜利" },
  { value: "LUCKY_BOX_GAME", name: "Lucky Box & 幸运抽奖" },
  { value: "CP_BUILD", name: "CP组建" },
  { value: "PURCHASE_STEALTH_BROWSE_PROPS", name: "购买隐身浏览道具" },
  { value: "PURCHASE_ROOM_LOCK", name: "购买房间锁" },
  { value: "GIVE_AWAY_GOLD_LAYOUT", name: "赠送装扮-金币" },
  { value: "PURCHASING_CAR_GIVEAWAY", name: "赠送车辆-金币" },
  { value: "PURCHASING_CHAT_BUBBLE_GIVEAWAY", name: "赠送聊天气泡-金币" },
  { value: "PURCHASING_AVATAR_FRAME_GIVEAWAY", name: "赠送头像框-金币" },
  { value: "PURCHASING_LAYOUT", name: "购买装扮-金币" },
  { value: "PURCHASING_CAR", name: "购买车辆-金币" },
  { value: "PURCHASING_CHAT_BUBBLE", name: "购买聊天气泡-金币" },
  { value: "PURCHASING_AVATAR_FRAME", name: "购买头像框-金币" },
  { value: "PURCHASE_NOBLE_VIP_GIVEAWAY", name: "赠送贵族VIP-金币" },
  { value: "PURCHASE_NOBLE_VIP", name: "购买贵族vip-金币" },
  { value: "PURCHASE_GOLD_SPECIAL_ID_GIVEAWAY", name: "赠送靓号-金币" },
  { value: "PURCHASE_GOLD_SPECIAL_ID", name: "购买靓号-金币" },
  { value: "LUDO_GAME", name: "LUDO游戏" },
  { value: "TURNTABLE_GAME", name: "转盘游戏" },
  { value: "GAME_BARBECUE", name: "烧烤游戏" },
  { value: "GAME_SLOT_MACHINE", name: "水果游戏" },
  { value: "JOIN_ROOM", name: "加入房间" },
  { value: "CUSTOM_ROOM_THEME", name: "自定义主题" },
  { value: "GIVE_GIFT", name: "赠送礼物" },
  { value: "ACTIVITY_REWARD", name: "活动奖励" },
  { value: "APP", name: "后台管理发送-APP" },
  { value: "HKYS_GAME", name: "Hkys第三方游戏" },
  { value: "ROOM_CONTRIBUTION_REWARD", name: "房间贡献活动奖励" },
  { value: "REWARD_COINS", name: "后台发送金币" },
  { value: "DEDUCT_COINS", name: "后台扣除金币" },
  { value: "WAGES", name: "工资" },
  { value: "ACTIVE_AGENT_ANCHOR_COUNT_REWARD", name: "代理的主播数量满足奖励" },
  {
    value: "ACTIVE_AGENT_MONTH_TARGET_REWARD",
    name: "代理名下主播累计月积分奖励"
  },
  { value: "ACTIVE_ANCHOR_MONTH_TARGET_REWARD", name: "主播月积分奖励" },
  { value: "ACTIVE_ANCHOR_DAY_TARGET_REWARD", name: "主播日积分奖励" },
  { value: "GAME_FRUIT_BET", name: "幸运摩天轮下注-自研" },
  { value: "GAME_FRUIT_AWARD", name: "幸运摩天轮奖励-自研" },
  { value: "GAME_KTV_WEEK_RANK_REWARD", name: "KTV游戏每周榜单奖励" },
  { value: "AGENT_ACTIVE_WEEK_REWARD", name: "代理活动奖励-周" },
  { value: "AGENT_ACTIVE_MONTH_REWARD", name: "代理活动奖励-月" },
  { value: "CONSUMPTION_ACTIVITY", name: "消耗活动" },
  { value: "ROOM_FAN_VOTES_ACTIVITY", name: "房间粉丝人气票" },
  { value: "TARGET_EXCHANGE_GOLD", name: "目标兑换金币" },
  { value: "HOST_TERMINATION_FEE", name: "主播申请解约" },
  { value: "HOST_TERMINATION_FEE_RETURN", name: "主播取消申请解约" },
  { value: "PURCHASING_RED_PACKET", name: "购买红包封面" },
  { value: "PURCHASING_RED_PACKET_GIVEAWAY", name: "赠送红包封面" },
  { value: "BLESSINGS_GIFT", name: "CP空间祝福" },
  { value: "SEND_FAMILY_GROUP_RED_PACKET", name: "发送家族群聊红包" },
  { value: "RECEIVE_FAMILY_GROUP_RED_PACKET", name: "领取家族群聊红包" },
  { value: "RECEIVE_FAMILY_GROUP_RED_PACKET_REFUND", name: "家族群聊红包退款" }
];

/**
 * 钻石来源类型
 */
const diamondOrigins = [
  { value: "ACTIVITY_REWARD", name: "活动奖励" },
  { value: "ROOM_SUPPORT_REWARD", name: "房间扶持奖励" },
  { value: "ROOM_HOMEOWNER_SUPPORT_REWARD", name: "房间扶持奖励 - 房主奖励" },
  { value: "CP_REWARD", name: "cp奖励" },
  { value: "GAME_BURST_CRYSTAL", name: "爆水晶游戏" },
  { value: "ROOM_REWARD", name: "房间奖励" },
  { value: "WEEK_STAR", name: "周星" },
  { value: "INVITED_NEW_USER_REWARD", name: "邀请新用户注册奖励" },
  { value: "INVITED_USER_FIRST_RECHARGE_REWARD", name: "邀请用户首次充值奖励" },
  { value: "INVITE_REGISTER", name: "邀请用户注册" },
  { value: "INVITE_FIRST_RECHARGE", name: "邀请的用户首次充值" },
  { value: "INVITED_USER_EACH_RECHARGE", name: "邀请用户每笔充值奖励百分比" },
  { value: "PURCHASE_NOBLE_VIP", name: "购买贵族vip" },
  { value: "PURCHASE_NOBLE_VIP_GIVEAWAY", name: "购买赠送贵族vip-钻石" },
  { value: "PURCHASING_AVATAR_FRAME", name: "购买头像框-钻石" },
  { value: "PURCHASING_DATA_CARD", name: "购买资料卡-钻石" },
  { value: "PURCHASING_CHAT_BUBBLE", name: "购买聊天气泡-钻石" },
  { value: "PURCHASING_CAR", name: "购买车辆-钻石" },
  { value: "PURCHASING_LAYOUT", name: "购买装扮-钻石" },
  { value: "PURCHASING_AVATAR_FRAME_GIVEAWAY", name: "赠送头像框-钻石" },
  { value: "PURCHASING_DATA_CARD_GIVEAWAY", name: "购买资料卡-钻石" },
  { value: "PURCHASING_CHAT_BUBBLE_GIVEAWAY", name: "赠送聊天气泡-钻石" },
  { value: "PURCHASING_THEME", name: "购买主题-钻石" },
  { value: "PURCHASING_THEME_GIVEAWAY", name: "赠送主题-钻石" },
  { value: "PURCHASING_CAR_GIVEAWAY", name: "赠送车辆" },
  { value: "PURCHASING_LAYOUT_GIVEAWAY", name: "赠送装扮" },
  { value: "GIVE_GIFT", name: "赠送礼物" },
  { value: "ACCEPT_GIFT", name: "接收礼物" },
  { value: "COMPENSATE", name: "补给" },
  { value: "REMOVE", name: "违规扣除" },
  { value: "WEEK_USER_CONSUME", name: "国王王后king" },
  { value: "DIAMOND_EXCHANGE_GOLD", name: "砖石兑换金币" },
  { value: "LIVE_BROADCAST_INTEGRAL", name: "直播积分" },
  { value: "DAILY_TASK", name: "每日任务" },
  { value: "CHECK_IN_DIAMOND", name: "钻石打卡" },
  { value: "CUMULATIVE_RECHARGE_REWARDS", name: "累计充值奖励" },
  { value: "OFFICIAL_GIFT", name: "官方赠送" },
  { value: "EXPIRED", name: "过期" },
  { value: "INVITE_USER_RED_PACKET_LOTTERY", name: "邀请用户红包抽奖" },
  { value: "REWARD_DIAMOND", name: "后台奖励钻石" },
  { value: "DEDUCT_DIAMOND", name: "后台扣除钻石" }
];

/**
 * 道具来源类型
 */
const propsOrigins = [
  { value: "CP_REWARD", name: "CP奖励" },
  { value: "CUMULATIVE_RECHARGE_REWARDS", name: "累计充值奖励" },
  { value: "CUMULATIVE_RECHARGE", name: "累计充值" },
  { value: "WEEK_STAR", name: "周星" },
  { value: "ROOM_REWARD", name: "房间奖励" },
  { value: "GAME_BURST_CRYSTAL", name: "爆水晶游戏" },
  { value: "FIRST_CHARGE_REWARD", name: "首次充值" },
  { value: "FAMILY_AWARD_RECEIVE", name: "家族每周宝箱奖励领取" },
  { value: "WEEK_CP_GIFT", name: "每周CP相互赠送礼物榜单 " },
  { value: "ROOM_PK", name: "房间PK奖励" },
  { value: "WEEK_USER_CONSUME", name: "King国王王后" },
  { value: "PET_REWARD", name: "宠物奖励" },
  { value: "PET_TURNTABLE_LOTTERY", name: "宠物转盘抽奖" },
  { value: "EGG", name: "砸金蛋" },
  { value: "POKER", name: "扑克游戏" },
  { value: "GAME_KING_AWARD", name: "游戏王奖励" },
  { value: "WEEK_KING", name: "每周国王" },
  { value: "WEEK_QUEEN", name: "每周王后" },
  { value: "DAILY_REGISTER", name: "每日签到" },
  { value: "LUCKY_GIFT_REWARD", name: "幸运礼物抽奖" },
  { value: "INVITE_USER_REWARDS", name: "邀请用户奖励" },
  { value: "WEEKLY_GAME_TASKS", name: "每周游戏任务奖励" },
  { value: "ACTIVITY_FRIENDSHIP_CARD_REWARDS", name: "每周最佳特殊关系奖励" },
  { value: "ACTIVITY_REWARD", name: "活动奖励" },
  { value: "SVIP_REWARD", name: "SVIP奖励" },
  { value: "USE_VOUCHER_SPECIAL_ID", name: "使用兑换券获得靓号" },
  { value: "PURCHASING_GOLD_PROPS", name: "金币购买-道具-自己用" },
  { value: "PURCHASING_GOLD_PROPS_GIVEAWAY", name: "金币购买-道具-赠送" },
  { value: "PURCHASING_DIAMOND_PROPS", name: "钻石购买-道具-自己用" },
  { value: "PURCHASING_DIAMOND_PROPS_GIVEAWAY", name: "钻石购买-道具-赠送" },
  { value: "PURCHASE_GOLDS_NOBLE_VIP", name: "金币购买VIP-购买" },
  { value: "PURCHASE_DIAMOND_NOBLE_VIP", name: "钻石购买VIP" },
  { value: "PURCHASE_GOLD_NOBLE_VIP_GIVEAWAY", name: "金币购买VIP-赠送" },
  { value: "OFFICIAL_GIFT", name: "官方-赠送" },
  { value: "PURCHASE_DIAMOND_NOBLE_VIP_GIVEAWAY", name: "钻石购买VIP-赠送" },
  { value: "PURCHASING_PACK", name: "购买礼包" },
  { value: "SIGN_IN", name: "签到获得" },
  { value: "USE_PROPS_VOUCHER", name: "使用道具兑换券" },
  { value: "CHECK_IN_REWARDS", name: "签到赠送" },
  { value: "PURCHASING_THEME", name: "购买主题-钻石" },
  { value: "PURCHASING_GOLD_THEME", name: "购买主题-金币" },
  { value: "PURCHASING_FREE_THEME", name: "购买主题-免费" },
  { value: "SHIPPING_AGENT", name: "货运代理" },
  { value: "REGISTER_REWARDS", name: "注册奖励" },
  { value: "BUY_OR_GIVE", name: "自己购买或朋友赠送" },
  { value: "ACTIVE_AGENT_ANCHOR_COUNT_REWARD", name: "代理的主播数量满足奖励" },
  {
    value: "ACTIVE_AGENT_MONTH_TARGET_REWARD",
    name: "代理名下主播累计月积分奖励"
  },
  { value: "ACTIVE_ANCHOR_MONTH_TARGET_REWARD", name: "主播月积分奖励" },
  { value: "ACTIVE_ANCHOR_DAY_TARGET_REWARD", name: "主播日积分奖励" }
];

// const enumConfigGroupNames = [
//   {
//     value: 'VIDEO_MATCH', name: '视频匹配', children: [
//       { value: 'VIDEO_MATCH_ALERT', name: '视频匹配弹窗' },
//       { value: 'VIDEO_MATCH_PR', name: '视频匹配概率%' },
//       { value: 'VIDEO_MATCH_CANDY', name: '视频匹配糖果' },
//       { value: 'VIDEO_MATCH_INTEGRAL', name: '视频匹配积分' }
//     ]
//   },
//   { value: 'AD_SWITCH', name: '广告开关' },
//   { value: 'OTHER', name: '常规参数' },
//   { value: 'ACCOUNT_PROCESS', name: '账号处理' },
//   {
//     value: 'TASK', name: '任务管理', children: [
//       { value: 'CLOCK_IN', name: '打卡签到' }
//     ]
//   }
// ]

const enumConfigGroupNames = [
  {
    value: "VIDEO_MATCH",
    name: "视频匹配",
    children: [
      { value: "PR", name: "概率%" },
      { value: "CANDY_OR_INTEGRAL", name: "糖果/积分" }
    ]
  },
  { value: "AD_CONTROL", name: "广告控制" },
  {
    value: "OTHER",
    name: "常规参数",
    children: [
      { value: "SECRET", name: "密钥" },
      { value: "SWITCH_CONTROL", name: "开关控制" },
      { value: "CANDY_OR_INTEGRAL", name: "糖果/积分" },
      { value: "DEFAULT", name: "默认参数" }
    ]
  },
  {
    value: "TASK",
    name: "任务管理",
    children: [
      { value: "CLOCK_IN", name: "打卡签到" },
      { value: "DAILY_TASK", name: "每日任务" }
    ]
  },
  {
    value: "VOICE",
    name: "语音厅",
    children: [
      { value: "SWITCH_COUNTR", name: "开关控制" },
      { value: "VOICE_NUMERICAL_CONTROL", name: "数值控制" }
    ]
  }
];

// 系统参数管理-操作
const enumConfigOperates = [
  { value: false, name: "可操作" },
  { value: true, name: "不可操作" }
];

// 系统参数管理-数据类型
const enumConfigDataTypes = [
  { value: "int", name: "int" },
  { value: "string", name: "string" },
  { value: "range", name: "range" },
  { value: "bool", name: "bool" },
  { value: "double", name: "double" },
  { value: "ratio", name: "ratio" }
];

// 数据类型限制表达式
const enumConfigDataTypesExpression = {
  string: {
    rex: "",
    msg: "string类型异常"
  },
  int: {
    rex: /^\d{1,5}$/,
    msg: "int范围0~99999"
  },
  range: {
    rex: /^\d{1,5}~\d{1,5}$/,
    msg: "range使用 ~ 分割两侧数值范围0~99999"
  },
  bool: {
    rex: /^(true)|(false)$/,
    msg: "bool可选值 true/false"
  },
  double: {
    rex: /^\d{1,5}(\.\d{0,2})?$/,
    msg: "double范围0~99999小数最多两位"
  },
  ratio: {
    rex: /^([0-9]|10)$/,
    msg: "ratio范围0~10"
  }
};

// 内购产品分组名称
const productConfigGroupNames = [
  { value: "CANDY", name: "GOLD" },
  { value: "GIFT_PACK", name: "GIFT_PACK" }
  // { value: 'VIP', name: 'VIP' },
];

// 内购产品状态
const productConfigShowcase = [
  { value: true, name: "上架" },
  { value: false, name: "下架" }
];

// 内购产品类型
const productTypes = [
  { value: "CONSUME", name: "消耗" },
  // { value: 'NOT_CONSUME', name: '非消耗' },
  { value: "AUTO_RENEWAL_SUBSCRIPTION", name: "自动续费订阅" }
  // { value: 'NOT_AUTO_RENEWAL_SUBSCRIPTION', name: '非自动续费订阅' }
];

// 平台
const platformOrigins = [
  {
    value: "iOS",
    name: "iOS",
    channels: [{ name: "AppStore", value: "AppStore" }]
  },
  {
    value: "Android",
    name: "Android",
    channels: [
      { name: "Google", value: "Google" },
      { name: "Huawei", value: "Huawei" }
    ]
  }
];

// 平台V2
const platformOriginsV2 = [
  { name: "AppStore", value: "AppStore" },
  { name: "Google", value: "Google" },
  { name: "Huawei", value: "Huawei" }
];

// 商品标签
const productTags = [
  { value: "MOST_POPULAR", name: "most popular" },
  { value: "BEST_VALUE", name: "best value" }
  // { value: 'VIP', name: 'vip' }
];

// 商品位置
const productPositions = [
  { value: 0, name: "无" },
  { value: 1, name: "金币商城" }
];

// 违规审批类型
const approvalTypes = [
  { value: "NICKNAME", name: "用户昵称" },
  { value: "AVATAR", name: "用户头像" },
  { value: "PHOTO_WALL", name: "照片墙" },
  { value: "LIVE", name: "live图" },
  { value: "SHORT_VIDEO", name: "短视频" },
  { value: "ROOM_NICKNAME", name: "房间昵称" },
  { value: "ROOM_AVATAR", name: "房间头像" },
  { value: "ROOM_NOTICE", name: "房间通知公告" },
  { value: "PROFILE_DESC", name: "个人资料签名" }
];

// 字典类型
const dictionaryTypes = [
  { value: "NONE", name: "系统" },
  { value: "ROOM_TAG", name: "房间标签" },
  { value: "ROOM_BLACK_TIME", name: "房间黑名单过期时长" }
];

// 礼物配置类型
const giftConfigTabs = [
  { value: "ORDINARY", name: "普通礼物" },
  { value: "NATIONAL_FLAG", name: "国旗礼物" },
  { value: "CP", name: "CP礼物" },
  { value: "FAMILY", name: "家族礼物" },
  { value: "EXCLUSIVE", name: "专属礼物" },
  { value: "ARISTOCRACY", name: "贵族礼物" },
  { value: "LUCKY_GIFT", name: "幸运礼物" }
];

// 贵族VIP类型
const nobleVipTabs = [
  { value: "VISCOUNT", name: "子爵" },
  { value: "EARL", name: "伯爵" },
  { value: "MARQUIS", name: "侯爵" },
  { value: "DUKE", name: "公爵" },
  { value: "KING", name: "国王" },
  { value: "EMPEROR", name: "皇帝" }
];

// 道具类型
const propsTypes = [
  { value: "AVATAR_FRAME", name: "头像框" },
  { value: "RIDE", name: "座驾" },
  { value: "NOBLE_VIP", name: "贵族" },
  { value: "THEME", name: "主题" },
  { value: "LAYOUT", name: "装扮" },
  { value: "CHAT_BUBBLE", name: "聊天气泡" },
  { value: "FLOAT_PICTURE", name: "飘窗" },
  { value: "FRAGMENTS", name: "碎片" },
  { value: "DATA_CARD", name: "资料卡" },
  { value: "SPECIAL_ID", name: "靓号" },
  { value: "CUSTOMIZE", name: "自定义(只读)" }
];

// 道具商品类型
const propsCommodityTypes = [
  { value: "AVATAR_FRAME", name: "头像框" },
  { value: "RIDE", name: "座驾" },
  { value: "NOBLE_VIP", name: "贵族" },
  { value: "THEME", name: "主题" },
  { value: "LAYOUT", name: "装扮" },
  { value: "FLOAT_PICTURE", name: "飘窗" },
  { value: "CHAT_BUBBLE", name: "聊天气泡" },
  { value: "DATA_CARD", name: "资料卡" }
];

// 活动道具类型
const activityPropsTypes = [
  { value: "AVATAR_FRAME", name: "头像框" },
  { value: "RIDE", name: "座驾" },
  { value: "NOBLE_VIP", name: "贵族" },
  { value: "GIFT", name: "礼物" },
  { value: "GOLD", name: "金币" }
];

// 道具场景类型
const scenesTypes = [
  { value: "STORE", name: "商店" },
  { value: "NOBLE_VIP", name: "贵族VIP" }
];

// 道具付费类型
const propsCurrencyType = [
  { value: "GOLD", name: "金币" },
  { value: "DIAMOND", name: "钻石" },
  { value: "FREE", name: "免费" }
];

// 有效天数
const propsValidDays = [
  { value: 1, name: "1" },
  { value: 7, name: "7" },
  { value: 15, name: "15" },
  { value: 30, name: "30" }
];

// 靓号类型
const specialIdTypes = [
  { value: "ABCDEF", name: "ABCDEF" },
  { value: "ABBBBB", name: "ABBBBB" },
  { value: "ABBBBA", name: "ABBBBA" },
  { value: "ABBBBC", name: "ABBBBC" },
  { value: "AAABBB", name: "AAABBB" },
  { value: "ABBABB", name: "ABBABB" },
  { value: "AABAAB", name: "AABAAB" },
  { value: "ABCABC", name: "ABCABC" },
  { value: "ABABAB", name: "ABABAB" },
  { value: "AABBCC", name: "AABBCC" },
  { value: "ABAABA", name: "ABAABA" }
];

// 券类型
const propsVoucherTypes = [
  { value: "SPECIAL_ID", name: "靓号" },
  { value: "PROPS", name: "道具" }
];

/**
 * 豆子来源类型
 */
const beanOrigins = [
  { value: "REWARD_COINS", name: "发送" },
  { value: "DEDUCT_COINS", name: "扣除" }
];

/**
 * 解锁宠物条件
 */
const petUnlockConditions = [
  { value: "WEALTH_LEVEL", name: "财富等级" },
  { value: "CHARM_LEVEL", name: "魅力等级" },
  { value: "GOLD", name: "金币" },
  { value: "DIAMOND", name: "钻石" },
  { value: "BEAN", name: "豆子" }
];

/**
 * 比对条件
 */
const unitConditions = [
  { value: "GT", name: ">" },
  { value: "GE", name: ">=" },
  { value: "EQ", name: "=" },
  { value: "LT", name: "<" },
  { value: "LE", name: "<=" }
];

/**
 * 支付渠道分组.
 */
const payChannelGroups = [
  { value: "UPI", name: "UPI" },
  { value: "ELECTRONIC_WALLET", name: "电子钱包" },
  { value: "BANK_CARD", name: "银行卡" },
  { value: "BANK_CARD_TRANSFER", name: "银行卡转账" },
  { value: "OFFLINE_NONBANK_OUTLETS", name: "线下非银网点" },
  { value: "CARRIER_BILLING", name: "运营商计费" },
  { value: "ONLINE_BANKING", name: "网上银行" },
  { value: "QUICK_PAYMENT", name: "快捷支付" },
  { value: "DELAYED_PAYMENT", name: "延迟支付" },
  { value: "E_WALLET", name: "E-wallet" },
  { value: "UNSECURED_PAYMENT", name: "无密支付" },
  { value: "CONVENIENCE_STORE", name: "便利店" },
  { value: "POINT_CARD_PAYMENT", name: "点卡支付" },
  { value: "SUPER_BUSINESS_BARCODE", name: "超商条码" },
  { value: "MOBILE_OR_PHONE_PAYMENT", name: "行动/电话小额付" },
  { value: "PHYSICAL_ATM", name: "实体ATM" },
  { value: "ATM_ONLINE", name: "ATM线上" },
  { value: "ATM_OFFLINE", name: "ATM线下" },
  { value: "WEB_ATM", name: "Web ATM" },
  { value: "BANK_VIRTUAL_ACCOUNT", name: "银行虚拟账号" },
  { value: "BANK_COUNTERS", name: "银行柜台" },
  { value: "ININAL", name: "预付费卡" },
  { value: "PAPARA", name: "Papara" }
];

/**
 * 支付渠道名称.
 */
const payChannelGroupNames = covertPayChannelGroupsMap();

function covertPayChannelGroupsMap() {
  const data = {};
  payChannelGroups.forEach(item => {
    data[item.value] = item;
  });
  return data;
}

/**
 * web商品类型.
 */
const productTypeConfs = [
  { value: "GOLD", name: "金币" },
  { value: "FREIGHT_GOLD", name: "货运金币" }
];

/**
 * 其他支付方式.
 */
const otherPaymentTypes = [
  { value: "PAYPAL", name: "Paypal" },
  { value: "PAYONEER", name: "Payoneer" },
  { value: "WESTERN_UNION", name: "西联汇款" },
  { value: "TRANSFER", name: "转账" }
];

/**
 * 付款类型描述.
 */
const rechargeTypeDesMap = {
  APPLE: "Apple",
  GOOGLE: "Google",
  PAYER_MAX: "PayerMax",
  STRIPE: "Stripe",
  SHIPPING_AGENT: "货运代理",
  SALARY_EXCHANGE: "工资兑换",
  HUAWEI: "HuaWei",
  PAY_PA: "Paypal",
  AIRWALLEX: "Airwallex"
};

/**
 * 系统操作房间用户.
 */
const sysOperatingRoomUserEvent = [
  { value: "SYS_PULL_BLACK_ROOM_USER", name: "拉黑用户" },
  { value: "SYS_REMOVE_ROOM_PULL_BLACK_USER", name: "将用户移出房间黑名单" },
  { value: "SYS_REMOVE_ROOM_USER", name: "踢出房间" }
];

/**
 * 请求日志.
 */
const apiRequestLogs = [
  { label: "身份变更V1", value: "ROOM_ROLES_CHANGE_V1" },
  { label: "身份变更V2", value: "ROOM_ROLES_CHANGE_V2" },
  { label: "身份变更V3", value: "ROOM_ROLES_CHANGE_V3" },
  { label: "身份变更-发出邀请", value: "ROOM_ROLES_CHANGE_INVITE" },
  { label: "身份变更-主动加入V1", value: "INITIATIVE_JOIN" },
  { label: "身份变更-主动退出V1", value: "INITIATIVE_EXIT" },
  { label: "房间资料变更", value: "ROOM_PROFILE_UPDATE" },
  { label: "房间设置变更", value: "ROOM_SETTING_UPDATE" },
  { label: "加入黑名单", value: "JOIN_BLACKLIST" },
  { label: "检测T麦克风", value: "CHECK_KILL_MICROPHONE" },
  { label: "T人日志", value: "KILL_MICROPHONE_USER" }
];

/**
 * 道具类型.
 */
const propsSupperTypes = [
  { label: "道具", value: "PROPS" },
  { label: "自定义", value: "CUSTOMIZE" },
  { label: "靓号", value: "SPECIAL_ID" },
  { label: "金币", value: "GOLD" },
  { label: "钻石", value: "DIAMOND" },
  { label: "游戏券", value: "GAME_COUPON" },
  { label: "礼物", value: "GIFT" }
];

/**
 * 静态封面图.
 */
const propsStaticSourceCover = {
  CUSTOMIZE:
    "http://img.sugartimeapp.com/back/manager-4013fd35-930a-4d4a-98dd-8f71d0824de7.png",
  SPECIAL_ID: "http://img.sugartimeapp.com/back/special_id.png",
  GOLD: "http://img.sugartimeapp.com/web/gold_icon_doller.png",
  DIAMOND: "http://img.sugartimeapp.com/web/timchat_diamond.png",
  GAME_COUPON: "http://img.sugartimeapp.com/back/game_coupon.png"
};

/**
 * 区域辅助数据类型.
 */
const regionAssistTypes = [
  {
    value: "ROOM_CONTRIBUTION_ACTIVITY_RATIO",
    name: "房间贡献活动奖励比例(%)"
  },
  { value: "GIFT_TO_OWN_GOLD_RATIO", name: "接收自己礼物获得金币比例(%)" },
  { value: "GIFT_TO_OTHER_GOLD_RATIO", name: "接收他人礼物获得金币比例(%)" },
  // { value: 'GIFT_TO_OWN_GOLD_RATIO_AGENCY', name: '接收自己礼物获得金币比例(%)-代理' },
  // { value: 'GIFT_TO_OTHER_GOLD_RATIO_AGENCY', name: '接收他人礼物获得金币比例(%)-代理' },
  { value: "WITHDRAW_PROPORTION_TIPS", name: "银行钱包兑换金币比例" },
  {
    value: "WITHDRAW_PROPORTION_DIAMOND_TIPS",
    name: "银行钱包工资钻石兑换金币比例"
  },
  {
    value: "WITHDRAW_PROPORTION_DIAMOND_USD_TIPS",
    name: "银行钱包工资钻石兑换美金比例"
  },
  { value: "RESIDUE_TARGET_EXCHANGE_PROPORTION", name: "剩余积分兑换$比例" },
  { value: "MIN_EXCHANGE_DIAMOND", name: "钻石起兑最小数量" },
  { value: "DIAMOND_EXCHANGE_GOLD", name: "钻石兑换金币比例(%)" },
  { value: "HOST_TERMINATION_FEE", name: "主播解约费(金币)" },
  { value: "WITHDRAWAL_COMMISSION_RATIO", name: "提现手续费比例(%)" },
  { value: "GIFT_TARGET_RATIO", name: "接收礼物获得目标比例(%)" }
];

// 区域业务关系分组类型.
const regionRelationGroup = [
  { value: "OPEN_PAY_COUNTRY", name: "开通支付国家" }
];

/**
 * 银行卡类型.
 */
const bankCardTypes = [
  {
    value: "PAY_PAL",
    name: "PayPal",
    bgColor: "bg-linear-green",
    iconUrl: "/web/card-payoal-icon.png"
  },
  {
    value: "PAYONEER",
    name: "Payoneer",
    bgColor: "bg-linear-blue",
    iconUrl: "/web/card-payonner-icon.png"
  },
  {
    value: "BANK",
    name: "Bank",
    bgColor: "bg-linear-green",
    iconUrl: "/web/card-bank-icon.png"
  },
  {
    value: "GCASH",
    name: "GCash",
    bgColor: "bg-linear-blue",
    iconUrl: "/web/card-gcash-icon.png"
  },
  {
    value: "EASYPAISA",
    name: "Easypaisa",
    bgColor: "bg-linear-green",
    iconUrl: "/web/card-easypaisa-icon.png"
  },
  {
    value: "JAZZCASH",
    name: "JazzCash",
    bgColor: "bg-linear-blue",
    iconUrl: "/web/card-jazz-cash-icon.png"
  },
  {
    value: "USDT",
    name: "USDT",
    bgColor: "bg-linear-green",
    iconUrl: "/web/card-usdt-icon.png"
  }
];

export {
  bankCardTypes,
  regionRelationGroup,
  regionAssistTypes,
  propsStaticSourceCover,
  propsSupperTypes,
  apiRequestLogs,
  sysOperatingRoomUserEvent,
  rechargeTypeDesMap,
  otherPaymentTypes,
  productTypeConfs,
  payChannelGroupNames,
  payChannelGroups,
  unitConditions,
  petUnlockConditions,
  beanOrigins,
  currencyDeductReasons,
  currencyRewardReasons,
  emojiType,
  emojiSourceType,
  propsVoucherTypes,
  specialIdTypes,
  activityPropsTypes,
  propsValidDays,
  propsCurrencyType,
  propsTypes,
  scenesTypes,
  candyPurchasingTypes,
  language,
  approvalType,
  audioType,
  orderStatus,
  currencyOrigins,
  diamondOrigins,
  enumConfigGroupNames,
  enumConfigOperates,
  enumConfigDataTypes,
  enumConfigDataTypesExpression,
  productConfigGroupNames,
  productConfigShowcase,
  productTypes,
  platformOrigins,
  productTags,
  productPositions,
  msgCopywritingTypes,
  approvalTypes,
  dictionaryTypes,
  giftConfigTabs,
  nobleVipTabs,
  inviteRewardTypes,
  approvalStatusArray,
  announcementArray,
  sendStatusArray,
  badgeInvalidTypes,
  propActivityTypes,
  propActivityTypesHelp,
  weekTypes,
  activityRedisTypes,
  roomEvents,
  freightBalanceOrigin,
  lotteryGameTypes,
  appStartPagePlanTypes,
  roomRoles,
  propsCommodityTypes,
  specifyLanguage,
  propsOrigins,
  platformOriginsV2
};
