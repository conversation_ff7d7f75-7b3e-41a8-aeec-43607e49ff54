// 注册来源
const registerOrigins = [
  { value: 'MOBILE', name: '手机号码' },
  { value: 'FACEBOOK', name: 'Facebook' },
  { value: 'GOOGLE', name: 'Google' },
  { value: 'APPLE', name: '<PERSON>' },
  { value: 'SNAPCHAT', name: '<PERSON>napcha<PERSON>' }
]

// 来源平台
const originPlatforms = [
  { value: 'iOS', name: 'iOS' },
  { value: 'Android', name: 'Android' },
  { value: 'H5', name: 'H5' }
]

// app平台
const appPlatforms = [
  { value: 'iOS', name: 'iOS' },
  { value: 'Android', name: 'Android' }
]

// 设备平台
const deviceTypes = [
  // { value: 'UMENG', name: '友盟' },
  { value: 'GOOGLE', name: '谷歌' }
]

// 支付方式
const payPlatforms = [
  { value: 'APPLE', name: 'Apple' },
  { value: 'GOOGLE', name: 'Google' },
  { value: 'STRIPE', name: '<PERSON><PERSON>' },
  { value: 'PAY_MAX', name: 'PayerMax' }
]

// 系统平台树
const sysPlatforms = [
  { value: 'iOS', label: 'iOS' },
  { value: 'Android', label: 'Android' }
]

// 系统平台
const sysOriginPlatforms = [
  // { value: 'TIM_CHAT', label: 'Halla', children: sysPlatforms },
  // { value: 'TIM_CHAT_LITE', label: 'Halla-new', children: sysPlatforms, parentValue: 'TIM_CHAT' },
  // { value: 'ASWAT', label: 'Aswat', permission: 'query:aswat', children: sysPlatforms },
  // { value: 'ASWAT_LITE', label: 'Aswat Lite', permission: 'query:aswat:lite', children: sysPlatforms, parentValue: 'ASWAT' },
  // { value: 'YAHLLA', label: 'Yahlla', permission: 'query:yahlla', children: sysPlatforms },
  // { value: 'TWO_FUN', label: '2Fun', permission: 'query:twoFun', children: sysPlatforms },
  //  { value: 'YOLO', label: 'Yolo', permission: 'query:yolo', children: sysPlatforms },
  // { value: 'CACOO', label: 'Cacoo', children: sysPlatforms },
  // { value: 'RICOM', label: 'Ricom', children: sysPlatforms },
  // { value: 'RICOM_LITE', label: 'Ricom Lite', children: sysPlatforms },
  // { value: 'SUGARTIME', label: 'Sume', children: sysPlatforms },
  // { value: 'SUGARTIME_LITE', label: 'WiWi', children: sysPlatforms },
  // { value: 'XIXI', label: 'XiXi', children: sysPlatforms },
  // { value: 'TARAB', label: 'Tarab', permission: 'query:tarab', children: sysPlatforms }
  // { value: 'MARCIE', label: 'Halar', permission: 'query:halar', children: sysPlatforms },
  { value: 'TAWAKS', label: 'Tawaks', permission: 'query:tawaks', children: sysPlatforms },
  { value: 'MARCIE', label: 'MARCIE', permission: 'query:system', children: sysPlatforms },
  { value: 'HOOKA', label: 'Hooka', permission: 'query:hooka', children: sysPlatforms },
  { value: 'WASLA', label: 'Wasla', permission: 'query:wasla', children: sysPlatforms }
]

// 礼物类型
const giftType = [
  { value: 'GIFT', label: '礼物' },
  { value: 'PROPS', label: '道具' },
  { value: 'GOLD', label: '金币' }
]

const giftInfo = [
  { value: 'GIFT', label: '礼物' },
  { value: 'PROPS', label: '道具' },
  { value: 'NOBLE_VIP', label: '贵族VIP' }
]

/**
 * 道具赠送类型
 */
const propsGiveTypes = [
  { value: 'GOLD', name: '金币' },
  { value: 'DIAMOND', name: '钻石' },
  { value: 'BADGE', name: '徽章' },
  { value: 'PROPS', name: '道具' }
]

/**
 * 徽章类型
 */
const badgeTypes = [
  { value: 'ACHIEVEMENT', name: '用户-成就徽章' },
  { value: 'ADMINISTRATOR', name: '用户-管理员' },
  { value: 'ACTIVITY', name: '用户-活动徽章' },
  { value: 'ROOM_ACHIEVEMENT', name: '房间-成就' },
  { value: 'FAMILY', name: '家族-徽章' }
]

export {
  badgeTypes,
  propsGiveTypes,
  registerOrigins,
  originPlatforms,
  appPlatforms,
  sysOriginPlatforms,
  giftType,
  giftInfo,
  payPlatforms,
  deviceTypes
}
