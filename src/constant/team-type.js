/**
 * 团队相关类型定义.
 */

/**
 * 团队状态.
 */
const teamStatus = [
  { value: 'AVAILABLE', name: '正常' },
  { value: 'CLOSE', name: '关闭' }
]

/**
   * 联系方式类型.
   */
const contactTypes = [
  { value: 'FACEBOOK', name: 'Facebook' },
  { value: 'TWITTER', name: 'Twitter' },
  { value: 'EMAIL', name: 'Email' },
  { value: 'MOBLIE', name: 'Mobile' },
  { value: 'OTHER', name: 'Other' }
]

/**
 * 联系方式类型-map
 */
const contactTypeToMap = () => {
  const map = {}
  contactTypes.forEach(item => {
    map[item.value] = item
  })
  return map
}

// 银行卡类型.
const bankCardType = [
  { value: 'PAY_PAL', name: 'PayPal' },
  { value: 'PAYONEER', name: 'Payoneer' },
  { value: 'BANK', name: 'Bank' }
]

// 团队成员角色.
const teamMemberRoles = [
  { value: 'OWN', name: '代理' },
  { value: 'ADMIN', name: '管理员' },
  { value: 'MEMBER', name: '成员' }
]

const teamMemberRoleMap = () => {
  const map = {}
  teamMemberRoles.forEach(item => {
    map[item.value] = item
  })
  return map
}

/**
 * 账单状态.
 */
const teamBillStatus = [
  { value: 'UNPAID', name: '未出账', tag: 'info', help: '当前进行中的' },
  { value: 'PAY_OUT', name: '已出账', tag: '', help: '本期账单已结束， 等待结算' },
  { value: 'SETTLED', name: '已结算', tag: 'success', help: '账单进行了核实,并支付成功的' },
  { value: 'HANG_UP', name: '挂起', tag: 'warning', help: '存在争议的/银行卡信息不正确等待核实' }
]

const teamBillStatusMap = () => {
  const map = {}
  teamBillStatus.forEach(item => {
    map[item.value] = item
  })
  return map
}

/**
 * @deprecated
 * 结算类型.
 */
const teamBillSettleTypes = [
  { value: 'MONEY', name: '美元' },
  { value: 'GOLD', name: '金币' },
  { value: 'MONEY_GOLD', name: '美元+金币' }
]

/**
 * @deprecated
 */
const teamBillSettleTypeMap = () => {
  const map = {}
  teamBillSettleTypes.forEach(item => {
    map[item.value] = item
  })
  return map
}

// 团队成员申请审核状态.
const teamApplicationProcessStatusList = [
  { value: 'WAIT', name: '待处理' },
  { value: 'AGREE', name: '同意' },
  { value: 'REJECT', name: '拒绝' }
]

// 团队成员申请审核状态.
const teamReasons = [
  { value: 'JOIN', name: '加入' },
  { value: 'QUIT', name: '退出' }
]

// 团队成员申请审核日志.
const teamApprovalReasons = [
  { value: 'MEMBER_JOIN', name: '成员加入' },
  { value: 'MEMBER_QUIT', name: '成员主动退出' },
  { value: 'MEMBER_OWN_REMOVE', name: '代理主动删除成员' },
  { value: 'MEMBER_ADMIN_REMOVE', name: '管理员主动删除成员' },
  { value: 'MEMBER_APPLY_TIME_OUT', name: '长时间未处理申请消息，自动退出' },
  { value: 'REMOVE_MEMBER_NOT_ACTIVE', name: '移除不活跃成员' },
  { value: 'SYSTEM_MEMBER_REMOVE', name: '系统检测自动移除' },
  { value: 'SWITCH_TEAM_OWN', name: '更换团长' },
  { value: 'SWITCH_TEAM_REMOVE', name: '切换团队移除' }
]

// BD操作日志.
const bdApprovalReasons = [
  { value: 'SYSTEM_DELETE_BD', name: '系统删除bd' },
  { value: 'SYSTEM_ADD_BD', name: '系统添加bd' },
  { value: 'SYSTEM_DELETE_BD_MEMBER', name: '系统删除bd' },
  { value: 'SYSTEM_DELETE_BD_LEAD', name: '系统删除bd lead' },
  { value: 'SYSTEM_ADD_BD_LEAD', name: '系统添加bd lead' },
  { value: 'SYSTEM_DELETE_BD_AND_DELETE_MEMBER', name: '系统删除bd，同时删除bd名下成员' },
  { value: 'SYSTEM_BD_LEAD_ADD_BD', name: '系统使用bd lead添加BD' },
  { value: 'ACCEPT_BD_INVITE', name: '接受成为BD邀请' }
]

export {
  teamBillSettleTypeMap,
  teamBillSettleTypes,
  teamBillStatusMap,
  teamBillStatus,
  teamMemberRoleMap,
  teamMemberRoles,
  contactTypeToMap,
  contactTypes,
  teamStatus,
  bankCardType,
  teamApplicationProcessStatusList,
  teamReasons,
  teamApprovalReasons,
  bdApprovalReasons
}
