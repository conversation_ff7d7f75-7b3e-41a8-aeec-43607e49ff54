<template>
  <div class="navbar">
    <hamburger :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />

    <breadcrumb class="breadcrumb-container" />

    <div class="right-menu flex-r">
      <!-- <div class="sys-origin right-menu-item">
        <el-dropdown>
          <span type="info" class="cursor-pointer">
            {{ sysOriginActive }}<i class="el-icon-arrow-down el-icon--right" />
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in sysOriginPlatforms"
              :key="item.value"
              @click.native="clickSysOrigin(item)"
            >{{ item.label }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div> -->
      <template v-if="device!=='mobile'">
        <search id="header-search" class="right-menu-item" />

        <screenfull id="screenfull" class="right-menu-item hover-effect" />

        <el-tooltip content="Global Size" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip>
      </template>

      <el-dropdown class="avatar-container" trigger="click">
        <div class="avatar-wrapper">
          <div class="user-avatar flex-c" :style="userAvatarColor">{{ firstNameStr }}</div>
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown" class="user-dropdown">
          <el-dropdown-item @click.native="clickSwitchTheme">切换模式</el-dropdown-item>
          <el-dropdown-item @click.native="resetPwd">修改密码</el-dropdown-item>
          <el-dropdown-item @click.native="logout">退出登录</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>

    <rest-password v-if="resetPasswordVisible" @success="resetPasswordVisible=false" @close="resetPasswordVisible=false" />

    <el-dialog
      title="选择主题"
      :visible.sync="themeSwitchVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      :before-close="()=>themeSwitchVisible=false"
      :modal-append-to-body="true"
      :append-to-body="true"
    >
      <div class="theme-list ">
        <el-radio-group v-model="themeVal" @change="themeChange">
          <el-radio label="normal">正常模式</el-radio>
          <el-radio label="dark">暗黑模式</el-radio>
          <el-radio label="auto">自动切换</el-radio>
        </el-radio-group>
        <div v-if="themeVal === 'auto'" style="margin-top: 20px;">
          07:00~18:00 正常模式、19:00~06:00 进入暗黑模式
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'
import RestPassword from '@/components/data/RestPassword'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import Search from '@/components/HeaderSearch'
import { sysOriginPlatforms } from '@/constant/origin'
import { getTheme, setTheme, checkSwitchTheme } from '@/utils/theme'
import { updateTheme } from '@/api/ops-system'
import { extractColorByText } from '@/utils'

export default {
  components: {
    Breadcrumb,
    Hamburger,
    RestPassword,
    Screenfull,
    SizeSelect,
    Search
  },
  data() {
    return {
      themeVal: getTheme() || 'auto',
      themeSwitchVisible: false,
      sysOriginActive: 'RICOM',
      sysOriginPlatforms,
      resetPasswordVisible: false
    }
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar',
      'device',
      'dashboard',
      'name'
    ]),
    userAvatarColor() {
      return 'background-color:' + extractColorByText(this.name) + ';'
    },
    firstNameStr() {
      return this.name ? this.name.trim().charAt(0) : '?'
    }
  },
  watch: {
    dashbaord(newVal) {
      this.switchTheme(newVal || 'auto')
    }
  },
  methods: {
    switchTheme(val) {
      setTheme(val)
      checkSwitchTheme()
    },
    themeChange(val) {
      this.switchTheme(val)
      updateTheme(val).then(res => {}).catch(er => {})
    },
    clickSysOrigin(item) {
      this.sysOriginActive = item.label
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    clickSwitchTheme() {
      this.themeSwitchVisible = true
    },
    resetPwd() {
      this.resetPasswordVisible = true
    },
    async logout() {
      this.$store.dispatch('user/resetToken').then(() => {
        // window.clearVuexAlong(true, true)
        location.reload()
      })
      // await this.$store.dispatch('user/logout')
      // this.$router.push(`/login?redirect=${this.$route.fullPath}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color:transparent;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        position: relative;
        .user-avatar {
          cursor: pointer;
          width: 35px;
          height: 35px;
          border-radius: 100%;
          color: #FFFFFF;
          font-weight: bold;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 20px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
