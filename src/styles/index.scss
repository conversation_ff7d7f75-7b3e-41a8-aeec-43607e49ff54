@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './seal.scss';
@import './mark.scss';
@import './custom-dark.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
  font-size: 14px ;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

// main-container global css
.app-container {
  padding: 20px;
}

//条件过滤
.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

//自定义树节点
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

/**
 文字颜色
*/
.font-info {
  color: #666;
 }

.font-success {
 color: #67C23A;
}

.font-warning {
  color: #E6A23C;
 }

.font-danger {
  color: #F56C6C;
}

.font-blue {
  color: #409EFF;
 }


 /** 
  通用-其他
**/

.simple-el-drop-table {
  color: #F56C6C;
}

.underline {
  text-decoration:underline
}

.cursor-pointer {
  cursor: pointer;
}

.nowrap-ellipsis {
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
}

.preview-img {
  position: relative;
  width: 50px;
  height: 50px;
  margin: auto;
  .preview-svga {
    position: absolute;
    right: 0px;
    top: -10px;
  }
}

/** 
flex 布局
*/
.flex-b, .flex-l, .flex-c, .flex-r {
  display: flex
}

.flex-b {
  justify-content: space-between;
  align-items: center
}

.flex-c {
  justify-content: center;
  align-items: center
}

.flex-l {
  justify-content: left;
  align-items: center
}

.flex-r {
  justify-content: right;
  align-items: center
}

.flex-wrap {
 flex-wrap: wrap;
}


/**
* 文字引用块
*/
.blockquote {
  margin-bottom: 10px;
  padding: 15px;
  line-height: 1.6;
  border-left: 5px solid $menuActiveText;
  border-radius: 0 2px 2px 0;
  background-color: #FAFAFA;
}
.blockquote-mini {
  padding: 10px !important;
}
.blockquote-info {
  border-left: 5px solid #666 !important;
}

.blockquote-success {
  border-left: 5px solid #67C23A !important;
}

.blockquote-warning {
  border-left: 5px solid #E6A23C !important;
}

.blockquote-danger {
  border-left: 5px solid #F56C6C !important;
}

/**
 区域块卡片.
*/
.block-card {
  margin: 3px 0px;
  display: block;
  padding: 5px 10px;
  background-color: #f8f8f8;
  color: #999;
  border-radius: 2px;
  transition: all .3s;
  -webkit-transition: all .3s;
 .block-card-title {
    padding-bottom: 10px;
    font-size: 14px;
 }
 .cite {
    font-style: normal;
    font-size: 30px;
    font-weight: 300;
    color: #009688;
 }
}
.block-card-selected {
  background-color: #e3f2f1 !important;
}


/**
* 区域卡.
*/
.region-card {
  .title {
    height: 38px;
    color: #333;
    font-weight: bold;
    line-height: 38px;
  }
  .subtitle {
    font-weight: 500;
    font-size: 12px;
    color: #666;
    line-height: 20px;
    margin: 8px 0;
  }
  .card {
    margin-bottom: 16px;
    padding: 16px;
    height: auto;
    overflow: visible;
    background-color: #fafafa;
    border-radius: 5px;
  }
}

.region-tag-color {
  color: #333;
  background-color: #eaeff2;
}
.region-tag-color:hover {
  background-color: #dfe9ee;
}

.attached {
  padding: 5px 20px;
  border-radius: 10px;
  background-color: rgba(226, 215, 215, 0.3);
}