// sidebar
$menuText:rgb(189, 189, 192);
$menuActiveText:#409EFF;
$subMenuActiveText:rgb(189, 189, 192); //https://github.com/ElemeFE/element/issues/12951

$menuBg:#23232b;
$menuHover:#282a34;

$subMenuBg:rgb(25, 26, 32);
$subMenuHover:#363945;

$sideBarWidth: 210px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}
