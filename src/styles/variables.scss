// sidebar - 白色主题
$menuText: #606266;
$menuActiveText: #8B5CF6;
$subMenuActiveText: #8B5CF6; //https://github.com/ElemeFE/element/issues/12951

$menuBg: #FFFFFF;
$menuHover: #F5F7FA;

$subMenuBg: #FAFAFA;
$subMenuHover: #F0F2F5;

$sideBarWidth: 210px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}
