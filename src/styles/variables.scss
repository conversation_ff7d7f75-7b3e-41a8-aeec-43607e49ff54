// sidebar - 紫色主题
$menuText: rgba(255, 255, 255, 0.9);
$menuActiveText: #FFFFFF;
$subMenuActiveText: #FFFFFF; //https://github.com/ElemeFE/element/issues/12951

$menuBg: linear-gradient(180deg, #8B5CF6 0%, #7C3A<PERSON> 50%, #6D28D9 100%);
$menuHover: rgba(255, 255, 255, 0.1);

$subMenuBg: rgba(139, 92, 246, 0.8);
$subMenuHover: rgba(255, 255, 255, 0.15);

$sideBarWidth: 210px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}
