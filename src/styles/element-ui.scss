// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}


// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

.el-dropdown-link {
  cursor: pointer;
  color: #409EFF;
}
.el-icon-arrow-down {
  font-size: 12px;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

// .el-drawer
.el-drawer__body {
  overflow: auto;
}

// 设置表头颜色
.el-table thead tr th{
  background:#fafafa !important;
  color:#515a6e;
}

.el-table th.el-table__cell.is-leaf, .el-table td.el-table__cell {
  border-bottom: none !important;
}

.el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
  border-bottom: 1px solid #EBEEF5 !important;
}

// 隐藏上传按钮
.upload-but-hide {
  .el-upload--picture-card {
    display: none;
  }
  .upload-but,.el-upload--text {
    display: none;
  }
}

// 图片错误占位
.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 18px;
}

// 上传图片card 小号
.upload-small {
  .el-upload--picture-card{
    width: 100px;
    height: 100px;
  }
 .el-upload{
    width: 100px;
    height: 100px;
    line-height: 100px;
  }
 .el-upload-list--picture-card .el-upload-list__item{
    width: 100px;
    height: 100px;
    line-height: 100px;
  }
 .el-upload-list--picture-card .el-upload-list__item-thumbnail{
    width: 100px;
    height: 100px;
    line-height: 100px;
  }
  .el-upload-list__item.is-success .el-upload-list__item-status-label {
    display: none;
  }
}

// el-drawer 尺寸适配
.drawer-auto-layout {
  width: 30% !important;
}

/* 小于等于 1200px */
@media (max-width: 1400px) {
  .drawer-auto-layout {
    width: 40% !important;
  }
}

/* 小于等于 1200px */
@media (max-width: 1200px) {
  .drawer-auto-layout {
    width: 50% !important;
  }
}

/* 小于等于 900px */
@media (max-width: 900px) {
  .drawer-auto-layout {
    width: 60% !important;
  }
}

/* 小于等于 800px */
@media (max-width: 800px) {
  .drawer-auto-layout {
    width: 70% !important;
  }
}

/* 小于等于 770px */
@media (max-width: 770px) {
  .drawer-auto-layout {
    width: 100% !important;
  }
}

.drawer-footer {
  position:absolute;
  bottom: 0px;
  left: 0px;
  right: 0px;
  background: #FFFFFF;
  padding: 20px 50px;
  z-index: 9;
  text-align: right;
}

.drawer-form {
  padding: 0px 20px 80px 20px;
}

/**
* el-input
*/
.el-input-group__prepend,.el-input-group__append {
  width: 90px !important;
}

