/* 自定义 element 暗黑模式样式 */
html.dark-theme {
    // --------------
    // Font Color
    $--color-text-primary: #E5EAF3;
    $--color-text-regular: #CFD3DC;
    $--color-text-secondary: #A3A6AD;
    $--color-text-placeholder: #8D9095;
    // Border Color
    $--border-color-base: #45474A;
    $--border-color-light: #45474A;
    $--border-color-lighter: #363637;
    $--border-color-extra-light: #2B2B2C;
    // Background Color
    $--color-white: #282830;
    $--color-black: #23232B;
    $--background-color-base: #45474A;

    $--sug-mask-color: rgb(0 0 0 / 80%);
	$--sug-login-shadow-light: 5px 5px 15px rgb(255 255 255 / 20%);
    
    color: $--color-text-regular;
    // layout
    .app-wrapper {
        .main-container {
            background-color: $--color-black !important;
        }
        .fixed-header {
            .navbar {
            background-color: $--color-black !important;
            }
        }
    }
    // .el-drawer
    .el-drawer__body {
        color: $--color-text-regular;
    }
    // input
    input:-webkit-autofill , textarea:-webkit-autofill, select:-webkit-autofill {
        -webkit-text-fill-color: $--color-text-primary !important;
        -webkit-box-shadow: 0 0 0px 1000px transparent inset !important;
        background-color:transparent;
        background-image: none;
        transition: background-color 50000s ease-in-out 0s;
     }

    .el-input-group__prepend {
        background-color: $--color-white !important;
    }
    // DatePicker
    .el-date-editor input {
        background-color: $--color-white !important;
    }
    // table
    .el-table thead tr th{
        background: $--color-white !important;
        color: $--color-text-primary;
    }
    .el-table th.el-table__cell.is-leaf, .el-table td.el-table__cell {
        border-bottom: none !important;
    }
    .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
        border-bottom: 1px solid $--border-color-light !important;
    }
    // loading
    .el-loading-mask{
        background: $--background-color-base !important;
    }
    // 引用块
    .blockquote {
        background-color: $--background-color-base;
        color: $--color-text-primary;
    }
    .drawer-footer {
        background-color: $--color-white;
    }
    /**
    * 区域卡.
    */
    .region-card {
        .title {
            color: $--color-text-primary;
        }
        .card {
            background-color: $--color-white;
        }
    }
    .region-tag-color {
        color: $--color-text-regular;
        background-color: $--background-color-base;
    }
    .region-tag-color:hover {
        background-color:  $--color-black;
    }
    // dashboard - 图表
    .customize-charts {
        .charts-tag {
            color: $--color-text-regular;
            background-color: $--background-color-base;
        }
        .charts-tag:hover {
            background-color:  $--color-black;
        }
        .charts-tag-selected {
            background-color: $--sug-mask-color;
        }
    }
    // 数据卡片
    .block-card {
        background-color: $--background-color-base;
        color:  $--color-text-regular;
       .cite {
            color: $--color-text-secondary;
       }
   }
   .block-card-selected {
        background-color: $--sug-mask-color !important;
   }
    // login
    .login-container {
        background-color: $--color-black !important;
        .login-box {
            background-color: $--sug-mask-color !important;
            input {
                background-color:transparent;
                -webkit-text-fill-color: $--color-text-primary !important;
                -webkit-box-shadow: 0 0 0px 1000px transparent inset !important;
                background-color:transparent;
                background-image: none;
                transition: background-color 50000s ease-in-out 0s;
            }
            .right-login {
                box-shadow: $--sug-login-shadow-light !important;
                .login-form {
                    .logo-text {
                        color: $--color-text-primary !important;
                    }
                }
            }
        }
    }

}
