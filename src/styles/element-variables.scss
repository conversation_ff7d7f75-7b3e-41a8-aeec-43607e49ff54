/**
* I think element-ui's default theme color is too light for long-term use.
* So I modified the default color and you can modify it to your liking.
**/

/* theme color */
$--color-primary: #1890ff;
$--color-success: #13ce66;
$--color-warning: #FFBA00;
$--color-danger: #ff4949;
// $--color-info: #1E1E1E;

$--button-font-weight: 400;

// $--color-text-regular: #1f2d3d;

$--border-color-light: #dfe4ed;
$--border-color-lighter: #e6ebf5;

$--table-border:1px solid#dfe6ec;
// --------------
// Font Color
// $--color-text-primary: #E5EAF3;
// $--color-text-regular: #CFD3DC;
// $--color-text-secondary: #A3A6AD;
// $--color-text-placeholder: #8D9095;
// // Border Color
// $--border-color-base: #45474A;
// $--border-color-light: #45474A;
// $--border-color-lighter: #363637;
// $--border-color-extra-light: #2B2B2C;
// // Background Color
// $--color-white: #282830;
// $--color-black: #23232B;
// $--background-color-base: #45474A;

// $--sug-bg-color: #23232B;

/* icon font path, required */
$--font-path: '~element-ui/lib/theme-chalk/fonts';

@import "~element-ui/packages/theme-chalk/src/index";

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  theme: $--color-primary;
}
