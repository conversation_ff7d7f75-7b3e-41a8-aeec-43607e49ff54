<template>
  <div class="filter-layout">

    1
  </div>
</template>
<script>
import RoomDeatilsDrawer from '@/components/data/RoomDeatilsDrawer'
export default {
  name: 'GameRedPacketDetailsDrawer',
  components: { RoomDeatilsDrawer },
  props: {
    row: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      roomDeatilsDrawerVisible: false
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    queryRoomDetails() {
      this.roomDeatilsDrawerVisible = true
    }
  }
}
</script>
  <style scoped lang="scss">
  .team-drawer-content {
      padding: 0px 10px;

  }
  </style>

