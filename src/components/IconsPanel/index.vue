<template>
  <el-dialog
    title="Icons面板"
    :visible="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="icons-container">
      <div v-for="(item,index) in iconList" :key="index" class="icons-item" @click="handeClick(item)">
        <div class="icon-content">
          <i>
            <svg-icon :icon-class="item.icon" />
          </i>
          <span>{{ item.name }}</span>
        </div>
      </div>
      <div style=" clear: both;" />
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'IconsPanel',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      iconList: [
        { name: 'alarm', icon: 'alarm' },
        { name: 'anchor_management', icon: 'anchor_management' },
        { name: 'application_manager', icon: 'application_manager' },
        { name: 'audio', icon: 'audio' },
        { name: 'authority', icon: 'authority' },
        { name: 'revenue', icon: 'revenue' },
        { name: 'broker', icon: 'broker' },
        { name: 'comprehensive_analysis', icon: 'comprehensive_analysis' },
        { name: 'dashboard', icon: 'dashboard' },
        { name: 'data_analysis', icon: 'data_analysis' },
        { name: 'data_overview', icon: 'data_overview' },
        { name: 'device', icon: 'device' },
        { name: 'device_maintain', icon: 'device_maintain' },
        { name: 'example', icon: 'example' },
        { name: 'eye', icon: 'eye' },
        { name: 'eye-open', icon: 'eye-open' },
        { name: 'bank_card', icon: 'bank_card' },
        { name: 'forecast_information', icon: 'forecast_information' },
        { name: 'form', icon: 'form' },
        { name: 'gift', icon: 'gift' },
        { name: 'gridding', icon: 'gridding' },
        { name: 'integral', icon: 'integral' },
        { name: 'ios', icon: 'ios' },
        { name: 'layer', icon: 'layer' },
        { name: 'link', icon: 'link' },
        { name: 'log', icon: 'log' },
        { name: 'message', icon: 'message' },
        { name: 'nested', icon: 'nested' },
        { name: 'overview_1', icon: 'overview_1' },
        { name: 'password', icon: 'password' },
        { name: 'process', icon: 'process' },
        { name: 'resources_91', icon: 'resources_91' },
        { name: 'crown', icon: 'crown' },
        { name: 'sys_manager_1', icon: 'sys_manager_1' },
        { name: 'sys_manager_2', icon: 'sys_manager_2' },
        { name: 'table', icon: 'table' },
        { name: 'tree', icon: 'tree' },
        { name: 'unknown', icon: 'unknown' },
        { name: 'user', icon: 'user' },
        { name: 'video', icon: 'video' },
        { name: 'flag', icon: 'flag' },
        { name: 'the_mall', icon: 'the_mall' },
        // 彩色图标
        { name: 'android', icon: 'android' },
        { name: 'facebook', icon: 'facebook' },
        { name: 'google', icon: 'google' },
        { name: 'snapchat', icon: 'snapchat' },
        { name: 'html5', icon: 'html5' },
        { name: 'phone', icon: 'phone' }
      ]
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
    },
    handeClick(item) {
      this.$emit('update:visible', false)
      this.$emit('iconClick', item.icon)
    }
  }
}
</script>

<style scoped lang="scss">
.icons-container {
  position: relative;
  max-height: 500px;
  overflow: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  .icons-item {
    width: 16.66%;
    height: 120px;
    border-right: 1px solid #eee;
    border-bottom: 1px solid #eee;
    margin-right: -1px;
    margin-bottom: -1px;
    color: #666;
    font-size: 13px;
    float: left;
    line-height: 20px;
    &:hover {
      color: #409eff;
    }
    cursor: pointer;
    .icon-content {
      margin: 30px auto;
      text-align: center;
      i {
        display: block;
        font-size: 32px;
        margin-bottom: 15px;
        color: #606266;
        transition: color .15s linear;
      }
      span{
        display: inline-block;
        padding: 0 3px;
        height: 1em;
      }
    }
  }
}
</style>
