<template>
  <div class="videoBox">
    <video
      ref="videoPlayer"
      class="video-js"
      @canplay="getTotalTime"
      @timeupdate="timeUpdate"
    >
    </video>
    <!-- 自定义控件 -->
    <div class="controlBar">
      <div class="progressBar" @mousedown="isDraging = true" @mouseup="isDraging = false">
        <el-slider
          v-model="currentTimeVal"
          :max="totalTimeVal"
          :format-tooltip="timeFormat"
          @change="progressUpdate"
        >
        </el-slider>
      </div>
      <div class="controlBtnBox">
        <div class="left">
          <i
            :class="[isPaused ? 'el-icon-video-play' : 'el-icon-video-pause']"
            class=" icon-size"
            @click="togglePlay()"
          >
          </i>
          <span>{{ currentTime }}/{{ totalTime }}</span>
        </div>
        <div class="right">
          <i class="el-icon-d-arrow-left icon-size" @click="back(player.currentTime())"></i>
          <i class="el-icon-d-arrow-right icon-size" @click="forward(player.currentTime())"></i>
          <i class="el-icon-bell icon-size" @click="toShowVolumeBar"></i>
          <div id="volumeBar" v-show="isShowVolumeBar">
            <el-slider
              v-model="volume"
              vertical
              height="100px"
              @input="changeVolume"
            >
            </el-slider>
          </div>
        </div>
        <div class="rateBox">
          <span @click="toShowOptions">{{ ratedisplay }}</span>
          <div class="rateOptions" v-show="isShowRateOptions">
              <span
                v-for="(r,index) in rateOptions"
                :key="index"
                @click="setPlayRate(r)">
                  {{ r }}x
              </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import 'video.js/dist/video-js.css'

export default {
  name: 'VideoPlayer',
  props: {
    options: {
      type: Object
    }
  },
  data() {
    return {
      player: null,
      // 当前播放速率
      rate: 1.0,
      // 播放速率
      rateOptions: [2.0, 1.75, 1.5, 1.0, 0.75, 0.5],
      // 显示速率选项和音量选项
      isShowRateOptions: false,
      isShowVolumeBar: false,
      // 音量
      volume: 30,
      // 是否暂停
      isPaused: true,
      // 当前播放时间点和视频总时长
      currentTime: '00:00:00',
      totalTime: '00:00:00',
      // 进度条的当前值，必须为number类型
      currentTimeVal: 0,
      // 进度条最大值，必须为number类型
      totalTimeVal: 0,
      // 是否在拖到进度条
      isDraging: false
    }
  },
  computed: {
    ratedisplay() {
      if (this.rate === 1) {
        return '倍速'
      } else {
        return this.rate + 'x'
      }
    }
  },
  mounted() {
    this.createVideoPlayer()
  },
  methods: {
    createVideoPlayer() {
      this.player = this.$videoJS(this.$refs.videoPlayer, this.options)
    },
    otherCloseVideo() {
      if (this.player != null) {
        this.player.pause()
        this.player.dispose()
        this.player = null
      }
    },
    // 显示速率选项
    toShowOptions() {
      this.isShowRateOptions = !this.isShowRateOptions
    },
    toShowVolumeBar() {
      this.isShowVolumeBar = !this.isShowVolumeBar
    },
    // 视频时长格式化
    timeFormat(time) {
      let hour = Math.floor(time / 3600)
      let minute = Math.floor((time % 3600) / 60)
      let second = Math.floor(time % 60)
      hour = hour < 10 ? '0' + hour : hour
      minute = minute < 10 ? '0' + minute : minute
      second = second < 10 ? '0' + second : second
      return `${hour}:${minute}:${second}`
    },
    //  获取视频的总时长和进度条最大值
    getTotalTime() {
      this.totalTime = this.timeFormat(this.player.duration())
      this.totalTimeVal = Math.floor(this.player.duration())
    },
    //  改变速率
    setPlayRate(rate) {
      this.rate = rate
      this.player.playbackRate(rate)
      this.isShowRateOptions = false
    },
    //  控制视频的播放与暂停
    togglePlay() {
      this.isPaused = !this.isPaused
      if (!this.isPaused) {
        this.player.play()
      } else {
        this.player.pause()
      }
    },
    //  更新视频当前播放时间
    timeUpdate() {
      //  如果当前正在拉到进度条，先停止更新当前播放时间，直接return结束这个函数
      //  没有这一句会出现拉动进度条跳转失败的bug
      if (this.isDraging) return
      if (!this.player) {
        return
      }
      this.currentTime = this.timeFormat(this.player.currentTime())
      this.currentTimeVal = this.player.currentTime()
      if (this.currentTime === this.totalTime) {
        //  当前时间更新到等于总时长时，要改变视频的播放状态按钮
        this.isPaused = true
      }
    },
    progressUpdate(val) {
      //  进度条拉动时更新进度条值并从拉到的位置播放
      this.player.currentTime(val)
      //  虽然mouseup已经可以改变isDraging的值，但下面这句不能少，不然视频播放结束再点击播放时，进度条不会回到最开始位置
      this.isDraging = false
    },
    changeVolume(val) {
      //  改变音量
      this.volume = val
      //  由于h5规定volum的值在0-1之间，所以这里要对获取到的val做一个处理(滑块的val是从0-100)
      this.player.volume(val / 100)
    },
    forward(ct) {
      //  快进
      this.progressUpdate(ct + 5)
    },
    back(ct) {
      //  后退
      this.progressUpdate(ct - 5)
    }

  }
}
</script>

<style scoped>
/deep/ .vjs-fluid:not(.vjs-audio-only-mode) {
  padding-top: 65%;
}

.videoBox {
  box-sizing: border-box;
  position: relative;
  width: 800px;
  height: 500px;
}

.controlBar {
  width: 90%;
  height: 55px;
  position: absolute;
  bottom: 20px;
  left: 5%;
  background-color: #817f7f5a;
  box-sizing: border-box;
  color: rgb(233, 231, 231);
}

.progressBar {
  box-sizing: border-box;
  position: relative;
  width: 100%;
  padding: 10px;
  height: 10%;
  /* background-color: aliceblue; */
}

.controlBtnBox {
  box-sizing: border-box;
  width: 100%;
  height: 60%;
  display: flex;
  justify-content: space-between;
  align-items: center;

}

/* 以下强行修改了el-slider样式 */
.progressBar /deep/ .el-slider__bar {
  height: 3px;
  background-color: #409EFF;
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
  position: absolute;
}

.progressBar /deep/ .el-slider__button {
  height: 8px;
  width: 8px;
}

.progressBar /deep/ .el-slider__runway {
  margin-top: 1px;
  margin-bottom: 1px;
  height: 3px;
}

.progressBar /deep/ .el-slider__button-wrapper {
  width: 28px;
  height: 33px;
}

.icon-size {
  font-size: 25px;
  cursor: pointer;
}

.left {
  padding-left: 10px;
  width: 50%;
  display: flex;
  align-items: center;
}

.left span {
  margin-left: 20px;
}

.right {
  width: 15%;
  display: flex;
  justify-content: space-around;
  position: relative;
}

.right i {
  display: block;
}

#volumeBar {
  width: 30px;
  height: 120px;
  background-color: #817f7f5a;
  position: absolute;
  top: -150px;
  right: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.rateBox {
  width: 15%;
  cursor: pointer;
}

.rateOptions {
  width: 80px;
  height: 180px;
  background-color: #817f7f5a;
  position: absolute;
  top: -185px;
  right: 50px;
  display: flex;
  flex-wrap: wrap;
  align-content: center;
}

.rateOptions span {
  display: block;
  width: 100%;
  height: 30px;
  text-align: center;
  line-height: 30px;
}

.rateOptions span:hover {
  background-color: #cec9c95a;
  color: #409EFF;
}
</style>
