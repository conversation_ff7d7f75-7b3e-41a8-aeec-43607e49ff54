<template>
  <div class="in-app-purchase-details-drawer">
    <el-drawer
      title="详情"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div v-if="order" v-loading="orderLoading" class="in-app-purchase-details-content">
        <div v-if="orderIsNull" style="text-align: center;">
          没有更多信息
        </div>
        <div v-else>
          <div v-if="order.acceptUserProfile" class="order-row">
            <div class="blockquote">接收用户</div>
            <div class="content">
              <user-table-exhibit :user-profile="order.acceptUserProfile" :query-details="true" />
            </div>
          </div>
          <div v-if="order.createUserProfile" class="order-row">
            <div class="blockquote">创建用户</div>
            <div class="content">
              <user-table-exhibit :user-profile="order.createUserProfile" :query-details="true" />
            </div>
          </div>
          <div v-if="order.createNickname" class="order-row">
            <div class="blockquote">创建用户</div>
            <div class="content" style="margin-bottom: 10px;">
              {{ order.createNickname }}
            </div>
          </div>
          <div v-if="order.details" class="order-row">
            <div class="blockquote">订单信息</div>
            <div class="content">
              <div class="order-info flex-b flex-wrap">
                <div class="order-info-col">环境:
                  <el-tag v-if="order.details.env === 'PROD'" type="success" size="mini">{{ order.details.env }}</el-tag>
                  <el-tag v-else-if="order.details.env === 'TEST'" type="warning" size="mini">{{ order.details.env }}</el-tag>
                  <el-tag v-else type="danger" size="mini">{{ order.details.env }}</el-tag>
                </div>
                <div class="order-info-col">类型: {{ orderReceiptTypeMap[order.details.receiptType] }}</div>
                <div class="order-info-col">状态:
                  <el-tag :type=" orderStatusMap[order.details.status].tagType" size="mini">{{ orderStatusMap[order.details.status].label }}</el-tag>
                </div>
                <div class="order-info-col">ID: {{ order.details.id }}</div>
                <div class="order-info-col">跟踪ID: {{ order.details.trackId }}</div>
                <div v-if="order.details.subscribeId" class="order-info-col">订阅ID: {{ order.details.subscribeId }}</div>
                <div class="order-info-col" :span="24">订单ID: {{ order.details.orderId }}</div>
                <div class="order-info-col">平台渠道:
                  <sys-origin-icon size="20px" :icon="order.details.sysOrigin" :desc="order.details.sysOrigin" />
                  <platform-svg-icon size="20px" :icon="order.details.factory.platform" />
                  <platform-svg-icon size="20px" :icon="order.details.factory.factoryCode" />
                </div>
                <div class="order-info-col">国家编号: {{ order.details.countryCode }}</div>
                <div class="order-info-col">货币类型: {{ order.details.currency }}</div>
                <div class="order-info-col">金额: {{ order.details.amount }} {{ order.details.currency }}</div>
                <div class="order-info-col">美元: {{ order.details.amountUsd }} USD</div>
                <div class="order-info-col">免费试用: {{ order.details.trialPeriod === true ? 'Yes' :'No' }}</div>

                <div v-if="order.details.reason" class="order-info-col">原因: {{ order.details.reason }}</div>
                <div class="order-info-col">购买时间: {{ order.details.purchaseDateMs }}</div>
                <div class="order-info-col">创建时间: {{ order.details.createTime }}</div>
                <div class="order-info-col">修改时间: {{ order.details.updateTime }}</div>
                <div class="order-info-col">锁版本: {{ order.details.version }}</div>
              </div>
            </div>
          </div>
          <div v-if="order.details && order.details.statusSteps && order.details.statusSteps.length>0" class="order-row">
            <div class="blockquote">状态流转</div>
            <div class="content">
              <el-steps align-center :space="200" :active="order.details.statusSteps.length" finish-status="success" style="margin: 10px 0px;">
                <el-step v-for="(item, index) in order.details.statusSteps" :key="index" :title="orderStatusMap[item.status].label" :description="item.createTime" />
              </el-steps>
            </div>
          </div>
          <div v-if="order.details && order.details.products && order.details.products.length > 0" class="order-row">
            <div class="blockquote">商品信息</div>
            <div class="content">
              <div class="product-card">
                <div v-if="order.details.productDescriptor" class="prodcut-desc">
                  {{ order.details.productDescriptor }}
                </div>
                <div v-for="(item, index) in order.details.products" :key="index" class="product-item">
                  <div class="flex-b">
                    <div class="product-cover">
                      <img :src="item.cover || $application.defaultImgUrl" alt="">
                    </div>
                    <div class="product-info">
                      <el-row>
                        <el-col :span="12">ID: {{ item.id }}</el-col>
                        <el-col :span="12">名称: {{ item.name }}</el-col>
                        <el-col :span="12">编号: {{ item.code }}</el-col>
                        <el-col :span="12">内容: {{ item.content }}</el-col>
                        <el-col :span="12">金额: {{ item.amountUsd }} USD</el-col>
                        <el-col :span="12">数量: {{ item.quantity }}</el-col>
                        <el-col :span="24">描述: {{ item.describe }}</el-col>
                      </el-row>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-if="order.details && order.details.metadata && Object.keys(order.details.metadata).length > 0" class="order-row">
            <div class="blockquote">元数据</div>
            <div class="content">
              <el-table
                :data="metadataList"
                element-loading-text="Loading"
                fit
                highlight-current-row
              >
                <el-table-column prop="key" label="字段" align="center" />
                <el-table-column prop="value" label="值" align="center" />
              </el-table>
            </div>
          </div>
          <div v-if="order.details && order.details.payNotices" class="order-row">
            <div class="blockquote">事件通知</div>
            <div class="content">
              <el-timeline :reverse="true">
                <el-timeline-item
                  v-for="(item, index) in order.details.payNotices"
                  :key="index"
                  placement="top"
                  :timestamp="item.createTime"
                >
                  <div class="event-info">
                    <div class="event-info-item">事件ID: {{ item.eventId }}</div>
                    <div class="event-info-item">事件类型: {{ item.noticeType }}</div>
                  </div>
                  <div class="request-data">
                    <json-editor v-model="item.noticeData" :read-only="true" />
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>

  </div>
</template>
<script>
import { getInAppPurchase } from '@/api/purchase'
import PlatformSvgIcon from '@/components/PlatformSvgIcon'
import JsonEditor from '@/components/JsonEditor'
export default {
  name: 'InAppPurchaseDetails',
  components: { PlatformSvgIcon, JsonEditor },
  props: {
    orderId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      orderLoading: false,
      order: {},
      orderIsNull: false,
      orderReceiptTypeMap: {
        'PAYMENT': '付款',
        'RECEIPT': '收款'
      },
      orderStatusMap: {
        'CREATE': {
          label: '创建',
          tagType: ''
        },
        'SUCCESS': {
          label: '成功',
          tagType: 'success'
        },
        'FAIL': {
          label: '失败',
          tagType: 'danger'
        },
        'CANCEL': {
          label: '取消',
          tagType: 'info'
        },
        'HANG': {
          label: '挂起',
          tagType: 'warning'
        },
        'REFUND': {
          label: '退款',
          tagType: 'danger'
        }
      }
    }
  },
  computed: {
    metadataList() {
      if (!this.order.details || !this.order.details.metadata) {
        return []
      }
      const resultList = []
      for (const key in this.order.details.metadata) {
        resultList.push({
          key,
          value: this.order.details.metadata[key]
        })
      }
      return resultList
    }
  },
  watch: {
    orderId: {
      handler(newVal) {
        this.loadInAppPurchase()
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    loadInAppPurchase() {
      const that = this
      that.orderLoading = true
      getInAppPurchase(that.orderId).then(res => {
        that.orderLoading = false
        that.order = res.body || {}
        that.orderIsNull = !(that.order && that.order.details && that.order.details.id)
      }).catch(er => {
        that.orderLoading = false
      })
    }
  }
}
</script>
  <style scoped lang="scss">
  .in-app-purchase-details-content {
      padding: 0px 10px;
      .order-info {
        .order-info-col {
            padding: 10px 0px;
            width: 50%;
            flex-shrink: 0;
        }
      }
      .event-info {
        .event-info-item {
            padding: 10px 0px;
        }
      }
      .product-card {
        .prodcut-desc {
          padding-bottom: 10px;
        }
        .product-item {
          margin-bottom: 15px;
          .product-cover {
            width: 100px;
            height: 100px;
            flex-shrink: 0;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .product-info {
            width: 100%;
            padding: 0px 10px;
            line-height: 24px;
          }
        }
      }
  }
  </style>

