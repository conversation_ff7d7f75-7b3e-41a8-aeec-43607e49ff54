<template>
  <div class="gift-history-drawer">
    <el-drawer
      title="详情"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div class="gift-history-content">
        <div class="blockquote">
          发送人
          <el-tag
            v-if="row.anchor === true"
            type="danger"
            size="mini"
          >主播</el-tag>
        </div>
        <div class="content">
          <user-table-exhibit
            :user-profile="row.userProfile"
            :query-details="true"
          />
        </div>
        <div v-if="row.roomProfile" class="blockquote">发送房间</div>
        <div v-if="row.roomProfile" class="content">
          <div class="flex-l" style="padding-bottom: 10px;">
            <div class="room-avatar">
              <el-image
                :src="row.roomProfile.roomCover"
                style="width: 100%; height: 100%; border-radius: 100%; margin: auto;"
              >
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </el-image>
              <flag-icon
                class="flag-icon"
                :code="row.roomProfile.countryCode"
                :tooltip="row.roomProfile.countryName"
                size="18"
              />
            </div>
            <div class="nowrap-ellipsis room-name">
              <el-link
                @click="queryRoomDetails()"
              ><a :title="row.roomProfile.roomName">{{
                row.roomProfile.roomName
              }}</a></el-link>
            </div>
          </div>
        </div>
        <div v-if="row.roomProfile" class="blockquote">信息快照</div>
        <div class="content gift-text">
          <el-row :gutter="10">
            <el-col :span="12">礼物ID: {{ row.giftId }}</el-col>
            <el-col
              :span="12"
            >礼物封面:
              <el-image
                style="width: 30px; height: 30px"
                :src="row.giftCover"
                fit="fill"
                :preview-src-list="[row.giftCover]"
              />
            </el-col>
            <el-col
              :span="12"
            >礼物货币: {{ row.giftValue.currencyType }}</el-col>
            <el-col :span="12">礼物类型: {{ row.giftValue.giftType }}</el-col>
            <el-col :span="12">礼物单价: {{ row.giftValue.unitPrice }}</el-col>
            <el-col :span="12">礼物价值: {{ row.giftValue.giftValue }}</el-col>
            <el-col
              :span="12"
            >实际价值: {{ row.giftValue.actualAmount }}</el-col>
            <el-col
              :span="12"
            >发送数量: {{ row.giftValue.quantity }} 个</el-col>
            <el-col
              :span="12"
            >接收用户: {{ row.giftValue.userSize }} 人</el-col>
            <!-- 这两个字段已移除(percentage&selfPercentage), 2023/4月份可移除-->
            <el-col
              v-if="row.giftValue.percentage"
              :span="12"
            >接收概率: {{ row.giftValue.percentage * 100 }}%</el-col>
            <el-col
              v-if="row.giftValue.selfPercentage"
              :span="12"
            >自己接收概率: {{ row.giftValue.selfPercentage * 100 }}%</el-col>
            <el-col
              :span="12"
            >背包: {{ row.giftValue.bag ? "Yes" : "No" }}</el-col>
            <el-col :span="12">跟踪ID: {{ row.trackId }}</el-col>
            <el-col :span="12">请求平台: {{ row.requestPlatform }}</el-col>
          </el-row>
        </div>
        <div class="blockquote">接收用户</div>
        <div class="content">
          <div
            v-for="(item, index) in row.acceptUsers"
            :key="index"
            style="margin: 10px 0px;"
          >
            <user-table-exhibit
              :user-profile="item.userProfile"
              :query-details="true"
            />
            <div class="receive-info">
              <el-tag
                v-if="item.anchor === true"
                type="danger"
                size="mini"
              >主播</el-tag>
              <el-tag
                v-if="item.anchor === true"
                size="mini"
              >接收目标:{{ item.targetAmount }}</el-tag>
              <el-tag size="mini">接收金额:{{ item.acceptAmount }}</el-tag>
              <el-tag
                size="mini"
              >接收概率:
                {{
                  (item.acceptUserId !== row.userId
                    ? item.percentage
                    : item.selfPercentage) * 100
                }}%</el-tag>
              <el-tag
                v-if="item.region"
                size="mini"
              >区域(发-接): {{ item.region
              }}<span
                v-if="item.hasOwnProperty('regionEq')"
              >、{{ item.regionEq ? "Yes" : "No" }}</span></el-tag>
              <el-tag
                v-if="item.hasOwnProperty('countTargetAmount')"
                size="mini"
              >统计状态: {{ item.countTargetAmount ? "Yes" : "No" }}</el-tag>
              <el-button
                type="text"
                size="mini"
                @click="clickCopyReceiptId(item)"
              ><i class="el-icon-document-copy" />单据ID</el-button>
            </div>
          </div>
        </div>
        <div class="blockquote">日志事件</div>
        <div class="content">
          <el-timeline>
            <el-timeline-item
              v-for="(item, index) in row.logs"
              :key="index"
              :timestamp="item.createTime | dateFormat"
            >
              {{ item.content }}
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-drawer>

    <room-deatils-drawer
      v-if="roomDeatilsDrawerVisible"
      :room-id="row.originId"
      @close="roomDeatilsDrawerVisible = false"
    />
  </div>
</template>
<script>
import RoomDeatilsDrawer from '@/components/data/RoomDeatilsDrawer'
import { copyText } from '@/utils'
export default {
  name: 'GiftHistoryDetailsDrawer',
  components: { RoomDeatilsDrawer },
  props: {
    row: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      roomDeatilsDrawerVisible: false
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    queryRoomDetails() {
      this.roomDeatilsDrawerVisible = true
    },
    copyContent(text) {
      copyText(text)
        .then(() => {
          this.$opsMessage.success()
        })
        .catch(er => {
          this.$opsMessage.fail()
        })
    },
    clickCopyReceiptId(row) {
      this.copyContent(row.receiptId)
    }
  }
}
</script>
<style scoped lang="scss">
.gift-history-content {
  padding: 0px 10px;
  .el-tabs__nav-scroll .el-tabs__item {
    font-size: 13px;
  }
  .room-avatar {
    position: relative;
    width: 50px;
    height: 50px;
    margin: auto;
    flex-shrink: 0;
    .flag-icon {
      position: absolute;
      bottom: 0px;
    }
  }

  .room-name {
    width: 100%;
    text-align: left;
    padding-left: 10px;
  }

  .gift-text {
    line-height: 40px;
  }
}
</style>
