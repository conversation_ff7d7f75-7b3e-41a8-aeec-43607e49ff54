<template>
  <div class="props-row">
    <div v-for="(item, index) in list" :key="index" class="props-item">
      <el-tooltip class="item" effect="dark" :content="formatPropsContent(item)" placement="top-start">
        <div class="quantity nowrap-ellipsis">{{ formatPropsContent(item) }} </div>
      </el-tooltip>
      <img v-if="item.type === 'GOLD'" src="@/assets/gold_icon.png" style="width:50px;height50px;cursor: pointer;">
      <img v-else-if="item.type === 'DIAMOND'" src="@/assets/diamond_icon.png" style="width:50px;height50px;cursor: pointer;">
      <img v-else-if="item.type === 'GAME_COUPON'" src="@/assets/game_coupon.png" style="width:50px;height50px;cursor: pointer;">
      <img v-else-if="item.type === 'SPECIAL_ID'" src="@/assets/special_id.png" style="width:50px;height:50px;cursor: pointer;">
      <div v-else class="preview-img">
        <el-image
          style="width: 50px; height: 50px"
          :src="item.cover"
          :preview-src-list="[item.cover]"
        >
          <div slot="error" class="image-slot">
            <i class="el-icon-picture-outline" />
          </div>
        </el-image>
        <div class="preview-svga">
          <svgaplayer
            type="popover"
            :url="item.sourceUrl"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'PropsRow',
  props: {
    list: {
      type: Array,
      require: true,
      default: () => []
    }
  },
  data() {
    return {
      propsTypeFormat: {
        'GIFT': {
          prefix: 'x',
          field: 'quantity',
          suffix: ''
        },
        'DIAMOND': {
          prefix: '',
          field: 'content',
          suffix: ''
        },
        'GOLD': {
          prefix: '',
          field: 'content',
          suffix: ''
        },
        'ROOM_BADGE': {
          prefix: '',
          field: 'quantity',
          suffix: 'D',
          forever: true
        },
        'BADGE': {
          prefix: '',
          field: 'quantity',
          suffix: 'D',
          forever: true
        },
        'SPECIAL_ID': {
          prefix: '',
          field: 'content',
          suffix: ''
        },
        'NOBLE_VIP': {
          prefix: '',
          field: 'quantity',
          suffix: 'D'
        },
        'AVATAR_FRAME': {
          prefix: '',
          field: 'quantity',
          suffix: 'D'
        },
        'DATA_CARD': {
          prefix: '',
          field: 'quantity',
          suffix: 'D'
        },
        'RIDE': {
          prefix: '',
          field: 'quantity',
          suffix: 'D'
        },
        'THEME': {
          prefix: '',
          field: 'quantity',
          suffix: 'D'
        },
        'EMOJI': {
          prefix: 'x',
          field: 'quantity',
          suffix: ''
        },
        'CHAT_BUBBLE': {
          prefix: '',
          field: 'quantity',
          suffix: 'D'
        },
        'GAME_COUPON': {
          prefix: '',
          field: 'quantity',
          suffix: ''
        },
        'FRAGMENTS': {
          prefix: 'x',
          field: 'quantity',
          suffix: ''
        },
        'FLOAT_PICTURE': {
          prefix: '',
          field: 'quantity',
          suffix: 'D'
        },
        'LAYOUT': {
          prefix: 'x',
          field: 'quantity',
          suffix: ''
        }
      }
    }
  },
  computed: {
  },
  created() {
  },
  methods: {
    formatPropsContent(item) {
      if (item.detailType === 'CUSTOMIZE' && item.remark) {
        return item.remark
      }

      const format = this.propsTypeFormat[item.detailType] || this.propsTypeFormat[item.type]

      if (!format) {
        return '?'
      }

      const val = item[format.field]
      if (format.forever === true && val <= 0) {
        return '永久'
      }

      return `${format.prefix}${val}${format.suffix}`
    }
  }

}
</script>
<style scoped lang="scss">
.props-row {
  width: 100%;
  display: flex;
  padding: 10px 0px;
  overflow: auto;
  .props-item {
    position: relative;
    width: 50px;
    margin: 0px 5px;
    .quantity {
      position: absolute;
      background-color: rgba(21, 21, 21, 0.6);
      color: #FFFFFF;
      border-radius: 2px;
      right: 0px;
      left: 0px;
      bottom: 0px;
      z-index: 1;
      text-align: center;
      cursor: pointer;
    }
  }
}

</style>
