<template>
  <div class="edit-user-gold-coin-reward-form">
    <el-dialog
      title="奖励用户金币"
      :visible="true"
      width="450px"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="formData"
        label-position="left"
        label-width="70px"
        style="width: 300px; margin-left:50px;"
      >
        <el-form-item label="类型" prop="rewardType">
          <el-select v-model="formData.rewardType" placeholder="请选择" style="width: 100%;">
            <el-option
              v-for="item in currencyRewardReasons"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="金币" prop="candy">
          <el-input
            v-model.trim="formData.candy"
            placeholder="需要奖励的金币数量"
          />
        </el-form-item>

        <el-form-item v-if="formData.rewardType == 3" label="美金" prop="usdCandy">
          <el-input
            v-model.trim="formData.usdCandy"
            placeholder="美金数量"
          />
        </el-form-item>

        <el-form-item label="备注" prop="remarks">
          <el-input
            v-model.trim="formData.remarks"
            placeholder="奖励备注"
          />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose()">
          取消
        </el-button>
        <el-button
          v-loading="listLoading"
          type="primary"
          @click="handleSubmit()"
        >
          提交
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { currencyRewardReasons } from '@/constant/type'
import { sendGold } from '@/api/app-user'

export default {
  name: 'EditUserGoldCoinDeductionForm',
  props: {
    userId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      listLoading: false,
      currencyRewardReasons,
      rules: {
        candy: [
          {
            required: true,
            message: '必填字段不可为空',
            trigger: 'blur'
          },
          {
            pattern: /^\d{1,7}(\.\d{0,2})?$/,
            message: 'double范围0~9999999小数最多两位',
            trigger: 'blur'
          }
        ],
        rewardType: [
          {
            required: true,
            message: '必填字段不可为空',
            trigger: 'blur'
          }
        ],
        usdCandy: [
          {
            required: true,
            message: '必填字段不可为空',
            trigger: 'blur'
          }
        ]
      },
      formData: {
        candy: '',
        remarks: '',
        usdCandy: '',
        rewardType: ''
      }
    }
  },
  methods: {
    handleSubmit() {
      const that = this
      that.$refs.dataForm.validate((valid) => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.listLoading = true
        sendGold({
          userId: that.userId,
          quantity: that.formData.candy,
          usdQuantity: that.formData.usdCandy,
          remarks: that.formData.remarks ? `${that.getSeletedCurrencyRewardReasonsName()}/${that.formData.remarks}` : that.getSeletedCurrencyRewardReasonsName(),
          type: that.formData.rewardType
        }).then(res => {
          that.$emit('success', Object.assign({}, that.formData))
          that.handleClose()
        }).catch(err => {
          that.listLoading = false
          that.$emit('fial', err)
        })
      })
    },
    getSeletedCurrencyRewardReasonsName() {
      const that = this
      const list = that.currencyRewardReasons.filter(item => item.value === that.formData.rewardType)
      return list.length > 0 ? list[0].name : ''
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
