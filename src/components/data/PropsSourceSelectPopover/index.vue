<template>
  <div class="props-source-slect">
    <el-popover
      v-model="visible"
      placement="bottom"
      trigger="click"
    >
      <div class="content flex-c flex-wrap">
        <el-input v-if="showFilter" v-model="queryString" placeholder="请输入关键字进行过滤" style="margin-bottom: 10px;" />
        <div v-for="(item, index) in nativeData" :key="index" class="item" @click="clickItem(item)">
          <div class="player">
            <svgaplayer
              type="popover"
              :url="item[property.svgaUrl] || ''"
            />
          </div>
          <div class="cover">
            <img :src="item[property.cover]" alt="cover">
          </div>
          <div class="label nowrap-ellipsis">{{ item[property.label] }}</div>
        </div>
      </div>
      <el-button slot="reference" type="text">

        <slot name="content">选择</slot>
      </el-button>
    </el-popover>
  </div>
</template>

<script>
import { deepClone } from '@/utils'
export default {
  name: 'PropsSourceSelectPopover',
  props: {
    showFilter: {
      type: Boolean,
      default: false
    },
    data: {
      type: Array,
      require: true,
      default: () => []
    },
    property: {
      type: Object,
      require: false,
      default: () => {
        return {
          label: 'label',
          cover: 'cover',
          svgaUrl: 'svgaUrl',
          value: 'value'
        }
      }
    }
  },
  data() {
    return {
      visible: false,
      queryString: '',
      nativeData: []
    }
  },
  watch: {
    data: {
      handler(newVal) {
        this.nativeData = this.filterData(this.queryString)
      },
      immediate: true
    },
    queryString: {
      handler(newVal) {
        const that = this
        if (!that.data || that.data.length <= 0) {
          return
        }

        that.nativeData = that.filterData(newVal)
      },
      immediate: true
    }
  },
  methods: {
    filterData(queryString) {
      const that = this
      if (!queryString) {
        return deepClone(that.data)
      }
      return that.data.filter(item => {
        const code = item.code || ''
        if (code.indexOf(queryString) >= 0) {
          return true
        }
        const name = item.name || ''
        if (name.indexOf(queryString) >= 0) {
          return true
        }

        const id = String(item.id) || ''
        if (id.indexOf(queryString) >= 0) {
          return true
        }

        const label = item[that.property.label] || ''
        if (label.indexOf(queryString) >= 0) {
          return true
        }

        return false
      })
    },
    clickItem(item) {
      this.visible = false
      this.$emit('select', item)
    }
  }
}
</script>
<style scoped lang="scss">
.content {
  max-width: 600px;
  max-height: 600px;
  overflow: auto;
  .item {
    width: 100px;
    position: relative;
    padding: 10px;
    border: 1px solid #d4e0eb;
    cursor: pointer;
    &:hover {
      background-color: #d4e0eb;
    }
    .player {
      position: absolute;
      right: 0px;
      top: -10px;
    }
    .cover {
      width: 100%;
      height: 70px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .label {
      text-align: center;
    }
  }
}
</style>
