<template>
  <el-dialog
    title="账号处理"
    :visible="true"
    width="30%"
    :before-close="handleClose"
  >
    <el-form
      ref="form"
      v-loading="loading"
      :model="form"
      :rules="formRules"
      label-width="80px"
    >
      <el-form-item v-loading="loadingAccountStatus" label="当前状态">
        {{ thisAccountStatus.value }}
      </el-form-item>
      <el-form-item label="处理状态" prop="accountStatusEnum">
        <el-select
          v-model="form.accountStatusEnum"
          :disabled="accountStatusTypes.length <= 0"
          style="width: 100%;"
          @change="selectChanged"
        >
          <el-option-group
            v-for="(item, index) in accountStatus"
            :key="index"
            :label="item.name"
          >
            <el-option
              v-for="oItem in item.options"
              :key="oItem.value"
              :label="oItem.name"
              :value="oItem.value"
            />
          </el-option-group>
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="form.accountStatusEnum === 'FREEZE'"
        label="冻结天数"
        prop="days"
      >
        <el-select v-model="form.days">
          <el-option label="1天" value="1" />
          <el-option label="2天" value="2" />
          <el-option label="3天" value="3" />
          <el-option label="4天" value="4" />
          <el-option label="5天" value="5" />
          <el-option label="6天" value="6" />
          <el-option label="7天" value="7" />
          <el-option label="8天" value="8" />
          <el-option label="9天" value="9" />
          <el-option label="10天" value="10" />
          <el-option label="15天" value="15" />
          <el-option label="30天" value="30" />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="description">
        <el-input
          v-model.trim="form.description"
          type="textarea"
          @keyup.native="descriptionKeyup"
        />
        <el-button
          v-if="form.accountStatusEnum === 'WARNING'"
          :loading="loadingTranslate"
          type="text"
          :disabled="disabledTranslate"
          @click="clickTranslate"
          >点击翻译</el-button
        >
      </el-form-item>
      <el-form-item v-if="form.descriptionTranslate" label="译文">
        <el-input v-model="form.descriptionTranslate" type="textarea" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submit()">提交</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import { accountHandle } from "@/api/approval";
import { getAccountStatus } from "@/api/app-user";
import { translateUserLangContent } from "@/api/translate";
import { mapGetters } from "vuex";
import { deepClone } from "@/utils";
export default {
  name: "AccountHandle",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    userId: {
      type: String,
      required: true
    }
  },
  data() {
    const commonRules = [
      { required: true, message: "必填字段不可为空", trigger: "blur" }
    ];
    return {
      loadingTranslate: false,
      loadingAccountStatus: true,
      disabledTranslate: false,
      thisAccountStatus: {},
      loading: false,
      accountStatusGroup: [
        {
          name: "执行处罚",
          options: [
            { value: "WARNING", name: "警告" },
            { value: "FREEZE", name: "冻结" },
            { value: "ARCHIVE", name: "封禁" },
            { value: "ARCHIVE_DEVICE", name: "封禁+设备" }
          ]
        },
        {
          name: "解除处罚",
          options: [
            { value: "UNTIE_ACOOUNT", name: "账号解封" },
            { value: "UNTIE_DEVICE", name: "设备解封" },
            { value: "UNTIE_DEVICE_AND_ACCOUNT", name: "设备+账号解封" }
          ]
        }
      ],
      form: {
        accountStatusEnum: "",
        days: 3,
        description: "",
        descriptionTranslate: "",
        beApprovalUserId: ""
      },
      formRules: {
        description: commonRules,
        days: commonRules,
        accountStatusEnum: commonRules
      },
      accountStatusTypes: []
    };
  },
  computed: {
    ...mapGetters(["buttonPermissions"]),
    accountStatus() {
      const that = this;
      const result = [];
      that.accountStatusGroup.forEach(row => {
        const newRow = deepClone(row);
        result.push(newRow);
        newRow.options = [];
        row.options.forEach(item => {
          if (that.buttonPermissions.includes(`user:account:${item.value}`)) {
            newRow.options.push(item);
            that.accountStatusTypes.push(item);
          }
        });
      });
      return result;
    }
  },
  watch: {
    userId: {
      handler(newVal) {
        if (newVal) {
          this.loadAccountStatus(newVal);
        }
      },
      immediate: true
    }
  },
  methods: {
    loadAccountStatus(userId) {
      const that = this;
      that.loadingAccountStatus = true;
      getAccountStatus(userId)
        .then(res => {
          that.loadingAccountStatus = false;
          that.thisAccountStatus = res.body;
        })
        .catch(() => {
          that.loadingAccountStatus = false;
        });
    },
    handleClose() {
      this.$emit("close");
    },
    selectChanged(value) {
      this.disabledTranslate = false;
      this.form.descriptionTranslate = "";
      if (value === "WARNING") {
        this.form.description =
          "由于您被其他用户多次合理举报，且经平台审查属实。现对您发出警告，请下次务必注意言行，避免账号被冻结或者封禁。";
      } else {
        this.form.description = "";
      }
    },
    submit() {
      const that = this;
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error("error submit!!");
          return false;
        }
        that.form.beApprovalUserId = that.userId;
        that.loading = true;
        accountHandle(that.form)
          .then(res => {
            that.loading = false;
            that.$emit("success", Object.assign({}, that.form));
            that.handleClose();
          })
          .catch(err => {
            that.loading = false;
            that.$emit("fial", err);
            that.handleClose();
          });
      });
    },
    clickTranslate() {
      const that = this;
      if (!that.form.description) {
        return;
      }
      that.loadingTranslate = true;
      translateUserLangContent({
        targetUserId: that.userId,
        content: that.form.description
      })
        .then(res => {
          that.loadingTranslate = false;
          that.form.descriptionTranslate = res.body;
          if (res.status === 3001) {
            that.$opsMessage.fail("相同语言");
            return;
          }
          if (res.status === 3003) {
            that.$opsMessage.fail("没有找到合适的语言");
            return;
          }
          that.disabledTranslate = true;
        })
        .catch(() => {
          that.loadingTranslate = false;
        });
    },
    descriptionKeyup() {
      this.disabledTranslate = false;
      this.form.descriptionTranslate = "";
    }
  }
};
</script>
