<template>
  <div class="add-other-recharge-form">
    <el-dialog
      :title="`添加线下充值(${sysOrigin})`"
      :visible="true"
      width="450px"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <el-alert
        type="info"
        :closable="false"
        style="margin:0px 0px 20px 0px;"
        description="注意: 慎重添加, 不支持撤回..."
      />
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="formData"
        label-width="50px"
        style="width: 350px; margin:auto;"
      >
        <el-form-item label="类型" prop="paymentType">
          <el-select v-model="formData.paymentType" style="width: 100%;">
            <el-option
              v-for="(item, index) in otherPaymentTypes"
              :key="index"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="金额" prop="amount">
          <el-input
            v-model.trim="formData.amount"
            placeholder="请输入充值金额USD"
          />
        </el-form-item>
        <el-form-item label="时间" prop="paymentTime">
          <el-date-picker
            v-model="formData.paymentDateNumber"
            style="width: 100%;"
            value-format="yyyyMMdd"
            type="date"
            placeholder="选择支付日期"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input
            v-model.trim="formData.remarks"
            type="textarea"
            :rows="3"
            resize="none"
            maxlength="150"
            placeholder="备注信息最多150个字符"
          />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose()">
          取消
        </el-button>
        <el-button
          v-loading="listLoading"
          type="primary"
          @click="handleSubmit()"
        >
          提交
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { orderOtherRechargeProcess } from '@/api/purchase'
import { otherPaymentTypes } from '@/constant/type'
export default {
  name: 'AddOtherRecharge',
  props: {
    userId: {
      type: String,
      required: true
    },
    sysOrigin: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      otherPaymentTypes,
      listLoading: false,
      rules: {
        paymentType: [{
          required: true,
          message: '必填字段不可为空',
          trigger: 'blur'
        }],
        amount: [
          {
            required: true,
            message: '必填字段不可为空',
            trigger: 'blur'
          },
          {
            pattern: /^\d{1,7}(\.\d{0,2})?$/,
            message: 'double范围0~9999999小数最多两位',
            trigger: 'blur'
          }
        ],
        paymentDateNumber: [
          {
            required: true,
            message: '必填字段不可为空',
            trigger: 'blur'
          }
        ]
      },
      formData: {
        paymentType: '',
        userId: '',
        amount: '',
        paymentTime: '',
        remarks: ''
      }
    }
  },
  methods: {
    handleSubmit() {
      const that = this
      that.$refs.dataForm.validate((valid) => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        if (!that.userId) {
          that.$opsMessage.fail('获取用户参数错误,请联系管理员!')
          return
        }

        if (!that.sysOrigin) {
          that.$opsMessage.fail('获取系统参数错误,请联系管理员!')
          return
        }
        that.formData.userId = that.userId
        that.formData.sysOrigin = that.sysOrigin
        that.listLoading = true
        orderOtherRechargeProcess(that.formData).then(res => {
          that.$emit('success', Object.assign({}, that.formData))
          that.handleClose()
        }).catch(err => {
          that.listLoading = false
          that.$emit('fial', err)
          that.handleClose()
        })
      })
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
<style lang="scss">
.add-other-recharge-form {
  .el-dialog__body {
    padding: 20px;
  }
}
</style>
