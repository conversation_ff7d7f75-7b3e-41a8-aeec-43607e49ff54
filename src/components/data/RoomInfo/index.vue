<template>
  <div class="room-info-content">
    <div v-loading="roomProfileLoading" class="header-content">
      <div class="avatar">
        <avatar :url="roomDetails.roomCover" size="small" />
      </div>
      <div class="right">
        <div class="text">{{ roomDetails.roomName }}</div>
      </div>
    </div>
    <div class="content-info">
      <el-collapse v-model="nActiveCollapseNames">
        <el-collapse-item title="" name="profileInfo">
          <template slot="title">
            <i class="el-icon-s-custom" />&nbsp;基本资料
          </template>
          <div class="block-info">

            <div v-if="userProfile" class="flex-l">
              <div class="avatar">
                <avatar :url="userProfile.userAvatar" size="mini" />
              </div>
              <div class="right" style="margin-left: 20px;">
                <div class="text">{{ userProfile.userNickname }}</div>
              </div>
            </div>

            <div class="text-item">
              <el-row>
                <el-col :span="12">房间ID：{{ roomDetails.id }}</el-col>
                <el-col :span="12">账号：{{ getAccountText() }}</el-col>
              </el-row>
            </div>
            <div class="text-item">
              <el-row>
                <el-col :span="12">用户ID：{{ roomDetails.userId }}</el-col>
                <el-col :span="12" class="icon-item">国家：
                  <flag-icon class="flag-icon" :code="roomDetails.countryCode" :tooltip="roomDetails.countryName" size="28" />
                </el-col>
              </el-row>
            </div>
            <div class="text-item">
              <el-row>
                <el-col :span="12">状态：
                  <el-tag v-if="roomDetails.event === 'AVAILABLE'" size="mini" type="success">正常</el-tag>
                  <el-tag v-else-if="roomDetails.event === 'ID_CHANGE'" size="mini" type="info">ID变更</el-tag>
                  <el-tag v-else-if="roomDetails.event === 'WAITING_CONFIRMED'" size="mini" type="warning">等待确认</el-tag>
                  <el-tag v-else-if="roomDetails.event === 'CLOSE'" size="mini" type="danger">关闭</el-tag>
                  <el-tag v-else size="mini" type="danger">未知</el-tag>
                </el-col>
                <el-col :span="12">注销：
                  {{ roomDetails.del ? 'Yes' : 'NO' }}
                </el-col>
              </el-row>
            </div>
            <div class="text-item">
              <el-row>
                <el-col :span="12" class="icon-item">平台：
                  <div class="sys-icon">
                    <sys-origin-icon :icon="userProfile.sysOriginChild || userProfile.originSys" :desc="userProfile.originSys || userProfile.sysOriginChild" />
                  </div>
                </el-col>
              </el-row>
            </div>
            <div>
              <div>公告：{{ roomDetails.roomDesc }}</div>
            </div>
            <div class="text-item">
              <el-row>
                <el-col :span="12">
                  创建时间：{{ roomDetails.createTime }}
                </el-col>
                <el-col :span="12">
                  修改时间：{{ roomDetails.updateTime }}
                </el-col>
              </el-row>
            </div>
          </div>
        </el-collapse-item>
        <el-collapse-item title="" name="settingInfo">
          <template slot="title">
            <i class="el-icon-s-opportunity" />&nbsp;设置信息
          </template>
          <div class="block-info">
            <div class="text-item">
              <el-row>
                <el-col :span="12">房间密码：{{ roomDetails.setting.password ? roomDetails.setting.password : '无' }}</el-col>
                <el-col :span="12">加入成员金币：{{ roomDetails.setting.joinGolds || 0 }}</el-col>
              </el-row>
            </div>
            <div class="text-item">
              <el-row>
                <el-col :span="12">成员人数：{{ roomDetails.counter.memberCount }}/{{ roomDetails.setting.maxMember }}</el-col>
                <el-col :span="12">管理员人数：{{ roomDetails.counter.adminCount }}/{{ roomDetails.setting.maxAdmin }}</el-col>
              </el-row>
            </div>
            <div class="text-item">
              <el-row>
                <el-col :span="12">麦克风权限：{{ roomDetails.setting.takeMicRole || '-' }}</el-col>
                <el-col :span="12">麦克风数量：{{ roomDetails.setting.mikeSize || '-' }}</el-col>
              </el-row>
            </div>
            <div class="text-item">
              <el-row>
                <el-col :span="12">游客上麦：{{ roomDetails.setting.touristMike ? '允许' : '不允许' }}</el-col>
                <el-col :span="12">游客发消息：{{ roomDetails.setting.touristMsg? '允许' : '不允许' }}</el-col>
              </el-row>
            </div>
            <div class="text-item">
              <el-row>
                <el-col :span="12">播放音乐：{{ roomDetails.setting.allowMusic ? '允许' : '不允许' }}</el-col>
                <el-col :span="12">管理员锁麦克风：{{ roomDetails.setting.adminLockSeat? '允许' : '不允许' }}</el-col>
              </el-row>
            </div>
            <div class="text-item">
              <el-row>
                <el-col :span="12">显示心动值：{{ roomDetails.setting.showHeartbeat ? '允许' : '不允许' }}</el-col>
              </el-row>
            </div>
          </div>
        </el-collapse-item>
        <!-- <el-collapse-item title="" name="propsInfo">
          <template slot="title">
            <i class="el-icon-s-opportunity" />&nbsp;装饰道具
          </template>
          <div class="block-info">
            <div v-if="roomDetails.useProps && roomDetails.useProps.length > 0" class="text-item flex-l">
              <el-tooltip v-for="(item, index) in roomDetails.useProps" :key="index" class="item" effect="dark">
                <div slot="content">
                  <div v-if="item.propsResources">
                    <div>ID:{{ item.propsResources.id }}</div>
                    <div>道具类型:{{ item.propsResources.type }}</div>
                    <div>道具编号:{{ item.propsResources.code }}</div>
                    <div>道具名称:{{ item.propsResources.name }}</div>
                  </div>
                  <div>过期时间:{{ item.expireTime | dateFormat('yyyy-MM-dd HH:mm:ss') }}</div>
                </div>

                <el-image
                  v-if="item.propsResources"
                  style="width: 50px; height: 50px"
                  :src="item.propsResources.cover"
                  :preview-src-list="[item.propsResources.cover]"
                />
              </el-tooltip>
            </div>
          </div>
        </el-collapse-item> -->
        <el-collapse-item title="" name="badgeInfo">
          <template slot="title">
            <i class="el-icon-s-opportunity" />&nbsp;装饰徽章
          </template>
          <div class="block-info">
            <div v-if="roomDetails.useBadges && roomDetails.useBadges.length > 0" class="text-item flex-l">
              <el-tooltip v-for="(item, index) in roomDetails.useBadges" :key="index" class="item" effect="dark">
                <div slot="content">
                  <div>ID:{{ item.id }}</div>
                  <div>徽章名称:{{ item.badgeName }}</div>
                  <div>徽章类型:{{ item.type }}</div>
                  <div>徽章KEY:{{ item.badgeKey }}</div>
                  <div>过期时间:{{ item.expireTime | dateFormat('yyyy-MM-dd HH:mm:ss') }}</div>
                </div>
                <el-image
                  style="width: 50px; height: 50px"
                  :src="item.selectUrl"
                  :preview-src-list="[item.selectUrl]"
                />
              </el-tooltip>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>
<script>
import { getRoomProfileDetailsByRoomId } from '@/api/room'
import Avatar from '@/components/data/Avatar'
export default {
  name: 'RoomBaseInfo',
  components: { Avatar },
  props: {
    roomId: {
      type: String,
      required: true
    },
    activeCollapseNames: {
      type: Array,
      required: false,
      default: () => ['settingInfo', 'profileInfo']
    }
  },
  data() {
    return {
      nActiveCollapseNames: [],
      roomProfileLoading: true,
      roomDetails: {},
      userProfile: {},
      roomSettingInfo: {}
    }
  },
  watch: {
    roomId: {
      immediate: true,
      handler(newVal) {
        this.renderData(newVal)
      }
    },
    activeCollapseNames: {
      immediate: true,
      handler(newVal) {
        this.nActiveCollapseNames = newVal
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    getAccountText() {
      const account = this.userProfile.account
      if (this.userProfile.ownSpecialId && this.userProfile.ownSpecialId.account) {
        return `${account} / ${this.userProfile.ownSpecialId.account}靓`
      }
      return account
    },
    renderData(roomId) {
      const that = this
      that.roomProfileLoading = true
      getRoomProfileDetailsByRoomId(roomId).then(res => {
        that.roomProfileLoading = false
        const { body } = res
        that.roomDetails = body.profile || {}
        that.userProfile = body.userProfile || {}
      }).catch(er => {
        that.roomProfileLoading = false
        that.$emit('roomProfile')
      })
    }
  }
}
</script>
<style scoped lang="scss">
.room-info-content {
    .header-content {
         display: flex;
         justify-content: flex-start;
         .right {
           padding: 24px 0px 0px 20px;
         }
    }
    .block-info {
        position: relative;
        .text-item {
          height: 36px;
          line-height: 36px;
          font-size: 14px;
          white-space:nowrap;
          overflow:hidden;
          text-overflow:ellipsis;
        }
   }
   .icon-item {
    position: relative;
      .flag-icon {
        display: inline;
        position: absolute;
        bottom: -8px;
      }
      .sys-icon {
        display: inline;
        position: absolute;
        bottom: -3px;
      }
   }

}
</style>
