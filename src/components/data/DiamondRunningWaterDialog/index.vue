<template>
  <div class="diamond-running-water-drawer">
    <el-dialog
      title="用户钻石流水"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      width="80%"
    >
      <div class="diamond-running-water-content">
        <el-table
          v-loading="listLoading"
          :data="list"
          element-loading-text="Loading"
          fit
          highlight-current-row
        >
          <el-table-column prop="balance" label="钻石余额" align="center" min-width="100" />
          <el-table-column prop="quantity" label="钻石" align="center" min-width="100" />
          <el-table-column prop="origin" label="来源" align="center" min-width="100" />
          <el-table-column prop="originDesc" label="来源描述" align="center" min-width="100" />
          <el-table-column prop="typeDesc" label="类型" align="center" min-width="100" />
          <el-table-column prop="remarks" label="备注" align="center" min-width="100" />
          <el-table-column
            prop="createTime"
            label="创建时间"
            align="center"
            width="200"
          >
            <template slot-scope="scope">
              {{ scope.row.createTime | dateFormat }}
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="listQuery.cursor"
          :limit.sync="listQuery.limit"
          @pagination="renderData"
        />

      </div>
    </el-dialog>
  </div>
</template>
<script>
import { pageUserDiamondRunWater } from '@/api/app-user'
import Pagination from '@/components/Pagination'
export default {
  name: 'RunningWaterPreviewTabs',
  components: { Pagination },
  props: {
    userId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      listLoading: false,
      list: [],
      total: 0,
      listQuery: {
        userId: '',
        lastId: ''
      },
      notMore: false
    }
  },
  watch: {
    userId: {
      handler(newVal) {
        if (newVal) {
          this.listQuery.userId = newVal
          this.renderData(true)
        }
      },
      immediate: true
    }
  },
  methods: {
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      pageUserDiamondRunWater(that.listQuery)
        .then(res => {
          const { body } = res
          that.total = body.total || 0
          that.list = body.records
          that.listLoading = false
        })
        .catch(er => {
          that.listLoading = false
          console.error(er)
        })
    },
    handleClose() {
      this.$emit('close')
    },
    clickLoadMore() {
      this.renderData()
    }
  }
}
</script>
<style scoped lang="scss">
.gold-running-water-content {
  max-height: 450px;
  overflow: auto;
}
</style>
