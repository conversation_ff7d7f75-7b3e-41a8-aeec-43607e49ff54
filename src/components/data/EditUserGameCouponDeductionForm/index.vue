<template>
  <div class="edit-user-gold-coin-deduction-form">
    <el-dialog
      title="扣除用户游戏券"
      :visible="true"
      width="450px"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="formData"
        label-position="left"
        label-width="70px"
        style="width: 300px; margin-left:50px;"
      >
        <el-form-item label="游戏券" prop="coupon">
          <el-input
            v-model.trim="formData.coupon"
            placeholder="需要扣除的游戏券数量"
          />
        </el-form-item>

        <el-form-item label="备注" prop="remarks">
          <el-input
            v-model.trim="formData.remarks"
            placeholder="扣除游戏券的原因"
          />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose()">
          取消
        </el-button>
        <el-button
          v-loading="listLoading"
          type="primary"
          @click="handleSubmit()"
        >
          提交
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { deductGameCoupon } from '@/api/app-user'

export default {
  name: 'EditUserGameCouponRewardForm',
  props: {
    userId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      listLoading: false,
      rules: {
        coupon: [
          {
            required: true,
            message: '请输入金额',
            trigger: 'blur'
          },
          {
            pattern: /^\d{1,7}(\.\d{0,2})?$/,
            message: 'double范围0~9999999小数最多两位',
            trigger: 'blur'
          }
        ]
      },
      formData: {
        coupon: '',
        remarks: ''
      }
    }
  },
  methods: {
    handleSubmit() {
      const that = this
      that.$refs.dataForm.validate((valid) => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.listLoading = true
        deductGameCoupon(that.userId, that.formData.coupon, that.formData.remarks).then(res => {
          that.listLoading = false
          that.$emit('success', Object.assign({}, that.formData))
          that.handleClose()
        }).catch(err => {
          that.listLoading = false
          that.$emit('fial', err)
          that.handleClose()
        })
      })
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
