<template>
  <div class="user-details-drawer">
    <el-drawer
      title="用户详情"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div v-if="buttonPermissions.includes('user:profile:panel:query')" class="user-deatils-content">
        <div class="user-info">
          <base-info :user-id="userId" @baseInfoSucces="handleBaseInfoSuccess" />
        </div>
        <!-- <div class="user-tag">
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane v-for="item in tabs" :key="item.name" :label="item.label" :name="item.component" />
            <component :is="activeName" :user-id="userId" />
          </el-tabs>
        </div> -->
      </div>
      <div v-else style="padding: 20px;">权限不足,请联系管理员授权: 组件权限/用户资料面板/查看(user:profile:panel:query)</div>
    </el-drawer>
  </div>
</template>
<script>
import BaseInfo from '@/components/data/UserInfo/BaseInfo'
import { mapGetters } from 'vuex'
// import PhotoWall from '@/components/data/UserInfo/PhotoWall'
export default {
  name: 'UserDeatilsDrawer',
  components: { BaseInfo
  // PhotoWall
  },
  props: {
    userId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      activeName: 'PhotoWall',
      baseInfo: {},
      shortVideoVisiable: false,
      tabs: [
        { component: 'PhotoWall', label: '照片墙' }
      ]
    }
  },
  computed: {
    ...mapGetters(['buttonPermissions'])
  },
  watch: {
    activeName(newValue) {
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    handleClick() {

    },
    handleBaseInfoSuccess(data) {
      this.baseInfo = data
    }
  }
}
</script>
<style scoped lang="scss">
.user-deatils-content {
    padding: 0px 10px;
  .el-tabs__nav-scroll .el-tabs__item {
    font-size: 13px;
    color: #303133;
  }
}
</style>
