<template>
  <div class="edit-user-form">
    <el-dialog
      title="编辑用户信息"
      :visible="true"
      width="450px"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <el-form
        ref="dataForm"
        v-loading="listLoading"
        :rules="rules"
        :model="formData"
        label-position="left"
        label-width="70px"
        style="width: 300px; margin-left:50px;"
      >
        <el-form-item label="昵称" prop="userNickname">
          <el-input
            v-model.trim="formData.userNickname"
            placeholder="请输入用户昵称"
          />
        </el-form-item>

        <el-form-item label="性别" prop="userSex">
          <el-select v-model="formData.userSex" placeholder="请选择性别" style="width: 100%;">
            <el-option
              v-for="item in genders"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item :label="'年龄('+(formData.age || 0)+')'" prop="email">
          <el-date-picker
            v-model="formAgeDate"
            style="width: 100%;"
            type="date"
            placeholder="选择出生日期"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>

        <el-form-item label="国家" prop="countryId">
          <el-select
            v-model="formData.countryId"
            placeholder="请选择国家"
            filterable
            :filter-method="userFilter"
            clearabl
          >
            <el-option
              v-for="item in countryList"
              :key="item.id"
              :label="item.enName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose()">
          取消
        </el-button>
        <el-button
          type="primary"
          @click="handleSubmit()"
        >
          提交
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { genders } from '@/constant/user'
import { getUserBaseInfo, updateVestBaseInfo, getCountryAlls } from '@/api/sys'
import { formatDate, getAge } from '@/utils'

export default {
  name: 'EditUserForm',
  props: {
    userId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      genders,
      allCountryList: [],
      countryList: [],
      listLoading: false,
      rules: {
        userNickname: [
          {
            required: true,
            message: '用户昵称必填',
            trigger: 'blur'
          }
        ],
        userSex: [{ required: true, message: '性别必填', trigger: 'blur' }],
        countryId: [{ required: true, message: '国家必填', trigger: 'blur' }]
      },
      formData: {
        userNickname: '',
        userSex: '',
        age: '',
        bornYear: '',
        bornMonth: '',
        bornDay: '',
        countryId: ''
      },
      formAgeDate: ''
    }
  },
  watch: {
    userId: {
      handler(newVal) {
        if (newVal) {
          this.getUserWhiteList()
          this.renderData()
        }
      },
      immediate: true
    },
    formAgeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.formData.bornYear = formatDate(newVal, 'yyyy')
          this.formData.bornMonth = formatDate(newVal, 'MM')
          this.formData.bornDay = formatDate(newVal, 'dd')
          this.formData.age = getAge(newVal)
          return
        }
      }
    }
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      getUserBaseInfo(that.userId).then(res => {
        that.listLoading = false
        Object.assign(that.formData, res.body)
        if (that.formData.bornYear && that.formData.bornMonth && that.formData.bornDay) {
          that.formAgeDate = that.formData.bornYear + '-' + that.formData.bornMonth + '-' + that.formData.bornDay
        }
      }).catch(er => {
        that.listLoading = false
      })
    },
    getUserWhiteList() {
      const that = this
      getCountryAlls().then(res => {
        that.listLoading = false
        this.allCountryList = res.body || {}
        this.userFilter()
      }).catch(er => {
        that.listLoading = false
      })
    },
    userFilter(query = '') {
      const arr = this.allCountryList.filter((item) => {
        return item.enName.includes(query) || item.id.includes(query)
      })
      if (arr.length > 50) {
        this.countryList = arr.slice(0, 50)
      } else {
        this.countryList = arr
      }
    },
    handleSubmit() {
      const that = this
      that.$refs.dataForm.validate((valid) => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.listLoading = true
        updateVestBaseInfo(that.formData).then(res => {
          that.listLoading = false
          that.$emit('success', Object.assign({}, that.formData))
          that.handleClose()
        }).catch(err => {
          that.listLoading = false
          that.$emit('fial', err)
          that.handleClose()
        })
      })
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
