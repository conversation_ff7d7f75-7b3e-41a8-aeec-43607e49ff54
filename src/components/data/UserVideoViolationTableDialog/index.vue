<!--违规记录表格-->
<template>

  <el-dialog
    title="违规记录"
    :visible="true"
    width="70%"
    :before-close="handleClose"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <div class="user_info">
      <avatar :url="row.userAvatar" size="small" />
      <div class="info">
        <span>用户昵称：{{ row.userNickname }}</span>

        <span>身份类型：{{ row.vipStatusName }}</span>

        <!-- <span>累计违规次数：{{ row.violationSize }}</span>-->
      </div>
    </div>

    <div class="content">
      <not-pass-history :user-id="row.id" />
    </div>
  </el-dialog>

</template>

<script>

import Avatar from '@/components/data/Avatar'
import NotPassHistory from '@/views/approval/not-pass-history'
export default {
  components: { Avatar, NotPassHistory },
  props: {
    row: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: false
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>

<style scoped lang='scss'>
  .user_info {
    position: relative;
    top: -20px;
    .info {
      position: absolute;
      top: 20px;
      left: 60px;
      span {
        padding: 0px 10px;
      }
    }
  }
  .content {
    max-height: 450px;
    overflow: auto;
  }
</style>
