<template>
  <div class="edit-user-game-coupon-reward-form">
    <el-dialog
      title="奖励用户游戏券"
      :visible="true"
      width="450px"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="formData"
        label-position="left"
        label-width="70px"
        style="width: 300px; margin-left:50px;"
      >

        <el-form-item label="类型" prop="rewardType">
          <el-select v-model="formData.rewardType" placeholder="请选择">
            <el-option
              v-for="item in currencyRewardReasons"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="游戏券" prop="coupon">
          <el-input
            v-model.trim="formData.coupon"
            placeholder="需要奖励的游戏券数量"
          />
        </el-form-item>

        <el-form-item label="备注" prop="remarks">
          <el-input
            v-model.trim="formData.remarks"
            placeholder="奖励备注"
          />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose()">
          取消
        </el-button>
        <el-button
          v-loading="listLoading"
          type="primary"
          @click="handleSubmit()"
        >
          提交
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { currencyRewardReasons } from '@/constant/type'
import { rewardGameCoupon } from '@/api/app-user'

export default {
  name: 'EditUserGameCouponRewardForm',
  props: {
    userId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      listLoading: false,
      currencyRewardReasons,
      rules: {
        coupon: [
          {
            required: true,
            message: '请输入游戏券数量',
            trigger: 'blur'
          },
          {
            pattern: /^\d{1,7}(\.\d{0,2})?$/,
            message: 'double范围0~9999999小数最多两位',
            trigger: 'blur'
          }
        ],
        rewardType: [
          {
            required: true,
            message: '请选择类型',
            trigger: 'blur'
          }
        ]
      },
      formData: {
        coupon: '',
        rewardType: '',
        remarks: ''
      }
    }
  },
  methods: {
    handleSubmit() {
      const that = this
      that.$refs.dataForm.validate((valid) => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.listLoading = true
        rewardGameCoupon(that.userId, that.formData.coupon, that.formData.remarks, that.formData.rewardType).then(res => {
          that.$emit('success', Object.assign({}, that.formData))
          that.handleClose()
        }).catch(err => {
          that.listLoading = false
          that.$emit('fial', err)
          that.handleClose()
        })
      })
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
