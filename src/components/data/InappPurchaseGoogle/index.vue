<template>
  <div>
    <el-dialog
      v-loading="loading"
      title="Google 内购订阅"
      :visible="true"
      width="60%"
      :before-close="handleClose"
      top="50px"
    >
      <div class="sub-block">
        <el-timeline :hide-timestamp="true">
          <el-timeline-item :type="reimburseDot">
            <h3>购买记录(站内)</h3>
            <el-card>
              <div class="typesetting">
                <div class="line">
                  <div class="label">环境：{{ purchaseHistory.evn }}</div>
                  <div class="label">原事务ID：{{ purchaseHistory.originalOrderId }}</div>
                  <div class="label">过期时间：{{ purchaseHistory.expiresDateMs }}</div>
                </div>

                <div class="line">
                  <div class="label">来源平台：{{ purchaseHistory.platform }}</div>
                  <div class="label">事务ID：{{ purchaseHistory.orderId }}</div>
                  <div class="label">购买时间：{{ purchaseHistory.purchaseDateMs }}</div>
                </div>

                <div class="line">
                  <div class="label">购买事件：{{ purchaseHistory.event }}</div>
                  <div class="label">单价：{{ purchaseHistory.unitPrice }} ({{ purchaseHistory.unit }})</div>
                  <div class="label">创建时间：{{ purchaseHistory.createTime }}</div>
                </div>

                <div class="line">
                  <div class="label">站内产品ID：{{ purchaseHistory.purchaseProductId }}</div>
                  <div class="label">订单状态：{{ purchaseHistory.status }}</div>
                  <div class="label">修改时间：{{ purchaseHistory.updateTime }}</div>
                </div>
                <div class="line">
                  <div class="label">APPLE产品ID：{{ purchaseHistory.productId }}</div>
                  <div class="label">产品类型：{{ purchaseHistory.productType }}</div>
                  <div class="label" />
                </div>
              </div>
            </el-card>
          </el-timeline-item>
          <el-timeline-item v-if="google.latestSubscription">
            <h3>订阅单据(Google)</h3>
            <el-card>
              <div class="typesetting">
                <div class="line">
                  <div class="label">授予订阅的时间：{{ dateFormat(latestSubscription.startTimeMillis) }}</div>
                  <div class="label">自动续费状态：{{ autoRenewStatusName }}</div>
                  <div class="label">订单ID：{{ latestSubscription.orderId }}</div>
                </div>

                <div class="line">
                  <div class="label">订阅到期的时间：{{ dateFormat(latestSubscription.expiryTimeMillis) }} </div>
                  <div class="label">订阅的购买类型：{{ latestSubscription.purchaseType }}</div>
                  <div class="label">付款状：{{ latestSubscriptionPaymentStateName }}</div>
                </div>
                <div class="line">
                  <div class="label">创建时间：{{ latestSubscription.createTime }}</div>
                  <div class="label">付款国家CODE：{{ latestSubscription.countryCode }}</div>
                  <div class="label">令牌：<el-button type="text" @click="copyReceipt(latestSubscription.linkedPurchaseToken)">点击复制</el-button></div>
                </div>
                <div class="line">
                  <div class="label">修改时间：{{ latestSubscription.updateTime }}</div>
                  <div class="label">订阅被取消：{{ latestSubscription.cancelReason }}
                    <el-tooltip class="item" effect="dark">
                      <div slot="content">
                        <p>0.用户取消了订阅</p>
                        <p>1.例如由于计费问题，系统取消了订阅</p>
                        <p>2.用新订阅替换了订阅</p>
                        <p>3.开发者取消了订阅</p>
                      </div>
                      <i class="el-icon-question" />
                    </el-tooltip>
                  </div>

                  <div class="label">json结构：<el-button type="text" @click="copyReceipt(latestSubscription.lastJson)">点击复制</el-button></div>
                </div>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getGoogleReceiptDetails } from '@/api/purchase'
import { copyText, formatDate } from '@/utils'
export default {
  name: 'InappPurchaseGoogle',
  props: {
    id: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      loading: true,
      google: {}
    }
  },
  computed: {
    purchaseHistory() {
      return this.google.purchaseHistory || {}
    },
    latestPurchase() {
      return this.google.latestPurchase || {}
    },
    latestSubscription() {
      return this.google.latestSubscription || {}
    },
    reimburseDot() {
      return this.latestReceipt && this.google.cancellationDateMs ? 'danger' : ''
    },
    autoRenewStatusName() {
      return this.latestSubscription.autoRenewing === true ? '订阅中' : '已取消'
    },
    latestSubscriptionPaymentStateName() {
      if (this.latestSubscription.paymentState === 0) {
        return '待付款'
      }
      if (this.latestSubscription.paymentState === 1) {
        return '收到付款'
      }
      if (this.latestSubscription.paymentState === 2) {
        return '免费试用'
      }
      if (this.latestSubscription.paymentState === 3) {
        return '待推迟的升级/降级'
      }
      return '异常'
    }
  },
  created() {
    const that = this
    getGoogleReceiptDetails(that.id).then(res => {
      const { body } = res
      that.google = body || {}
      that.loading = false
    }).catch(er => {
      that.loading = false
    })
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    copyReceipt(content) {
      if (!content) {
        this.$opsMessage.info('内容为空')
        return
      }
      copyText(content).then(() => {
        this.$opsMessage.success()
      }).catch(er => {
        this.$opsMessage.fail()
      })
    },
    dateFormat(date) {
      return formatDate(date)
    }
  }
}
</script>
<style scoped lang="scss">
.sub-block {
    max-height: 600px;
    overflow: auto;
}
.typesetting {
    color: #666;
    line-height: 30px;
    .line {
        display: flex;
        .label {
            width: 100%;
            overflow: hidden;
            text-overflow:ellipsis;
            white-space: nowrap;
        }
    }
}
</style>
