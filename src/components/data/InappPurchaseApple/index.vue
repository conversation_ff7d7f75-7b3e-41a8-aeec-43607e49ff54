<template>
  <div>
    <el-dialog
      v-loading="loading"
      title="Apple 内购订阅"
      :visible="true"
      width="60%"
      :before-close="handleClose"
      top="50px"
    >
      <div class="sub-block">
        <el-timeline :hide-timestamp="true">
          <el-timeline-item :type="reimburseDot">
            <h3>购买记录(站内)</h3>
            <el-card>
              <div class="typesetting">
                <div class="line">
                  <div class="label">环境：{{ purchaseHistory.evn }}</div>
                  <div class="label">原事务ID：{{ purchaseHistory.originalOrderId }}</div>
                  <div class="label">过期时间：{{ purchaseHistory.expiresDateMs }}</div>
                </div>

                <div class="line">
                  <div class="label">来源平台：{{ purchaseHistory.platform }}</div>
                  <div class="label">事务ID：{{ purchaseHistory.orderId }}</div>
                  <div class="label">购买时间：{{ purchaseHistory.purchaseDateMs }}</div>
                </div>

                <div class="line">
                  <div class="label">购买事件：{{ purchaseHistory.event }}</div>
                  <div class="label">单价：{{ purchaseHistory.unitPrice }} ({{ purchaseHistory.unit }})</div>
                  <div class="label">创建时间：{{ purchaseHistory.createTime }}</div>
                </div>

                <div class="line">
                  <div class="label">站内产品ID：{{ purchaseHistory.purchaseProductId }}</div>
                  <div class="label">订单状态：{{ purchaseHistory.status }}</div>
                  <div class="label">修改时间：{{ purchaseHistory.updateTime }}</div>
                </div>
                <div class="line">
                  <div class="label">APPLE产品ID：{{ purchaseHistory.productId }}</div>
                  <div class="label">产品类型：{{ purchaseHistory.productType }}</div>
                  <div class="label" />
                </div>
              </div>
            </el-card>
          </el-timeline-item>
          <el-timeline-item>
            <h3>等待续费订阅(Apple)</h3>
            <el-card>
              <div class="typesetting">
                <div class="line">
                  <div class="label">原事务ID：{{ pendingRenewalInfo.originalTransactionId }}</div>
                  <div class="label">产品ID：{{ pendingRenewalInfo.productId }}</div>
                  <div class="label">自动续费产品ID：{{ pendingRenewalInfo.autoRenewProductId }}</div>
                </div>

                <div class="line">
                  <div class="label">自动续费状态：{{ autoRenewStatusName }}</div>
                  <div class="label">
                    努力续费中：{{ inBillingRetryPeriodName }}
                    <el-tooltip class="item" effect="dark" content="可能由于计费周期问题，Apple正在努力尝试自动订阅中">
                      <i class="el-icon-question" />
                    </el-tooltip>
                  </div>
                  <div class="label">续订宽限到期时间：{{ pendingRenewalInfo.gracePeriodExpiresDateMs }}</div>
                </div>

                <div class="line">
                  <div class="label">订阅过期原因：{{ pendingRenewalInfo.expirationIntent }}
                    <el-tooltip class="item" effect="dark">
                      <div slot="content">
                        <p>1.客户自愿取消订阅</p>
                        <p>2.帐单错误；例如，客户的付款信息不再有效</p>
                        <p>3.客户不同意最近涨价</p>
                        <p>4.产品更新时无法购买</p>
                        <p>5.未知错误</p>
                      </div>
                      <i class="el-icon-question" />
                    </el-tooltip>
                  </div>
                </div>
              </div>
            </el-card>
          </el-timeline-item>
          <el-timeline-item>
            <h3>单据信息(Apple)</h3>
            <el-card>
              <div class="typesetting">
                <div class="line">
                  <div class="label">取消时间：{{ latestReceipt.cancellationDateMs }}
                    <el-tooltip class="item" effect="dark" content="此字段仅适用于已退款的交易。">
                      <i class="el-icon-question" />
                    </el-tooltip>
                  </div>
                  <div class="label">产品ID：{{ latestReceipt.productId }}</div>
                  <div class="label">到期时间：{{ latestReceipt.expiresDateMs }}</div>
                </div>

                <div class="line">
                  <div class="label">取消原因：{{ latestReceipt.cancellationReason }}
                    <el-tooltip class="item" effect="dark">
                      <div slot="content">
                        <p> 0) 表示交易因其他原因被取消；例如，如果客户是意外购买的。</p>
                        <p> 1) 表示客户因应用程序中的实际或感知问题而取消了交易。</p>
                      </div>
                      <i class="el-icon-question" />
                    </el-tooltip>
                  </div>

                  <div class="label">原事务ID：{{ latestReceipt.transactionId }}</div>
                  <div class="label">原购买时间：{{ dateFormat(latestReceipt.originalPurchaseDateMs) }}</div>
                </div>

                <div class="line">
                  <div class="label">所属订阅组：{{ latestReceipt.subscriptionGroupIdentifier }}</div>
                  <div class="label">事务ID：{{ latestReceipt.transactionId }}</div>
                  <div class="label">购买时间：{{ latestReceipt.purchaseDateMs }}</div>
                </div>

                <div class="line">
                  <div class="label">网格列ID：{{ latestReceipt.webOrderLineItemId }}</div>
                  <div class="label">创建时间：{{ latestReceipt.createTime }}</div>
                  <div class="label">修改时间：{{ latestReceipt.updateTime }}</div>
                </div>
                <div class="line">
                  <div class="label">免费试用：{{ latestReceipt.trialPeriod === true ? '是' : '否' }}</div>
                  <div class="label"><el-button type="text" @click="openUrl('status')">收据状态</el-button>：{{ latestReceipt.status }}
                    <el-tooltip class="item" effect="dark">
                      <div slot="content">
                        <p> 如果收据有效，则为0；如果有错误，则为状态码。  </p>
                        <p> 状态代码反映了整个应用收据的状态。</p>
                        <p> 点击标题查看详细信息</p>
                      </div>
                      <i class="el-icon-question" />
                    </el-tooltip>
                  </div>
                  <div class="label">Base64单据：<el-button type="text" @click="copyReceipt($event)">点击复制</el-button></div>
                </div>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getAppleReceiptDetails } from '@/api/purchase'
import { copyText, formatDate } from '@/utils'
export default {
  name: 'InappPurchaseApple',
  props: {
    id: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      loading: true,
      apple: {}
    }
  },
  computed: {
    inBillingRetryPeriodName() {
      return this.apple.pendingRenewalInfo && this.apple.pendingRenewalInfo.inBillingRetryPeriod === true ? '是' : '否'
    },
    purchaseHistory() {
      return this.apple.purchaseHistory || {}
    },
    latestReceipt() {
      return this.apple.latestReceipt || {}
    },
    pendingRenewalInfo() {
      return this.apple.pendingRenewalInfo || {}
    },
    reimburseDot() {
      return this.latestReceipt && this.apple.cancellationDateMs ? 'danger' : ''
    },
    autoRenewStatusName() {
      return this.pendingRenewalInfo.autoRenewStatus === true ? '订阅中' : '已取消'
    }
  },
  created() {
    const that = this
    getAppleReceiptDetails(that.id).then(res => {
      const { body } = res
      that.apple = body || {}
      that.loading = false
    }).catch(er => {
      that.loading = false
    })
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    copyReceipt() {
      copyText(this.latestReceipt.latestReceipt).then(() => {
        this.$opsMessage.success()
      }).catch(er => {
        this.$opsMessage.fail()
      })
    },
    openUrl(type) {
      if (type === 'status') {
        window.open('https://developer.apple.com/documentation/appstorereceipts/status')
      }
    },
    dateFormat(date) {
      return formatDate(date)
    }
  }
}
</script>
<style scoped lang="scss">
.sub-block {
    max-height: 600px;
    overflow: auto;
}
.typesetting {
    color: #666;
    line-height: 30px;
    .line {
        display: flex;
        .label {
            width: 100%;
            overflow: hidden;
            text-overflow:ellipsis;
            white-space: nowrap;
        }
    }
}
</style>
