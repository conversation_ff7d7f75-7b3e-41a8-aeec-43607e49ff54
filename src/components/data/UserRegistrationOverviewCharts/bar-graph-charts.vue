<template>
  <div class="register-logout-count-charts">
    <div id="charts" ref="charts" :style="'width: 100%;height:'+ height +';'" />
  </div>
</template>

<script>
import { copyText } from '@/utils'
export default {
  props: {
    height: {
      type: String,
      default: '600px'
    },
    chartsData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      charts: null
    }
  },
  watch: {
    chartsData: {
      immediate: true,
      handler(newVal) {
        if (this.charts) {
          this.charts.clear()
          this.renderCharts()
        }
      }
    }
  },
  created() {
  },
  mounted() {
    const that = this
    that.$nextTick(() => {
      that.charts = that.$echarts.init(that.$refs.charts)
      that.renderCharts()
      window.addEventListener('resize', () => {
        that.charts.resize()
      })
      that.charts.on('click', param => {
        copyText(param.name).then(() => {
          this.$opsMessage.success()
        }).catch(er => {
          this.$opsMessage.fail()
        })
      })
    })
  },
  methods: {
    newSeries(name, data) {
      return {
        name,
        type: 'bar',
        stack: name,
        smooth: 0.6,
        symbol: 'none',
        symbolSize: 10,
        data,
        areaStyle: {},
        barWidth: 40
      }
    },
    newRich(imageUrl) {
      return {
        height: 40,
        align: 'center',
        backgroundColor: {
          image: imageUrl
        }
      }
    },
    proccessData() {
      const that = this
      console.log(2222);
      const charts = {
        xAxisData: [],
        seriesData: [],
        richData: {
          value: {
            lineHeight: 30,
            align: 'center'
          }
        }
      }
      console.log(that.chartsData);
      if (that.chartsData.length > 0) {
        that.chartsData.forEach(item => {
          //const propsSource = item.sysOrigin
          console.log(item.sysOrigin);
          console.log(item.logo);
          charts.xAxisData.push(item.sysOrigin)
          charts.seriesData.push([item.sysOrigin, item.saleQuantity || 0])
          charts.richData[item.sysOrigin] = that.newRich(item.logo)
        })
      }
      return charts
    },
    renderCharts() {
      const that = this
      console.log(1111);
      const proccessChartsData = that.proccessData()
      console.log(proccessChartsData);
      that.charts.setOption({
        color: ['#66b3ff', '#ce90e8', '#ff9c6e', '#5cdbd3'],
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(50,50,50,0.5)',
          axisPointer: {
            type: 'cross',
            label: {
              lineStyle: { color: '#009688' },
              crossStyle: { color: '#008acd' },
              shadowStyle: { color: 'rgba(200,200,200,0.2)' }
            }
          },
          formatter: function(val) {
            return `数量: ${val[0].data[1]}`
          }
        },
        legend: {
          show: false,
          textStyle: { color: '#8e929b' }
        },
        grid: {
          x: 20,
          y: 50,
          x2: 20,
          y2: 30,
          containLabel: true,
          borderColor: '#eee'
        },
        toolbox: { color: ['#1e90ff', '#1e90ff', '#1e90ff', '#1e90ff'], effectiveColor: '#ff4500' },
        xAxis: [
          {
            show: true,
            type: 'category',
            data: proccessChartsData.xAxisData || [],
            splitArea: {
              show: true,
              areaStyle: { color: ['rgba(250,250,250,0.1)', 'rgba(200,200,200,0.1)'] }
            },
            axisLine: { lineStyle: { color: '#b7bdc7' }},
            splitLine: { lineStyle: { color: ['#eee'] }},
            axisLabel: {
              interval: 0,
              formatter: function(value) {
                return '{' + value + '| }'
              },
              rich: proccessChartsData.richData || {}
            }
          }
        ],
        yAxis: [{
          type: 'value',
          axisTick: { show: true, length: 0 },
          splitNumber: 5,
          splitLine: { lineStyle: { color: ['#eee'] }},
          axisLine: { lineStyle: { color: '#b7bdc7' }}
        }],
        series: {
          type: 'bar',
          smooth: 0.6,
          symbol: 'none',
          symbolSize: 10,
          data: proccessChartsData.seriesData || [],
          areaStyle: {},
          barWidth: 40
        }

      }, true)
    }
  }
}
</script>
