<template>
  <div class="props-config-edit">
    <el-dialog
      title="用户注册情况"
      :visible="true"
      :before-close="handleClose"
      :close-on-click-modal="false"
      width="80%"
    >
      <user-registration-overview-charts height="300px" />
    </el-dialog>
  </div>
</template>
<script>
import UserRegistrationOverviewCharts from '@/components/data/UserRegistrationOverviewCharts'
export default {
  components: { UserRegistrationOverviewCharts },
  data() {
    return {}
  },
  methods: {
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
<style scoped lang="scss">

</style>
