<template>
  <div class="gold-running-water-drawer">
    <el-dialog
      title="用户金币流水"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      width="80%"
    >
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :clearable="false"
          @change="renderData"
        />
      </div>
      <div class="gold-running-water-content">
        <el-alert type="warning" :closable="false">
          注意: 数据存在生命周期, 最多查看最近1年数据
        </el-alert>
        <el-table
          v-loading="listLoading"
          :data="list"
          fit
          highlight-current-row
        >
          <el-table-column
            label="系统"
            align="center"
            show-overflow-tooltip
            min-width="80"
          >
            <template slot-scope="scope">
              <sys-origin-icon
                :icon="scope.row.runningWater.sysOrigin"
                :desc="scope.row.runningWater.sysOrigin"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="类型"
            align="center"
            show-overflow-tooltip
            min-width="80"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.runningWater.type === 0">收入</span>
              <span v-else-if="scope.row.runningWater.type === 1">支出</span>
              <span v-else>未知</span>
            </template>
          </el-table-column>
          <el-table-column
            label="金币"
            align="center"
            show-overflow-tooltip
            min-width="120"
          >
            <template slot-scope="scope">
              <span
                v-if="scope.row.runningWater.type === 0"
                class="font-danger"
              >+{{ scope.row.runningWater.quantity }}</span>
              <span
                v-else-if="scope.row.runningWater.type === 1"
              >-{{ scope.row.runningWater.quantity }}</span>
              <span v-else>{{ scope.row.runningWater.quantity }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="余额"
            prop="runningWater.balance"
            align="center"
            show-overflow-tooltip
            min-width="120"
          />
          <el-table-column
            label="来源"
            prop="runningWater.originName"
            align="center"
            show-overflow-tooltip
            min-width="300"
          >
            <template slot-scope="scope">
              {{ scope.row.runningWater.originName }}
              <span
                v-if="scope.row.runningWater.remark"
              >({{ scope.row.runningWater.remark }})</span>
              <span
                v-if="scope.row.backOperationName"
              >、{{ scope.row.backOperationName }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="创建时间"
            prop="runningWater.createTime"
            align="center"
            show-overflow-tooltip
            width="200"
          >
            <template slot-scope="scope">
              {{ scope.row.runningWater.createTime | dateFormat }}
            </template>
          </el-table-column>
        </el-table>
        <div
          v-if="list && list.length > 0"
          style="text-align: center; margin-top:20px;"
        >
          <el-button
            v-if="!notMore"
            size="mini"
            :disabled="listLoading"
            @click="clickLoadMore"
          >加载更多</el-button>
          <span v-else>已加载全部</span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { listGoldRunningWater } from '@/api/purchase'
import { beforeDateObject, datePlusDays, getMonthLastDate } from '@/utils'
export default {
  name: 'RunningWaterPreviewTabs',
  props: {
    userId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      listLoading: false,
      list: [],
      notProccessRangeDate: false,
      beforeIntervalDays: 1,
      rangeDate: [],
      listQuery: {
        userId: '',
        lastId: '',
        startTime: '',
        endTime: ''
      },
      notMore: false,
      pickerOptions: {
        disabledDate(date) {
          return date.getTime() > datePlusDays(new Date(), 1).getTime()
        }
      }
    }
  },
  watch: {
    userId: {
      handler(newVal) {
        if (newVal) {
          this.listQuery.userId = newVal
          this.renderData()
        }
      },
      immediate: true
    },
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (this.notProccessRangeDate === true) {
          this.notProccessRangeDate = false
          return
        }
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.notProccessRangeDate = true
        const defaultRangeTime = this.defaultRangeTimestamp()
        this.rangeDate = defaultRangeTime
        this.listQuery.startTime = defaultRangeTime[0]
        this.listQuery.endTime = defaultRangeTime[1]
      }
    }
  },
  methods: {
    renderData() {
      const that = this
      if (!that.listQuery.userId) {
        return
      }
      that.listLoading = true
      listGoldRunningWater(that.listQuery)
        .then(res => {
          that.listLoading = false
          const { body } = res
          const list = body || []
          that.notMore = list.length <= 0
          that.list = that.list.concat(list)
          if (that.list && that.list.length > 0) {
            that.listQuery.lastId =
              that.list[that.list.length - 1].runningWater.id
          }
        })
        .catch(er => {
          that.listLoading = false
          console.error(er)
        })
    },
    defaultRangeTimestamp() {
      const defStartDate = beforeDateObject(this.beforeIntervalDays)
      const mDate = datePlusDays(new Date(), 1)
      mDate.setHours(23)
      mDate.setMinutes(59)
      mDate.setSeconds(59)
      const monthLastDate = getMonthLastDate()
      monthLastDate.setHours(23)
      monthLastDate.setMinutes(59)
      monthLastDate.setSeconds(59)
      const nowDate = mDate.getMonth() !== monthLastDate.getMonth() ? monthLastDate : mDate

      if (nowDate.getMonth() !== defStartDate.getMonth()) {
        // 最近两小时
        const startDate = nowDate.getTime() - 60 * 60 * 1000 * 2

        if (startDate.getMonth() !== nowDate.getMonth()) {
          // 最近2分钟
          return [nowDate.getTime() - 60 * 1000 * 2, nowDate.getTime()]
        }

        return [defStartDate.getTime(), nowDate.getTime()]
      }

      return [defStartDate.getTime(), nowDate.getTime()]
    },
    handleClose() {
      this.$emit('close')
    },
    clickLoadMore() {
      this.renderData()
    }
  }
}
</script>
<style scoped lang="scss">
.gold-running-water-content {
  max-height: 450px;
  overflow: auto;
}
</style>
