<template>
  <el-drawer
    title="重置密码"
    :visible="true"
    :before-close="clickClose"
    :close-on-press-escape="false"
    :wrapper-closable="false"
    :modal-append-to-body="true"
    :append-to-body="true"
    custom-class="drawer-auto-layout"
  >

    <div class="drawer-form">
      <el-form ref="dataForm" :model="formData" :rules="rules" label-width="80px">
        <el-form-item label="原始密码" prop="oldPassword">
          <el-input
            v-model.trim="formData.oldPassword"
            type="password"
            placeholder="请输入原始密码"
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model.trim="formData.newPassword"
            type="password"
            placeholder="请输入新密码"
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="rePassword">
          <el-input
            v-model.trim="formData.rePassword"
            type="password"
            placeholder="请输入确认密码"
          />
        </el-form-item>

      </el-form>
    </div>
    <div class="drawer-footer">
      <el-button @click="clickClose">取消</el-button>
      <el-button type="primary" :loading="submitLoading" :disabled="submitLoading" @click="clickSubmit">提交</el-button>
    </div>
  </el-drawer>

</template>

<script>

import { resetPassword } from '@/api/ops-system'

export default {
  data() {
    const passwordValid = (rule, value, callback) => {
      if (!value) {
        callback(new Error('必填字体不可为空'))
      } else if (value.length < 4) {
        callback(new Error('密码不可小于4位'))
      } else {
        callback()
      }
    }
    return {
      submitLoading: false,
      formData: {
        newPassword: '',
        oldPassword: '',
        rePassword: ''
      },
      rules: {
        newPassword: [{ required: true, validator: passwordValid, trigger: 'blur' }],
        oldPassword: [{ required: true, validator: passwordValid, trigger: 'blur' }],
        rePassword: [{ required: true, validator: passwordValid, trigger: 'blur' }]
      }
    }
  },
  methods: {
    clickSubmit() {
      const that = this
      that.$refs.dataForm.validate(valid => {
        if (valid) {
          if (that.formData.newPassword !== that.formData.rePassword) {
            that.$opsMessage.fail('两次密码输入不一致')
            return false
          }
          that.submitLoading = true
          resetPassword(that.formData).then(res => {
            that.submitLoading = false
            that.$emit('success')
            that.$store.dispatch('user/resetToken').then(() => {
              // window.clearVuexAlong(true, true)
              location.reload()
            })
          }).catch(er => {
            that.submitLoading = false
            that.$emit('fial', er)
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    },
    clickClose() {
      if (this.submitLoading === true) {
        this.$opsMessage.warn('Processing submission!')
        return
      }
      this.$emit('close')
    }
  }
}
</script>
