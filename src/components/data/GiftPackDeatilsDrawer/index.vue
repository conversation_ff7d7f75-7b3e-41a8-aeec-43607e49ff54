<template>
  <div class="user-details-drawer">
    <el-drawer
      title="礼包详情"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      size="450px"
      :modal-append-to-body="false"
    >
      <div class="room-deatils-content">
        <div>
          <gift-pack-info :row="row" @baseInfoSucces="handleBaseInfoSuccess" />
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import GiftPackInfo from '@/components/data/GiftPack'
export default {
  name: 'GiftPackDeatilsDrawer',
  components: { GiftPackInfo },
  props: {
    row: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      baseInfo: {}
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    handleBaseInfoSuccess(data) {
      this.baseInfo = data
    }
  }
}
</script>
<style scoped lang="scss">
.room-deatils-content {
    padding: 0px 10px;
  .el-tabs__nav-scroll .el-tabs__item {
    font-size: 13px;
    color: #303133;
    border: 1px sol;
  }
}
</style>
