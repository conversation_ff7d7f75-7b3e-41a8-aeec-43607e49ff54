<template>
  <div class="edit-user-bean-balance">
    <el-dialog
      title="豆子余额操作"
      :visible="true"
      width="450px"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="formData"
        label-position="left"
        label-width="70px"
        style="width: 300px; margin-left:50px;"
      >

        <el-form-item label="类型" prop="type">
          <el-select v-model="formData.type" placeholder="请选择" style="width: 100%;">
            <el-option label="发送" :value="0" />
            <el-option label="扣除" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="原因" prop="reason">
          <el-select v-if="formData.type === 0" v-model="formData.reason" placeholder="请选择" style="width: 100%;">
            <el-option
              v-for="item in currencyRewardReasons"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
          <el-select v-else v-model="formData.reason" placeholder="请选择" style="width: 100%;">
            <el-option
              v-for="item in currencyDeductReasons"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="豆子" prop="quantity">
          <el-input
            v-model.trim="formData.quantity"
            placeholder="请输入数量"
          />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model.trim="formData.remark"
            type="textarea"
            placeholder="奖励备注"
            resize="none"
          />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose()">
          取消
        </el-button>
        <el-button
          v-loading="submitLoading"
          type="primary"
          @click="handleSubmit()"
        >
          提交
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { currencyRewardReasons, currencyDeductReasons } from '@/constant/type'
import { addOrSubtract } from '@/api/bean'

export default {
  name: 'EditUserBeanBalance',
  props: {
    userId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      currencyDeductReasons,
      currencyRewardReasons,
      rules: {
        candy: [
          {
            required: true,
            message: '请输入豆子',
            trigger: 'blur'
          },
          {
            pattern: /^\d{1,7}(\.\d{0,2})?$/,
            message: 'double范围0~9999999小数最多两位',
            trigger: 'blur'
          }
        ],
        type: [
          {
            required: true,
            message: '必填字段不可为空',
            trigger: 'blur'
          }
        ],
        userId: [
          {
            required: true,
            message: '必填字段不可为空',
            trigger: 'blur'
          }
        ],
        reason: [
          {
            required: true,
            message: '必填字段不可为空',
            trigger: 'blur'
          }
        ]
      },
      formData: {
        type: '',
        userId: '',
        quantity: '',
        reason: '',
        remark: ''
      },
      submitLoading: false
    }
  },
  methods: {
    handleSubmit() {
      const that = this
      that.formData.userId = that.userId
      that.$refs.dataForm.validate((valid) => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.submitLoading = true
        addOrSubtract(that.formData).then(res => {
          that.submitLoading = false
          that.$emit('success', Object.assign({}, that.formData))
          that.handleClose()
        }).catch(err => {
          that.submitLoading = false
          that.$emit('fial', err)
          that.handleClose()
        })
      })
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
