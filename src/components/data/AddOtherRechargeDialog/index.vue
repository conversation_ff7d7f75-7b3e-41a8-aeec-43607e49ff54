<template>
  <el-dialog
    title="线下充值订单记录"
    :visible="true"
    :before-close="handleClose"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    width="80%"
  >
    <div class="filter-container">
      <el-table
        v-loading="listLoading"
        :data="list"
        element-loading-text="Loading"
        fit
        highlight-current-row
        max-height="350px"
      >
        <el-table-column label="支付类型" align="center">
          <template slot-scope="scope">
            {{ otherPaymentTypesMap[scope.row.paymentType] }}
          </template>
        </el-table-column>
        <el-table-column label="金额" prop="amount" align="center" />
        <el-table-column
          label="支付日期"
          prop="paymentDateNumber"
          align="center"
        />
        <el-table-column label="备注" prop="remarks" align="center" />
        <el-table-column label="创建时间" prop="createTime" align="center">
          <template slot-scope="scope">
            {{ scope.row.createTime | dateFormat }}
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="listQuery.cursor"
        :limit.sync="listQuery.limit"
        @pagination="renderData"
      />
      <user-deatils-drawer
        v-if="userDeatilsDrawer"
        :user-id="thatSelectedUserId"
        @close="userDeatilsDrawer = false"
      />
    </div>
  </el-dialog>
</template>

<script>
import { orderOtherRechargePage } from '@/api/purchase'
import { otherPaymentTypes } from '@/constant/type'
import Pagination from '@/components/Pagination'
export default {
  name: 'AddOtherRechargeDialog',
  components: { Pagination },
  props: {
    userId: {
      type: String,
      required: true
    },
    sysOrigin: {
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      userDeatilsDrawer: false,
      thatSelectedUserId: '',
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        sysOrigin: '',
        userId: ''
      },
      listLoading: false,
      otherPaymentTypesMap: {}
    }
  },
  watch: {
    userId: {
      handler(newVal) {
        if (newVal) {
          this.renderData()
        }
      },
      immediate: true
    }
  },
  created() {
    const that = this
    otherPaymentTypes.forEach(item => {
      that.otherPaymentTypesMap[item.value] = item.name
    })
  },
  methods: {
    renderData() {
      const that = this
      that.listQuery.userId = that.userId
      that.listQuery.sysOrigin = that.sysOrigin
      if (!that.listQuery.userId) {
        return
      }
      that.listLoading = true
      orderOtherRechargePage(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleImageUrl(row) {
      return row.userAvatar ? row.userAvatar : ''
    },
    handleClose() {
      this.$emit('close')
    },
    handleSearch() {
      this.renderData(true)
    },
    queryUserDetails(userId) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = userId
    }
  }
}
</script>
