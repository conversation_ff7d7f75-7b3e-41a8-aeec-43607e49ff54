<template>
  <el-dialog
    title="反馈处理"
    :visible="true"
    width="30%"
    :before-close="handleClose"
  >
    <el-form ref="form" v-loading="loading" :model="form" label-width="80px">
      <el-form-item label="处理结果">
        <el-input v-model="form.remarks" type="textarea" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submit()">提交</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>

</template>

<script>
import { processFeedback } from '@/api/table'
export default {
  name: 'FeedbackDrawer',
  props: {
    row: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      remarks: '',
      loading: false,
      form: {
        id: '',
        userId: '',
        remarks: ''
      }
    }
  },
  watch: {
    row: {
      immediate: true,
      handler(newVal) {
        this.form.id = newVal.id
        this.form.userId = newVal.userId
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    submit() {
      const that = this
      that.loading = true
      processFeedback(that.form).then(res => {
        that.loading = false
        that.$emit('success', Object.assign({}, that.form))
        that.handleClose()
      }).catch(err => {
        that.loading = false
        that.$emit('fial', err)
        that.handleClose()
      })
    }
  }
}
</script>
