<template>
  <div class="user-input">
    <el-input v-model.trim="inputValue" clearable :disabled="!sysOriginVal" :placeholder="placeholder" class="input-with-select" @input="handleInput">
      <el-select slot="prepend" v-model="selectType" placeholder="类型" :disabled="!sysOriginVal" @change="selectTypeChange">
        <el-option label="长ID" value="LONG" />
        <el-option label="短ID" value="SHORT" />
      </el-select>

      <el-select v-if="showSysOriginTypeSelect && selectType !== 'LONG' && permissionsSysOriginPlatforms.length > 0" slot="append" v-model="sysOriginType" placeholder="平台" @change="sysOriginTypeChange">
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
    </el-input>
  </div>

</template>
<script>
import { mapGetters } from 'vuex'
export default {
  name: 'AccountInput',
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    sysOrigin: {
      type: String,
      require: false,
      default: ''
    },
    type: {
      type: String,
      require: false,
      default: 'USER'
    },
    placeholder: {
      type: String,
      require: false,
      default: ''
    },
    defaultSelectType: {
      type: String,
      require: false,
      default: 'SHORT'
    }
  },
  data() {
    return {
      inputValue: '',
      selectType: 'SHORT',
      sysOriginType: ''
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms']),
    showSysOriginTypeSelect() {
      return !this.sysOrigin && this.permissionsSysOriginPlatforms && this.permissionsSysOriginPlatforms.length > 0
    },
    sysOriginVal() {
      return this.sysOrigin || this.sysOriginType
    }
  },
  watch: {
    sysOrigin(newVal) {
      this.emitInput()
    },
    defaultSelectType: {
      handler(newVal) {
        this.selectType = newVal || 'SHORT'
      },
      immediate: true
    }
  },
  created() {
    if (this.value !== this.inputValue) {
      this.inputValue = this.filterString(this.value)
    }
    if (this.permissionsSysOriginPlatforms && this.permissionsSysOriginPlatforms.length > 0) {
      this.sysOriginType = this.permissionsSysOriginPlatforms[0].value
    }
  },
  methods: {
    clearValue() {
      this.inputValue = ''
    },
    handleInput(val) {
      if (this.selectType === 'LONG') {
        this.inputValue = this.filterString(val)
      }
      this.emitInput()
    },
    filterString(val) {
      if (!val) {
        return val
      }
      return String(val).replace(/[^\d]/g, '')
    },
    sysOriginTypeChange() {
      this.emitInput()
    },
    selectTypeChange() {
      if (this.selectType === 'LONG') {
        this.inputValue = this.filterString(this.inputValue)
      }
      this.emitInput()
    },
    handleVal() {
      const vals = []
      if (!this.inputValue) {
        return ''
      }
      vals.push(`type:${this.type}`)
      vals.push(`accountType:${this.selectType}`)
      vals.push(`sysOrigin:${this.sysOriginVal}`)
      vals.push(`content:${this.inputValue}`)
      return `*${vals.join(',')}*`
    },
    emitInput() {
      this.$emit('input', this.handleVal())
    }
  }
}
</script>
