<template>
  <div class="user-photo-wall">
    <div v-loading="photoWallsLoading" class="photo-wall">
      <div v-for="item in photoWalls" :key="item.id" class="photo-wall-item">
        <el-image
          style="width: 120px;height: 100px;"
          :src="item.resourceUrl"
          :preview-src-list="photoWallUrls"
        />
      </div>
    </div>
  </div>
</template>
<script>
import { getUserPhotoWallNormal } from '@/api/app-user'
export default {
  name: 'UserPhotoWall',
  props: {
    userId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      photoWallsLoading: true,
      photoWalls: [],
      photoWallUrls: []
    }
  },
  watch: {
    userId: {
      immediate: true,
      handler(newVal) {
        this.renderData(newVal)
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    renderData(userId) {
      const that = this
      getUserPhotoWallNormal(userId).then(res => {
        that.photoWallsLoading = false
        that.photoWalls = res.body || []
        that.photoWallUrls = that.photoWalls.map(photoWall => photoWall.resourceUrl)
      }).catch(er => {
        that.photoWallsLoading = false
      })
    }
  }
}
</script>
<style scoped lang="scss">
.user-photo-wall {
    .photo-wall {
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        .photo-wall-item{
          margin-right: 10px;
        }
    }
}
</style>
