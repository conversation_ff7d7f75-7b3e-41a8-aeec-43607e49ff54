<template>
  <div class="user-bank-card-list">
    <el-drawer
      title="银行卡列表"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div class="filter-container">
        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-edit"
          style="margin: 10px;"
          @click="handleCreate"
        >
          新增
        </el-button>
      </div>
      <div v-loading="listLoading">
        <el-row v-for="(item, index) in list" :key="index">
          <el-card style="margin: 10px;">
            <div>
              <el-tag effect="plain">卡号: {{ item.cardNo }}</el-tag>
              <el-tag effect="plain">收款人: {{ item.payee }}</el-tag>
              <el-tag effect="plain">银行: {{ item.cardName }}</el-tag>
              <el-tag
                v-if="item.status === 'PENDING'"
                effect="plain"
                type="info"
              >审核状态: 待审核</el-tag>
              <el-tag
                v-if="item.status === 'PASS'"
                effect="plain"
                type="success"
              >审核状态: 正常</el-tag>
              <el-tag
                v-if="item.status === 'NOT_PASS'"
                effect="plain"
                type="danger"
              >审核状态: 驳回</el-tag>
              <el-tag
                v-if="item.use"
                effect="plain"
                type="success"
              >使用中: 是</el-tag>
              <el-tag
                v-if="!item.use"
                effect="plain"
                type="info"
              >使用中: 否</el-tag>
            </div>
            <div class="bottom clearfix">
              <time class="time">创建时间:{{ item.createTime }}</time>
            </div>
            <div class="clearfix">
              <el-button
                type="text"
                @click.native="handleUpdate(item)"
              >修改</el-button>
              <el-button
                v-if="!item.use"
                type="text"
                @click.native="handleUse(item)"
              >使用</el-button>
              <el-button
                type="text"
                @click.native="handleDelete(item)"
              >删除</el-button>
            </div>
          </el-card>
        </el-row>
      </div>
      <el-dialog
        width="450px"
        :title="textOptTitle2"
        :visible.sync="form2Visible"
        append-to-body
      >
        <div v-loading="submitLoading">
          <el-form
            ref="dataForm"
            :rules="rules"
            :model="dataForm"
            label-position="left"
            label-width="70px"
            style="width: 300px; margin-left:50px;"
          >
            <el-form-item
              v-if="dataForm.id === ''"
              label="类型"
              prop="cardType"
            >
              <el-select
                v-model="dataForm.cardType"
                placeholder="类型"
                style="width: 120px"
                class="filter-item"
                @change="getBankCardTypeName(dataForm)"
              >
                <el-option
                  v-for="(item, index) in bankCardType"
                  :key="index"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item
              v-if="dataForm.id === '' && dataForm.cardType === 'BANK'"
              label="银行"
              prop="cardName"
            >
              <el-input
                v-model.trim="dataForm.cardName"
                placeholder="请输入银行(英文)名称"
              />
            </el-form-item>

            <el-form-item label="收款人" prop="payee">
              <el-input v-model.trim="dataForm.payee" placeholder="收款人" />
            </el-form-item>

            <el-form-item label="卡号" prop="cardNo">
              <el-input v-model.trim="dataForm.cardNo" placeholder="卡号" />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer" style="text-align: center;">
            <el-button @click="form2Visible = false">
              取消
            </el-button>
            <el-button
              v-loading="listLoading"
              type="primary"
              @click="handleSubmit()"
            >
              提交
            </el-button>
          </div>
        </div>
      </el-dialog>
    </el-drawer>
  </div>
</template>

<script>
import {
  listBankCardTable,
  updateBankCard,
  addBankCard,
  deleteBankCard,
  useBankCard
} from '@/api/user'
import { bankCardType } from '@/constant/team-type'
function getFormData() {
  return {
    id: '',
    userId: '',
    cardType: '',
    payee: '',
    cardNo: '',
    sysOrigin: ''
  }
}
export default {
  name: 'BankCardList',
  props: {
    row: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      bankCardType,
      submitLoading: false,
      list: [],
      form2Visible: false,
      listLoading: false,
      textOptTitle2: '添加',
      rules: {
        cardType: [
          { required: true, message: '请填写卡片类型', trigger: 'blur' }
        ],
        payee: [{ required: true, message: '请填写收款人', trigger: 'blur' }],
        cardNo: [{ required: true, message: '请填写卡号', trigger: 'blur' }],
        cardName: [
          { required: true, message: '请填写银行英语名称', trigger: 'blur' }
        ]
      },
      dataForm: {
        id: '',
        userId: '',
        cardType: '',
        cardName: '',
        payee: '',
        cardNo: '',
        status: '',
        sysOrigin: ''
      }
    }
  },
  created() {
    this.renderData()
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      listBankCardTable(that.row.id).then(res => {
        const { body } = res
        that.list = body
        that.listLoading = false
      })
    },
    handleSubmit() {
      const that = this
      that.$refs.dataForm.validate(valid => {
        if (valid) {
          that.submitLoading = true
          that.dataForm.userId = that.row.id
          that.dataForm.sysOrigin = that.row.userProfile.originSys
          if (that.dataForm.id) {
            updateBankCard(that.dataForm)
              .then(res => {
                that.submitLoading = false
                that.formVisible = false
                that.form2Visible = false
                that.dataForm = getFormData()
                that.renderData()
              })
              .catch(er => {
                that.submitLoading = false
                console.error(er)
                this.$emit('fail')
              })
            return
          }
          addBankCard(that.dataForm)
            .then(res => {
              that.submitLoading = false
              that.formVisible = false
              that.dataForm = getFormData()
              that.form2Visible = false
              that.renderData()
            })
            .catch(er => {
              that.submitLoading = false
              console.error(er)
            })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    },
    handleDelete(row) {
      const that = this
      that
        .$confirm('确认删除吗？', '提示', {
          type: 'warning'
        })
        .then(() => {
          that.listLoading = true
          deleteBankCard(row.id)
            .then(res => {
              that.listLoading = false
              that.$opsMessage.success()
              that.renderData()
            })
            .catch(() => {
              that.listLoading = false
            })
        })
        .catch(() => {})
    },
    handleUse(row) {
      const that = this
      that
        .$confirm('确认使用该银行卡吗？', '提示', {
          type: 'warning'
        })
        .then(() => {
          that.listLoading = true
          useBankCard(row.id)
            .then(res => {
              that.listLoading = false
              that.$opsMessage.success()
              that.renderData()
            })
            .catch(() => {
              that.listLoading = false
            })
        })
        .catch(() => {})
    },
    handleClose() {
      this.$emit('close')
    },
    handleCreate() {
      this.textOptTitle2 = '添加'
      this.form2Visible = true
      this.dataForm = getFormData()
    },
    handleUpdate(row) {
      this.textOptTitle2 = '修改'
      this.form2Visible = true
      this.dataForm = Object.assign(this.dataForm, row)
    },
    getBankCardTypeName(_dataForm) {
      const that = this
      if (_dataForm.cardType === 'BANK') {
        return
      }
      that.bankCardType.forEach(function(v, i) {
        if (v.value === _dataForm.cardType) {
          that.dataForm.cardName = v.name
          return
        }
      })
    }
  }
}
</script>

<style>
.time {
  font-size: 13px;
  color: #999;
}

.bottom {
  margin-top: 13px;
  line-height: 12px;
}

.button {
  padding: 0;
  float: right;
}

.image {
  width: 30%;
  margin-top: 10px;
  display: block;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>
