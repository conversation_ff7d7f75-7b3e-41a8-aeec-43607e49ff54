<!--账号处理日志列表-->
<template>
  <div class="account-status-latest-log">
    <div
      v-for="(item, index) in list"
      :key="index"
      class="block"
    >
      <el-tag type="danger"> <strong>”{{ item.approvalUserName }}“</strong>  操作 <strong>“{{ item.statusName }}”</strong> {{ item.createTime }}</el-tag>
    </div>
  </div>
</template>

<script>

import { getUserStatusLogLatestList } from '@/api/approval'

export default {
  name: 'UserVideoViolationTableDialog',
  props: {
    userId: {
      type: String,
      required: true
    },
    size: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: false
    }
  },
  watch: {
    userId: {
      handler(newVal) {
        this.renderData(newVal, this.size)
      },
      immediate: true
    }
  },
  methods: {
    renderData(userId, size) {
      const that = this
      that.listLoading = true
      getUserStatusLogLatestList({ beApprovalUser: userId, size }).then(res => {
        that.listLoading = false
        const { body } = res
        that.list = body || []
      }).catch(er => {
        that.listLoading = false
      })
    }
  }
}
</script>
<style scoped>
.account-status-latest-log > .block {
  margin-bottom: 10px;
}
</style>
