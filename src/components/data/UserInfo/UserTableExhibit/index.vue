<template>
  <div v-if="userProfile" class="user-table-exhibit flex-l">
    <div class="avatar">
      <avatar :url="userProfile['userAvatar']" :gender="userProfile['userSex']" :size="size || 'small'" />
      <flag-icon v-if="userProfile.countryCode && showCountryFlag" class="flag-icon" :code="userProfile.countryCode" :tooltip="userProfile.countryName" :size="size === 'mini' ? '12px' : '18px'" />
    </div>
    <div class="info nowrap-ellipsis" :class="size">
      <div class="nickname">
        <el-link v-if="queryDetails === true" @click="queryUserDetails()"><a :title="userProfile.userNickname"> {{ userProfile.userNickname }} </a></el-link>
        <a v-else :title="userProfile.userNickname"> {{ userProfile.userNickname }} </a>
      </div>
      <div class="sex-account" :class="{'font-danger': isSpecialId}">
        <sys-origin-icon v-if="showSysOrigin" :icon="userProfile.sysOriginChild || userProfile.originSys" :desc="userProfile.sysOriginChild || userProfile.originSys" size="18px" />
        <gender :gender="userProfile['userSex']" :gender-name="userProfile['userSexName']" :desc="getAccountText()" />
        <el-tag v-if="tagName" size="mini" :type="tagType">{{ tagName }}</el-tag>
      </div>
    </div>

    <user-deatils-drawer
      v-if="userDeatilsDrawer"
      :user-id="userProfile[customProps.userId]"
      @close="userDeatilsDrawer=false"
    />
  </div>
</template>

<script>
export default {
  name: 'UserTableExhibit',
  props: {
    queryDetails: {
      type: Boolean,
      require: false,
      default: false
    },
    userProfile: {
      type: Object,
      require: true,
      default: () => {}
    },
    customProps: {
      type: Object,
      require: false,
      default: () => {
        return {
          'userId': 'id',
          'userAvatar': 'userAvatar',
          'userSex': 'userSex',
          'userSexName': 'userSexName'
        }
      }
    },
    showCountryFlag: {
      type: Boolean,
      require: false,
      default: true
    },
    showSysOrigin: {
      type: Boolean,
      require: false,
      default: false
    },
    showAccount: {
      type: Boolean,
      require: false,
      default: true
    },
    size: {
      type: String,
      require: false,
      default: 'small'
    },
    tagName: {
      type: String,
      require: false,
      default: ''
    },
    tagType: {
      type: String,
      require: false,
      default: ''
    }
  },
  data() {
    return {
      userDeatilsDrawer: false
    }
  },
  computed: {
    isSpecialId() {
      if (!this.userProfile) {
        return false
      }
      return !!(this.userProfile.ownSpecialId && this.userProfile.ownSpecialId.account)
    }
  },
  created() {
  },
  methods: {
    getAccountText() {
      if (!this.showAccount) {
        return
      }
      if (!this.userProfile) {
        return 'error'
      }
      if (this.isSpecialId) {
        return `${this.userProfile.ownSpecialId.account}靓`
      }
      return this.userProfile.account
    },
    queryUserDetails() {
      this.userDeatilsDrawer = true
    }
  }
}
</script>
<style scoped lang="scss">
.mini {
  > div {
    line-height: 20px !important;
  }
  .nickname {
    a {
      font-size: 12px;
    }
  }
}
.user-table-exhibit {
  text-align: left;
  .avatar {
    width: 50px;
    position: relative;
    .flag-icon {
      position: absolute;
      bottom: 0px;
    }
  }
  .info {
    padding: 0px 5px;
    width: 100%;
    > div {
      line-height: 22px;
    }
  }
 }
</style>
