<template>
  <div class="user-base-info-content">
    <div v-loading="baseInfoLoading" class="header-content flex-l">
      <div class="avatar">
        <avatar
          :url="userBaseInfo.userAvatar"
          :gender="userBaseInfo.userSex"
          size="small"
        />
      </div>
      <div class="content">
        <div class="text">
          {{ userBaseInfo.userNickname }}、
          <span v-if="userBaseInfo.userSex === 1">男</span>
          <span v-else> 女 </span>
        </div>
        <div
          v-if="userRunProfile.wearBadge && userRunProfile.wearBadge.length > 0"
          class="wear-badge"
        >
          <el-tooltip
            v-for="(item, index) in userRunProfile.wearBadge"
            :key="index"
            class="item"
            effect="dark"
          >
            <div slot="content">
              <div>ID:{{ item.id }}</div>
              <div>徽章名称:{{ item.badgeName }}</div>
              <div>徽章类型:{{ item.type }}</div>
              <div>徽章KEY:{{ item.badgeKey }}</div>
              <div>
                过期时间:{{
                  item.expireTime | dateFormat("yyyy-MM-dd HH:mm:ss")
                }}
              </div>
            </div>
            <el-image
              style="width: 30px; height: 30px"
              :src="item.selectUrl"
              :preview-src-list="[item.selectUrl]"
            />
          </el-tooltip>
        </div>
      </div>
      <div
        v-if="buttonPermissions.includes('user:profile:panel:rundata')"
        class="operation"
      >
        <el-dropdown>
          <span class="el-dropdown-link">
            <i class="el-icon-more" />
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              @click.native="queryUserRunProfile()"
            >查看运行数据
            </el-dropdown-item>
            <el-dropdown-item
              @click.native="removeUserRunProfile()"
            >删除运行数据
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <div v-loading="registerLoading">
      <el-collapse v-model="nActiveCollapseNames" @change="collapseChange">
        <el-collapse-item title="" name="accountInfo">
          <template slot="title">
            <i class="el-icon-s-opportunity" />&nbsp;账户信息

            <span
              v-if="userBaseInfo.del === true"
            >（已注销：{{ userBaseInfo.updateTime | dateFormat }}）</span>
            <span v-else>
              （{{ userBaseInfo.accountStatusName }}
              <span
                v-if="userBaseInfo.accountStatus === 'FREEZE'"
              >：{{ userBaseInfo.freezingTime | dateFormat }}</span>）
            </span>

            <!-- <i class="el-icon-s-opportunity" />&nbsp;认证信息
            <span>({{ thisAccountStatus.value }})</span> -->
          </template>
          <div class="block-info">
            <div class="text-item">
              <flag-icon
                class="flag-icon"
                :code="userBaseInfo.countryCode"
                :tooltip="userBaseInfo.countryName"
                size="38"
              />
              <div class="tags">
                <el-tag>金币：{{ candy || 0 }}</el-tag>
                &nbsp;
                <!-- <el-tag>当前订阅：<a :title="notExpiredSubscriptionName">{{ notExpiredSubscriptionName }}</a> -->
                <!-- <span v-if="balance.vipEvent > 0">{{ balance.vipExpireTime }}</span> </el-tag>-->
              </div>
            </div>
            <div
              v-if="
                userBaseInfo.accountStatus &&
                  userBaseInfo.accountStatus != 'NORMAL'
              "
              style="margin: 10px 0px 10px 0px"
            >
              <account-status-latest-log :user-id="userId" :size="1" />
            </div>
          </div>
        </el-collapse-item>
        <el-collapse-item title="" name="baseInfo">
          <template slot="title">
            <i class="el-icon-s-custom" />&nbsp;基本信息
          </template>
          <div class="block-info">
            <div class="text-item">
              <el-row>
                <el-col :span="12">ID:{{ userBaseInfo.id }}</el-col>
                <el-col
                  :span="12"
                >账号:{{ getAccountText(userBaseInfo) }}
                </el-col>
              </el-row>
            </div>
            <div class="text-item">
              <el-row>
                <el-col
                  :span="12"
                >主播:{{ identity.anchor ? "Yes" : "No" }}
                </el-col>
                <el-col
                  :span="12"
                >主播代理:{{ identity.agent ? "Yes" : "No" }} / 货运代理:{{
                  identity.freightAgent ? "Yes" : "No"
                }}
                </el-col>
              </el-row>
            </div>
            <div class="text-item">
              <el-row>
                <el-col
                  :span="12"
                >BD:{{ identity.bd ? "Yes" : "No" }} / BD Leader:{{
                  identity.bdLeader ? "Yes" : "No"
                }}
                </el-col>
                <el-col
                  :span="12"
                >等级Lv:
                  <el-tag
                    size="mini"
                  >财富{{ userBaseInfo.wealthLevel }}
                  </el-tag>
                  <el-tag size="mini">魅力{{ userBaseInfo.charmLevel }}</el-tag>
                </el-col>
              </el-row>
            </div>
            <div class="text-item">
              <el-row>
                <el-col
                  :span="12"
                >生日：<span
                  v-if="userBaseInfo.bornYear"
                >{{ userBaseInfo.bornYear }}-{{ userBaseInfo.bornMonth }}-{{
                  userBaseInfo.bornDay
                }}</span></el-col>
                <el-col
                  :span="12"
                >年龄：<span
                  v-if="userBaseInfo.age"
                >({{ userBaseInfo.age }})</span></el-col>
              </el-row>
            </div>
            <div class="text-item">
              <el-row>
                <el-col :span="12">国家：{{ userBaseInfo.countryName }}</el-col>
                <el-col :span="12">邮箱：{{ userRegisterInfo.email }}</el-col>
              </el-row>
            </div>
            <div class="text-item">
              <el-row>
                <el-col
                  :span="12"
                >注册平台：
                  <platform-svg-icon
                    size="20px"
                    :icon="userRegisterInfo.originPlatform"
                    :desc="'注册平台：' + userRegisterInfo.originPlatform"
                  />
                  <platform-svg-icon
                    size="20px"
                    :icon="userRegisterInfo.authType"
                    :desc="'注册方式：' + userRegisterInfo.authType"
                  />
                </el-col>
                <el-col
                  :span="12"
                >手机型号：<a :title="userRegisterInfo.originPhoneModel">{{
                  userRegisterInfo.originPhoneModel
                }}</a></el-col>
              </el-row>
            </div>

            <div class="text-item">
              <el-row>
                <el-col
                  :span="12"
                >账号类型：
                  {{ userBaseInfo.userTypeName }}
                </el-col>
                <el-col
                  :span="12"
                >注册时间：<a :title="userBaseInfo.createTime | dateFormat">{{
                  userBaseInfo.createTime | dateFormat
                }}</a></el-col>
              </el-row>
            </div>
            <div class="text-item">
              <el-row>
                <el-col
                  :span="12"
                >来源系统：
                  <span
                    v-if="
                      !userBaseInfo.sysOriginChild ||
                        userBaseInfo.sysOriginChild === userBaseInfo.originSys
                    "
                  >
                    {{ userBaseInfo.originSys }}
                  </span>
                  <span v-else>
                    {{ userBaseInfo.sysOriginChild }}
                  </span>
                </el-col>
                <el-col
                  :span="12"
                >所属区域: {{ userBaseInfo.regionName }}
                </el-col>
              </el-row>
            </div>
            <div v-if="identity && identity.historyIdentity && identity.historyIdentity.length > 0" class="text-item">
              <el-row>
                <el-col :span="12">历史身份: {{ identity.historyIdentity.join(",") }}</el-col>
              </el-row>
            </div>
            <div class="text-item">
              <el-row>
                <el-col :span="24">
                  <el-button
                    v-if="!authType.openId"
                    :loading="authTypeLoading"
                    type="text"
                    @click="queryOpenId"
                  >查看OPENID
                  </el-button>
                  <div v-else>
                    <el-tooltip class="item" effect="dark">
                      <div slot="content">
                        <div v-if="authType.openId">
                          <p>{{ authType.openId }}</p>
                        </div>
                        <div v-else>Empty</div>
                      </div>
                      <i
                        style="cursor: pointer;"
                        class="el-icon-document-copy"
                        @click="copyContent(authType.openId)"
                      />
                    </el-tooltip>
                    OPENID:{{ authType.openId }}
                  </div>
                </el-col>
              </el-row>
            </div>
            <div class="text-item">
              <el-row>
                <el-col :span="24">
                  <el-button
                    v-if="!aTokenRequest"
                    :loading="aTokenLoading"
                    type="text"
                    @click="queryAToken"
                  >查看登录令牌
                  </el-button>
                  <div v-else>
                    <div>
                      <el-row>
                        <el-col :span="22" class="nowrap-ellipsis">
                          <el-tooltip class="item" effect="dark">
                            <div slot="content">
                              <div v-if="aToken.token">
                                <p>点击可CopyToken</p>
                                <p>AToken: {{ aToken.token }}</p>
                              </div>
                              <div v-else>未登录</div>
                            </div>
                            <i
                              style="cursor: pointer;"
                              class="el-icon-document-copy"
                              @click="copyContent(aToken.token)"
                            />
                          </el-tooltip>
                          AToken:{{ aToken.token || "未登录" }}
                        </el-col>
                        <el-col
                          v-if="
                            buttonPermissions.includes(
                              'user:profile:panel:del:openid'
                            )
                          "
                          :span="2"
                          style="text-align: right;"
                        >
                          <div v-if="aToken.token" class="operation">
                            <el-dropdown>
                              <span class="el-dropdown-link">
                                <i class="el-icon-more" />
                              </span>
                              <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item
                                  @click.native="delToken()"
                                >删除令牌
                                </el-dropdown-item>
                                <el-dropdown-item
                                  @click.native="
                                    copyContent(
                                      aToken ? JSON.stringify(aToken) : 'Empty'
                                    )
                                  "
                                >Copy All
                                </el-dropdown-item>
                              </el-dropdown-menu>
                            </el-dropdown>
                          </div>
                        </el-col>
                      </el-row>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
            <div v-if="userRegisterInfo.residentialAddress" class="text-item">
              居住地址：{{ userRegisterInfo.residentialAddress }}
            </div>

            <div
              v-if="
                userRunProfile.useProps && userRunProfile.useProps.length > 0
              "
              class="text-item flex-l"
            >
              佩戴道具:
              <el-tooltip
                v-for="(item, index) in userRunProfile.useProps"
                :key="index"
                class="item"
                effect="dark"
              >
                <div slot="content">
                  <div v-if="item.propsResources">
                    <div>ID:{{ item.propsResources.id }}</div>
                    <div>道具类型:{{ item.propsResources.type }}</div>
                    <div>道具编号:{{ item.propsResources.code }}</div>
                    <div>道具名称:{{ item.propsResources.name }}</div>
                  </div>
                  <div>
                    过期时间:{{
                      item.expireTime | dateFormat("yyyy-MM-dd HH:mm:ss")
                    }}
                  </div>
                </div>

                <el-image
                  v-if="item.propsResources"
                  style="width: 30px; height: 30px"
                  :src="item.propsResources.cover"
                  :preview-src-list="[item.propsResources.cover]"
                />
              </el-tooltip>
            </div>
          </div>
        </el-collapse-item>
        <el-collapse-item title="" name="vipInfo">
          <template slot="title">
            <i class="el-icon-s-check" />&nbsp;VIP权益
          </template>
          <div v-loading="vipLoading" class="vip-info">
            <div class="blockquote-content block-info">
              <div class="text-item">
                <el-row>
                  <el-col
                    :span="12"
                  >任意发言:
                    {{ vipActualEquity.speak ? "Yes" : "No" }}
                  </el-col>
                  <el-col
                    :span="12"
                  >防踢:
                    {{ vipActualEquity.prohibitKick ? "Yes" : "No" }}
                  </el-col>
                </el-row>
              </div>
              <div class="text-item">
                <el-row>
                  <el-col
                    :span="12"
                  >任意上麦:
                    {{ vipActualEquity.microphone ? "Yes" : "No" }}
                  </el-col>
                </el-row>
              </div>
              <el-alert
                title="具备实际权益的VIP来源: 购买,朋友赠送,活动获得; 系统后台赠送则不存在VIP实际权益,所以会产生VIP5被踢出房间的情况."
                type="warning"
                :closable="false"
              />
            </div>
          </div>
        </el-collapse-item>
        <el-collapse-item
          v-if="
            buttonPermissions.includes(
              'user:profile:panel:query:associated:device'
            )
          "
          title=""
          name="deviceInfo"
        >
          <template slot="title">
            <i class="el-icon-s-platform" />&nbsp;设备信息
          </template>
          <div v-loading="deviceLoading" class="device-info">
            <div v-if="deviceFlash && !deviceInfo" class="text-center">
              没有设备信息
            </div>
            <div v-else>
              <div class="blockquote">
                当前最新设备
              </div>
              <div
                v-if="deviceInfo.latestDevice"
                class="blockquote-content block-info"
              >
                <div class="text-item">
                  <el-row>
                    <el-col
                      :span="12"
                    >语言：{{ deviceInfo.latestDevice.language }}
                    </el-col>
                    <el-col
                      :span="12"
                    >时区：{{ deviceInfo.latestDevice.zoneId }}
                    </el-col>
                  </el-row>
                </div>
                <div class="text-item">
                  <el-row>
                    <el-col
                      :span="12"
                    >ip：{{ deviceInfo.latestDevice.ip }}
                    </el-col>
                    <el-col
                      :span="12"
                    >客户端：{{ deviceInfo.latestDevice.requestClient }}
                    </el-col>
                  </el-row>
                </div>
                <div class="text-item">
                  <el-row>
                    <el-col
                      :span="12"
                      class="nowrap-ellipsis"
                    >设备型号：<a
                      :title="deviceInfo.latestDevice.phoneModel"
                    >{{ deviceInfo.latestDevice.phoneModel }}</a></el-col>
                    <el-col
                      :span="12"
                    >设备系统：{{ deviceInfo.latestDevice.phoneSysVersion }}
                    </el-col>
                  </el-row>
                </div>

                <div class="text-item">
                  <el-row>
                    <el-col
                      :span="12"
                    >编译版本：{{ deviceInfo.latestDevice.buildVersion }}
                    </el-col>
                    <el-col
                      :span="12"
                    >App版本：{{ deviceInfo.latestDevice.appVersion }}
                    </el-col>
                  </el-row>
                </div>

                <div class="text-item">
                  <el-row>
                    <el-col
                      :span="12"
                    >Push平台：{{ deviceInfo.latestDevice.deviceType }}
                    </el-col>
                    <el-col
                      :span="12"
                    >登记平台：{{ deviceInfo.latestDevice.sysOrigin }}
                    </el-col>
                  </el-row>
                </div>

                <div class="text-item">
                  <el-row>
                    <el-col
                      :span="24"
                      class="nowrap-ellipsis"
                    >Push设备：<a :title="deviceInfo.latestDevice.deviceId">{{
                      deviceInfo.latestDevice.deviceId
                    }}</a></el-col>
                  </el-row>
                </div>
                <div class="text-item">
                  <el-row>
                    <el-col
                      :span="24"
                      class="nowrap-ellipsis"
                    >imei ID：<a :title="deviceInfo.latestDevice.imei">{{
                      deviceInfo.latestDevice.imei
                    }}</a></el-col>
                  </el-row>
                </div>
                <div class="text-item">
                  <el-row>
                    <el-col
                      :span="24"
                      class="nowrap-ellipsis"
                    >登记时间：<a
                      :title="deviceInfo.latestDevice.createTime | dateFormat"
                    >{{
                      deviceInfo.latestDevice.createTime | dateFormat
                    }}</a></el-col>
                  </el-row>
                </div>
              </div>
              <div v-else class="text-center">
                当前用户没有登记设备信息
              </div>

              <div
                v-if="associatedDeviceNotEmpty || associatedIpNotEmpty"
                class="blockquote"
              >
                设备关联信息
              </div>
              <div
                v-if="associatedDeviceNotEmpty || associatedIpNotEmpty"
                class="blockquote-content"
              >
                <el-tabs v-model="associatedDeviceTabActiveName">
                  <el-tab-pane
                    v-if="associatedDeviceNotEmpty"
                    label="设备"
                    name="associatedDevice"
                  >
                    <associated-device-timeline
                      :associated-devices="deviceInfo.associatedDevice"
                    />
                  </el-tab-pane>
                  <el-tab-pane
                    v-if="associatedIpNotEmpty"
                    label="IP"
                    name="associatedIp"
                  >
                    <associated-device-timeline
                      :associated-devices="deviceInfo.associatedIp"
                    />
                  </el-tab-pane>
                </el-tabs>
              </div>
            </div>
          </div>
        </el-collapse-item>
        <el-collapse-item title="" name="expendInfo">
          <template slot="title">
            <i class="el-icon-s-grid" />&nbsp;扩展信息
          </template>
          <div v-loading="expendLoading" class="expend-info">
            <div v-if="expendFlash && !expend.userId" class="text-center">
              没有找到Expend信息,数据异常请联系管理员!!!
            </div>
            <div v-else>
              <div class="text-item">
                <el-row>
                  <el-col :span="12">语言：{{ expend.language }}</el-col>
                  <el-col :span="12">时区：{{ expend.lastZoneId }}</el-col>
                </el-row>
              </div>
              <div class="text-item">
                <el-row>
                  <el-col
                    :span="12"
                  >购买过：{{ expend.purchasing === true ? "是" : "否" }}
                  </el-col>
                  <el-col
                    :span="12"
                  >活跃时间：{{ expend.lastActiveTime | dateFormat }}
                  </el-col>
                </el-row>
              </div>
              <div v-if="expend.registerCountryCode" class="text-item">
                注册国家：{{ expend.registerCountryCode }}
              </div>
              <div v-if="expend.signature" class="text-item">
                个性签名：{{ expend.signature }}
              </div>
            </div>
          </div>
        </el-collapse-item>
        <el-collapse-item title="" name="giftWall">
          <template slot="title">
            <i class="el-icon-s-cooperation" />&nbsp;礼物墙
          </template>

          <div
            v-if="giftWallsFlash && giftWalls.length <= 0"
            class="text-center"
          >
            没有礼物信息, 快去给他送点吧!
          </div>
          <div v-else>
            <div class="photo-wall">
              <div
                v-for="item in giftWalls"
                :key="item.id"
                class="photo-wall-item"
              >
                <el-image
                  style="width: 50px; height: 50px"
                  :src="item.giftPhoto"
                />
                <div style="margin:-10px auto;width:74%">
                  {{ item.quantity }}
                </div>
              </div>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>

    <el-dialog
      title="运行缓存数据"
      :visible.sync="userRunProfileVisible"
      :append-to-body="true"
    >
      <div style="height:300px;overflow: auto;">
        <json-editor v-model="userRunProfileEdit" />
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  getUserExpend,
  removeUserRunProfileById,
  getUserRunProfileById,
  getDeviceByUserId,
  delLoginToken,
  getUserBaseInfo,
  getUserRegister,
  getUserCandyBalance,
  getUserAuthType,
  getLoginToken,
  getGiftWall,
  getUserVipEquity,
  getUserIdentity
} from '@/api/app-user'
import Avatar from '@/components/data/Avatar'
import PlatformSvgIcon from '@/components/PlatformSvgIcon'
import AccountStatusLatestLog from '@/components/data/UserInfo/AccountStatusLatestLog'
import JsonEditor from '@/components/JsonEditor'
import AssociatedDeviceTimeline from './AssociatedDeviceTimeline'
import { copyText } from '@/utils'
import { mapGetters } from 'vuex'

export default {
  name: 'UserBaseInfo',
  components: {
    AssociatedDeviceTimeline,
    Avatar,
    AccountStatusLatestLog,
    PlatformSvgIcon,
    JsonEditor
  },
  props: {
    userId: {
      type: String,
      required: true
    },
    activeCollapseNames: {
      type: Array,
      required: false,
      default: () => ['baseInfo', 'accountInfo']
    }
  },
  data() {
    return {
      associatedDeviceTabActiveName: 'associatedDevice',
      thatUserId: '',
      userRunProfileVisible: false,
      nowTime: Date.now(),
      nActiveCollapseNames: [],
      giftWalls: [],
      giftWallsLoading: false,
      giftWallsFlash: false,
      baseInfoLoading: true,
      balanceLoading: false,
      registerLoading: false,
      userBaseInfo: {},
      userRegisterInfo: {},
      candy: 0,
      notExpiredSubscriptionName: '-',
      authType: {},
      thisAccountStatus: {},
      authTypeLoading: false,
      deviceInfo: '',
      deviceFlash: false,
      deviceLoading: false,
      vipLoading: false,
      vipActualEquity: {},
      identity: {},
      userRunProfileLoading: false,
      userRunProfile: {},
      userRunProfileEdit: {},
      aTokenLoading: false,
      aTokenRequest: false,
      aToken: {
        token: ''
      },
      expendLoading: false,
      expendFlash: false,
      expend: {}
    }
  },
  computed: {
    ...mapGetters(['buttonPermissions']),
    associatedDeviceNotEmpty() {
      return (
        this.deviceInfo &&
        this.deviceInfo.associatedDevice &&
        this.deviceInfo.associatedDevice.length > 0
      )
    },
    associatedIpNotEmpty() {
      return (
        this.deviceInfo &&
        this.deviceInfo.associatedIp &&
        this.deviceInfo.associatedIp.length > 0
      )
    }
  },
  watch: {
    userId: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.thatUserId = newVal
          this.renderData(newVal)
        }
      }
    },
    activeCollapseNames: {
      immediate: true,
      handler(newVal) {
        this.nActiveCollapseNames = newVal
      }
    }
  },
  methods: {
    copyContent(content) {
      const that = this
      copyText(content)
        .then(() => {
          that.$opsMessage.success()
        })
        .catch(() => {
          that.$opsMessage.fail()
        })
    },
    handleClose() {
      this.$emit('close')
    },
    renderData(userId) {
      const that = this
      getUserBaseInfo(userId)
        .then(res => {
          that.baseInfoLoading = false
          that.userBaseInfo = res.body || {}
          that.$emit('baseInfoSucces', Object.assign({}, that.userBaseInfo))
        })
        .catch(er => {
          that.baseInfoLoading = false
          that.$emit('baseInfoFail')
        })

      that.loadUserRunProfileById(userId)

      that.registerLoading = true

      getUserRegister(userId)
        .then(res => {
          that.registerLoading = false
          that.userRegisterInfo = res.body || {}
        })
        .catch(er => {
          that.registerLoading = false
        })

      getUserCandyBalance(userId)
        .then(res => {
          this.candy = res.body
        })
        .catch(er => {})

      getUserIdentity(userId)
        .then(res => {
          that.identity = res.body
        })
        .catch(er => {})
    },
    loadUserRunProfileById(userId) {
      const that = this
      that.userRunProfileLoading = true
      getUserRunProfileById(userId)
        .then(res => {
          that.userRunProfileLoading = false
          that.userRunProfile = res.body || {}
        })
        .catch(er => {
          that.userRunProfileLoading = false
        })
    },
    queryUserRunProfile() {
      this.userRunProfileVisible = true
      this.userRunProfileEdit = Object.assign({}, this.userRunProfile)
    },
    removeUserRunProfile() {
      const that = this
      removeUserRunProfileById(that.thatUserId)
        .then(res => {
          that.$opsMessage.success()
          that.userRunProfile = {}
        })
        .catch(er => {
          that.$opsMessage.fail()
        })
    },
    getAccountText(baseInfo) {
      if (!baseInfo) {
        return ''
      }
      const account = baseInfo.account
      if (baseInfo.ownSpecialId && baseInfo.ownSpecialId.account) {
        return `${account} / ${baseInfo.ownSpecialId.account}靓`
      }
      return account
    },
    delToken() {
      const that = this
      delLoginToken(that.userId)
        .then(res => {
          that.$opsMessage.success()
          that.aToken = {}
        })
        .catch(er => {})
    },
    queryAToken() {
      const that = this
      that.aTokenRequest = true
      that.aTokenLoading = true
      getLoginToken(that.userId)
        .then(res => {
          that.aTokenLoading = false
          that.aToken.token = res.body || ''
        })
        .catch(er => {
          that.aTokenLoading = false
        })
    },
    queryOpenId() {
      const that = this
      that.authTypeLoading = true
      getUserAuthType(that.userId)
        .then(res => {
          that.authTypeLoading = false
          that.authType = res.body || {}
        })
        .catch(er => {
          that.authTypeLoading = false
        })
    },
    loadDeviceInfo() {
      const that = this
      if (that.deviceLoading) {
        return
      }
      that.deviceLoading = true
      getDeviceByUserId(that.userId)
        .then(res => {
          that.deviceLoading = false
          that.deviceFlash = true
          that.deviceInfo = res.body || {}

          if (that.associatedDeviceNotEmpty) {
            that.associatedDeviceTabActiveName = 'associatedDevice'
          } else if (that.associatedIpNotEmpty) {
            that.associatedDeviceTabActiveName = 'associatedIp'
          }
        })
        .catch(er => {
          that.deviceLoading = false
        })
    },
    loadUserExpend() {
      const that = this
      if (that.expendLoading) {
        return
      }
      that.expendLoading = true
      getUserExpend(that.userId)
        .then(res => {
          that.expendLoading = false
          that.expendFlash = true
          that.expend = res.body || {}
        })
        .catch(er => {
          that.expendLoading = false
        })
    },
    loadGiftWall() {
      const that = this
      if (that.giftWallsLoading) {
        return
      }
      that.giftWallsLoading = true
      getGiftWall(that.userId)
        .then(res => {
          that.giftWallsLoading = false
          that.giftWallsFlash = true
          that.giftWalls = res.body || []
        })
        .catch(er => {
          that.giftWallsLoading = false
        })
    },
    loadUserVipEquity() {
      const that = this
      if (that.vipLoading) {
        return
      }
      that.vipLoading = true
      getUserVipEquity(that.userId)
        .then(res => {
          that.vipLoading = false
          that.vipActualEquity = res.body || {}
        })
        .catch(er => {
          that.vipLoading = false
        })
    },
    collapseChange(activeNames) {
      const that = this
      if (activeNames.length <= 0) {
        return
      }
      const lastName = activeNames[activeNames.length - 1]
      if (lastName === 'expendInfo' && !that.expendFlash) {
        that.loadUserExpend()
        return
      }

      if (lastName === 'deviceInfo' && !that.deviceFlash) {
        that.loadDeviceInfo()
        return
      }

      if (lastName === 'giftWall' && !that.giftWallsFlash) {
        that.loadGiftWall()
        return
      }

      if (lastName === 'vipInfo' && !that.giftWallsFlash) {
        that.loadUserVipEquity()
      }
    }
  }
}
</script>
<style scoped lang="scss">
.user-base-info-content {
  .header-content {
    .content {
      width: 100%;
      padding: 0rem 10px;
    }
  }

  .block-info {
    position: relative;

    .text-item {
      height: 36px;
      line-height: 36px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .flag-icon {
    position: absolute;
  }

  .tags {
    position: absolute;
    left: 58px;
  }
}

.photo-wall {
  position: relative;

  .photo-wall-item {
    float: left;
    padding: 15px;
    text-align: center;
  }
}

.text-center {
  text-align: center;
}
</style>
