<template>
  <div class="associated-device-timeline">
    <el-timeline :reverse="true">
      <el-timeline-item
        v-for="(item, index) in associatedDevices"
        :key="index"
        :timestamp="item.latestDevice.createTime | dateFormat"
        placement="top"
      >
        <el-card>
          <user-table-exhibit
            :user-profile="item.userProfile"
            :query-details="true"
          />
          <div class="associated-device-info block-info">
            <div class="text-item">
              <el-row>
                <el-col
                  :span="12"
                >语言：{{ item.latestDevice.language }}</el-col>
                <el-col :span="12">时区：{{ item.latestDevice.zoneId }}</el-col>
              </el-row>
            </div>
            <div class="text-item">
              <el-row>
                <el-col :span="12">ip：{{ item.latestDevice.ip }}</el-col>
                <el-col
                  :span="12"
                >客户端：{{ item.latestDevice.requestClient }}</el-col>
              </el-row>
            </div>
            <div class="text-item">
              <el-row>
                <el-col
                  :span="12"
                  class="nowrap-ellipsis"
                >设备型号：<a :title="item.latestDevice.phoneModel">{{
                  item.latestDevice.phoneModel
                }}</a></el-col>
                <el-col
                  :span="12"
                >设备系统：{{ item.latestDevice.phoneSysVersion }}</el-col>
              </el-row>
            </div>

            <div class="text-item">
              <el-row>
                <el-col
                  :span="12"
                >编译版本：{{ item.latestDevice.buildVersion }}</el-col>
                <el-col
                  :span="12"
                >App版本：{{ item.latestDevice.appVersion }}</el-col>
              </el-row>
            </div>

            <div class="text-item">
              <el-row>
                <el-col
                  :span="12"
                >Push平台：{{ item.latestDevice.deviceType }}</el-col>
                <el-col
                  :span="12"
                >登记平台：{{ item.latestDevice.sysOrigin }}</el-col>
              </el-row>
            </div>

            <div class="text-item">
              <el-row>
                <el-col
                  :span="24"
                  class="nowrap-ellipsis"
                >Push设备：<a :title="item.latestDevice.deviceId">{{
                  item.latestDevice.deviceId
                }}</a></el-col>
              </el-row>
            </div>
            <div class="text-item">
              <el-row>
                <el-col
                  :span="24"
                  class="nowrap-ellipsis"
                >imei ID：<a :title="item.latestDevice.imei">{{
                  item.latestDevice.imei
                }}</a></el-col>
              </el-row>
            </div>
            <div class="text-item">
              <el-row>
                <el-col
                  :span="24"
                  class="nowrap-ellipsis"
                >注册时间：<a
                  :title="item.userProfile.createTime | dateFormat"
                >{{ item.userProfile.createTime | dateFormat }}</a></el-col>
              </el-row>
            </div>
          </div>
        </el-card>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>
<script>
export default {
  props: {
    associatedDevices: {
      type: Array,
      require: true,
      default: () => []
    }
  },
  data() {
    return {}
  }
}
</script>
<style scoped lang="scss">
.associated-device-timeline {
  .block-info {
    position: relative;
    .text-item {
      height: 36px;
      line-height: 36px;
    }
  }
}
</style>
