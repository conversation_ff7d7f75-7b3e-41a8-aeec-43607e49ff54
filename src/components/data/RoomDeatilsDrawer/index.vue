<template>
  <div class="user-details-drawer">
    <el-drawer
      title="房间资料详情"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div class="room-deatils-content">
        <div>
          <room-info :room-id="roomId" @baseInfoSucces="handleBaseInfoSuccess" />
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import RoomInfo from '@/components/data/RoomInfo'
export default {
  name: 'RoomDeatilsDrawer',
  components: { RoomInfo },
  props: {
    roomId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      baseInfo: {}
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    handleBaseInfoSuccess(data) {
      this.baseInfo = data
    }
  }
}
</script>
<style scoped lang="scss">
.room-deatils-content {
    padding: 0px 10px;
  .el-tabs__nav-scroll .el-tabs__item {
    font-size: 13px;
  }
}
</style>
