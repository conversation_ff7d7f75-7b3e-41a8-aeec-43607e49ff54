<template>
  <el-drawer
    title="关联资源组"
    :visible="true"
    :before-close="handleClose"
    :close-on-press-escape="false"
    :wrapper-closable="false"
    :modal-append-to-body="true"
    :append-to-body="true"
    custom-class="drawer-auto-layout"
  >

    <div v-loading="submitLoading" class="associated-resources">
      <div class="filter-container">
        <el-select
          v-model="listQuery.shelfStatus"
          placeholder="上/下架"
          style="width: 15%"
          class="filter-item"
          clearable
          @change="handleSearch"
        >
          <el-option v-for="item in showcaseTypes" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>

        <el-input
          v-model.trim="listQuery.id"
          v-number
          placeholder="ID"
          clearable
          style="width: 30%"
          class="filter-item"
        />
        <el-input
          v-model.trim="listQuery.name"
          placeholder="类型名称"
          clearable
          style="width: 30%"
          class="filter-item"
        />
        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleSearch"
        >
          搜索
        </el-button>
      </div>

      <div v-loading="listLoading" />
      <div v-for="(rootItem, rootIndex) in list" :key="rootIndex" class="source-card">
        <el-card class="box-card">
          <div slot="header" class="clearfix">

            <span>{{ rootItem.name }}
              <el-tag v-if="rootItem.shelfStatus" size="mini" type="success">上架</el-tag>
              <el-tag v-else size="mini" type="danger">下架</el-tag>
            </span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="clickSelected(rootItem)">确认选择 </el-button>
          </div>
          <div class="content">
            <div class="source-list">
              <div class="props-row">
                <div v-for="(item, index) in rootItem.rewardConfigList" :key="index" class="item">
                  <el-tooltip v-if="item.type === 'GOLD'" effect="dark" :content="item.content" placement="top-start">
                    <img src="@/assets/gold_icon.png" style="width:50px;height:50px;cursor: pointer;">
                  </el-tooltip>
                  <el-tooltip v-else-if="item.type === 'DIAMOND'" effect="dark" :content="item.content" placement="top-start">
                    <img src="@/assets/diamond_icon.png" style="width:50px;height:50px;cursor: pointer;">
                  </el-tooltip>
                  <el-tooltip v-else-if="item.type === 'GAME_COUPON'" effect="dark" :content="item.content" placement="top-start">
                    <img src="@/assets/game_coupon.png" style="width:50px;height:50px;cursor: pointer;">
                  </el-tooltip>
                  <el-tooltip v-else-if="item.type === 'SPECIAL_ID'" effect="dark" :content="item.content" placement="top-start">
                    <img v-if="item.type === 'SPECIAL_ID'" src="@/assets/special_id.png" style="width:50px;height:50px;cursor: pointer;">
                  </el-tooltip>
                  <div v-else class="preview-img">
                    <el-image
                      style="width: 50px; height: 50px"
                      :src="item.cover"
                      :preview-src-list="[item.cover]"
                    >
                      <div slot="error" class="image-slot">
                        <i class="el-icon-picture-outline" />
                      </div>
                    </el-image>
                    <div class="preview-svga">
                      <svgaplayer
                        type="popover"
                        :url="item.sourceUrl"
                      />
                    </div>
                    <div style="text-align: center;width: 50px; word-wrap: break-word;">{{ item.name }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <div class="footer">
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="listQuery.cursor"
        :limit.sync="listQuery.limit"
        layout="total, prev, pager, next"
        @pagination="renderData"
      />
    </div>

  </el-drawer>

</template>

<script>

import { pagePropsActivityRewardGroup, offPropsActivityRewardGroup } from '@/api/props'
import Pagination from '@/components/Pagination'
import { propsTypes } from '@/constant/type'

export default {
  components: { Pagination },
  props: {
    sysOrigin: {
      type: String,
      require: true,
      default: ''
    }
  },
  data() {
    return {
      submitLoading: false,
      showcaseTypes: [
        { value: false, name: '下架' },
        { value: true, name: '上架' }
      ],
      thisRow: {},
      thatSelectedUserId: {},
      propsTypes,
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        id: '',
        name: '',
        shelfStatus: true,
        sysOrigin: ''
      },
      listLoading: true
    }
  },
  watch: {
    sysOrigin: {
      handler(newVal) {
        if (newVal) {
          this.listQuery.sysOrigin = newVal
          this.renderData(true)
        }
      },
      immediate: true
    }
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        this.listQuery.cursor = 1
        this.list = []
      }
      that.listLoading = true
      if (isClean === true) {
        that.listQuery.cursor = 1
      }
      pagePropsActivityRewardGroup(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSwitchChange(row) {
      offPropsActivityRewardGroup(row.id, row.shelfStatus)
        .then(res => {})
        .catch(er => {
          row.shelfStatus = !row.shelfStatus
        })
    },
    clickSelected(row) {
      const that = this
      that.$emit('select', row)
    },
    handleSearch() {
      this.renderData(true)
    },
    queryUserDetails(row) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = row.id
    },
    handleMouseEnter(row) {
      this.thisRow = row
      this.thatSelectedUserId = row.id
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
<style scoped lang="scss">
.associated-resources {
  padding: 0px 10px 100px 10px;
  .source-card {
    margin-bottom: 10px;
     .props-row {
        width: 100%;
        display: flex;
        padding: 10px 0px;
        .item {
          width: 50px;
          margin: 0px 5px;
          height: 100px;
        }
        overflow: auto;
      }
  }
}
.footer {
    position:absolute;
    bottom: 0px;
    left: 0px;
    right: 0px;
    background-color: #FFFFFF;
    z-index: 9;
    text-align: center;
}
.dark-theme {
  .footer {
    background-color: #282830;
  }
}

</style>
