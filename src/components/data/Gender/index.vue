<script>
export default {
  name: 'Gender',
  functional: true,
  props: {
    gender: {
      type: Number,
      default: 1
    },
    genderName: {
      type: String,
      default: '男'
    },
    desc: {
      type: String,
      default: ''
    }
  },
  render(h, context) {
    const { desc, gender, genderName } = context.props
    const vnodes = []
    let descRes = ''
    if (genderName) {
      descRes += genderName + ' - '
    }

    if (desc) {
      descRes += desc
    }
    if (descRes.endsWith(' - ')) {
      descRes = descRes.substring(0, descRes.length - 3)
    }

    vnodes.push(<span class='gender'>
      <a title={ descRes }><i class={gender === 1 ? 'el-icon-male' : gender === 0 ? 'el-icon-female' : 'el-icon-pear' } />&nbsp;{ desc }</a>
    </span>)
    return vnodes
  }
}
</script>

<style scoped lang="scss">
.gender {
  i {
    font-size: 15px;
    font-weight: bold;
  }
  .el-icon-male {
    color: #99CCFF
  }
  .el-icon-female {
    color: #FFCCCC;
  }
}
</style>
