<template>
  <el-select
    v-model="selectValue"
    :loading="loading"
    :disabled="disabled"
    style="width: 100%;"
    :multiple="multiple"
    :multiple-limit="multipleLimit"
    :filterable="filterable"
    :clearable="clearable"
    :placeholder="placeholder"
    collapse-tags
    @change="selectChanged"
  >
    <el-input v-model="selectValue" style="display:none;" />
    <el-option
      v-for="(item, index) in permissionsSysOriginPlatforms"
      :key="index"
      :label="item.label"
      :value="item.value"
    >
      <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
      <span style="float: left;margin-left:10px">{{ item.label }}</span>
    </el-option>
  </el-select>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: 'SysOriginPermissionSelect',
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    sysOrigin: {
      type: String,
      require: true,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    },
    multipleLimit: {
      type: Number,
      default: 0
    },
    filterable: {
      type: Boolean,
      default: true
    },
    clearable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectValue: '',
      loading: false
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms', 'permissionsSysOriginPlatformAlls']),
    permissionsSysOrigins() {
      const that = this

      if (that.multiple === false) {
        return that.permissionsSysOriginPlatforms
      }

      if (!that.permissionsSysOriginPlatformAlls || that.permissionsSysOriginPlatformAlls.length <= 0) {
        return []
      }

      if (!that.permissionsSysOriginPlatforms || that.permissionsSysOriginPlatforms.length <= 0) {
        return []
      }
      return that.permissionsSysOriginPlatformAlls.filter(item => that.permissionsSysOriginPlatforms.some(permissions => item.value.startsWith(permissions.value))) || []
    }
  },

  created() {
    this.selectValue = this.multiple === true ? [] : ''

    if (this.value) {
      this.selectValue = this.value
      return
    }

    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (querySystem) {
      if (this.multiple === true) {
        this.selectValue.push(querySystem.value)
        this.initLoad(this.selectValue)
      } else {
        this.selectValue = querySystem.value
        this.initLoad(this.selectValue)
      }
    }
  },
  methods: {
    clearValue() {
      this.selectValue = ''
    },
    initLoad(val) {
      this.$emit('input', val)
      this.$emit('onLoad', val)
    },
    selectChanged(val) {
      this.$emit('change', val)
      this.$emit('input', val)
    }

  }
}
</script>

