<template>
  <div class="user-base-info-content">
    <div v-loading="registerLoading">
      <el-collapse v-model="nActiveCollapseNames">
        <el-collapse-item title="" name="packInfo">
          <template slot="title">
            <i class="el-icon-goods" />&nbsp;礼包基本信息
          </template>
          <div class="block-info">
            <div class="text-item">
              <el-row>
                <el-col :span="12">ID：{{ row.id }}</el-col>
                <el-col :span="12">产品单价：{{ row.unitPrice }}</el-col>
              </el-row>
            </div>
          </div>
        </el-collapse-item>

        <el-collapse-item title="" name="goldInfo">
          <template slot="title">
            <i class="el-icon-s-help" />&nbsp;金币
          </template>
          <div class="block-info">
            <div class="text-item">
              <el-row>
                <el-col :span="12">赠送金币：{{ candy }}</el-col>
              </el-row>
            </div>
          </div>
        </el-collapse-item>

        <el-collapse-item title="" name="giftInfo">
          <template slot="title">
            <i class="el-icon-s-cooperation" />&nbsp;礼物
          </template>
          <div class="photo-wall">
            <div v-for="item in giftWalls" :key="item.id" class="photo-wall-item">
              <el-tooltip :content="`${item.name}/${item.candy}(糖果)`">
                <el-image
                  style="width: 50px; height: 50px"
                  :src="item.url"
                />
              </el-tooltip>
              <span>数量:{{ item.quantity }}</span>
            </div>
          </div>
        </el-collapse-item>

        <el-collapse-item title="" name="avatarInfo">
          <template slot="title">
            <i class="el-icon-s-cooperation" />&nbsp;头像框
          </template>
          <div class="photo-wall">
            <div v-for="item in avatarWalls" :key="item.id" class="photo-wall-item">
              <el-tooltip :content="`${item.name}/${item.candy}(1天)`">
                <el-image
                  style="width: 50px; height: 50px"
                  :src="item.url"
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline" />
                  </div>
                </el-image>
              </el-tooltip>
              <span>有效期:{{ item.quantity }} (天)</span>
            </div>
          </div>
        </el-collapse-item>

        <el-collapse-item title="" name="rideInfo">
          <template slot="title">
            <i class="el-icon-s-cooperation" />&nbsp;坐骑
          </template>
          <div class="photo-wall">
            <div v-for="item in rideWalls" :key="item.id" class="photo-wall-item">
              <el-tooltip :content="`${item.name}/${item.candy}(1天)`">
                <el-image
                  style="width: 50px; height: 50px"
                  :src="item.url"
                />
              </el-tooltip>
              <span>有效期:{{ item.quantity }} (天)</span>
            </div>
          </div>
        </el-collapse-item>

      </el-collapse>

    </div>
  </div>
</template>
<script>
import { getGiftPackGold, getGiftPackConfigInfo } from '@/api/gift-pack'
export default {
  name: 'GiftPackInfo',
  props: {
    row: {
      type: Object,
      required: true
    },
    activeCollapseNames: {
      type: Array,
      required: false,
      default: () => ['packInfo', 'goldInfo', 'giftInfo', 'avatarInfo', 'rideInfo']
    }
  },
  data() {
    return {
      giftWalls: [],
      avatarWalls: [],
      rideWalls: [],
      nActiveCollapseNames: [],
      balanceLoading: false,
      registerLoading: false,
      candy: 0,
      authTypeLoading: false
    }
  },
  watch: {
    row: {
      immediate: true,
      deep: true,
      handler(newVal) {
        this.renderData(newVal.id)
      }
    },
    activeCollapseNames: {
      immediate: true,
      handler(newVal) {
        this.nActiveCollapseNames = newVal
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    renderData(giftPackId) {
      const that = this
      getGiftPackGold(giftPackId).then(res => {
        const { body } = res
        that.candy = body || 0
      }).catch(() => {
      })

      that.registerLoading = true
      getGiftPackConfigInfo({ giftPackId: giftPackId, type: 'GIFT' }).then(res => {
        const { body } = res
        that.registerLoading = false
        that.giftWalls = body || []
      }).catch(er => { that.registerLoading = false })

      getGiftPackConfigInfo({ giftPackId: giftPackId, type: 'AVATAR_FRAME' }).then(res => {
        const { body } = res
        that.avatarWalls = body || []
      }).catch(er => {
      })

      getGiftPackConfigInfo({ giftPackId: giftPackId, type: 'RIDE' }).then(res => {
        const { body } = res
        that.rideWalls = body || []
      }).catch(er => {
      })
    }
  }
}
</script>
<style scoped lang="scss">
.user-base-info-content {
    .header-content {
         display: flex;
         justify-content: flex-start;
         .right {
           padding: 24px 0px 0px 20px;
         }
    }
    .block-info {
        position: relative;
        .text-item {
          height: 36px;
          line-height: 36px;
          font-size: 14px;
          color: #4d4d4d;
          white-space:nowrap;
          overflow:hidden;
          text-overflow:ellipsis;
        }
   }
   .flag-icon {
     position: absolute;
   }
   .tags {
     position: absolute;
     left: 58px;
   }
}
.photo-wall {
  position: relative;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  .photo-wall-item{
    width: 33%;
    height: 50px;
    float: left;
    margin-bottom:10px;
  }
}
</style>
