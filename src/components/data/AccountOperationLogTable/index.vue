<!--账号处理日志列表-->
<template>
  <div class="accout-handler-log">
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
      height="300"
    >
      <el-table-column label="违规图" align="center">
        <template slot-scope="scope">
          <el-image
            style="width: 100px; height: 100px"
            :src="scope.row.userAvatar"
            fit="fill"
            :preview-src-list="[scope.row.coverPicture]"
          />
        </template>
      </el-table-column>
      <el-table-column prop="labelNames" label="机器标签" align="center" />
      <el-table-column prop="scores" label="参考分值" align="center" />
      <el-table-column prop="approvalResultName" label="确认结果" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.approvalResult === 'NORMAL'" style="color:#67C23A;">{{ scope.row.approvalResultName }}</span>
          <span v-else style="color:#F56C6C;">{{ scope.row.approvalResultName }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="approvalNickname" label="审批用户" align="center" />
      <el-table-column prop="createTime" label="记录时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" label="处理时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.updateTime | dateFormat }}
        </template>
      </el-table-column>

    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
  </div>
</template>

<script>

import { getVideoCallCensorApproval } from '@/api/approval'
import Pagination from '@/components/Pagination'

export default {
  name: 'UserVideoViolationTableDialog',
  components: { Pagination },
  props: {
    userId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      list: [],
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: '',
        approvalStatus: 2
      },
      total: 0,
      listLoading: false
    }
  },
  watch: {
    userId: {
      handler(newVal) {
        this.listQuery.userId = newVal
        this.renderData()
      },
      immediate: true
    }
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      getVideoCallCensorApproval(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      }).catch(er => {
        that.listLoading = false
      })
    }
  }
}
</script>
