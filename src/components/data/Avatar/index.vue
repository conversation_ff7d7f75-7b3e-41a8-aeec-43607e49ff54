<script>
import { extractColorByText } from '@/utils'
// import defaultAvatar from '@/assets/default_avatar.png'
export default {
  name: 'Avatar',
  functional: true,
  props: {
    url: {
      type: String,
      default: ''
    },
    // gender: {
    //   type: Number,
    //   default: 1
    // },
    // large / medium / small / mini
    size: {
      type: String,
      default: 'small'
    },
    customizeStyle: {
      type: String,
      default: null
    },
    nameAvatar: {
      type: String,
      default: null
    }
  },
  render(h, context) {
    const { url, size, customizeStyle, nameAvatar } = context.props
    const vnodes = []
    let styleStr = 'border-radius: 50%;'
    if (size === 'large') {
      styleStr = 'width: 100px; height: 100px;border-radius: 50%;'
    } else if (size === 'medium') {
      styleStr = 'width: 80px; height: 80px;border-radius: 50%;'
    } else if (size === 'small') {
      styleStr = 'width: 50px; height: 50px;border-radius: 50%;'
    } else if (size === 'mini') {
      styleStr = 'width: 30px; height: 30px;border-radius: 50%;'
    }

    const isNameAvatar = nameAvatar && !url

    if (isNameAvatar) {
      const color = extractColorByText(nameAvatar)

      if (styleStr.indexOf('background') < 0) {
        styleStr = `background-color: ${color};${styleStr}`
      }

      if (styleStr.indexOf('font-weight') < 0) {
        styleStr = `font-weight: bold;${styleStr}`
      }

      styleStr = `font-size:16px;color: #FFFFFF;${styleStr}`
    }

    if (customizeStyle) {
      styleStr = customizeStyle
    }

    if (isNameAvatar) {
      const firstName = nameAvatar.trim().charAt(0)
      vnodes.push(<div
        class='flex-c'
        style= { styleStr }
      >
        { firstName }
      </div>)

      return vnodes
    }

    const avatarUrl = url // || defaultAvatar
    vnodes.push(<el-image
      style= { styleStr }
      src={ avatarUrl }
      lazy={ true }
      fit='fill'
      preview-src-list={ [avatarUrl] }
    >
      <div slot='error' class='image-slot'>
        <i class='el-icon-picture-outline'></i>
      </div>
    </el-image>)

    return vnodes
  }
}
</script>
<style scoped>
</style>
