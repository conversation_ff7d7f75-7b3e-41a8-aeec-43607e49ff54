<template>
  <div class="gift-select">
    <el-drawer
      :title="'礼物选择('+giftSize+')'"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div class="gift-content">
        <el-input v-model="queryString" placeholder="请输入礼物ID/礼物名称/code" @input="inputQueryString" />
        <div v-if="listLoading" v-loading="listLoading" style="margin-top: 24px;" />
        <div class="gift-list flex-c flex-wrap">
          <div v-for="(item, index) in list" :key="index" class="gift-item">
            <div class="gift" @click="clickGift(item)">
              <div class="tips">点击选择</div>
              <img :src="item.giftPhoto">
            </div>
            <div class="gift-amount flex-c">
              <img v-if="item.type === 'GOLD'" src="@/assets/gold_icon.png">
              <img v-if="item.type === 'DIAMOND'" src="@/assets/diamond_icon.png">
              {{ item.giftCandy }}
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>

import { listGiftBySysOrigin } from '@/api/gift'
export default {
  props: {
    sysOrigin: {
      type: String,
      require: true,
      default: ''
    }
  },
  data() {
    return {
      queryString: '',
      list: [],
      listLoading: false,
      searchList: []
    }
  },
  computed: {
    giftSize() {
      return this.list ? this.list.length : 0
    }
  },
  watch: {
    sysOrigin: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (!newVal) {
          return
        }
        this.loadGift()
      }
    }
  },
  created() {
    this.loadGift()
  },
  methods: {
    loadGift() {
      const that = this
      that.listLoading = true
      listGiftBySysOrigin(that.sysOrigin).then(res => {
        that.listLoading = false
        that.list = res.body || []
        that.searchList = that.list
      }).catch(er => {
        that.listLoading = false
      })
    },
    clickGift(row) {
      this.$emit('select', row)
    },
    inputQueryString() {
      const that = this
      if (!that.queryString || !that.queryString.trim()) {
        that.list = that.searchList
        return
      }
      if (!that.searchList || that.searchList.length <= 0) {
        that.list = []
        return
      }

      that.list = that.searchList.filter(item => (item.giftName && item.giftName.indexOf(this.queryString) >= 0) ||
      (item.id && item.id.indexOf(this.queryString) >= 0) ||
      (item.giftCode && item.giftCode.indexOf(this.queryString) >= 0))
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
<style scoped lang="scss">
.gift-content {
  padding: 0px 10px;
  .gift-list {
    padding: 10px 0px;
    .gift-item {
      position: relative;
      width: 22%;
      margin: 5px;
      .gift {
        position: relative;
        border: 1px dashed #999999;
        border-radius: 5px;
        height: 100px;
        width: 100%;
        overflow: hidden;
        cursor: pointer;
        .tips {
          position: absolute;
          color: #FFFFFF;
          left: 20%;
          top: 40%;
          cursor: pointer;
          display: none;
        }
        img {
          width: 100%;
          height: 100%;
        }
        &:hover {
          .tips {
            display: block !important;
          }
        }
      }
      .gift-amount {
          text-align: center;
          padding: 2px;
          img {
            width: 20px;
            height: 20px;
          }
      }
    }

  }
}

</style>
