<template>
  <div class="sales-overview-charts">
    <div class="charts-body">
      <div class="filter-container">
        <el-select
          v-model="listQuery.sysOrigin"
          placeholder="系统"
          style="width: 120px"
          class="filter-item"
          @change="renderData"
        >
          <el-option
            v-for="item in permissionsSysOriginPlatforms"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
            <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
            <span style="float: left;margin-left:10px">{{ item.label }}</span>
          </el-option>
        </el-select>
        <el-select
          v-model="listQuery.dateType"
          placeholder="时间类型"
          style="width: 120px"
          class="filter-item"
          @change="changeDateType"
        >
          <el-option
            v-for="item in queryTypes"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-select
          v-model="listQuery.propsType"
          placeholder="道具类型"
          style="width: 120px"
          class="filter-item"
          @change="renderData"
        >
          <el-option v-for="item in propsTypes" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>

        <div class="filter-item">
          <el-date-picker
            v-model="listQuery.date"
            :clearable="false"
            :value-format="selectedDateType.dateValueFormat"
            :type="selectedDateType.dateType"
            placeholder="选择日期"
            :editable="false"
            @change="renderData"
          />
        </div>
        <div class="filter-item">
          总额: {{ sale.total || '-' }}
        </div>
      </div>
      <div v-loading="loading">
        <bar-graph-charts :charts-data="sale.propsSales" :height="height" />
      </div>

    </div>
  </div>
</template>
<script>
import { getPropsSale } from '@/api/statistics'
import { formatDate } from '@/utils'
import { propsTypes } from '@/constant/type'
import { mapGetters } from 'vuex'
import BarGraphCharts from './bar-graph-charts'

export default {
  components: { BarGraphCharts },
  props: {
    height: {
      type: String,
      require: false,
      default: '600px'
    }
  },
  data() {
    return {
      propsTypes,
      dateTypeMap: {
        'DAY': {
          dateType: 'date',
          dateValueFormat: 'yyyyMMdd',
          defaultDate: formatDate(new Date(), 'yyyyMMdd')
        },
        'MONTH': {
          dateType: 'month',
          dateValueFormat: 'yyyyMM',
          defaultDate: formatDate(new Date(), 'yyyyMM')
        },
        'YEAR': {
          dateType: 'year',
          dateValueFormat: 'yyyy',
          defaultDate: formatDate(new Date(), 'yyyy')
        }
      },
      selectedDateType: {},
      listQuery: {
        sysOrigin: '',
        dateType: 'MONTH',
        propsType: 'AVATAR_FRAME',
        date: ''
      },
      queryTypes: [
        { label: '天', value: 'DAY' },
        { label: '月', value: 'MONTH' },
        { label: '年', value: 'YEAR' }
      ],
      loading: false,
      sale: {}
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    this.selectedDateType = this.dateTypeMap[this.listQuery.dateType]
    this.listQuery.date = this.selectedDateType.defaultDate
    if (this.permissionsSysOriginPlatforms && this.permissionsSysOriginPlatforms.length > 0) {
      this.listQuery.sysOrigin = this.permissionsSysOriginPlatforms[0].value
    }
    this.renderData()
  },
  methods: {
    renderData() {
      const that = this
      that.loading = true
      getPropsSale(that.listQuery).then(res => {
        that.loading = false
        that.sale = res.body || {}
      }).catch(er => {
        that.loading = false
      })
    },
    changeDateType() {
      this.selectedDateType = this.dateTypeMap[this.listQuery.dateType]
      this.listQuery.date = this.selectedDateType.defaultDate
      this.renderData()
    }
  }
}
</script>
<style scoped lang="scss">

</style>
