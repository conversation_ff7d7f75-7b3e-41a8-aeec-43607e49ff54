<template>
  <div class="props-config-edit">
    <el-dialog
      title="总揽销售报表"
      :visible="true"
      :before-close="handleClose"
      :close-on-click-modal="false"
      width="80%"
    >
      <props-sales-overview-charts height="300px" />
    </el-dialog>
  </div>
</template>
<script>
import PropsSalesOverviewCharts from '@/components/data/PropsSalesOverviewCharts'
export default {
  components: { PropsSalesOverviewCharts },
  data() {
    return {}
  },
  methods: {
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
<style scoped lang="scss">

</style>
