<!--账户审批记录表格-->
<template>
  <el-dialog
    title="账户审批记录"
    :visible="true"
    width="70%"
    :before-close="handleClose"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
      height="400"
    >
      <el-table-column prop="statusName" label="审批类型" align="center" />
      <el-table-column prop="approvalUserName" label="审批人" align="center">
        <template slot-scope="scope">
          <span
            v-if="scope.row.originType"
          >{{ scope.row.approvalUserName }} (App)</span>
          <span
            v-if="!scope.row.originType"
          >{{ scope.row.approvalUserName }} (后台)</span>
          <span v-else>待处理</span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="审批时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column prop="description" label="备注" align="center" />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
  </el-dialog>
</template>

<script>
import { getUserStatusLogTable } from '@/api/approval'
import Pagination from '@/components/Pagination'

export default {
  components: { Pagination },
  props: {
    row: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      list: [],
      listQuery: {
        cursor: 1,
        limit: 20,
        beApprovalUser: ''
      },
      total: 0,
      listLoading: false
    }
  },
  watch: {
    row: {
      immediate: true,
      deep: true,
      handler(newVal) {
        this.listQuery.beApprovalUser = newVal.id
        this.renderData()
      }
    }
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      getUserStatusLogTable(that.listQuery)
        .then(res => {
          const { body } = res
          that.total = body.total || 0
          that.list = body.records
          that.listLoading = false
        })
        .catch(er => {
          that.listLoading = false
        })
    },
    changeActive($event) {
      $event.currentTarget.play()
    },
    removeActive($event) {
      $event.currentTarget.pause()
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
