<template>
  <div class="search-room-input">
    <el-tooltip v-model="showTips" :manual="true" effect="dark" :content="tipsContent">
      <el-input v-model.trim="content" v-loading="loading" :class="{'success': roomProfile.id && roomProfile.id > 0 , 'fail': (!roomProfile.id || roomProfile.id < 0) && failShow }" placeholder="请输入房间ID" class="input-with-select" @blur="clickSearch">
        <el-select slot="prepend" v-model="selectType" placeholder="类型" @change="clickSearch">
          <el-option label="长ID" value="LONG_ID" />
          <el-option label="短ID" value="SHORT_ID" />
        </el-select>
      </el-input>
    </el-tooltip>

    <el-dialog
      title="房间搜索"
      :visible="visibleSelecteDialog"
      :before-close="handleClose"
      :append-to-body="true"
    >
      <span>找到多个结果, 请选择您期望</span>
      <div class="select-room">
        <div v-for="(item, index) in roomProfiles" :key="index" class="user-item flex-l">
          <el-image
            style="width: 40px; height: 40px; margin-right:10px;border-radius: 100%; flex-shrink: 0;"
            :src="item.roomCover"
            :preview-src-list="[item.roomCover]"
          >
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline" />
            </div>
          </el-image>
          <sys-origin-icon :icon="item.sysOrigin" />
          <div class="nickname nowrap-ellipsis">
            {{ item.roomName }}
          </div>
          <div class="selected-but">
            <el-button type="text" @click="clickSelected(item)">选择</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>

</template>
<script>
import { getRoomProfileBySysOriginAccount, listRoomProfileByAccount, getRoomProfileByRoomId } from '@/api/room'
export default {
  name: 'SearchRoomInput',
  props: {
    sysOrigin: {
      type: String,
      require: false,
      default: ''
    },
    roomId: {
      type: String,
      require: false,
      default: ''
    }
  },
  data() {
    return {
      showTips: false,
      tipsContent: '',
      loading: false,
      content: '',
      selectType: 'SHORT_ID',
      failShow: false,
      roomProfile: {},
      roomProfiles: [],
      visibleSelecteDialog: false
    }
  },
  watch: {
    roomId: {
      handler(val) {
        if (val === '') {
          return
        }
        if (val.length > 15) {
          this.selectType = 'LONG_ID'
          this.content = val
          this.clickSearch()
          return
        }
        if (val.length < 15) {
          this.selectType = 'SHORT_ID'
          this.content = val
          return
        }
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      this.$emit('closeSelectDialog')
      this.visibleSelecteDialog = false
    },
    clickSelected(item) {
      this.visibleSelecteDialog = false
      this.$emit('success', item)
    },
    emitFail() {
      this.roomProfiles = []
      this.roomProfile = {}
      this.failShow = true
      this.$emit('fail')
    },
    failTipsContent(content) {
      this.emitFail()
      this.showTips = true
      this.tipsContent = content
    },
    clickSearch() {
      const that = this
      that.$emit('load')
      that.failShow = false
      that.showTips = false
      if (!that.content) {
        that.emitFail()
        that.showTips = false
        that.failShow = false
        return
      }
      if (!/\d+/.test(that.content)) {
        that.failTipsContent('输入内容必须是正整数!')
        return
      }
      if (that.content.length < 1) {
        that.failTipsContent('输入账号必须>=1位数!')
        return
      }
      if (that.roomProfile && (that.roomProfile.roomAccount === that.content || that.roomProfile.roomAccount === that.content)) {
        that.$emit('success', that.roomProfile)
        return
      }
      that.loading = true
      that.search(that.selectType, that.content).then(res => {
        that.loading = false
        const result = res.body
        if (!result) {
          that.failTipsContent('没有找到房间信息!')
          return
        }

        if (result.length > 0) {
          this.failShow = false
          that.roomProfiles = result
          if (that.roomProfiles.length === 1) {
            that.roomProfile = that.roomProfiles[0]
            that.$emit('success', that.roomProfile)
            return
          }
          that.visibleSelecteDialog = true
          return
        }

        if (result.length === 0) {
          that.failTipsContent('没有找到房间信息!')
          return
        }

        that.roomProfile = result
        that.$emit('success', that.roomProfile)
      }).catch(er => {
        that.loading = false
        console.error('clickSearch', er)
        that.failTipsContent('请求数据错误!')
      })
    },
    search(type, id) {
      if (type === 'LONG_ID') {
        return getRoomProfileByRoomId(id)
      }
      if (type === 'SHORT_ID') {
        return this.sysOrigin ? getRoomProfileBySysOriginAccount(this.sysOrigin, id) : listRoomProfileByAccount(id)
      }
    }
  }
}
</script>
<style lang="scss">
.search-room-input {
  .el-select .el-input {
    width: 100px;
  }
  .success > .el-input__inner {
    border: 1px solid #67C23A;
  }
  .fail > .el-input__inner {
     border: 1px solid #F56C6C;
  }
}
</style>
<style scoped lang="scss">
.search-room-input {
.select-room {
  .user-item {
    padding: 5px 0rem;
    img {
      border-radius: 100%;
    }
    .nickname {
      width: 100%;
      padding: 10px;
    }
  }
}
}
</style>
