<template>
  <div class="props-selected">
    <el-drawer
      title="选择道具"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div class="drawer-content">
        <div class="add-conf-but">
          <el-select
            v-model="listQuery.propsType"
            style="width: 30%"
            placeholder="道具类型"
            :disabled="disableAddContent"
            @change="propsTypeChange"
          >
            <el-option
              v-for="(item, index) in configPropsTypes"
              :key="index"
              :label="item.name"
              :value="item.propsType"
            />
          </el-select>
          <el-input v-model="listQuery.queryString" placeholder="道具名称/ID" style="width: 65%;" />
        </div>
        <div class="props-content">
          <div v-if="listLoading" v-loading="listLoading" style="margin-top: 24px;" />
          <div class="props-list flex-c flex-wrap">
            <div v-for="(item, index) in list" :key="index" class="props-item">
              <div v-if="item.sourceUrl" class="player">
                <svgaplayer
                  type="popover"
                  :url="item.sourceUrl || ''"
                />
              </div>
              <div class="props" @click="clickPropsItem(item)">
                <div class="tips">点击选择</div>
                <img :src="item.cover">
              </div>
              <div class="props-amount flex-c">
                <div class="nowrap-ellipsis"> {{ item.name }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>

import { listGiftBySysOrigin } from '@/api/gift'
import { listBadgePictureBySysOrigin } from '@/api/badge'
import { listSysOriginTypeList, listNotFamilyBySysOriginType } from '@/api/props'
import { listGroupBySysOrigin } from '@/api/sys-emoji'
export default {
  props: {
    propsType: {
      type: String,
      return: false,
      default: 'AVATAR_FRAME'
    },
    sysOrigin: {
      type: String,
      return: true,
      default: ''
    }
  },
  data() {
    return {
      listQuery: {
        propsType: '',
        queryString: ''
      },
      configPropsTypes: [
        { propsType: 'AVATAR_FRAME', name: '头像框', loading: false, loadData: false, list: [], placeholder: '天' },
        { propsType: 'RIDE', name: '座驾', loading: false, loadData: false, list: [], placeholder: '天' },
        { propsType: 'NOBLE_VIP', name: '贵族', loading: false, loadData: false, list: [], placeholder: '天' },
        { propsType: 'THEME', name: '主题背景', loading: false, loadData: false, list: [], placeholder: '天' },
        { propsType: 'GIFT', name: '礼物', loading: false, loadData: false, list: [], placeholder: '数量' },
        { propsType: 'BADGE', name: '用户徽章', loading: false, loadData: false, list: [], placeholder: '天' },
        { propsType: 'ROOM_BADGE', name: '房间徽章', loading: false, loadData: false, list: [], placeholder: '天' },
        { propsType: 'EMOJI', name: '表情包', loading: false, loadData: false, list: [], placeholder: '数量' },
        { propsType: 'CHAT_BUBBLE', name: '聊天气泡', loading: false, loadData: false, list: [], placeholder: '天' },
        { propsType: 'FLOAT_PICTURE', name: '飘窗', loading: false, loadData: false, list: [], placeholder: '天' },
        { propsType: 'CUSTOMIZE', name: '自定义(只读)', loading: false, loadData: false, list: [], placeholder: '描述' },
        { propsType: 'FRAGMENTS', name: '碎片', loading: false, loadData: false, list: [], placeholder: '数量' }
      ],
      configPropsTypesMap: {},
      disableAddContent: false,
      svgaplayerVisable: false
    }
  },
  computed: {
    list() {
      const that = this
      const sources = that.configPropsTypesMap[that.listQuery.propsType] || {}
      const sourceList = sources.list || []
      if (!that.listQuery.queryString) {
        return sourceList
      }
      return sourceList.filter(row => (row.name && row.name.indexOf(that.listQuery.queryString) >= 0) ||
      (row.id && row.id.indexOf(that.listQuery.queryString) >= 0))
    },
    listLoading() {
      const sources = this.configPropsTypesMap[this.listQuery.propsType]
      return sources && sources.loading === true
    }
  },
  watch: {
    propsType: {
      handler(newVal) {
        if (newVal) {
          this.listQuery.propsType = newVal
        }
      },
      immediate: true
    },
    sysOrigin: {
      handler(newVal) {
        if (!newVal) {
          return
        }
      },
      immediate: true
    }
  },
  created() {
    this.configPropsTypesMap = this.configPropsTypesToMap()
    this.propsTypeChange()
  },
  methods: {
    configPropsTypesToMap() {
      const resultMap = {}
      this.configPropsTypes.forEach(row => {
        resultMap[row.propsType] = row
      })
      return resultMap
    },
    propsTypeChange() {
      this.loadSource(this.listQuery.propsType)
    },
    loadSource(type) {
      const that = this
      if (that.configPropsTypesMap[type].loadData === true) {
        return
      }
      that.configPropsTypesMap[type].loading = true
      if (that.isGift(type)) {
        that.disableAddContent = true
        listGiftBySysOrigin(that.sysOrigin).then(res => {
          that.disableAddContent = false
          that.configPropsTypesMap[type].loadData = true
          that.configPropsTypesMap[type].loading = false
          const list = res.body || []
          that.configPropsTypesMap[type].list = list.map(item => {
            return { type, id: item.id, name: item.giftName, amount: item.giftCandy, cover: item.giftPhoto, sourceUrl: item.giftSourceUrl }
          })
        }).catch(er => {
          that.disableAddContent = false
          that.configPropsTypesMap[type].loading = false
          console.error(er)
        })
        return
      }

      const badgeType = that.getBadgeType(type)
      if (badgeType) {
        that.disableAddContent = true
        listBadgePictureBySysOrigin(that.sysOrigin, badgeType).then(res => {
          that.disableAddContent = false
          that.configPropsTypesMap[type].loadData = true
          that.configPropsTypesMap[type].loading = false
          const list = res.body || []
          that.configPropsTypesMap[type].list = list.map(item => {
            return { type, id: item.badgeConfigId, name: item.badgeName, amount: 0, cover: item.selectUrl, sourceUrl: item.animationUrl }
          })
        }).catch(er => {
          that.disableAddContent = false
          that.configPropsTypesMap[type].loading = false
          console.error(er)
        })
        return
      }

      if (type === 'NOBLE_VIP') {
        that.disableAddContent = true
        listNotFamilyBySysOriginType(that.sysOrigin, 'NOBLE_VIP').then(res => {
          that.disableAddContent = false
          that.configPropsTypesMap[type].loadData = true
          that.configPropsTypesMap[type].loading = false
          const list = res.body || []
          that.configPropsTypesMap[type].list = list.map(item => {
            return { type, id: item.id, name: item.name, amount: item.amount, cover: item.cover, sourceUrl: item.sourceUrl }
          })
        }).catch(er => {
          that.disableAddContent = false
          that.configPropsTypesMap[type].loading = false
          console.error(er)
        })
        return
      }

      if (type === 'EMOJI') {
        listGroupBySysOrigin(that.sysOrigin).then(res => {
          that.disableAddContent = false
          that.configPropsTypesMap[type].loadData = true
          that.configPropsTypesMap[type].loading = false
          const list = res.body || []
          that.configPropsTypesMap[type].list = list.map(item => {
            return { type, id: item.id, name: item.groupName, cover: item.cover }
          })
        }).catch(er => {
          that.disableAddContent = false
          that.configPropsTypesMap[type].loading = false
          console.error(er)
        })
        return
      }

      that.disableAddContent = true
      listSysOriginTypeList(that.sysOrigin, type).then(res => {
        that.disableAddContent = false
        that.configPropsTypesMap[type].loadData = true
        that.configPropsTypesMap[type].loading = false
        const list = res.body || []
        that.configPropsTypesMap[type].list = list.map(item => {
          return { type, id: item.id, name: item.name, amount: item.amount, cover: item.cover, sourceUrl: item.sourceUrl }
        })
      }).catch(er => {
        that.disableAddContent = false
        that.configPropsTypesMap[type].loading = false
        console.error(er)
      })
    },
    getBadgeType(type) {
      if (this.isUserBadge(type)) {
        return 'ACTIVITY'
      }

      if (this.isRoomBadge(type)) {
        return 'ROOM_ACHIEVEMENT'
      }
      return null
    },
    isUserBadge(type) {
      return type === 'BADGE'
    },
    isRoomBadge(type) {
      return type === 'ROOM_BADGE'
    },
    isGift(type) {
      return type === 'GIFT'
    },
    handleClose() {
      this.$emit('close')
    },
    clickPropsItem(row) {
      this.$emit('select', row)
    },
    clickSvgaplayer() {
      this.svgaplayerVisable = true
    }
  }
}
</script>
<style scoped lang="scss">
.drawer-content {
  overflow: auto;
  padding: 15px;
  .props-content {
    padding: 0px 10px;
    .props-list {
      padding: 10px 0px;
      .props-item {
        position: relative;
        width: 22%;
        margin: 5px;
        .player {
            position: absolute;
            right: 5px;
            top: -10px;
            z-index: 10;
          }
        .props {
          position: relative;
          border: 1px dashed #999999;
          border-radius: 5px;
          height: 100px;
          width: 100%;
          overflow: hidden;
          cursor: pointer;
          .tips {
            position: absolute;
            color: #e7dbdb;
            left: 20%;
            top: 40%;
            cursor: pointer;
            display: none;
          }
          img {
            width: 100%;
            height: 100%;
          }
          &:hover {
            .tips {
              display: block !important;
            }
          }
        }
        .props-amount {
            text-align: center;
            padding: 2px;
            img {
              width: 20px;
              height: 20px;
            }
        }
      }

    }
  }
}

</style>
