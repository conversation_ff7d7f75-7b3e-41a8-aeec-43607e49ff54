<script>
export default {
  name: 'SysOriginIcon',
  functional: true,
  props: {
    icon: {
      type: String,
      default: ''
    },
    desc: {
      type: String,
      default: ''
    },
    size: {
      type: String,
      default: '28px'
    }
  },
  render(h, context) {
    const unknown = 'unknown'
    const svgIconMap = {
      'sugartime': 'sugartime',
      'sugartime_lite': 'sugartime_lite',
      'ricom': 'ricom',
      'ricom_lite': 'ricom_lite',
      'xixi': 'xixi',
      'tim_chat': 'tim_chat',
      'global_setting': 'application_manager',
      'cacoo': 'cacoo',
      'aswat': 'aswat',
      'yahlla': 'yahlla',
      'tim_chat_lite': 'tim_chat_lite',
      'aswat_lite': 'aswat_lite',
      'two_fun': 'two_fun',
      'yolo': 'yolo',
      'tarab': 'tarab',
      'halar': 'halar',
      'marcie': 'marcie'
    }
    const { icon, desc, size } = context.props
    const vnodes = []
    const iconLowerCase = icon.toLowerCase()
    const styleStr = `font-size:${size}`
    const iconClass = svgIconMap[iconLowerCase]
    if (iconClass) {
      vnodes.push(<el-tooltip class='item' effect='dark'>
        <div slot='content'>{(desc || icon)}</div>
        <svg-icon style={styleStr} class='platform-svg-icon' icon-class={iconClass}/>
      </el-tooltip>)
    } else {
      const descUnknown = desc + unknown
      vnodes.push(<el-tooltip class='item' effect='dark'>
        <div slot='content'>{ descUnknown }</div>
        <svg-icon style={styleStr} class='platform-svg-icon' icon-class={unknown}/>
      </el-tooltip>)
    }

    return vnodes
  }
}
</script>
<style scoped>
    .platform-svg-icon{
        font-size: 28px;
        cursor: pointer;
    }
</style>
