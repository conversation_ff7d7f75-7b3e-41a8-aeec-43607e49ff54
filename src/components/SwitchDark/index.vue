<template>
  <el-switch
    v-model="isDark"
    inline-prompt
    active-color="#0a0a0a"
    inactive-color="#dcdfe6"
    active-icon-class="el-icon-sunny"
    inactive-icon-class="el-icon-moon"
    @change="onAddDarkChange"
  />
</template>
<script>
import { getTheme, setTheme, removeTheme } from '@/utils/theme'
export default {
  data() {
    return {
      isDark: getTheme() !== 'dark-theme'
    }
  },
  methods: {
    onAddDarkChange() {
      const body = document.documentElement
      if (body.className && body.className.indexOf('dark-theme') >= 0) {
        removeTheme()
        body.setAttribute('class', '')
        return
      }
      setTheme('dark-theme')
      body.setAttribute('class', 'dark-theme')
    }
  }
}
</script>
