<script>
export default {
  name: 'PlatformSvgIcon',
  functional: true,
  props: {
    icon: {
      type: String,
      default: ''
    },
    desc: {
      type: String,
      default: ''
    },
    size: {
      type: String,
      default: '28px'
    }
  },
  render(h, context) {
    const unknown = 'unknown'
    const platformSvgIconMap = {
      'ios': 'ios',
      'apple': 'ios',
      'android': 'android',
      'phone': 'phone',
      'mobile': 'phone',
      'html5': 'html5',
      'html': 'html5',
      'h5': 'html5',
      'google': 'google',
      'facebook': 'facebook',
      'snapchat': 'snapchat',
      'huawei': 'huawei',
      'payermax': 'payermax',
      'payer_max': 'payermax',
      'paypal': 'paypal',
      'pay_pal': 'paypal',
      'stripe': 'stripe',
      'airwallex': 'airwallex'
    }
    const { icon, desc, size } = context.props
    const vnodes = []
    const iconLowerCase = icon.toLowerCase()
    const styleStr = `font-size:${size}`
    if (iconLowerCase) {
      const iconClass = platformSvgIconMap[iconLowerCase]
      if (iconClass !== undefined) {
        vnodes.push(<el-tooltip class='item' effect='dark'>
          <div slot='content'>{(desc || icon)}</div>
          <svg-icon style={styleStr} class='platform-svg-icon' icon-class={iconClass}/>
        </el-tooltip>)
      } else {
        vnodes.push(<div slot='content'>{(desc || icon)}</div>)
      }
    } else {
      const descUnknown = desc + unknown
      vnodes.push(<el-tooltip class='item' effect='dark'>
        <div slot='content'>{ descUnknown }</div>
        <svg-icon style={styleStr} class='platform-svg-icon' icon-class={unknown}/>
      </el-tooltip>)
    }

    return vnodes
  }
}
</script>
<style scoped>
    .platform-svg-icon{
        font-size: 28px;
        cursor: pointer;
        border-radius: 5px;
    }
</style>
