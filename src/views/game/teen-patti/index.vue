<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="item in tables" :key="item.name" :label="item.title" :name="item.component" />
      <component :is="activeName" />
    </el-tabs>
  </div>
</template>
<script>
import Config from './config'
import List from './list'
export default {
  name: 'GameTeenPatti',
  components: { Config, List },
  data() {
    return {
      activeName: 'List',
      tables: [
        {
          title: '记录',
          component: 'List'
        }, {
          title: '配置',
          component: 'Config'
        }
      ]
    }
  }
}
</script>
