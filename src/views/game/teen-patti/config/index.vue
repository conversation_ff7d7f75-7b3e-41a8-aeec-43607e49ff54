<template>
  <div class="fruit-form">
    <el-collapse v-model="activeNames">
      <el-collapse-item title="参数配置" name="config">
        <el-form ref="form" v-loading="listLoading" :model="form" :rules="formRules" label-width="150px">
          <el-form-item prop="lotteryRatio">
            <div slot="label" style="display: inline-block;">
              <el-tooltip class="item" effect="dark" content="系统出奖概率值, 中不中奖就看你的运气咯~">
                <div> 中奖概率<i class="el-icon-question" /></div>
              </el-tooltip>
            </div>
            <el-input v-model="form.lotteryRatio" v-number placeholder="有效范围0~100" />
          </el-form-item>
          <el-form-item prop="extractAmountRatio">
            <div slot="label" style="display: inline-block;">
              <el-tooltip class="item" effect="dark" content="系统抽取手续费比率: 本轮写入奖金池的金额中获得 %? 比率给系统">
                <div> 系统抽取比率<i class="el-icon-question" /></div>
              </el-tooltip>
            </div>
            <el-input v-model="form.extractAmountRatio" v-number placeholder="有效范围0~100" />
          </el-form-item>
          <el-form-item>
            <div slot="label" style="display: inline-block;">
              <el-tooltip class="item" effect="dark" content="扣除手续费后累计奖金池余额">
                <div> 奖金池余额<i class="el-icon-question" /></div>
              </el-tooltip>
            </div>
            {{ bonusBalance }}
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :loading="submitLoading" @click="onSubmit()">提交</el-button>
          </el-form-item>
        </el-form>
      </el-collapse-item>
    </el-collapse>

  </div>
</template>
<script>
import { getConfigTeenPatti, updateConfigTeenPatti, getBonusAmountBalanceTeenPatti } from '@/api/game'
export default {
  name: 'DoubleLayerFruitConfig',
  data() {
    return {
      activeNames: ['config', 'monthly-count'],
      listLoading: false,
      submitLoading: false,
      form: {
        lotteryRatio: '',
        extractAmountRatio: '',
        everyDayAllowLossAmount: '',
        fruitSpecialConfigs: [
          { fruitType: 'STRAWBERRY', shipments: '', betweenLotteryRatio: '' },
          { fruitType: 'BANANA', shipments: '', betweenLotteryRatio: '' },
          { fruitType: 'WATERMELON', shipments: '', betweenLotteryRatio: '' },
          { fruitType: 'GRAPE', shipments: '', betweenLotteryRatio: '' },
          { fruitType: 'APPLE', shipments: '', betweenLotteryRatio: '' },
          { fruitType: 'UGLY_ORANGE', shipments: '', betweenLotteryRatio: '' },
          { fruitType: 'PEAR', shipments: '', betweenLotteryRatio: '' },
          { fruitType: 'CHERRY', shipments: '', betweenLotteryRatio: '' }
        ]
      },
      formRules: {
        lotteryRatio: [{ required: true, message: '不可为空', trigger: 'blur' }],
        extractAmountRatio: [{ required: true, message: '不可为空', trigger: 'blur' }]
      },
      monthlyCountLoading: false,
      monthlyCounts: [],
      bonusBalance: 0
    }
  },
  created() {
    const that = this
    that.loadConfig()
    that.loadBonusBalance()
  },
  methods: {
    loadConfig() {
      const that = this
      that.listLoading = true
      getConfigTeenPatti().then(res => {
        that.listLoading = false
        const { body } = res
        if (body) {
          that.form = body
        }
      }).catch(er => {
        that.listLoading = false
        console.error(er)
      })
    },
    loadBonusBalance() {
      const that = this
      getBonusAmountBalanceTeenPatti().then(res => {
        that.bonusBalance = res.body || 0
      })
    },
    onSubmit() {
      const that = this
      that.$refs.form.validate((valid) => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.$confirm('是否确定修改配置值?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          that.submitLoading = true
          updateConfigTeenPatti(that.form).then(res => {
            that.submitLoading = false
            that.$message({
              type: 'success',
              message: 'Successful'
            })
          }).catch(er => {
            that.$opsMessage.success()
          })
        }).catch(() => {
          that.submitLoading = false
          that.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
      })
    }
  }
}
</script>
