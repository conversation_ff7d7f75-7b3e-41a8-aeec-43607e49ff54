<template>
  <el-dialog
    title="押注用户"
    :visible="true"
    :before-close="handleClose"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    width="80%"
  >
    <div class="app-container">
      <div class="filter-container">
        <div class="filter-item">
          <account-input v-model="listQuery.userId" placeholder="用户ID" />
        </div>
        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          :disabled="searchDisabled"
          @click="handleSearch"
        >
          搜索
        </el-button>
      </div>
      <el-table
        v-loading="listLoading"
        :data="list"
        :before-close="handleClose"
        element-loading-text="Loading"
        fit
        highlight-current-row
        max-height="350px"
      >
        <el-table-column label="头像" align="center" width="150">
          <template slot-scope="scope">
            <user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" />
          </template>
        </el-table-column>
        <el-table-column label="押注类型" prop="userBet.type" align="center" />
        <el-table-column label="押注数量" prop="userBet.betQuantity" align="center" />
        <el-table-column label="倍数" prop="userBet.multiple" align="center" />
        <el-table-column label="是否中奖" align="center">
          <template slot-scope="scope">
            {{ scope.row.userBet.lottery ? '中奖('+scope.row.userBet.receiveStatus+')' : '未中奖' }}
          </template>
        </el-table-column>
        <el-table-column prop="userBet.createTime" label="创建时间" align="center">
          <template slot-scope="scope">
            {{ scope.row.userBet.createTime | dateFormat }}
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="listQuery.cursor"
        :limit.sync="listQuery.limit"
        @pagination="renderData"
      />

      <user-deatils-drawer
        v-if="userDeatilsDrawerVisible"
        :user-id="thatSelectedUserId"
        @close="userDeatilsDrawerVisible=false"
      />
    </div>
  </el-dialog>
</template>

<script>
import { pageTableUserBetTeenPatti } from '@/api/game'
import Pagination from '@/components/Pagination'
export default {
  name: 'TeenPattiBteUser',
  components: { Pagination },
  props: {
    roundId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: '',
        roundId: ''
      },
      listLoading: false,
      searchDisabled: false,
      userDeatilsDrawerVisible: false,
      thatSelectedUserId: ''
    }
  },
  watch: {
    roundId: {
      handler(newVal) {
        this.listQuery.roundId = newVal
        this.renderData()
      },
      immediate: true
    }
  },
  methods: {
    renderData() {
      const that = this
      if (!that.listQuery.roundId) {
        return
      }
      that.listLoading = true
      pageTableUserBetTeenPatti(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleClose() {
      this.$emit('close')
    },
    handleSearch() {
      this.renderData(true)
    },
    queryUserDetails(id) {
      this.userDeatilsDrawerVisible = true
      this.thatSelectedUserId = id
    }
  }
}
</script>
