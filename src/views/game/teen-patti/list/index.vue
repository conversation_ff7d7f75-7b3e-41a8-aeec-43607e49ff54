<template>
  <div class="app-container-teen-patti">
    <div class="filter-container">
      <el-select
        v-model="listQuery.userBet"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="有押注" :value="true" />
        <el-option label="没有押注" :value="false" />
      </el-select>
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleSearch"
        />
      </div>
      <el-input
        v-model.trim="listQuery.rounds"
        v-number
        placeholder="回合数(只查下注局)"
        clearable
        style="width: 200px;"
        class="filter-item"
      />
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-alert
      v-if="gameTotals"
      :type="gameTotals.balance > 0 ? 'success' : 'warning'"
      :closable="false"
    >
      <span
        style="font-size: 20px;"
      >押注 {{ gameTotals.totalBet }} - 支出 {{ gameTotals.odds }} = 余
        {{ gameTotals.balance }}</span>
    </el-alert>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column type="expand">
        <template slot-scope="scope">
          <div v-if="!scope.row.cards">没有更多内容</div>
          <div v-else class="card-info flex-c">
            <div
              v-for="(item, index) in seats"
              :key="index"
              class="card-item "
              dir="ltr"
            >
              <div class="flex-c">
                <div
                  v-for="(cardItem, cardIndex) in scope.row.parseCards[item]
                    .cards"
                  :key="cardIndex"
                  class="card"
                >
                  <img :src="webBaseUrl + cardUrlMaps[cardItem]">
                </div>
              </div>
              <div class="card-desc">
                类型: {{ scope.row.parseCards[item].type }}、牌类:
                {{ scope.row.parseCards[item].resultType }} 、得分:
                {{ scope.row.parseCards[item].score }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="ID" prop="id" align="center" />
      <el-table-column label="中奖类型" prop="type" align="center" />
      <el-table-column label="盈亏" align="center">
        <template slot-scope="scope">
          {{ scope.row.betTotal }} - {{ scope.row.oddsTotal }} =
          {{ scope.row.betTotal - scope.row.oddsTotal }}
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.draw ? "已开奖" : "未开奖" }}
        </template>
      </el-table-column>
      <el-table-column prop="gameStatus" label="游戏状态" align="center" />
      <el-table-column prop="rounds" label="回合数" align="center" />
      <el-table-column
        prop="createTime"
        label="创建时间"
        width="200"
        align="center"
      >
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button
            v-show="scope.row.draw"
            type="text"
            @click.native="handleBetUser(scope.row)"
          >押注用户</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <bte-user
      v-if="gameFruitMachineDialogVisible"
      :round-id="thatSelectedGameId"
      @close="gameFruitMachineDialogVisible = false"
    />
  </div>
</template>

<script>
import {
  pageTableTeenPatti,
  getIncomeAndExpenditureTeenPatti
} from '@/api/game'
import Pagination from '@/components/Pagination'
import { pickerOptions } from '@/constant/el-const'
import { sysOriginPlatforms } from '@/constant/origin'
import BteUser from './bte-user'

export default {
  name: 'TeenPattiList',
  components: { Pagination, BteUser },
  data() {
    return {
      sysOriginPlatforms,
      searchDisabled: false,
      pickerOptions,
      userDeatilsDrawerVisible: false,
      gameFruitMachineDialogVisible: false,
      thatSelectedGameId: '',
      gameTotals: null,
      list: [],
      total: 0,
      rangeDate: [],
      listQuery: {
        cursor: 1,
        limit: 20,
        startTime: '',
        endTime: '',
        rounds: '',
        userBet: true
      },
      listLoading: true,
      seats: ['A', 'B', 'C'],
      webBaseUrl: 'http://img.sugartimeapp.com',
      cardUrlMaps: {
        'DEFAULT:CARD': '/web/game-peet-card-wait-v2.png',
        'A:SPADES': '/web/game-teen-patti-a-1.png',
        'A:HEARTS': '/web/game-teen-patti-a-2.png',
        'A:PLUM_BOSSOM': '/web/game-teen-patti-a-3.png',
        'A:SQUARE': '/web/game-teen-patti-a-4.png',
        'K:SPADES': '/web/game-teen-patti-k-1.png',
        'K:HEARTS': '/web/game-teen-patti-k-2.png',
        'K:PLUM_BOSSOM': '/web/game-teen-patti-k-3.png',
        'K:SQUARE': '/web/game-teen-patti-k-4.png',
        'Q:SPADES': '/web/game-teen-patti-q-1.png',
        'Q:HEARTS': '/web/game-teen-patti-q-2.png',
        'Q:PLUM_BOSSOM': '/web/game-teen-patti-q-3.png',
        'Q:SQUARE': '/web/game-teen-patti-q-4.png',
        'J:SPADES': '/web/game-teen-patti-j-1.png',
        'J:HEARTS': '/web/game-teen-patti-j-2.png',
        'J:PLUM_BOSSOM': '/web/game-teen-patti-j-3.png',
        'J:SQUARE': '/web/game-teen-patti-j-4.png',
        '10:SPADES': '/web/game-teen-patti-10-1.png',
        '10:HEARTS': '/web/game-teen-patti-10-2.png',
        '10:PLUM_BOSSOM': '/web/game-teen-patti-10-3.png',
        '10:SQUARE': '/web/game-teen-patti-10-4.png',
        '9:SPADES': '/web/game-teen-patti-9-1.png',
        '9:HEARTS': '/web/game-teen-patti-9-2.png',
        '9:PLUM_BOSSOM': '/web/game-teen-patti-9-3.png',
        '9:SQUARE': '/web/game-teen-patti-9-4.png',
        '8:SPADES': '/web/game-teen-patti-8-1.png',
        '8:HEARTS': '/web/game-teen-patti-8-2.png',
        '8:PLUM_BOSSOM': '/web/game-teen-patti-8-3.png',
        '8:SQUARE': '/web/game-teen-patti-8-4.png',
        '7:SPADES': '/web/game-teen-patti-7-1.png',
        '7:HEARTS': '/web/game-teen-patti-7-2.png',
        '7:PLUM_BOSSOM': '/web/game-teen-patti-7-3.png',
        '7:SQUARE': '/web/game-teen-patti-7-4.png',
        '6:SPADES': '/web/game-teen-patti-6-1.png',
        '6:HEARTS': '/web/game-teen-patti-6-2.png',
        '6:PLUM_BOSSOM': '/web/game-teen-patti-6-3.png',
        '6:SQUARE': '/web/game-teen-patti-6-4.png',
        '5:SPADES': '/web/game-teen-patti-5-1.png',
        '5:HEARTS': '/web/game-teen-patti-5-2.png',
        '5:PLUM_BOSSOM': '/web/game-teen-patti-5-3.png',
        '5:SQUARE': '/web/game-teen-patti-5-4.png',
        '4:SPADES': '/web/game-teen-patti-4-1.png',
        '4:HEARTS': '/web/game-teen-patti-4-2.png',
        '4:PLUM_BOSSOM': '/web/game-teen-patti-4-3.png',
        '4:SQUARE': '/web/game-teen-patti-4-4.png',
        '3:SPADES': '/web/game-teen-patti-3-1.png',
        '3:HEARTS': '/web/game-teen-patti-3-2.png',
        '3:PLUM_BOSSOM': '/web/game-teen-patti-3-3.png',
        '3:SQUARE': '/web/game-teen-patti-3-4.png',
        '2:SPADES': '/web/game-teen-patti-2-1.png',
        '2:HEARTS': '/web/game-teen-patti-2-2.png',
        '2:PLUM_BOSSOM': '/web/game-teen-patti-2-3.png',
        '2:SQUARE': '/web/game-teen-patti-2-4.png'
      }
    }
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    }
  },
  created() {
    this.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      pageTableTeenPatti(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0

        const list = body.records || []

        list.forEach(item => {
          if (item.cards) {
            item.parseCards = that.parseCards(item.cards)
          }
        })
        that.list = list
        that.listLoading = false
      })

      if (that.listQuery.startTime && that.listQuery.endTime) {
        getIncomeAndExpenditureTeenPatti(that.listQuery).then(res => {
          const { body } = res
          that.gameTotals = body
        })
      } else {
        that.gameTotals = null
      }
    },
    handleSearch() {
      this.renderData(true)
    },
    loadSearchUser() {
      this.searchDisabled = true
    },
    searchUserSuccess(res) {
      this.searchDisabled = false
      this.userInfo = res
      if (!res) {
        return
      }
      this.listQuery.userId = res.id
    },
    handleBetUser(row) {
      this.thatSelectedGameId = String(row.id)
      this.gameFruitMachineDialogVisible = true
    },
    parseCards(cards) {
      const cardStep = cards.split(',')
      if (cardStep.length !== 3) {
        console.error('[开奖]卡牌错误')
        return
      }

      const resultMap = {}
      cardStep.forEach(item => {
        const fields = item.split(':')
        const type = fields[0]
        const cardGroup = fields[1]
        const cardType = fields[2]
        const score = fields[3]

        const cardItems = cardGroup.split('#')
        const cardGroupArray = []
        const cardResults = []
        cardItems.forEach(cardItem => {
          const card = cardItem.split('-')
          const cardObj = {
            suit: card[0],
            card: card[1]
          }
          cardGroupArray.push(cardObj)
          cardResults.push(cardObj.card + ':' + cardObj.suit)
        })

        resultMap[type] = {
          type,
          cards: cardResults,
          cardGroup: cardGroupArray,
          resultType: cardType,
          score
        }
      })
      return resultMap
    }
  }
}
</script>
<style scoped lang="scss">
.card-info {
  .card-item {
    width: 100%;
    .card {
      height: 120px;
      width: 93px;
      margin: 0px -23px;
      cursor: pointer;
      border: 1px solid rgb(241, 238, 237);
      img {
        width: 100%;
        height: 100%;
      }
    }
    .card-desc {
      text-align: center;
      padding-top: 20px;
      font-weight: bold;
    }
  }
}
</style>
