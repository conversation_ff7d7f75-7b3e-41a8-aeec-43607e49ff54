<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <account-input v-model="listQuery.userId" :sys-origin="listQuery.sysOrigin" placeholder="用户ID" />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleBlacklist"
      >
        加入黑名单
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="sysOrigin" label="系统" align="center">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.originSys" :desc="scope.row.originSys" />
        </template>
      </el-table-column>
      <el-table-column label="头像" align="center" width="150">
        <template slot-scope="scope">
          <avatar :url="scope.row.userAvatar" :gender="scope.row.userSex" />
          <div
            style="width:100%;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;"
          >
            <a :title="scope.row.actualAccount">
              {{ scope.row.actualAccount }}
            </a>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="昵称" align="center">
        <template slot-scope="scope">
          <div
            style="width:100%;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;"
          >
            <el-link @click="queryUserDetails(scope.row.id)"><a :title="scope.row.userNickname"> {{ scope.row.userNickname }} </a></el-link>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="userSex" label="性别" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.userSex === 1">
            男
          </span>
          <span v-else>
            女
          </span>
        </template>
      </el-table-column>
      <el-table-column width="70" label="国旗" align="center">
        <template slot-scope="scope">
          <a :title="scope.row.countryName" @click="copyTextContent(scope.row.countryName)">
            <flag-icon :code="scope.row.countryCode" style="margin-top: 20px;" />
          </a>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="拉黑时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="120">
        <template slot-scope="scope">
          <el-button type="text" @click.native="delBlacklist(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <user-deatils-drawer
      v-if="userDeatilsDrawerVisible"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawerVisible=false"
    />

    <el-dialog
      :title="textOptTitle"
      :visible.sync="formVisible"
      :before-close="handleClose"
      :close-on-click-modal="false"
      width="400px"
    >
      <div v-loading="submitLoading">
        <el-form ref="form" :model="form" :rules="rules" label-width="100px" style="margin-left: 15px;">
          <el-form-item label="平台">
            <el-input v-model.trim="listQuery.sysOrigin" :disabled="true" type="text" />
          </el-form-item>
          <el-form-item label="账号" prop="account">
            <el-input v-model.trim="form.account" type="text" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm()">保存</el-button>
            <el-button @click="handleClose()">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import { trumpetBlacklistTable, addTrumpetBlacklist, deleteTrumpetBlacklist } from '@/api/game'
import Pagination from '@/components/Pagination'
import { pickerOptions } from '@/constant/el-const'
import { mapGetters } from 'vuex'

function getFormData() {
  return {
    account: '',
    sysOrigin: ''
  }
}
export default {
  name: 'DynamicBlacklist',
  components: { Pagination },
  data() {
    return {
      submitLoading: false,
      formVisible: false,
      textOptTitle: '',
      searchDisabled: false,
      pickerOptions,
      userDeatilsDrawerVisible: false,
      thatSelectedUserId: '',
      list: [],
      form: { userId: '' },
      total: 0,
      rangeDate: [],
      listQuery: {
        cursor: 1,
        limit: 20,
        sysOrigin: 'MARCIE',
        userId: ''
      },
      rules: {
        account: [{ required: true, trigger: 'blur', message: '必填参数不可为空' }]
      },
      listLoading: true,
      clickUserId: ''
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      trumpetBlacklistTable(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    handleClose() {
      this.formVisible = false
      this.resetForm()
    },
    resetForm() {
      this.form = getFormData()
    },
    handleBlacklist() {
      this.textOptTitle = '喇叭-禁止用户发喇叭'
      this.formVisible = true
      this.form = getFormData()
    },
    queryUserDetails(id) {
      this.userDeatilsDrawerVisible = true
      this.thatSelectedUserId = id
    },
    delBlacklist(item) {
      const that = this
      that.listLoading = true
      deleteTrumpetBlacklist(item.id).then((res) => {
        that.listLoading = false
        that.$message({
          message: '删除成功',
          type: 'success'
        })
        that.renderData()
      }).catch(er => {
        that.listLoading = false
      })
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (valid) {
          that.submitLoading = true
          that.form.sysOrigin = that.listQuery.sysOrigin
          addTrumpetBlacklist(that.form).then(res => {
            that.submitLoading = false
            that.formVisible = false
            that.resetForm()
            that.renderData(true)
          }).catch(er => {
            that.submitLoading = false
            this.$emit('fail')
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
 .store-table-expand {
    font-size: 0;
    label {
    width: 90px;
    color: #99a9bf;
    .el-form-item {
      margin-right: 0;
      margin-bottom: 0;
      width: 50%;
    }
   }
 }
</style>
