<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in permissionsSysOriginPlatforms"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.del"
        placeholder="状态"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option label="未删除" :value="0" />
        <el-option label="已删除" :value="1" />
      </el-select>
      <div class="filter-item">
        <account-input v-model="listQuery.userId" :sys-origin="listQuery.sysOrigin" placeholder="发送人账号" />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="sysOrigin" label="来源系统" align="center">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column label="用户" align="center" width="100">
        <template slot-scope="scope">
          <avatar :url="scope.row.userProfile.userAvatar" />
          <div
            style="width:100%;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;"
          >
            <el-link v-if="scope.row.userId" @click="queryUserDetails(scope.row.userId)">
              {{ scope.row.userProfile.userNickname }}
            </el-link>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="账号" align="center" min-width="100">
        <template slot-scope="scope">
          <p>短ID:{{ scope.row.userProfile.actualAccount }}</p>
          <p>用户ID:{{ scope.row.userId }}</p>
        </template>
      </el-table-column>
      <el-table-column prop="amount" label="支付金币" align="center" min-width="100" />
      <el-table-column prop="message" label="内容" align="center" min-width="100" />
      <el-table-column prop="createTime" label="发送时间" align="center" min-width="100">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column v-if="listQuery.del == 1" prop="updateTime" label="删除时间" align="center" min-width="100">
        <template slot-scope="scope">
          {{ scope.row.updateTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column v-if="listQuery.del == 1" label="操作人" align="center" min-width="100">
        <template slot-scope="scope">
          <p>{{ scope.row.sysUser.nickname }}</p>
        </template>
      </el-table-column>
      <el-table-column v-if="listQuery.del != 1" fixed="right" label="操作" align="center">
        <template slot-scope="scope">
          <div>
            <el-button type="text" class="button" @click="deleteById(scope.row.id)">删除</el-button>
            <el-button type="text" class="button" @click="accountHanle(scope.row.userId)">账号处理</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <div v-if="list && list.length > 0" style="text-align: center; margin-top:20px;">
      <el-button v-if="!notMore " size="mini" :disabled="listLoading" @click="clickLoadMore">加载更多</el-button>
      <span v-else>已加载全部</span>
    </div>

    <details-drawer
      v-if="dtailsDrawerVisible"
      :row="thatRow"
      @close="dtailsDrawerVisible=false"
    />
    <user-deatils-drawer
      v-if="userDeatilsDrawerVisible"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawerVisible=false"
    />

    <account-hanle
      v-if="accountHandleVisible"
      :user-id="thatSelectedUserId"
      @success="renderDataSuccess"
      @close="accountHandleVisible=false"
    />

  </div>
</template>

<script>

import { listTrumpet, deleteTrumpet } from '@/api/game'
import { pickerOptions } from '@/constant/el-const'
import AccountHanle from '@/components/data/AccountHanle'
import { mapGetters } from 'vuex'

export default {
  name: 'GameTrumpet',
  components: { AccountHanle },
  data() {
    return {
      searchDisabled: false,
      pickerOptions,
      userDeatilsDrawerVisible: false,
      accountHandleVisible: false,
      thatSelectedUserId: '',
      list: [],
      dtailsDrawerVisible: false,
      thatRow: {},
      listQuery: {
        sysOrigin: 'HALAR',
        del: 0,
        userId: '',
        limit: '50',
        lastId: ''
      },
      sysOrigins: [],
      listLoading: true
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData(true)
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        that.list = []
        that.listQuery.lastId = ''
      }
      that.listLoading = true
      listTrumpet(that.listQuery).then(res => {
        that.listLoading = false
        const { body } = res
        const list = body || []
        that.notMore = list.length <= 0
        that.list = that.list.concat(list)
        if (that.list && that.list.length > 0) {
          that.listQuery.lastId = that.list[that.list.length - 1].id
        }
      }).catch(er => {
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    clickLoadMore() {
      this.renderData()
    },
    queryUserDetails(id) {
      this.userDeatilsDrawerVisible = true
      this.thatSelectedUserId = id
    },
    accountHanle(userId) {
      this.accountHandleVisible = true
      this.thatSelectedUserId = userId
    },
    renderDataSuccess() {
      this.$message({
        message: '操作成功',
        type: 'success'
      })
      this.renderData(true)
    },
    deleteById(id) {
      const that = this
      that.$confirm('确定删除选择的喇叭记录吗?', '提示', {
        type: 'warning'
      }).then(() => {
        that.listLoading = true
        deleteTrumpet(id).then(res => {
          that.$message.success('删除成功')
          this.renderData(true)
        }).catch(er => {
          that.$message.error('删除失败')
          that.listLoading = false
        })
      }).catch(() => {})
    }
  }
}
</script>
