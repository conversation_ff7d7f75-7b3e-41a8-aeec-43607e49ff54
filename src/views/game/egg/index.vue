<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="item in tables" :key="item.name" :label="item.title" :name="item.component" />
      <component :is="activeName" />
    </el-tabs>
  </div>
</template>
<script>
import EggExchangeRecord from './egg-exchange-record'
import EggLotteryRecord from './egg-lottery-record'
import EggLotteryCnf from './egg-lottery-cnf'
export default {
  name: 'GameEgg',
  components: { EggExchangeRecord, EggLotteryRecord, EggLotteryCnf },
  data() {
    return {
      activeName: 'EggExchangeRecord',
      tables: [
        {
          title: '碎片兑换',
          component: 'EggExchangeRecord'
        }, {
          title: '抽奖记录',
          component: 'EggLotteryRecord'
        },
        {
          title: '配置',
          component: 'EggLotteryCnf'
        }
      ]
    }
  }
}
</script>
