<template>
  <div class="gift-edit">
    <el-dialog
      :title="eventName"
      :visible="true"
      :before-close="handleClose"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      top="50px"
      width="80%"
    >
      <div v-loading="submitLoading">
        <el-form ref="form" :model="form" :rules="rules" label-width="110px" style="margin-right:50px;">
          <el-form-item label-width="0">
            <el-col :md="12" :sm="24" class="col-margin">
              <el-form-item label="平台" prop="sysOrigin">
                <el-select
                  v-model="form.sysOrigin"
                  :disabled="isUpdate"
                  placeholder="归属平台"
                  style="width: 100%"
                  class="filter-item"
                  @change="listRegion()"
                >
                  <el-option
                    v-for="(item, index) in permissionsSysOriginPlatforms"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
                    <span style="float: left;margin-left:10px">{{ item.label }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="12" :sm="24">
              <el-form-item prop="regionList" label="区域">
                <el-select v-model="form.regionList" v-loading="loading" multiple placeholder="请选择" style="width:100%;">
                  <el-option
                    v-for="(item, index) in regions"
                    :key="index"
                    :label="item.regionName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-form-item>
          <el-form-item prop="giftTab" label-width="0">
            <el-col :md="12" :sm="24">
              <el-form-item prop="resourceType" label="资源类型">
                <el-select
                  v-model="form.resourceType"
                  placeholder="资源类型"
                  style="width:100%;"
                  class="filter-item"
                  @change="listGiftOrEmojiV2()"
                >
                  <el-option label="礼物" :value="'GIFT'" />
                  <el-option label="表情包" :value="'EMOJI'" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="12" :sm="24" class="col-margin">
              <el-form-item prop="resourceId" label="资源">
                <el-select v-model="form.resourceId" v-loading="giftLoading" placeholder="请选择" style="width:100%;" @change="showPhoto">
                  <el-option
                    v-for="(item, index) in gifts"
                    :key="index"
                    :label="item.resourceId"
                    :value="item.resourceId"
                    :style="'height:65px'"
                  >
                    <div style="float: left;">
                      <img :src="item.photoInfo" width="65px" height="65px">
                    </div>
                  </el-option>
                </el-select>
                <div style="float: left;">
                  <img class="img" :src="photoUrl" width="65px" height="65px">
                </div>
              </el-form-item>
            </el-col>
          </el-form-item>
        </el-form>
        <div slot="footer" style="text-align: center;">
          <el-button @click="handleClose()">取消</el-button>
          <el-button type="primary" @click="submitForm()">保存</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { updateSongGift, addSongGift, listGiftOrEmoji } from '@/api/game-song-gift'
import { mapGetters } from 'vuex'
import { regionConfigTable } from '@/api/sys'

export default {
  name: 'GiftEdit',
  props: {
    // ADD or UPDATE
    event: {
      type: String,
      required: true,
      default: 'ADD'
    },
    selectParam: {
      type: Object,
      default: null
    }
  },
  data() {
    function commonFormRules() {
      return [
        { required: true, message: '必填字段', trigger: 'blur' }
      ]
    }
    return {
      submitLoading: false,
      giftLoading: false,
      regions: [],
      gifts: [],
      photoUrl: '',
      photoInfo: '',
      form: {
        id: '',
        resourceId: '',
        resourceType: 'GIFT',
        sysOrigin: 'HALAR',
        regionList: []
      },
      rules: {
        resourceId: commonFormRules(),
        resourceType: commonFormRules(),
        sysOrigin: commonFormRules(),
        regionList: commonFormRules()
      }
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms']),
    eventName() {
      return this.event === 'ADD' ? '添加' : this.event === 'UPDATE' ? '修改' : 'ERROR'
    },
    isUpdate() {
      return this.event === 'UPDATE'
    }
  },
  watch: {
    selectParam: {
      handler(newVal) {
        if (!newVal) {
          return
        }
        if (newVal.regions) {
          newVal.regionList = newVal.regions.split(',')
        }

        this.form = Object.assign({}, newVal)
        this.listRegion()
        this.listGiftOrEmoji()
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    listRegion() {
      const that = this
      that.loading = true
      regionConfigTable({ 'sysOrigin': that.form.sysOrigin }).then(res => {
        that.regions = res.body || []
        that.loading = false
      }).catch(er => {
        that.loading = false
      })
    },
    listGiftOrEmoji() {
      const that = this
      that.giftLoading = true
      listGiftOrEmoji({ 'resourceType': that.form.resourceType, 'sysOrigin': that.form.sysOrigin }).then(res => {
        that.gifts = res.body || []
        that.giftLoading = false
        this.showPhoto()
      }).catch(er => {
        that.giftLoading = false
      })
    },
    listGiftOrEmojiV2() {
      const that = this
      that.giftLoading = true
      that.form.resourceId = ''
      that.photoUrl = ''
      listGiftOrEmoji({ 'resourceType': that.form.resourceType, 'sysOrigin': that.form.sysOrigin }).then(res => {
        that.gifts = res.body || []
        that.giftLoading = false
      }).catch(er => {
        that.giftLoading = false
      })
    },
    showPhoto() {
      const that = this
      const item = this.gifts.find(item1 => item1.resourceId === this.form.resourceId)
      that.photoUrl = item.photoInfo
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (valid) {
          that.submitLoading = true
          if (that.form.id) {
            updateSongGift(that.form).then(res => {
              that.submitLoading = false
              that.$emit('success', { result: res, event: that.event })
            }).catch(er => {
              that.submitLoading = false
              that.$emit('fial', { error: er, event: that.event })
            })
            return
          }
          addSongGift(that.form).then(res => {
            that.submitLoading = false
            that.$emit('success', { result: res, event: that.event })
          }).catch(er => {
            that.submitLoading = false
            console.error(er)
            that.$emit('fial', { error: er, event: that.event })
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    }
  }
}
</script>
<style scoped>
.col-margin {
  margin-bottom: 20px;
}
img[src=""],img:not([src]){
            opacity: 0;
        }
</style>
