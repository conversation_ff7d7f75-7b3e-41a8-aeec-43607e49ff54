<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="归属系统"
        style="width: 120px"
        class="filter-item"
        @change="changeSysOrigin"
      >
        <el-option
          v-for="(item, index) in permissionsSysOriginPlatforms"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.region"
        v-loading="loading"
        placeholder="区域"
        style="width:120px;"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in regions"
          :key="index"
          :label="item.regionName"
          :value="item.id"
        />
      </el-select>
      <el-select
        v-model="listQuery.resourceType"
        placeholder="资源类型"
        style="width:120px;"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="礼物" :value="'GIFT'" />
        <el-option label="表情包" :value="'EMOJI'" />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        新增
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="资源ID" width="200" align="center" prop="resourceId" />
      <el-table-column label="封面" align="center">
        <template slot-scope="scope">
          <el-image
            style="width: 50px; height: 50px"
            :src="scope.row.photoInfo"
            :preview-src-list="[scope.row.photoInfo]"
          />
        </template>
      </el-table-column>
      <el-table-column label="类型" align="center">
        <template slot-scope="scope">
          <div>
            <el-tag v-if="scope.row.resourceType === 'GIFT'" type="danger">礼物</el-tag>
            <el-tag v-else-if="scope.row.resourceType === 'EMOJI'">表情包</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="regionNameStr" label="区域" align="center" />
      <el-table-column prop="createTime" label="创建时间" align="center" min-width="170">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button type="text" @click.native="handleUpdate(scope.row)">修改</el-button>
          <el-button type="text" @click.native="handlDel(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <gift-edit
      v-if="formVisible"
      :event="giftEditEvent"
      :select-param="giftEditParam"
      @close="handleClose"
      @success="giftEditSuccess"
      @fail="giftEditFail"
    />

  </div>
</template>

<script>
import { songGiftTable, deleteSongGift } from '@/api/game-song-gift'
import { regionConfigTable } from '@/api/sys'
import Pagination from '@/components/Pagination'
import GiftEdit from './edit'
import { mapGetters } from 'vuex'

export default {
  components: { Pagination, GiftEdit },
  data() {
    return {
      giftEditParam: {},
      giftEditEvent: 'ADD',
      loading: false,
      pushTextHistoryLoading: false,
      pushTextHistoryVisible: false,
      disabledTranslate: false,
      list: [],
      delarr: [],
      total: 0,
      regions: [],
      listQuery: {
        cursor: 1,
        limit: 20,
        sysOrigin: 'HALAR',
        resourceType: '',
        region: ''
      },
      formVisible: false,
      listLoading: true
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.listRegion()
    that.renderData()
  },
  methods: {
    changeSysOrigin() {
      this.listRegion()
      this.handleSearch()
    },
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        that.listQuery.cursor = 1
        that.list = []
      }
      that.listLoading = true
      songGiftTable(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    listRegion() {
      const that = this
      that.loading = true
      regionConfigTable({ 'sysOrigin': that.listQuery.sysOrigin }).then(res => {
        that.regions = res.body || []
        that.loading = false
      }).catch(er => {
        that.loading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    queryUserDetails(row) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = row.id
    },

    handlDel(id) {
      const that = this
      that.$confirm('此操作将永删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteSongGift(id).then(res => {
          that.renderData(true)
        })
      }).catch(() => {
        that.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },

    handleClose() {
      this.formVisible = false
    },
    handleCreate() {
      this.giftEditEvent = 'ADD'
      this.giftEditParam = {}
      this.formVisible = true
    },
    handleUpdate(row) {
      this.giftEditEvent = 'UPDATE'
      this.formVisible = true
      this.giftEditParam = row
    },
    giftEditSuccess(res) {
      this.$opsMessage.success()
      this.formVisible = false
      this.renderData()
    },
    giftEditFail() {
      this.$opsMessage.fail()
    }
  }
}
</script>
