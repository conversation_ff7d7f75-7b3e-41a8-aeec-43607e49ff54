<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="changeSysOrigin"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.region"
        v-loading="loading"
        placeholder="区域"
        style="width:120px;"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in regions"
          :key="index"
          :label="item.regionName"
          :value="item.id"
        />
      </el-select>
      <div class="filter-item">
        <el-date-picker
          v-model="listQuery.dateNumber"
          type="month"
          placeholder="选择日期"
          format="yyyy 年 MM 月"
          value-format="yyyyMM"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>

    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="归属系统" align="center">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column prop="songId" label="歌曲id" align="center" min-width="100" />
      <el-table-column prop="songName" label="歌名" align="center" min-width="100" />
      <el-table-column prop="popularityValue" label="人气值" align="center" min-width="100" />
      <el-table-column prop="frequency" label="点播次数" align="center" min-width="100" />
      <el-table-column prop="dateNumber" label="年月" align="center" min-width="100" />
      <el-table-column prop="regionNameStr" label="区域" align="center" min-width="100" />
      <el-table-column prop="createTime" label="创建时间" align="center" width="200">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
  </div>
</template>

<script>
import { hotSongCount } from '@/api/game-song-count'
import { mapGetters } from 'vuex'
import Pagination from '@/components/Pagination'
import { pickerOptions } from '@/constant/el-const'
import { regionConfigTable } from '@/api/sys'
export default {
  name: 'HotSongCount',
  components: { Pagination },
  data() {
    return {
      list: [],
      total: 0,
      pickerOptions,
      regions: [],
      listQuery: {
        cursor: 1,
        limit: 20,
        sysOrigin: 'MARCIE',
        dateNumber: '',
        region: ''
      },
      listLoading: true
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData(true)
    that.listRegion()
  },
  methods: {
    listRegion() {
      const that = this
      that.loading = true
      regionConfigTable({ 'sysOrigin': that.listQuery.sysOrigin }).then(res => {
        that.regions = res.body || []
        that.loading = false
      }).catch(er => {
        that.loading = false
      })
    },
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      hotSongCount(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    changeSysOrigin() {
      this.listRegion()
      this.handleSearch()
    },
    handleSearch() {
      this.renderData(true)
    },
    querySearch(queryString, cb) {
      var restaurants = this.propsOrigins
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
      cb(results)
    },
    handleSelect(item) {
      this.renderData(true)
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) >= 0) || (restaurant.name.toLowerCase().indexOf(queryString.toLowerCase()) >= 0)
      }
    }
  }
}
</script>
