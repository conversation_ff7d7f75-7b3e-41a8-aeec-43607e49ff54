<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="changeSysOrigin"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.region"
        v-loading="loading"
        placeholder="区域"
        style="width:120px;"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in regions"
          :key="index"
          :label="item.regionName"
          :value="item.id"
        />
      </el-select>
      <div class="filter-item">
        <div><account-input v-model="listQuery.userId" placeholder="用户id" type="USER" :sys-origin="listQuery.sysOrigin" /></div>
      </div>
      <div class="filter-item">
        <search-room-input @success="searchRoomSuccess" @fail="searchRoomFail" @load="loadSearchRoom" />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>

    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="归属系统" align="center" min-width="50">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column label="用户" align="center" min-width="100">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column label="房间" align="center">
        <template slot-scope="scope">
          <div class="room-profile flex-l">
            <div class="avatar" style="margin: auto 0.2rem;">
              <el-image
                style="width:1.5rem;height: 1.5rem;border-radius: .2rem;"
                :src="scope.row.roomProfile.roomCover"
                :preview-src-list="[scope.row.roomProfile.roomCover]"
              >
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </el-image>
            </div>
            <div class="info nowrap-ellipsis">
              <div class="nickname">
                <el-link v-if="scope.row.roomProfile.roomName" @click="queryRoomDetails(scope.row.roomProfile.id)">
                  {{ scope.row.roomProfile.roomName }}
                </el-link>
              </div>
              <div class="desc">
                {{ scope.row.roomProfile.roomAccount }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="songId" label="歌曲Id" align="center" min-width="100" />
      <el-table-column prop="songName" label="歌名" align="center" min-width="100" />
      <el-table-column prop="popularityValue" label="人气值" align="center" min-width="100" />
      <el-table-column prop="regionNameStr" label="区域" align="center" min-width="100" />
      <el-table-column prop="createTime" label="创建时间" align="center" width="200">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
    <user-deatils-drawer
      v-if="userDeatilsDrawer"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawer=false"
    />
    <room-deatils-drawer
      v-if="roomDeatilsDrawerVisible"
      :room-id="roomId"
      @close="roomDeatilsDrawerVisible=false"
    />
  </div>
</template>

<script>
import { userSongCount } from '@/api/game-song-count'
import { mapGetters } from 'vuex'
import Pagination from '@/components/Pagination'
import { pickerOptions } from '@/constant/el-const'
import { regionConfigTable } from '@/api/sys'
import RoomDeatilsDrawer from '@/components/data/RoomDeatilsDrawer'
export default {
  name: 'UserSongCount',
  components: { Pagination, RoomDeatilsDrawer },
  data() {
    return {
      list: [],
      total: 0,
      pickerOptions,
      regions: [],
      searchDisabled: false,
      userDeatilsDrawer: false,
      roomDeatilsDrawerVisible: false,
      roomId: '',
      thatSelectedUserId: '',
      listQuery: {
        cursor: 1,
        limit: 20,
        sysOrigin: 'MARCIE',
        region: '',
        roomId: '',
        userId: ''
      },
      listLoading: true
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData(true)
    that.listRegion()
  },
  methods: {
    listRegion() {
      const that = this
      that.loading = true
      regionConfigTable({ 'sysOrigin': that.listQuery.sysOrigin }).then(res => {
        that.regions = res.body || []
        that.loading = false
      }).catch(er => {
        that.loading = false
      })
    },
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      userSongCount(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    changeSysOrigin() {
      this.listRegion()
      this.handleSearch()
    },
    handleSearch() {
      this.renderData(true)
    },
    querySearch(queryString, cb) {
      var restaurants = this.propsOrigins
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
      cb(results)
    },
    handleSelect(item) {
      this.renderData(true)
    },
    loadSearchRoom() {
      this.searchDisabled = true
    },
    queryRoomDetails(roomId) {
      this.roomDeatilsDrawerVisible = true
      this.roomId = roomId
    },
    searchRoomSuccess(res) {
      this.searchDisabled = false
      if (!res) {
        return
      }
      this.listQuery.roomId = res.id
    },
    searchRoomFail() {
      this.listQuery.roomId = ''
      this.searchDisabled = false
    },
    queryUserDetails(userId) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = userId
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) >= 0) || (restaurant.name.toLowerCase().indexOf(queryString.toLowerCase()) >= 0)
      }
    }
  }
}
</script>
