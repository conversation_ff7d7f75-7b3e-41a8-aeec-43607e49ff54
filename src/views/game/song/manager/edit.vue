<template>
  <div class="song-edit">
    <el-dialog
      :title="eventName"
      :visible="true"
      :before-close="handleClose"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      top="50px"
      width="80%"
    >
      <div v-loading="submitLoading">
        <el-form ref="form" :model="form" :rules="rules" label-width="110px" style="margin-right:50px;">
          <el-form-item label-width="0">
            <el-col :md="12" :sm="24" class="col-margin">
              <el-form-item label="平台" prop="sysOrigin">
                <el-select
                  v-model="form.sysOrigin"
                  :disabled="isUpdate"
                  placeholder="归属平台"
                  style="width: 100%"
                  class="filter-item"
                  @change="listRegion()"
                >
                  <el-option
                    v-for="(item, index) in permissionsSysOriginPlatforms"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
                    <span style="float: left;margin-left:10px">{{ item.label }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="12" :sm="24">
              <el-form-item prop="regionList" label="区域">
                <el-select v-model="form.regionList" v-loading="loading" multiple placeholder="请选择" style="width:100%;">
                  <el-option
                    v-for="(item, index) in regions"
                    :key="index"
                    :label="item.regionName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-form-item>

          <el-form-item label-width="0px">
            <el-col :md="12" :sm="24" class="col-margin">
              <el-form-item label="歌名" prop="songName">
                <el-input v-model.trim="form.songName" type="text" placeholder="歌名" />
              </el-form-item>
            </el-col>
            <el-col :md="12" :sm="24" class="col-margin">
              <el-form-item label="歌手" prop="songUser">
                <el-input v-model.trim="form.songUser" type="text" placeholder="歌手" />
              </el-form-item>
            </el-col>
          </el-form-item>
          <el-form-item label-width="0">
            <el-col :md="12" :sm="24">
              <el-form-item label="状态" prop="shelfStatus">
                <el-select
                  v-model="form.shelfStatus"
                  placeholder="状态"
                  clearable
                  style="width:100%;"
                  class="filter-item"
                >
                  <el-option label="上架" :value="true" />
                  <el-option label="下架" :value="false" />
                </el-select>
              </el-form-item></el-col>
            <el-col :md="12" :sm="24">
              <el-form-item label="排序" prop="sort">
                <el-input v-model="form.sort" v-number type="text" placeholder="请输入排序权重" />
              </el-form-item>
            </el-col>
          </el-form-item>
          <el-form-item label-width="0">
            <el-col :md="12" :sm="24" class="col-margin">
              <el-form-item label="封面" prop="coverUrl">
                <el-upload
                  :file-list="coverUrlFileList"
                  :class="{'upload-but-hide': songCoverUploadVisible}"
                  action=""
                  list-type="picture-card"
                  :http-request="httpRequestSongCover"
                  :limit="1"
                  :show-file-list="true"
                  :on-remove="removeSongCover"
                  accept="image/*"
                >
                  <i slot="default" class="el-icon-plus" />
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :md="12" :sm="24">
              <el-form-item label="音频文件(MP3格式)" prop="songSourceUrl" label-width="205px">
                <el-upload
                  v-loading="songSourceLoading"
                  :class="{'upload-but-hide': songUploadVisible}"
                  action=""
                  :http-request="httpRequestUploadSourceUrl"
                  :on-remove="handleRemoveSourceUrl"
                  :file-list="sourceUrlFileList"
                  :limit="1"
                  accept=".mp3,.Aac,.Wav"
                >
                  <div class="upload-but">
                    <el-button size="small" type="primary">点击上传</el-button>
                    <div slot="tip" class="el-upload__tip">只能上传mp3、Aac、Wav文件</div>
                  </div>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-form-item>
          <el-form-item label-width="0">
            <el-col :md="12" :sm="24">
              <el-form-item label="歌词文件(lrc格式)" prop="lyricSourceUrl" label-width="200px">
                <el-upload
                  v-loading="lyricSourceLoading"
                  :class="{'upload-but-hide': lyricUploadVisible}"
                  action=""
                  :http-request="httpRequestLyricUploadSourceUrl"
                  :on-remove="handleRemoveLyricSourceUrl"
                  :file-list="lyricSourceUrlFileList"
                  :limit="1"
                  accept=".lrc"
                >
                  <div class="upload-but">
                    <el-button size="small" type="primary">点击上传</el-button>
                    <div slot="tip" class="el-upload__tip">只能上传lrc文件</div>
                  </div>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :md="12" :sm="24">
              <el-form-item label="时长" prop="songDuration">
                <el-input v-model="form.songDuration" v-number type="text" placeholder="请输入歌曲时长(秒)" />
              </el-form-item>
            </el-col>
          </el-form-item>
        </el-form>
        <div slot="footer" style="text-align: center;">
          <el-button @click="handleClose()">取消</el-button>
          <el-button type="primary" @click="submitForm()">保存</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { updateSong, addSong } from '@/api/game-song-config'
import { mapGetters } from 'vuex'
import { regionConfigTable } from '@/api/sys'

export default {
  name: 'SongEdit',
  props: {
    // ADD or UPDATE
    event: {
      type: String,
      required: true,
      default: 'ADD'
    },
    selectParam: {
      type: Object,
      default: null
    }
  },
  data() {
    function commonFormRules() {
      return [
        { required: true, message: '必填字段', trigger: 'blur' }
      ]
    }
    return {
      songCoverUploadVisible: false,
      songUploadVisible: false,
      lyricUploadVisible: false,
      submitLoading: false,
      songSourceLoading: false,
      lyricSourceLoading: false,
      regions: [],
      coverUrlFileList: [],
      sourceUrlFileList: [],
      lyricSourceUrlFileList: [],
      form: {
        id: '',
        coverUrl: '',
        songSourceUrl: '',
        lyricSourceUrl: '',
        songName: '',
        songUser: '',
        shelfStatus: '',
        songDuration: '',
        sort: '',
        sysOrigin: 'HALAR',
        regionList: []
      },
      rules: {
        coverUrl: commonFormRules(),
        songName: commonFormRules(),
        songUser: commonFormRules(),
        shelfStatus: commonFormRules(),
        songSourceUrl: commonFormRules(),
        lyricSourceUrl: commonFormRules(),
        sort: commonFormRules(),
        sysOrigin: commonFormRules(),
        regionList: commonFormRules(),
        songDuration: commonFormRules()
      }
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms']),
    eventName() {
      return this.event === 'ADD' ? '上传' : this.event === 'UPDATE' ? '修改' : 'ERROR'
    },
    isUpdate() {
      return this.event === 'UPDATE'
    }
  },
  watch: {
    selectParam: {
      handler(newVal) {
        if (!newVal) {
          return
        }
        if (newVal.regions) {
          newVal.regionList = newVal.regions.split(',')
        }
        if (newVal.coverUrl) {
          this.songCoverUploadVisible = true
          this.coverUrlFileList.push({ url: newVal.coverUrl })
        }
        if (newVal.songSourceUrl) {
          this.songUploadVisible = true
          this.sourceUrlFileList.push({ url: newVal.songSourceUrl })
        }
        if (newVal.lyricSourceUrl) {
          this.lyricUploadVisible = true
          this.lyricSourceUrlFileList.push({ url: newVal.lyricSourceUrl })
        }

        this.form = Object.assign({}, newVal)
        this.listRegion()
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    httpRequestUploadSourceUrl(file) {
      const that = this
      that.songUploadVisible = true
      that.songSourceLoading = true
      that.$simpleUploadFlie(file, 'songs').then(res => {
        that.$opsMessage.success()
        that.form.songSourceUrl = res.name
        that.songSourceLoading = false
      }).catch(er => {
        console.error(er)
        that.songUploadVisible = false
        that.$opsMessage.fail()
      })
    },
    handleRemoveSourceUrl(file, fileList) {
      this.form.songSourceUrl = ''
      this.songUploadVisible = false
    },
    httpRequestLyricUploadSourceUrl(file) {
      const that = this
      that.lyricUploadVisible = true
      that.lyricSourceLoading = true
      that.$simpleUploadFlie(file, 'songs').then(res => {
        that.$opsMessage.success()
        that.form.lyricSourceUrl = res.name
        that.lyricSourceLoading = false
      }).catch(er => {
        console.error(er)
        that.lyricUploadVisible = false
        that.$opsMessage.fail()
      })
    },
    handleRemoveLyricSourceUrl(file, fileList) {
      this.form.lyricSourceUrl = ''
      this.lyricUploadVisible = false
    },
    httpRequestSongCover(file) {
      const that = this
      that.songCoverUploadVisible = true
      this.$simpleUploadFlie(file, 'songs').then(res => {
        that.form.coverUrl = res.name
      }).catch(er => {
        console.error(er)
        that.songCoverUploadVisible = false
        that.$opsMessage.fail('上传失败，请尝试重新上传!')
      })
    },
    listRegion() {
      const that = this
      that.loading = true
      regionConfigTable({ 'sysOrigin': that.form.sysOrigin }).then(res => {
        that.regions = res.body || []
        that.loading = false
      }).catch(er => {
        that.loading = false
      })
    },
    removeSongCover() {
      this.form.coverUrl = ''
      this.songCoverUploadVisible = false
    },
    submitForm() {
      const that = this
      console.log(that.form)
      that.$refs.form.validate(valid => {
        if (valid) {
          that.submitLoading = true
          if (that.form.id) {
            updateSong(that.form).then(res => {
              that.submitLoading = false
              that.$emit('success', { result: res, event: that.event })
            }).catch(er => {
              that.submitLoading = false
              that.$emit('fial', { error: er, event: that.event })
            })
            return
          }
          addSong(that.form).then(res => {
            that.submitLoading = false
            that.$emit('success', { result: res, event: that.event })
          }).catch(er => {
            that.submitLoading = false
            console.error(er)
            that.$emit('fial', { error: er, event: that.event })
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    }
  }
}
</script>
<style scoped>
.col-margin {
  margin-bottom: 20px;
}
</style>
