<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="归属系统"
        style="width: 120px"
        class="filter-item"
        @change="changeSysOrigin"
      >
        <el-option
          v-for="(item, index) in permissionsSysOriginPlatforms"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.sortType"
        placeholder="排序方式"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option v-for="item in sortTypes" :key="item.value" :label="item.name" :value="item.value" />
      </el-select>
      <el-select
        v-model="listQuery.shelfStatus"
        placeholder="上/下架"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option v-for="item in showcaseTypes" :key="item.value" :label="item.name" :value="item.value" />
      </el-select>
      <el-select
        v-model="listQuery.region"
        v-loading="loading"
        placeholder="区域"
        style="width:120px;"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in regions"
          :key="index"
          :label="item.regionName"
          :value="item.id"
        />
      </el-select>
      <el-input
        v-model.trim="listQuery.songId"
        placeholder="歌曲ID"
        clearable
        style="width: 200px;"
        class="filter-item"
      />
      <el-input
        v-model.trim="listQuery.songName"
        placeholder="歌曲名称"
        clearable
        style="width: 200px;"
        class="filter-item"
      />

      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="至"
          start-placeholder="上传开始日期"
          end-placeholder="上传结束日期"
        />
      </div>

      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        上传
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column type="index" width="50" label="No" />
      <el-table-column label="排序" width="200" align="center" prop="sort" />
      <el-table-column label="ID" width="200" align="center" prop="id" />
      <el-table-column label="封面" align="center">
        <template slot-scope="scope">
          <el-image
            style="width: 50px; height: 50px"
            :src="scope.row.coverUrl"
            :preview-src-list="[scope.row.coverUrl]"
          />
        </template>
      </el-table-column>
      <el-table-column prop="songName" label="歌名" align="center" />
      <el-table-column prop="songUser" label="歌手" align="center" />
      <el-table-column prop="songDuration" label="时长(秒)" align="center" />
      <el-table-column prop="regionNameStr" label="区域" align="center" />
      <el-table-column width="100" label="上/下架" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.shelfStatus"
            :active-value="true"
            :inactive-value="false"
            @change="handleSwitchChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="上传时间" align="center" min-width="170">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="80">
        <template slot-scope="scope">
          <el-dropdown>
            <span class="el-dropdown-link">
              <i class="el-icon-more" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item type="text" @click.native="handleShow(scope.row)">详情</el-dropdown-item>
              <el-dropdown-item type="text" @click.native="handleUpdate(scope.row)">编辑</el-dropdown-item>
              <el-dropdown-item type="text" @click.native="deleteSong(scope.row.id)">删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <song-edit
      v-if="formVisible"
      :event="songEditEvent"
      :select-param="songEditParam"
      @close="handleClose"
      @success="songEditSuccess"
      @fail="songEditFail"
    />

    <song-show
      v-if="showVisible"
      :select-param="songEditParam"
      @close="handleClose"
    />

  </div>
</template>

<script>
import { songTable, switchShelfStatus, deleteSong } from '@/api/game-song-config'
import { regionConfigTable } from '@/api/sys'
import Pagination from '@/components/Pagination'
import SongEdit from './edit'
import SongShow from './show'
import { mapGetters } from 'vuex'

export default {
  components: { Pagination, SongEdit, SongShow },
  data() {
    return {
      sortTypes: [
        { value: 'DESC', name: '降序' },
        { value: 'ASC', name: '升序' }
      ],
      showcaseTypes: [
        { value: true, name: '上架' },
        { value: false, name: '下架' }
      ],
      songEditParam: {},
      songEditEvent: 'ADD',
      rangeDate: '',
      loading: false,
      pushTextHistoryLoading: false,
      pushTextHistoryVisible: false,
      disabledTranslate: false,
      fileList: [],
      pushTextHistory: [],
      list: [],
      delarr: [],
      total: 0,
      regions: [],
      listQuery: {
        cursor: 1,
        limit: 20,
        startCreateDate: '',
        endCreateDate: '',
        songName: '',
        sysOrigin: 'MARCIE',
        songId: '',
        shelfStatus: true,
        region: '',
        sortType: 'ASC'
      },
      formVisible: false,
      showVisible: false,
      listLoading: true
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startCreateDate = newVal[0]
          this.listQuery.endCreateDate = newVal[1]
          return
        }
        this.listQuery.startCreateDate = ''
        this.listQuery.endCreateDate = ''
      }
    }
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.listRegion()
    that.renderData(true)
  },
  methods: {
    changeSysOrigin() {
      this.listRegion()
      this.handleSearch()
    },
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        that.listQuery.cursor = 1
        that.list = []
      }
      that.listLoading = true
      songTable(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    listRegion() {
      const that = this
      that.loading = true
      regionConfigTable({ 'sysOrigin': that.listQuery.sysOrigin }).then(res => {
        that.regions = res.body || []
        that.loading = false
      }).catch(er => {
        that.loading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    queryUserDetails(row) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = row.id
    },

    deleteSong(id) {
      const that = this
      that.$confirm('此操作将永删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteSong({ id: id }).then(res => {
          that.renderData(true)
        })
      }).catch(() => {
        that.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },

    handleClose() {
      this.formVisible = false
      this.showVisible = false
    },
    handleCreate() {
      this.songEditEvent = 'ADD'
      this.songEditParam = {}
      this.formVisible = true
    },
    handleUpdate(row) {
      this.songEditEvent = 'UPDATE'
      this.formVisible = true
      this.songEditParam = row
    },
    handleShow(row) {
      this.showVisible = true
      this.songEditParam = row
    },
    songEditSuccess(res) {
      this.$opsMessage.success()
      this.formVisible = false
      this.renderData()
    },
    songEditFail() {
      this.$opsMessage.fail()
    },
    handleSwitchChange(row) {
      switchShelfStatus(row.id, row.shelfStatus)
        .then((res) => {})
        .catch(er => {
          row.shelfStatus = !row.shelfStatus
        })
    }
  }
}
</script>
