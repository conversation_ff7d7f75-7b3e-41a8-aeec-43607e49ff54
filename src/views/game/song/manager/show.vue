<template>
  <div class="song-show">
    <el-dialog
      :visible="true"
      :before-close="handleClose"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      top="50px"
      width="80%"
    >
      <div v-loading="submitLoading">
        <el-form :model="form" label-width="110px" style="margin-right:50px;">
          <el-form-item label-width="0">
            <el-col :md="12" :sm="24" class="col-margin">
              <el-form-item label="平台" prop="sysOrigin">
                <el-select
                  v-model="form.sysOrigin"
                  :disabled="true"
                  placeholder="归属平台"
                  style="width: 100%"
                  class="filter-item"
                  @change="listRegion()"
                >
                  <el-option
                    v-for="(item, index) in permissionsSysOriginPlatforms"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
                    <span style="float: left;margin-left:10px">{{ item.label }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="12" :sm="24">
              <el-form-item prop="regionList" label="区域">
                <el-select v-model="form.regionList" v-loading="loading" :disabled="true" multiple placeholder="请选择" style="width:100%;">
                  <el-option
                    v-for="(item, index) in regions"
                    :key="index"
                    :label="item.regionName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-form-item>

          <el-form-item label-width="0px">
            <el-col :md="12" :sm="24" class="col-margin">
              <el-form-item label="歌名" prop="songName">
                <el-input v-model.trim="form.songName" type="text" placeholder="歌名" :disabled="true" />
              </el-form-item>
            </el-col>
            <el-col :md="12" :sm="24" class="col-margin">
              <el-form-item label="歌手" prop="songUser">
                <el-input v-model.trim="form.songUser" type="text" placeholder="歌手" :disabled="true" />
              </el-form-item>
            </el-col>
          </el-form-item>
          <el-form-item label-width="0">
            <el-col :md="12" :sm="24">
              <el-form-item label="状态" prop="shelfStatus">
                <el-select
                  v-model="form.shelfStatus"
                  placeholder="状态"
                  :disabled="true"
                  style="width:100%;"
                  class="filter-item"
                >
                  <el-option label="上架" :value="true" />
                  <el-option label="下架" :value="false" />
                </el-select>
              </el-form-item></el-col>
            <el-col :md="12" :sm="24">
              <el-form-item label="排序" prop="sort">
                <el-input v-model="form.sort" v-number type="text" placeholder="请输入排序权重" :disabled="true" />
              </el-form-item>
            </el-col>
          </el-form-item>
          <el-form-item label-width="0">
            <el-col :md="12" :sm="24" class="col-margin">
              <el-form-item label="封面" prop="coverUrl">
                <el-upload
                  :file-list="coverUrlFileList"
                  :class="{'upload-but-hide': songCoverUploadVisible}"
                  :disabled="true"
                  action=""
                  list-type="picture-card"
                  :limit="1"
                  :show-file-list="true"
                  accept="image/*"
                >
                  <i slot="default" class="el-icon-plus" />
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :md="12" :sm="24">
              <el-form-item label="音频文件(MP3格式)" prop="songSourceUrl" label-width="205px">
                <el-upload
                  :class="{'upload-but-hide': songUploadVisible}"
                  :disabled="true"
                  action=""
                  :file-list="sourceUrlFileList"
                  :limit="1"
                  accept=".mp3,.aac,.wav"
                >
                  <div class="upload-but">
                    <el-button size="small" type="primary">点击上传</el-button>
                    <div slot="tip" class="el-upload__tip">只能上传mp3、aac、wav文件</div>
                  </div>
                </el-upload>
                <a label-width="250px" style="margin-left: -135px;" @click="downloadSongFile">下载歌曲</a>
              </el-form-item>
            </el-col>
          </el-form-item>
          <el-form-item label-width="0">
            <el-col :md="12" :sm="24">
              <el-form-item label="歌词文件(lrc格式)" prop="lyricSourceUrl" label-width="193px">
                <el-upload
                  :class="{'upload-but-hide': lyricUploadVisible}"
                  :disabled="true"
                  action=""
                  :file-list="lyricSourceUrlFileList"
                  :limit="1"
                  accept=".lrc"
                >
                  <div class="upload-but">
                    <el-button size="small" type="primary">点击上传</el-button>
                    <div slot="tip" class="el-upload__tip">只能上传lrc文件</div>
                  </div>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :md="12" :sm="24">
              <el-form-item label="时长(秒)" prop="songDuration">
                <el-input v-model="form.songDuration" v-number type="text" placeholder="请输入歌曲时长(秒)" :disabled="true" />
              </el-form-item>
            </el-col>
          </el-form-item>
          <el-form-item label-width="0">
            <el-col :md="12" :sm="48">
              <el-form-item label="" label-width="80px">
                <a style="margin-left: -10px;" @click="downloadFile">下载lrc歌词</a>
              </el-form-item>
            </el-col>
          </el-form-item>
        </el-form>
        <div slot="footer" style="text-align: center;">
          <el-button @click="handleClose()">关闭</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import { regionConfigTable } from '@/api/sys'

export default {
  name: 'SongShow',
  props: {
    selectParam: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      songCoverUploadVisible: false,
      songUploadVisible: false,
      lyricUploadVisible: false,
      submitLoading: false,
      regions: [],
      coverUrlFileList: [],
      sourceUrlFileList: [],
      lyricSourceUrlFileList: [],
      lrcFile: '',
      songFile: '',
      form: {
        id: '',
        coverUrl: '',
        songSourceUrl: '',
        lyricSourceUrl: '',
        songName: '',
        songUser: '',
        shelfStatus: '',
        sort: '',
        sysOrigin: 'HALAR',
        regionList: []
      }
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    selectParam: {
      handler(newVal) {
        if (!newVal) {
          return
        }
        if (newVal.regions) {
          newVal.regionList = newVal.regions.split(',')
        }
        if (newVal.coverUrl) {
          this.songCoverUploadVisible = true
          this.coverUrlFileList.push({ url: newVal.coverUrl })
        }
        if (newVal.songSourceUrl) {
          this.songUploadVisible = true
          this.sourceUrlFileList.push({ url: newVal.songSourceUrl })
        }
        if (newVal.lyricSourceUrl) {
          this.lyricUploadVisible = true
          this.lyricSourceUrlFileList.push({ url: newVal.lyricSourceUrl })
        }

        this.form = Object.assign({}, newVal)
        this.listRegion()
        this.lrcFile = newVal.lyricSourceUrl
        this.songFile = newVal.songSourceUrl
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    listRegion() {
      const that = this
      that.loading = true
      regionConfigTable({ 'sysOrigin': that.form.sysOrigin }).then(res => {
        that.regions = res.result || []
        that.loading = false
      }).catch(er => {
        that.loading = false
      })
    },
    downloadFile() {
      const that = this
      const fileUrl = that.lrcFile
      const link = document.createElement('a')
      link.href = fileUrl
      link.download = that.form.songName + '.lrc'
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },
    downloadSongFile() {
      const that = this
      const fileUrl = that.songFile
      const link = document.createElement('a')
      link.href = fileUrl
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }
}
</script>
<style scoped>
.col-margin {
  margin-bottom: 20px;
}
</style>
