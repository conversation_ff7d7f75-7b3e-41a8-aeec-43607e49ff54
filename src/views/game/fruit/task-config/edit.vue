<template>
  <div class="edit-config">
    <el-dialog
      :title="title"
      :visible="true"
      width="550px"
      top="50px"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <div style="height: 500px;overflow: auto;">
        <el-form
          ref="dataForm"
          :rules="rules"
          :model="formData"
          style="width: 400px; margin-left:50px;"
        >
          <el-form-item label="系统" prop="sysOrigin">
            <el-select
              v-model="formData.sysOrigin"
              placeholder="选择系统"
              style="width:100%;"
              class="filter-item"
            >
              <el-option
                v-for="item in permissionsSysOriginPlatforms"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
                <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
                <span style="float: left;margin-left:10px">{{ item.label }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="关联类型" prop="bountyType">
            <el-select
              v-model="formData.bountyType"
              placeholder="类型"
              clearable
              style="width:100%;"
              class="filter-item"
            >
              <el-option label="每日" :value="'DAILY'" />
              <el-option label="每周" :value="'WEEKLY'" />
              <el-option label="每周(争霸赛)" :value="'WEEKLY_COMPETITION'" />
            </el-select>
          </el-form-item>
          <el-form-item label="名称" prop="name">
            <el-input v-model.trim="formData.name" type="text" placeholder="名称" />
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input v-model="formData.sort" v-number placeholder="排序" />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">
          取消
        </el-button>
        <el-button
          :loading="submitLoading"
          type="primary"
          @click="handleSubmit"
        >
          提交
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import { addOrUpdateGameFruitBountyConfig } from '@/api/game-fruit-task-config'
import { mapGetters } from 'vuex'
export default {
  name: 'AwardConfigEdit',
  props: {
    updateData: {
      type: Object,
      default: null
    },
    sysOrigin: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      formData: {
        id: '',
        sysOrigin: 'MARCIE',
        name: '',
        sort: '',
        bountyType: ''
      },
      rules: {
        sysOrigin: [{ required: true, message: '必填项不可为空', trigger: 'blur' }],
        name: [{ required: true, message: '必填项不可为空', trigger: 'blur' }],
        lotteryType: [{ required: true, message: '必填项不可为空', trigger: 'blur' }],
        sort: [{ required: true, message: '必填项不可为空', trigger: 'blur' }]
      },
      submitLoading: false
    }
  },
  computed: {
    isAdd() {
      return this.updateData === null
    },
    title() {
      return this.isAdd ? `添加(${this.sysOrigin})` : `修改(${this.updateData.sysOrigin})`
    },
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    updateData: {
      immediate: true,
      deep: true,
      handler(newVal) {
        Object.assign(this.formData, newVal)
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    handleSubmit() {
      const that = this
      that.$refs.dataForm.validate(valid => {
        if (valid) {
          that.submitLoading = true
          that.formData.sysOrigin = that.sysOrigin
          addOrUpdateGameFruitBountyConfig(that.formData).then(res => {
            that.submitLoading = false
            this.$emit('success', 'create')
          }).catch(er => {
            that.submitLoading = false
            console.error(er)
            this.$emit('fail')
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    }
  }
}
</script>
<style scoped>
.col-margin {
  margin-bottom: 20px;
}
img[src=""],img:not([src]){
            opacity: 0;
        }
</style>
