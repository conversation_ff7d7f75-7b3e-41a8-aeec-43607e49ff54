<template>
  <div class="app-container">

    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatformAlls"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.bountyType"
        placeholder="关联类型"
        style="width:120px;"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="每日" :value="'DAILY'" />
        <el-option label="每周" :value="'WEEKLY'" />
        <el-option label="每周(争霸赛)" :value="'WEEKLY_COMPETITION'" />
      </el-select>
      <el-button
        :loading="searchLoading"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-edit"
        @click="handleAdd"
      >
        添加
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="sysOrigin" label="平台" align="center" />
      <el-table-column label="类型" align="center">
        <template slot-scope="scope">
          <div>
            <el-tag v-if="scope.row.bountyType === 'DAILY'">每日</el-tag>
            <el-tag v-if="scope.row.bountyType === 'WEEKLY'">每周</el-tag>
            <el-tag v-else-if="scope.row.bountyType === 'WEEKLY_COMPETITION'">每周(争霸赛)</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="名称" align="center" width="200" />
      <el-table-column prop="sort" label="排序" align="center" width="200" />
      <el-table-column prop="createTime" label="创建时间" align="center" width="200">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="380">
        <template slot-scope="scope">
          <el-button type="text" @click.native="hanldeEidt(scope.row)">编辑</el-button>
          <el-button type="text" @click.native="handleAwardConfigList(scope.row)">奖励配置</el-button>
          <el-button type="text" @click.native="handlDel(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
    <config-edit
      v-if="gameFruitBountyConfigEditVisible"
      :update-data="updateData"
      :sys-origin="listQuery.sysOrigin"
      @close="gameFruitBountyConfigEditVisible = false"
      @success="configEditSuccess"
    />

    <details-config
      v-if="detailsConfigVisible"
      :bounty-info="thatSelectedBountyConfigInfo"
      :event="editEvent"
      @close="detailsConfigVisible=false"
    />

  </div>
</template>

<script>
import { getGameFruitBountyConfigTable, deleteGameFruitBountyConfig } from '@/api/game-fruit-task-config'
import Pagination from '@/components/Pagination'
import ConfigEdit from './edit.vue'
import DetailsConfig from './details.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'GameFruitBountyConfigTable',
  components: {
    Pagination, ConfigEdit, DetailsConfig
  },
  data() {
    return {
      gameFruitBountyConfigEditVisible: false,
      detailsConfigVisible: false,
      thatSelectedBountyConfigInfo: {},
      updateData: null,
      editEvent: 'ADD',
      thisRow: {},
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        sysOrigin: 'HALAR',
        bountyType: ''
      },
      listLoading: true,
      searchLoading: false,
      clickUserId: ''
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatformAlls', 'permissionsFirstSysOrigin'])
  },
  created() {
    if (!this.permissionsFirstSysOrigin) {
      return
    }
    this.listQuery.sysOrigin = this.permissionsFirstSysOrigin.value
    this.renderData(true)
  },
  methods: {
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      getGameFruitBountyConfigTable(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.searchLoading = that.listLoading = false
      }).catch(er => {
        that.searchLoading = that.listLoading = false
      })
    },
    handleSearch() {
      this.searchLoading = true
      this.renderData(true)
    },
    handleAwardConfigList(row) {
      this.thatSelectedBountyConfigInfo = row
      this.detailsConfigVisible = true
    },
    handleAdd() {
      this.gameFruitBountyConfigEditVisible = true
      this.updateData = null
    },
    hanldeEidt(row) {
      this.gameFruitBountyConfigEditVisible = true
      this.updateData = row
    },
    configEditSuccess(event) {
      this.updateData = null
      this.gameFruitBountyConfigEditVisible = false
      this.renderData(event === 'create')
    },
    // 删除
    handlDel(row) {
      this.$confirm('确认删除吗？', '提示', {
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        deleteGameFruitBountyConfig(row.id).then((res) => {
          this.listLoading = false
          this.$message({
            message: '删除成功',
            type: 'success'
          })
          this.renderData()
        })
      }).catch(() => {
        this.listLoading = false
      })
    }
  }
}
</script>
<style scoped lang="scss">
.this-level {
  padding-bottom: 30px;
  display: flex;
  > div {
    width: 150px;
  }
}
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
