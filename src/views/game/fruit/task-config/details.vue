<template>
  <div class="app-container">
    <el-dialog
      title="摩天轮任务奖励配置列表"
      :visible.sync="isDialog"
      :before-close="handleClose"
      :close-on-click-modal="false"
      :fullscreen="true"
      top="20px"
    >
      <div class="filter-container">
        <el-button
          class="filter-item"
          style="margin-left: 10px;"
          type="primary"
          icon="el-icon-edit"
          @click="handleAdd"
        >
          添加
        </el-button>
      </div>
      <el-table
        v-loading="listLoading"
        :data="dataList"
        element-loading-text="Loading"
        fit
        highlight-current-row
      >
        <el-table-column type="index" width="50" label="No" />
        <el-table-column prop="sysOrigin" label="平台" align="center" width="280" />
        <el-table-column label="挑战的任务" align="center">
          <template slot-scope="scope">
            <div>
              <el-tag
                v-if="scope.row.taskType === 'NUMBER_OF_PROFIT_ROUNDS'"
              >盈利轮数</el-tag>
              <el-tag v-if="scope.row.taskType === 'WEEK_COMPETITION'">周争霸赛</el-tag>
              <el-tag v-if="scope.row.taskType === 'TASK_COMPLETE_DAY'">日任务全完成</el-tag>
              <el-tag v-if="scope.row.taskType === 'TASK_COMPLETE_WEEK'">周任务全完成</el-tag>
              <el-tag v-if="scope.row.taskType === 'NUMBER_OF_PROFIT_COINS'">盈利金币数</el-tag>
              <el-tag v-if="scope.row.taskType === 'NUMBER_OF_WINNING_ROUNDS'">游戏连赢轮数</el-tag>
              <el-tag v-if="scope.row.taskType === 'NUMBER_OF_DAYS_WEEK'">一周玩游戏天数</el-tag>
              <el-tag
                v-else-if="scope.row.taskType === 'WINNING_ONE_OF_THE_PRIZES_ONE'"
              >游戏中中奖某奖项需达到的次数</el-tag>
              <el-tag
                v-else-if="scope.row.taskType === 'WINNING_ONE_OF_THE_PRIZES_TWO'"
              >游戏中中奖披萨或蔬菜需达到的次数</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="数量" align="center" width="200" />
        <el-table-column prop="sort" label="排序" align="center" width="200" />
        <el-table-column prop="createTime" label="创建时间" align="center" width="300">
          <template slot-scope="scope">
            {{ scope.row.createTime | dateFormat }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" align="center">
          <template slot-scope="scope">
            <el-button type="text" @click.native="hanldeEidt(scope.row)">编辑</el-button>
            <el-button type="text" @click.native="hanldeResourceEdit(scope.row)">资源组配置</el-button>
            <el-button type="text" @click.native="hanldeDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="listQuery.cursor"
        :limit.sync="listQuery.limit"
        @pagination="renderData"
      />
      <resource-edit
        v-if="formResourceEditVisable"
        :row="thisRow"
        :sys-origin="editSysOrigin"
        :task-id="taskId"
        @success="formResourceEditSuccess"
        @close="formResourceEditVisable=false"
      />
      <div>
        <el-dialog
          title="任务配置"
          :visible.sync="innerEditVisible"
          width="550px"
          top="50px"
          :close-on-press-escape="false"
          :close-on-click-modal="false"
          append-to-body
        >
          <div style="height: 500px;overflow: auto;">
            <el-form
              ref="dataForm"
              :rules="formRules"
              :model="formData"
              style="width: 400px; margin-left:50px;"
            >
              <el-form-item label="系统" prop="sysOrigin">
                <el-select
                  v-model="formData.sysOrigin"
                  placeholder="选择系统"
                  style="width:100%;"
                  class="filter-item"
                >
                  <el-option
                    v-for="item in permissionsSysOriginPlatforms"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
                    <span style="float: left;margin-left:10px">{{ item.label }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="挑战的任务" prop="taskType">
                <el-select
                  v-model="formData.taskType"
                  placeholder="任务类型"
                  clearable
                  style="width:100%;"
                  class="filter-item"
                >
                  <el-option label="周争霸赛" :value="'WEEK_COMPETITION'" />
                  <el-option label="盈利轮数" :value="'NUMBER_OF_PROFIT_ROUNDS'" />
                  <el-option label="日任务全完成" :value="'TASK_COMPLETE_DAY'" />
                  <el-option label="周任务全完成" :value="'TASK_COMPLETE_WEEK'" />
                  <el-option label="盈利金币数" :value="'NUMBER_OF_PROFIT_COINS'" />
                  <el-option label="游戏连赢轮数" :value="'NUMBER_OF_WINNING_ROUNDS'" />
                  <el-option label="一周玩游戏天数" :value="'NUMBER_OF_DAYS_WEEK'" />
                  <el-option label="游戏中中奖某奖项需达到的次数" :value="'WINNING_ONE_OF_THE_PRIZES_ONE'" />
                  <el-option label="游戏中中奖披萨或蔬菜需达到的次数" :value="'WINNING_ONE_OF_THE_PRIZES_TWO'" />
                </el-select>
              </el-form-item>
              <el-form-item label="排序" prop="sort">
                <el-input v-model="formData.sort" v-number placeholder="排序" />
              </el-form-item>
              <el-form-item v-if="formData.taskType != 'WEEK_COMPETITION'" label="数值" prop="quantity">
                <el-input v-model="formData.quantity" v-number placeholder="数值" />
              </el-form-item>
              <el-form-item v-if="formData.taskType === 'WEEK_COMPETITION'" label="排行榜前几" prop="quantity">
                <el-input v-model="formData.quantity" v-number placeholder="排行榜前几" />
              </el-form-item>
              <el-form-item v-if="formData.taskType === 'WEEK_COMPETITION'" label="一周内完成天数" prop="quantityDays">
                <el-input v-model="formData.quantityDays" v-number placeholder="一周内完成天数" />
              </el-form-item>
              <el-form-item v-if="formData.taskType === 'WINNING_ONE_OF_THE_PRIZES_ONE' || formData.taskType === 'WINNING_ONE_OF_THE_PRIZES_TWO'" label="奖项" prop="gameAwardId">
                <el-select
                  v-model="formData.gameAwardId"
                  multiple
                  placeholder="奖项"
                  style="width:100%;"
                  class="filter-item"
                >
                  <el-option
                    v-for="item in images"
                    :key="item.id"
                    :label="item.id"
                    :value="item.id"
                    :style="'height:65px'"
                  >
                    <div style="float: left;">
                      <img :src="item.img" width="65px" height="65px">
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </div>
          <div slot="footer" class="dialog-footer">
            <el-button @click="innerEditVisible = false">
              取消
            </el-button>
            <el-button
              :loading="submitLoading"
              type="primary"
              @click="handleSubmit"
            >
              提交
            </el-button>
          </div>
        </el-dialog>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getGameFruitTaskConfig, addGameFruitTaskConfig, deleteGameFruitTaskConfig, getGameFruitImages } from '@/api/game-fruit-task-config'
import { mapGetters } from 'vuex'
import Pagination from '@/components/Pagination'
import ResourceEdit from './resource-edit'
import { getByGroupId } from '@/api/props'
import { deepClone } from '@/utils'
export default {
  name: 'TaskDetailsConfigTable',
  components: {
    Pagination,
    ResourceEdit
  },
  props: {
    bountyInfo: {
      type: Object,
      default: null
    },
    updateDetailsData: {
      type: Object,
      default: null
    }
  },
  data() {
    const commonRules = [
      { required: true, message: '必填字段不可为空', trigger: 'blur' }
    ]
    return {
      dataList: [],
      images: [],
      isDialog: true,
      innerEditVisible: false,
      formResourceEditVisable: false,
      thisRow: {},
      editSysOrigin: '',
      bountyConfigInfoSysOrigin: '',
      taskId: '',
      resourceId: '',
      formData: {
        id: '',
        bountyId: '',
        resourceGroupId: '',
        taskType: '',
        sort: '',
        quantity: '',
        quantityDays: '',
        gameAwardId: [],
        sysOrigin: 'HALAR'
      },
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        bountyId: ''
      },
      formRules: {
        taskType: commonRules,
        quantity: commonRules,
        sysOrigin: commonRules,
        sort: commonRules
      },
      submitLoading: false,
      listLoading: true
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    bountyInfo: {
      handler(newVal) {
        const newBountyConfigInfo = deepClone(newVal)
        this.listQuery.bountyId = newBountyConfigInfo.id
        this.bountyConfigInfoSysOrigin = newBountyConfigInfo.sysOrigin
        this.renderData()
        this.initImages()
      },
      immediate: true
    },
    updateDetailsData: {
      immediate: true,
      deep: true,
      handler(newVal) {
        Object.assign(this.formData, newVal)
        this.detailsData = newVal
      }
    }
  },
  created() {
  },
  methods: {
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      getGameFruitTaskConfig(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.dataList = body.records
        that.listLoading = false
      }).catch(er => {
        that.listLoading = false
      })
    },
    initImages() {
      const that = this
      getGameFruitImages(this.bountyConfigInfoSysOrigin).then(res => {
        const { body } = res
        that.images = body || []
      }).catch(er => {
      })
    },
    handleAdd() {
      this.innerEditVisible = true
      this.formData = {}
    },
    hanldeEidt(row) {
      this.innerEditVisible = true
      this.detailsData = row
      this.formData = this.detailsData
    },
    // 删除
    hanldeDelete(row) {
      this.$confirm('确认删除吗？', '提示', {
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        deleteGameFruitTaskConfig(row.id).then((res) => {
          this.listLoading = false
          this.$message({
            message: '删除成功',
            type: 'success'
          })
          this.renderData()
        })
      }).catch(() => {
        this.listLoading = false
      })
    },
    handleClose() {
      this.$emit('close')
    },
    handleSubmit() {
      const that = this
      that.$refs.dataForm.validate(valid => {
        if (valid) {
          that.submitLoading = true
          that.formData.bountyId = that.listQuery.bountyId
          addGameFruitTaskConfig(that.formData).then(res => {
            that.submitLoading = false
            that.innerEditVisible = false
            that.renderData(true)
            this.$emit('success', 'create')
          }).catch(er => {
            that.submitLoading = false
            that.innerEditVisible = false
            console.error(er)
            this.$emit('fail')
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    },
    hanldeResourceEdit(row) {
      const that = this
      that.editSysOrigin = row.sysOrigin
      that.taskId = row.id
      that.resourceId = row.resourceGroupId
      // console.log('row', row)
      if (that.resourceId) {
        getByGroupId(that.resourceId).then(res => {
          const { body } = res
          that.thisRow = body
        })
      } else {
        that.thisRow = {}
      }
      this.formResourceEditVisable = true
    },
    formResourceEditSuccess() {
      this.formResourceEditVisable = false
      this.renderData(true)
    }
  }
}
</script>
<style scoped lang="scss">
.this-level {
  padding-bottom: 30px;
  display: flex;
  > div {
    width: 150px;
  }
}
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
.payer-source {
  position: relative;
  .paler-icon {
    position:absolute;
    left: 60px;
    top: -20px;
  }
}
img[src=""],img:not([src]){
            opacity: 0;
        }
</style>
