<template>
  <div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="每日数据" name="days">
        <el-table
          v-loading="listLoading"
          :data="list"
          element-loading-text="Loading"
          fit
          highlight-current-row
          @sort-change="handleSortChange"
        >
          <el-table-column label="日期" prop="date" sortable align="center" min-width="200" />
          <el-table-column label="平台" prop="sysOrigin" sortable align="center" min-width="200">
            <template slot-scope="scope">
              <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
            </template>
          </el-table-column>
          <el-table-column label="开蔬菜" sortable prop="vegetableNum" align="center" />
          <el-table-column label="开披萨" sortable prop="pizzaNum" align="center" />
          <el-table-column label="日新增" sortable prop="newNum" align="center" />
          <el-table-column label="日活跃" sortable prop="playerNum" align="center" />
          <el-table-column label="日下注" sortable prop="userExpend" align="center" />
          <el-table-column label="日中奖" sortable prop="platformExpend" align="center" />
          <el-table-column label="日盈利" sortable prop="profit" align="center" />
          <el-table-column label="盈利率" sortable prop="earningsRate" align="center">
            <template slot-scope="scope">
              {{ scope.row.earningsRate ? scope.row.earningsRate.toFixed(2) : 0 }} %
            </template>
          </el-table-column>
        </el-table>
        <div>
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="listQuery.cursor"
            :limit.sync="listQuery.limit"
            @pagination="renderData"
          />
        </div>
        <div style="padding: 20px;">
          <div slot="title" class="count-item">
            <i>
              <strong class="strong-style"> 筛选结果汇总</strong>
            </i>
            <i>
              <strong class="strong-style"> 下注金币: {{ gameFruitCount.filterBetAmount || '-' }}</strong>
            </i>
            <i>
              <strong class="strong-style"> 获得奖励: {{ gameFruitCount.filterAward || '-' }}</strong>
            </i>
            <i>
              <strong class="strong-style"> 参与人数: {{ gameFruitCount.filterActiveCount || '-' }}</strong>
            </i>
          </div>
        </div>
        <div style="padding: 20px;">
          <div slot="title">
            <i>
              <strong class="strong-style"> 总计</strong>
            </i>
            <i>
              <strong class="strong-style"> 下注金币: {{ gameFruitCount.sumBetAmount || '-' }}</strong>
            </i>
            <i>
              <strong class="strong-style"> 获得奖励: {{ gameFruitCount.sumAward || '-' }}</strong>
            </i>
            <i>
              <strong class="strong-style"> 参与人数: {{ gameFruitCount.sumActiveCount || '-' }}</strong>
            </i>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="用户数据" name="users">
        <div class="filter-container">
          <div class="filter-item">
            <div><account-input v-model="listQuery.userId" :sys-origin="listQuery.sysOrigin" :query-details="true" placeholder="用户id" /></div>
          </div>
          <el-button
            class="filter-item"
            type="primary"
            icon="el-icon-search"
            @click="renderUserData"
          >
            搜索
          </el-button>
        </div>
        <div>
          <el-table
            v-loading="listLoading"
            :data="userList"
            element-loading-text="Loading"
            fit
            highlight-current-row
            @sort-change="handleSortChange"
          >
            <el-table-column label="下注用户" prop="userProfile" align="center">
              <template slot-scope="scope">
                <user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" :tag-name="scope.row.backOperationName" />
              </template>
            </el-table-column>
            <el-table-column label="日期" prop="date" align="center" min-width="200" />
            <el-table-column label="平台" prop="sysOrigin" align="center" min-width="200">
              <template slot-scope="scope">
                <sys-origin-icon :icon="scope.row.userProfile.originSys" :desc="scope.row.userProfile.originSys" />
              </template>
            </el-table-column>
            <el-table-column label="日下注次数" prop="betSize" align="center" />
            <el-table-column label="日下注" prop="betAmount" align="center" />
            <el-table-column label="日得奖" prop="winnersAmount" align="center" />
            <el-table-column label="日盈利" prop="earnings" align="center" />
            <el-table-column label="盈利率" prop="earningsRate" align="center">
              <template slot-scope="scope">
                {{ scope.row.earningsRate }} %
              </template>
            </el-table-column>
            <el-table-column label="历史盈利" prop="earningsSum" align="center" />
            <el-table-column label="注册时间" prop="createTime" align="center">
              <template slot-scope="scope">
                {{ scope.row.createTime | dateFormat }}
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div>
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="listQuery.cursor"
            :limit.sync="listQuery.limit"
            @pagination="renderUserData"
          />
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { getGameFruitDaysRecord, pageDaysUserData } from '@/api/game'
import Pagination from '@/components/Pagination'
import { pickerOptions } from '@/constant/el-const'
import { sysOriginPlatforms } from '@/constant/origin'
import { mapGetters } from 'vuex'

export default {
  name: 'EggExchangeRecord',
  components: { Pagination },
  data() {
    return {
      sysOriginPlatforms,
      searchDisabled: false,
      pickerOptions,
      thatSelectedGameId: '',
      gameTotals: [0, 0],
      list: [],
      userList: [],
      total: 0,
      activeName: 'days',
      rangeDate: [],
      gameFruitCount: {},
      listQuery: {
        cursor: 1,
        limit: 10,
        sysOrigin: 'HALAR',
        date: '',
        newNum: '',
        playerNum: '',
        userExpend: '',
        platformExpend: '',
        profit: '',
        userId: '',
        earningsRate: ''
      },
      listLoading: true
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    this.renderData()
  },
  methods: {
    handleClick(row) {
      this.listQuery.cursor = 1
      this.listQuery.limit = 10
      this.clearSort()
      if (this.activeName === 'days') {
        this.renderData()
        return
      }
      this.renderUserData()
    },
    handleSortChange(row) {
      this.clearSort()
      if (row.order) {
        this.listQuery[row.prop] = row.order === 'ascending'
      }
      this.renderData()
      console.log('row', row, this.listQuery[row.prop])
    },
    clearSort() {
      this.listQuery.date = ''
      this.listQuery.newNum = ''
      this.listQuery.playerNum = ''
      this.listQuery.userExpend = ''
      this.listQuery.platformExpend = ''
      this.listQuery.profit = ''
      this.listQuery.earningsRate = ''
    },
    renderData(isClean) {
      console.log('row', 111)
      const that = this
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      getGameFruitDaysRecord(that.listQuery).then(res => {
        const { body } = res
        that.total = body.pageResult.total || 0
        that.list = body.pageResult.records
        that.gameFruitCount = body
        that.listLoading = false
      })
    },
    renderUserData(isClean) {
      const that = this
      if (isClean === true) {
        that.userList = []
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      pageDaysUserData(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.userList = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    }
  }
}
</script>
<style scoped lang="scss">
.store-table-expand {
  font-size: 0;
  label {
    width: 90px;
    color: #99a9bf;
    .el-form-item {
      margin-right: 0;
      margin-bottom: 0;
      width: 50%;
    }
  }
}
</style>
