<template>
  <div>
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="归属系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in permissionsSysOriginPlatforms"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <div><account-input v-model="listQuery.userId" :sys-origin="listQuery.sysOrigin" :query-details="true" placeholder="用户id" type="USER" /></div>
      </div>
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>

    </div>
    <div class="app-container-egg">
      <el-table
        v-loading="listLoading"
        :data="list"
        element-loading-text="Loading"
        fit
        :default-sort="{prop: 'date', order: 'descending'}"
        highlight-current-row
        @sort-change="handleSortChange"
      >
        <el-table-column label="序号" prop="round" sortable align="center" min-width="80">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="游戏轮数" prop="round" sortable align="center" min-width="120" />
        <el-table-column label="下注用户" sortable prop="userProfile" align="center">
          <template slot-scope="scope">
            <user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" :tag-name="scope.row.backOperationName" />
          </template>
        </el-table-column>
        <el-table-column label="最后下注时间" sortable prop="lastBetTime" align="center">
          <template slot-scope="scope">
            {{ scope.row.lastBetTime | dateFormat }}
          </template>
        </el-table-column>
        <el-table-column label="下注选项" sortable prop="betPrize" align="center" />
        <el-table-column label="下注金额" sortable prop="betAmount" align="center" />
        <el-table-column label="本轮开奖选项" sortable prop="lotteryPrize" align="center" />
        <el-table-column label="获得奖励" sortable prop="awards" align="center" />
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="listQuery.cursor"
        :limit.sync="listQuery.limit"
        @pagination="renderData"
      />

      <div style="padding: 30px;">
        <div slot="title" class="count-item">
          <i>
            <strong class="strong-style"> 筛选结果汇总</strong>
          </i>
          <i>
            <strong class="strong-style"> 下注金额: {{ gameFruitCount.betAmount || '-' }}</strong>
          </i>
          <i>
            <strong class="strong-style"> 获得奖励: {{ gameFruitCount.awards || '-' }}</strong>
          </i>
          <i>
            <strong class="strong-style"> 参与人数: {{ gameFruitCount.playerNum || '-' }}</strong>
          </i>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { pageUserBetRecord } from '@/api/game'
import Pagination from '@/components/Pagination'
import { pickerOptions } from '@/constant/el-const'
import { sysOriginPlatforms } from '@/constant/origin'
import { mapGetters } from 'vuex'

export default {
  name: 'EggExchangeRecord',
  components: { Pagination },
  data() {
    return {
      sysOriginPlatforms,
      searchDisabled: false,
      pickerOptions,
      thatSelectedGameId: '',
      gameTotals: [0, 0],
      list: [],
      total: 0,
      rangeDate: [],
      gameFruitCount: { playerNum: '', betAmount: '', awards: '' },
      listQuery: {
        cursor: 1,
        limit: 20,
        sysOrigin: '',
        userId: '',
        startTime: '',
        endTime: ''
      },
      listLoading: true
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          console.log(111, newVal)
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    }
  },
  created() {
    const that = this
    console.log('that.listQuery.sysOrigin', that.listQuery.sysOrigin)
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    // 获取当前日期
    const currentDate = new Date()

    // 设置当前时间为0点
    currentDate.setHours(0, 0, 0, 0)

    // 获取当日0点的时间戳
    this.listQuery.startTime = currentDate.getTime()
    this.listQuery.endTime = new Date().getTime()
    this.rangeDate[0] = currentDate.getTime()
    this.rangeDate[1] = new Date().getTime()
    this.renderData()
  },
  methods: {
    handleSortChange(row) {
      if (row.order) {
        this.listQuery[row.prop] = row.order === 'ascending'
      }
      this.renderData()
      console.log('row', row, this.listQuery[row.prop])
    },
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      pageUserBetRecord(that.listQuery).then(res => {
        const { body } = res
        that.total = body.gameUserBetRecords.total || 0
        that.list = body.gameUserBetRecords.records
        that.gameFruitCount.betAmount = body.betAmount
        that.gameFruitCount.awards = body.awards
        that.gameFruitCount.playerNum = body.playerNum
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    }
  }
}
</script>
<style scoped lang="scss">
.store-table-expand {
  font-size: 0;
  label {
    width: 90px;
    color: #99a9bf;
    .el-form-item {
      margin-right: 0;
      margin-bottom: 0;
      width: 50%;
    }
  }
}
</style>
