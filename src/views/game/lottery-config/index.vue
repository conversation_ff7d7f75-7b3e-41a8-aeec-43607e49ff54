<template>
  <div class="app-container">

    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="归属系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in permissionsSysOriginPlatforms"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <!-- <el-select
        v-model="listQuery.shelfStatus"
        placeholder="上/下架"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option v-for="item in showcaseTypes" :key="item.value" :label="item.name" :value="item.value" />
      </el-select> -->

      <el-input
        v-model.trim="listQuery.id"
        v-number
        placeholder="ID"
        clearable
        style="width: 200px;"
        class="filter-item"
      />
      <el-select
        v-model="listQuery.gameType"
        placeholder="游戏类型"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option v-for="item in lotteryGameTypes" :key="item.value" :label="item.name" :value="item.value" />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        添加
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
      @cell-mouse-enter="handleMouseEnter"
    >
      <el-table-column prop="id" width="200" label="ID" align="center" />
      <el-table-column width="100" prop="gameType" label="类型名称" align="center">
        <template slot-scope="scope">
          <span v-for="(item, index) in lotteryGameTypes" :key="index">
            <span v-if="item.value === scope.row.gameType">{{ item.name }}</span>
          </span>
        </template>
      </el-table-column>
      <!-- <el-table-column width="200" label="上/下架" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.shelfStatus"
            :active-value="true"
            :inactive-value="false"
            @change="handleSwitchChange(scope.row)"
          />
        </template>
      </el-table-column> -->
      <el-table-column label="道具" align="center">
        <template slot-scope="scope">
          <props-row :list="scope.row.rewardConfigList" />
        </template>
      </el-table-column>
      <el-table-column label="时间" align="center">
        <template slot-scope="scope">
          <div>创建时间：{{ scope.row.createTime | dateFormat }}</div>
          <div>修改时间：{{ scope.row.updateTime | dateFormat }}</div>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="150">
        <template slot-scope="scope">
          <el-button type="text" @click.native="handleUpdate(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <form-edit
      v-if="formEditVisable"
      :row="thisRow"
      :sys-origin="editSysOrigin"
      @success="formEditSuccess"
      @close="formEditVisable=false"
    />
  </div>
</template>

<script>
import { pageGameLotteryGroup, offGameLotteryGroup } from '@/api/game'
import Pagination from '@/components/Pagination'
import { propsTypes, lotteryGameTypes } from '@/constant/type'
import FormEdit from './form-edit'
import { sysOriginPlatforms } from '@/constant/origin'
import { mapGetters } from 'vuex'
import PropsRow from '@/components/data/PropsRow'
export default {
  components: { Pagination, FormEdit, PropsRow },
  data() {
    return {
      editSysOrigin: '',
      sysOriginPlatforms,
      lotteryGameTypes,
      showcaseTypes: [
        { value: false, name: '下架' },
        { value: true, name: '上架' }
      ],
      formEditVisable: false,
      thisRow: {},
      thatSelectedUserId: {},
      propsTypes,
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        id: '',
        gameType: '',
        shelfStatus: true,
        sysOrigin: 'MARCIE'
      },
      listLoading: true
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      that.listLoading = true
      if (isClean === true) {
        that.listQuery.cursor = 1
      }
      pageGameLotteryGroup(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleCreate() {
      this.thisRow = null
      this.formEditVisable = true
      this.editSysOrigin = this.listQuery.sysOrigin
    },
    handleUpdate(row) {
      this.formEditVisable = true
    },
    handleSwitchChange(row) {
      offGameLotteryGroup(row.id, row.shelfStatus)
        .then(res => {})
        .catch(er => {
          row.shelfStatus = !row.shelfStatus
        })
    },
    handleSearch() {
      this.renderData(true)
    },
    queryUserDetails(row) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = row.id
    },
    handleMouseEnter(row) {
      this.thisRow = row
      this.thatSelectedUserId = row.id
      this.editSysOrigin = row.sysOrigin
    },
    formEditSuccess() {
      this.formEditVisable = false
      this.renderData(false)
    }
  }
}
</script>
<style scoped lang="scss">
 .store-table-expand {
    font-size: 0;
    label {
    width: 90px;
    color: #99a9bf;
    .el-form-item {
      margin-right: 0;
      margin-bottom: 0;
      width: 50%;
    }
   }
 }
</style>
