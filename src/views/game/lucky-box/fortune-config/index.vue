<template>
  <div class="app-container">
    <div v-loading="loading" class="fortune-config">
      <el-form ref="form" :model="form" :rules="rules" label-width="130px">
        <el-alert
          title="单抽获取以下幸运值概率(单位%)"
          type="info"
          :closable="false"
          style="margin-bottom: 20px;"
        />
        <el-form-item prop="oneDot" label="1点">
          <el-input v-model="form.oneDot" v-number />
        </el-form-item>
        <el-form-item prop="twoDot" label="2点">
          <el-input v-model.trim="form.twoDot" v-number />
        </el-form-item>
        <el-form-item label="3点" prop="threeDot">
          <el-input v-model="form.threeDot" v-number />
        </el-form-item>
        <el-alert
          title="10抽获取以下幸运值概率(单位%)"
          type="info"
          :closable="false"
          style="margin-bottom: 20px;"
        />
        <el-form-item prop="aroundElevenDot" label="11-15点">
          <el-input v-model="form.aroundElevenDot" v-number />
        </el-form-item>
        <el-form-item prop="aroundSixteenDot" label="16-20点">
          <el-input v-model.trim="form.aroundSixteenDot" v-number />
        </el-form-item>
        <el-form-item label="21-25点" prop="aroundTwentyOneDot">
          <el-input v-model="form.aroundTwentyOneDot" v-number />
        </el-form-item>
        <el-form-item label="26-30点" prop="aroundTwentySixDot">
          <el-input v-model="form.aroundTwentySixDot" v-number />
        </el-form-item>
        <el-alert
          title="50抽获取以下幸运值概率(单位%)"
          type="info"
          :closable="false"
          style="margin-bottom: 20px;"
        />
        <el-form-item prop="aroundFiftyOneDot" label="51-100点">
          <el-input v-model="form.aroundFiftyOneDot" v-number />
        </el-form-item>
        <el-form-item prop="aroundOneHundredDot" label="101-150点">
          <el-input v-model.trim="form.aroundOneHundredDot" v-number />
        </el-form-item>
        <el-alert
          title="幸运时刻配置"
          type="info"
          :closable="false"
          style="margin-bottom: 20px;"
        />
        <el-form-item label="幸运时刻1" prop="luckyTimeOne">
          <el-input v-model="form.luckyTimeOne" v-number />
        </el-form-item>
        <el-form-item label="幸运时刻2" prop="luckyTimeTwo">
          <el-input v-model="form.luckyTimeTwo" v-number />
        </el-form-item>
        <el-form-item label="幸运时刻时长(S)" prop="luckyTimeDuration">
          <el-input v-model="form.luckyTimeDuration" v-number />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">提交</el-button>
        </el-form-item>
      </el-form>
    </div>

  </div>
</template>
<script>
import { getLuckyBoxFortuneConfig, addLuckyBoxFortuneConfig } from '@/api/game-lucky-box-config'
import { mapGetters } from 'vuex'
export default {
  name: 'FortuneConfig',
  data() {
    const commonValid = [{ required: true, message: '必填字段不可为空', trigger: 'blur' }]
    return {
      loading: false,
      form: {
        id: '',
        oneDot: '',
        twoDot: '',
        threeDot: '',
        aroundElevenDot: '',
        aroundSixteenDot: '',
        aroundTwentyOneDot: '',
        aroundTwentySixDot: '',
        aroundFiftyOneDot: '',
        aroundOneHundredDot: '',
        luckyTimeOne: '',
        luckyTimeTwo: '',
        luckyTimeDuration: '',
        sysOrigin: 'MARCIE'
      },
      fortuneConfigQuery: {
        sysOrigin: 'MARCIE'
      },
      rules: {
        oneDot: commonValid,
        twoDot: commonValid,
        threeDot: commonValid,
        aroundElevenDot: commonValid,
        aroundSixteenDot: commonValid,
        aroundTwentyOneDot: commonValid,
        aroundTwentySixDot: commonValid,
        aroundFiftyOneDot: commonValid,
        aroundOneHundredDot: commonValid,
        luckyTimeOne: commonValid,
        luckyTimeTwo: commonValid,
        luckyTimeDuration: commonValid,
        sysOrigin: commonValid
      }
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.fortuneConfigQuery.sysOrigin = querySystem.value
    that.fortuneConfig()
  },
  methods: {
    fortuneConfig() {
      const that = this
      that.submitLoading = true
      getLuckyBoxFortuneConfig(that.fortuneConfigQuery).then(res => {
        const { body } = res
        that.form = body
        that.submitLoading = false
      }).catch(er => {
        that.submitLoading = false
        console.error(er)
        that.$emit('fial', { error: er })
      })
    },
    onSubmit() {
      const that = this
      var probability_1 = parseInt(that.form.oneDot) + parseInt(that.form.twoDot) + parseInt(that.form.threeDot)
      var probability_2 = parseInt(that.form.aroundElevenDot) + parseInt(that.form.aroundSixteenDot) + parseInt(that.form.aroundTwentyOneDot) + parseInt(that.form.aroundTwentySixDot)
      var probability_3 = parseInt(that.form.aroundFiftyOneDot) + parseInt(that.form.aroundOneHundredDot)

      if (parseInt(that.form.oneDot) === 0 || parseInt(that.form.twoDot) === 0 || parseInt(that.form.threeDot) === 0 ||
        parseInt(that.form.aroundElevenDot) === 0 || parseInt(that.form.aroundSixteenDot) === 0 ||
        parseInt(that.form.aroundTwentyOneDot) === 0 || parseInt(that.form.aroundTwentySixDot) === 0 ||
        parseInt(that.form.aroundFiftyOneDot) === 0 || parseInt(that.form.aroundOneHundredDot) === 0) {
        that.$opsMessage.fail('概率必须大于0！')
        return
      }

      if (probability_1 !== 100 || probability_2 !== 100 || probability_3 !== 100) {
        that.$opsMessage.fail('概率必须等于100！')
        return
      }
      that.$refs.form.validate((valid) => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }

        that.$confirm('是否确认提交?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          that.loading = true
          that.form.sysOrigin = that.fortuneConfigQuery.sysOrigin
          addLuckyBoxFortuneConfig(that.form).then(res => {
            that.loading = false
            that.$opsMessage.success()
            that.fortuneConfig()
          }).catch(er => {
            that.loading = false
            console.error(er)
          })
        }).catch(() => { })
      })
    }
  }
}
</script>
