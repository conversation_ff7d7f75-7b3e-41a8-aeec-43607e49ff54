<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="changeSysOrigin"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <div><account-input v-model="listQuery.userId" placeholder="用户id" type="USER" :sys-origin="listQuery.sysOrigin" /></div>
      </div>
      <el-select
        v-model="listQuery.frequency"
        placeholder="抽奖频数"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option
          label="1"
          :value="'1'"
        />
        <el-option
          label="10"
          :value="'10'"
        />
        <el-option
          label="50"
          :value="'50'"
        />
      </el-select>
      <div class="filter-item">
        <search-room-input @success="searchRoomSuccess" @fail="searchRoomFail" @load="loadSearchRoom" />
      </div>
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>

    </div>
    <div v-loading="countLoading">
      <div slot="title" class="count-item">
        <i>
          <strong class="strong-style"> 筛选结果汇总</strong>
        </i>
        <i>
          <strong class="strong-style"> 参加人数: {{ gameLuckyBoxCount.userCount || '-' }}</strong>
        </i>
        <i>
          <strong class="strong-style"> 用户支出糖果数: {{ gameLuckyBoxCount.userExpendCount || '-' }}</strong>
        </i>
        <i>
          <strong class="strong-style"> 出奖糖果数: {{ gameLuckyBoxCount.awardCount || '-' }}</strong>
        </i>
      </div>
      <div slot="title" class="count-item">
        <i>
          <strong class="strong-style"> 抽奖券结果汇总</strong>
        </i>
        <i>
          <strong class="strong-style"> 抽奖券数量: {{ gameLuckyBoxCount.ticketCount || '-' }}</strong>
        </i>
        <i>
          <strong class="strong-style"> 消耗抽奖券数量: {{ gameLuckyBoxCount.consumeTicketCount || '-' }}</strong>
        </i>
        <i>
          <strong class="strong-style"> 减去抽奖券出奖糖果数: {{ gameLuckyBoxCount.awardNotTicketCount || '-' }}</strong>
        </i>
      </div>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="归属系统" align="center" min-width="50">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column label="用户" align="center" min-width="100">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column label="房间" align="center">
        <template slot-scope="scope">
          <div class="room-profile flex-l">
            <div class="avatar" style="margin: auto 0.2rem;">
              <el-image
                style="width:1rem;height: 1rem;border-radius: .2rem;"
                :src="scope.row.roomProfile.roomCover"
                :preview-src-list="[scope.row.roomProfile.roomCover]"
              >
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </el-image>
            </div>
            <div class="info nowrap-ellipsis">
              <div class="nickname">
                <el-link v-if="scope.row.roomProfile.roomName" @click="queryRoomDetails(scope.row.roomProfile.id)">
                  {{ scope.row.roomProfile.roomName }}
                </el-link>
              </div>
              <div class="desc">
                {{ scope.row.roomProfile.roomAccount }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="payAmount" label="支出金币" align="center" min-width="100" />
      <el-table-column prop="frequency" label="抽奖频数" align="center" min-width="100" />
      <el-table-column prop="giftAmount" label="中奖礼物总价值" align="center" min-width="100" />
      <el-table-column prop="createTime" label="创建时间" align="center" width="200">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
    <user-deatils-drawer
      v-if="userDeatilsDrawer"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawer=false"
    />
    <room-deatils-drawer
      v-if="roomDeatilsDrawerVisible"
      :room-id="roomId"
      @close="roomDeatilsDrawerVisible=false"
    />
  </div>
</template>

<script>
import { pageLuckyBoxGameRecord, countLuckyBoxGame } from '@/api/game-lucky-box-config'
import { mapGetters } from 'vuex'
import Pagination from '@/components/Pagination'
import { regionConfigTable } from '@/api/sys'
import RoomDeatilsDrawer from '@/components/data/RoomDeatilsDrawer'
export default {
  name: 'LuckyGiftGiveRecord',
  components: { RoomDeatilsDrawer, Pagination },
  data() {
    return {
      list: [],
      regions: [],
      countLoading: false,
      searchDisabled: false,
      userDeatilsDrawer: false,
      roomDeatilsDrawerVisible: false,
      roomId: '',
      thatSelectedUserId: '',
      gameLuckyBoxCount: {},
      rangeDate: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        sysOrigin: 'HALAR',
        roomId: '',
        userId: '',
        frequency: '',
        startTime: '',
        endTime: ''
      },
      listLoading: true
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    }
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData(true)
    that.listRegion()
    that.getCountLuckyBoxGame()
  },
  methods: {
    listRegion() {
      const that = this
      that.loading = true
      regionConfigTable({ 'sysOrigin': that.listQuery.sysOrigin }).then(res => {
        that.regions = res.body || []
        that.loading = false
      }).catch(er => {
        that.loading = false
      })
    },
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.listQuery.cursor = 1
        that.list = []
      }
      that.listLoading = true
      pageLuckyBoxGameRecord(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      }).catch(er => {
        that.listLoading = false
      })
    },
    changeSysOrigin() {
      this.listRegion()
      this.handleSearch()
    },
    handleSearch() {
      this.renderData(true)
      this.getCountLuckyBoxGame()
    },
    getCountLuckyBoxGame() {
      const that = this
      that.countLoading = true
      countLuckyBoxGame(that.listQuery).then(res => {
        that.countLoading = false
        that.gameLuckyBoxCount = res.body || {}
      }).catch(er => {
        that.countLoading = false
      })
    },
    loadSearchRoom() {
      this.searchDisabled = true
    },
    queryRoomDetails(roomId) {
      this.roomDeatilsDrawerVisible = true
      this.roomId = roomId
    },
    searchRoomSuccess(res) {
      this.searchDisabled = false
      if (!res) {
        return
      }
      this.listQuery.roomId = res.id
    },
    searchRoomFail() {
      this.listQuery.roomId = ''
      this.searchDisabled = false
    },
    queryUserDetails(userId) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = userId
    }
  }
}
</script>

<style scoped lang="scss">
.strong-style{
  padding-right: 20px;
  font-style: normal;

}
.count-item{
  padding-bottom: 20px;
}
</style>
