<template>
  <div class="game-luckbox-drawer">
    <el-drawer
      title="详情"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div class="game-luckbox-content">
        <div class="blockquote">抽奖人</div>
        <div class="content">
          <user-table-exhibit
            :user-profile="row.userProfile"
            :query-details="true"
          />
        </div>
        <div class="blockquote">抽奖情况</div>
        <div class="content lucky-info">
          <el-row :gutter="10">
            <el-col :span="12">ID: {{ row.id }}</el-col>
            <el-col :span="12" class="flex-l"
              >系统:
              <sys-origin-icon :icon="row.sysOrigin" :desc="row.sysOrigin"
            /></el-col>
            <el-col :span="12">门票: {{ row.tickets }}</el-col>
            <el-col :span="12">抽奖数: {{ row.lotterySize }}</el-col>
            <el-col :span="12">礼物价值: {{ row.giftTotalAmount }}</el-col>
            <el-col :span="12">盈亏: {{ row.earn }}</el-col>
            <el-col :span="12">幸运抽取%: {{ row.luckyDrawRatio }}</el-col>
            <el-col :span="12">幸运抽取金额: {{ row.luckyDrawAmount }}</el-col>
            <el-col :span="12">投入奖金抽取%: {{ row.poolPutAmount }}</el-col>
            <el-col :span="12">投入奖金抽取金额: {{ row.poolPutRatio }}</el-col>
            <el-col :span="12">创建时间: {{ row.createTime | dateFormat }}</el-col>
            <el-col :span="12">过期时间: {{ row.expiredTime }}</el-col>
          </el-row>
        </div>
        <div class="blockquote">中奖礼物({{ row.giftTotalAmount }})</div>
        <div class="content">
          <el-row :gutter="10">
            <el-col
              v-for="(item, index) in row.luckProps"
              :key="index"
              :span="4"
              style="margin-bottom: 10px"
            >
              <div class="flex-c">
                <div class="gift">
                  <div class="cover">
                    <el-image
                      style="width: 60px; height: 60px"
                      :src="item.propsCover"
                      fit="fill"
                      :preview-src-list="[item.propsCover]"
                    >
                      <div slot="error" class="image-slot">
                        <i class="el-icon-picture-outline" />
                      </div>
                    </el-image>
                  </div>
                  <div class="name">
                    {{ item.amount }} * {{ item.quantity }}
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="blockquote">日志事件</div>
        <div class="content">
          <el-timeline>
            <el-timeline-item
              v-for="(item, index) in row.logs"
              :key="index"
              :timestamp="item.createTime | dateFormat"
            >
              {{ item.content }}
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-drawer>

    <room-deatils-drawer
      v-if="roomDeatilsDrawerVisible"
      :room-id="row.roomId"
      @close="roomDeatilsDrawerVisible = false"
    />
  </div>
</template>
<script>
import RoomDeatilsDrawer from "@/components/data/RoomDeatilsDrawer";
export default {
  name: "GameLockyboxDetailsDrawer",
  components: { RoomDeatilsDrawer },
  props: {
    row: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      roomDeatilsDrawerVisible: false
    };
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    queryRoomDetails() {
      this.roomDeatilsDrawerVisible = true;
    }
  }
};
</script>
<style scoped lang="scss">
.game-luckbox-content {
  padding: 0px 10px;
  .el-tabs__nav-scroll .el-tabs__item {
    border: 1px sol;
  }
  .lucky-info {
    line-height: 30px;
  }
}
</style>
