<template>
  <div class="edit-bounty">
    <el-dialog
      :title="title"
      :visible="true"
      width="550px"
      top="50px"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <div style="height: 500px;overflow: auto;">
        <el-form
          ref="dataForm"
          :rules="rules"
          :model="formData"
          style="width: 400px; margin-left:50px;"
        >
          <el-form-item label="系统" prop="sysOrigin">
            <el-select
              v-model="formData.sysOrigin"
              placeholder="选择系统"
              style="width:100%;"
              class="filter-item"
            >
              <el-option
                v-for="item in permissionsSysOriginPlatforms"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
                <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
                <span style="float: left;margin-left:10px">{{ item.label }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="类型" prop="type">
            <el-select
              v-model="formData.type"
              placeholder="类型"
              clearable
              style="width:100%;"
              class="filter-item"
            >
              <el-option label="每日" :value="'DAILY'" />
              <el-option label="每周" :value="'WEEKLY'" />
            </el-select>
          </el-form-item>
          <el-form-item label="礼物类型">
            <el-select
              v-model="formData.giftType"
              placeholder="礼物类型"
              style="width:100%;"
              class="filter-item"
              @change="getGiftListInfo()"
            >
              <el-option v-for="(item) in giftConfigTabs" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item prop="giftId" label="资源">
            <el-select v-model="formData.giftId" v-loading="giftLoading" placeholder="请选择" style="width:100%;" @change="showPhoto">
              <el-option
                v-for="(item, index) in gifts"
                :key="index"
                :label="item.id"
                :value="item.id"
                :style="'height:65px'"
              >
                <div style="float: left;">
                  <img :src="item.giftPhoto" width="65px" height="65px">
                </div>
              </el-option>
            </el-select>
            <div style="float: left;">
              <img class="img" :src="photoUrl" width="65px" height="65px">
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">
          取消
        </el-button>
        <el-button
          :loading="submitLoading"
          type="primary"
          @click="handleSubmit"
        >
          提交
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import { addOrUpdateBountyConfig } from '@/api/game-lucky-box-config'
import { mapGetters } from 'vuex'
import { giftConfigTabs } from '@/constant/type'
import { listByTabV2 } from '@/api/gift'
export default {
  name: 'StandardConfigEdit',
  props: {
    updateData: {
      type: Object,
      default: null
    },
    sysOrigin: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      giftConfigTabs,
      gifts: [],
      photoUrl: '',
      giftPhoto: '',
      giftLoading: false,
      formData: {
        id: '',
        sysOrigin: 'MARCIE',
        giftType: '',
        giftId: '',
        type: ''
      },
      gift: {
        id: '',
        giftPhoto: ''

      },
      rules: {
        sysOrigin: [{ required: true, message: '必填项不可为空', trigger: 'blur' }],
        giftType: [{ required: true, message: '必填项不可为空', trigger: 'blur' }],
        giftId: [{ required: true, message: '必填项不可为空', trigger: 'blur' }],
        type: [{ required: true, message: '必填项不可为空', trigger: 'blur' }]
      },
      submitLoading: false
    }
  },
  computed: {
    isAdd() {
      return this.updateData === null
    },
    title() {
      return this.isAdd ? `添加(${this.sysOrigin})` : `修改(${this.updateData.sysOrigin})`
    },
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    updateData: {
      immediate: true,
      deep: true,
      handler(newVal) {
        Object.assign(this.formData, newVal)
        this.getGiftList()
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    handleSubmit() {
      const that = this
      that.$refs.dataForm.validate(valid => {
        if (valid) {
          that.submitLoading = true
          that.formData.sysOrigin = that.sysOrigin
          addOrUpdateBountyConfig(that.formData).then(res => {
            that.submitLoading = false
            this.$emit('success', 'create')
          }).catch(er => {
            that.submitLoading = false
            console.error(er)
            this.$emit('fail')
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    },
    getGiftListInfo() {
      const that = this
      that.formData.giftId = ''
      that.photoUrl = ''
      that.getGiftList()
    },
    getGiftList() {
      const that = this
      that.giftLoading = true
      listByTabV2({ 'sysOrigin': that.formData.sysOrigin, 'giftTab': that.formData.giftType }).then(res => {
        that.gifts = res.body || []
        that.giftLoading = false
        this.showPhoto()
      }).catch(er => {
        that.giftLoading = false
        console.error(er)
      })
    },
    showPhoto() {
      const that = this
      const item = this.gifts.find(item1 => item1.id === that.formData.giftId) || that.gift
      that.photoUrl = item.giftPhoto
    }
  }
}
</script>
<style scoped>
.col-margin {
  margin-bottom: 20px;
}
img[src=""],img:not([src]){
            opacity: 0;
        }
</style>
