<template>
  <div class="app-container">

    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatformAlls"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.lotteryType"
        placeholder="关联类型"
        style="width:120px;"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="经典" :value="'CLASSICS'" />
        <el-option label="星座" :value="'CONSTELLATION'" />
      </el-select>
      <el-select
        v-model="listQuery.closed"
        placeholder="状态"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="正常" :value="false" />
        <el-option label="关闭" :value="true" />
      </el-select>
      <el-button
        :loading="searchLoading"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-edit"
        @click="handleAdd"
      >
        添加
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="sysOrigin" label="平台" align="center" width="200" />
      <el-table-column prop="name" label="名称" width="200" />
      <el-table-column label="类型" align="center" width="200">
        <template slot-scope="scope">
          <div>
            <el-tag v-if="scope.row.lotteryType === 'CLASSICS'">经典</el-tag>
            <el-tag v-else-if="scope.row.lotteryType === 'CONSTELLATION'">星座</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="giftQuantity" label="展示礼物数量" width="200" />
      <el-table-column width="200" label="正常/关闭" align="center" min-width="100">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.closed"
            :active-value="false"
            :inactive-value="true"
            @change="handleSwitchChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center" width="200">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click.native="hanldeEidt(scope.row)">编辑</el-button>
          <el-button type="text" @click.native="hanldeGiftConfig(scope.row)">礼物配置</el-button>
          <el-button type="text" @click.native="handleProbabilityConfigList(scope.row)">概率配置</el-button>
          <el-button type="text" @click.native="handlDel(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
    <config-edit
      v-if="standardConfigEditVisible"
      :update-data="updateData"
      :sys-origin="listQuery.sysOrigin"
      @close="standardConfigEditVisible = false"
      @success="standardConfigEditSuccess"
    />

    <gift-config-edit
      v-if="giftConfigVisible"
      :standard-info="thatSelectedStandardConfig"
      :event="luckyBoxEditEvent"
      @close="giftConfigVisible=false"
    />

    <probability-config
      v-if="probabilityConfigVisible"
      :standard-id="thatSelectedStandardConfigId"
      :event="luckyBoxEditEvent"
      @close="probabilityConfigVisible=false"
    />

  </div>
</template>

<script>
import { getStandardConfig, deleteStandardConfig, switchStatus } from '@/api/game-lucky-box-config'
import Pagination from '@/components/Pagination'
import ConfigEdit from './edit.vue'
import GiftConfigEdit from './gift-details.vue'
import ProbabilityConfig from './probability.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'StandardConfigTable',
  components: {
    Pagination, ConfigEdit, ProbabilityConfig, GiftConfigEdit
  },
  data() {
    return {
      standardConfigEditVisible: false,
      probabilityConfigVisible: false,
      giftConfigVisible: false,
      thatSelectedStandardConfigId: '',
      thatSelectedStandardConfig: {},
      updateData: null,
      luckyBoxEditEvent: 'ADD',
      thisRow: {},
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        sysOrigin: 'MARCIE',
        lotteryType: '',
        closed: ''
      },
      listLoading: true,
      searchLoading: false
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatformAlls', 'permissionsFirstSysOrigin'])
  },
  created() {
    if (!this.permissionsFirstSysOrigin) {
      return
    }
    this.listQuery.sysOrigin = this.permissionsFirstSysOrigin.value
    this.renderData(true)
  },
  methods: {
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      getStandardConfig(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.searchLoading = that.listLoading = false
      }).catch(er => {
        that.searchLoading = that.listLoading = false
      })
    },
    handleSearch() {
      this.searchLoading = true
      this.renderData(true)
    },
    handleProbabilityConfigList(row) {
      this.thatSelectedStandardConfigId = String(row.id)
      this.probabilityConfigVisible = true
    },
    hanldeGiftConfig(row) {
      this.thatSelectedStandardConfig = row
      this.giftConfigVisible = true
    },
    handleAdd() {
      this.standardConfigEditVisible = true
      this.updateData = null
    },
    hanldeEidt(row) {
      this.standardConfigEditVisible = true
      this.updateData = row
    },
    standardConfigEditSuccess(event) {
      this.updateData = null
      this.standardConfigEditVisible = false
      this.renderData(event === 'create')
    },
    handleSwitchChange(row) {
      switchStatus(row.id, row.closed)
        .then(res => {})
        .catch(er => {
          row.closed = !row.closed
        })
    },
    // 删除
    handlDel(row) {
      this.$confirm('确认删除吗？', '提示', {
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        deleteStandardConfig(row.id).then((res) => {
          this.listLoading = false
          this.$message({
            message: '删除成功',
            type: 'success'
          })
          this.renderData()
        })
      }).catch(() => {
        this.listLoading = false
      })
    }
  }
}
</script>
<style scoped lang="scss">
.this-level {
  padding-bottom: 30px;
  display: flex;
  > div {
    width: 150px;
  }
}
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
