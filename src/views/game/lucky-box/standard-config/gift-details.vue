<template>
  <div class="app-container">
    <el-dialog
      title="礼物配置列表"
      :visible.sync="isDialog"
      :before-close="handleClose"
      :close-on-click-modal="false"
      :fullscreen="true"
      top="20px"
    >
      <div class="filter-container">
        <el-button
          class="filter-item"
          style="margin-left: 10px;"
          type="primary"
          icon="el-icon-edit"
          :disabled="isAdd"
          @click="handleAdd"
        >
          添加
        </el-button>
        <el-alert
          title="luckyBox礼物配置-必读"
          type="warning"
          :description="description"
          show-icon
          :closable="false"
        />
      </div>
      <el-table
        v-loading="listLoading"
        :data="dataList"
        element-loading-text="Loading"
        fit
        highlight-current-row
      >
        <el-table-column type="index" width="50" label="No" />
        <el-table-column prop="sysOrigin" label="平台" align="center" width="280" />
        <el-table-column prop="name" label="名称" align="center" width="280" />
        <el-table-column label="封面" align="center">
          <template slot-scope="scope">
            <el-image
              style="width: 50px; height: 50px"
              :src="scope.row.photoUrl"
              :preview-src-list="[scope.row.photoUrl]"
            />
          </template>
        </el-table-column>
        <el-table-column prop="giftCandy" label="金币" align="center" width="280" />
        <el-table-column prop="createTime" label="创建时间" align="center" width="300">
          <template slot-scope="scope">
            {{ scope.row.createTime | dateFormat }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" align="center">
          <template slot-scope="scope">
            <el-button type="text" @click.native="hanldeEidt(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div>
        <el-dialog
          title="礼物配置"
          width="550px"
          top="50px"
          :visible.sync="innerEditVisible"
          :close-on-press-escape="false"
          :close-on-click-modal="false"
          append-to-body
        >
          <div style="height: 500px;overflow: auto;">
            <el-form
              ref="dataForm"
              :rules="formRules"
              :model="formData"
              style="width: 400px; margin-left:50px;"
            >
              <el-form-item label="系统" prop="sysOrigin">
                <el-select
                  v-model="formData.sysOrigin"
                  placeholder="选择系统"
                  style="width:100%;"
                  class="filter-item"
                >
                  <el-option
                    v-for="item in permissionsSysOriginPlatforms"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
                    <span style="float: left;margin-left:10px">{{ item.label }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="名称" prop="name">
                <el-input v-model="formData.name" placeholder="名称" />
              </el-form-item>
              <el-form-item label="序号" prop="sort">
                <el-input v-model="formData.sort" placeholder="序号" />
              </el-form-item>
              <el-form-item label="礼物类型">
                <el-select
                  v-model="formData.giftType"
                  placeholder="礼物类型"
                  style="width:100%;"
                  class="filter-item"
                  @change="getGiftListInfo()"
                >
                  <el-option v-for="(item) in giftConfigTabs" :key="item.value" :label="item.name" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item prop="giftId" label="资源">
                <el-select v-model="formData.giftId" v-loading="giftLoading" placeholder="请选择" style="width:100%;" @change="showPhoto">
                  <el-option
                    v-for="(item, index) in gifts"
                    :key="index"
                    :label="item.id"
                    :value="item.id"
                    :style="'height:65px'"
                  >
                    <div style="float: left;">
                      <img :src="item.giftPhoto" width="65px" height="65px">
                    </div>
                  </el-option>
                </el-select>
                <div style="float: left;">
                  <img class="img" :src="photoUrl" width="65px" height="65px">
                </div>
              </el-form-item>
            </el-form>
          </div>
          <div slot="footer" class="dialog-footer">
            <el-button @click="innerEditVisible = false">
              取消
            </el-button>
            <el-button
              :loading="submitLoading"
              type="primary"
              @click="handleSubmit"
            >
              提交
            </el-button>
          </div>
        </el-dialog>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getGameLuckyBoxGiftConfig, addOrUpdateGiftConfig } from '@/api/game-lucky-box-config'
import { mapGetters } from 'vuex'
import { giftConfigTabs } from '@/constant/type'
import { listByTabV2 } from '@/api/gift'
export default {
  name: 'GiftConfigTable',
  components: { },
  props: {
    standardInfo: {
      type: Object,
      required: true
    },
    updateGiftData: {
      type: Object,
      default: null
    }
  },
  data() {
    const commonRules = [
      { required: true, message: '必填字段不可为空', trigger: 'blur' }
    ]
    return {
      standardConfig: {
        id: '',
        giftQuantity: ''
      },
      gifts: [],
      isAdd: true,
      photoUrl: '',
      giftPhoto: '',
      giftLoading: false,
      giftConfigTabs,
      dataList: [],
      isDialog: true,
      innerEditVisible: false,
      listQuery: {
        standardId: '',
        sysOrigin: 'MARCIE'
      },
      formData: {
        id: '',
        standardId: '',
        sysOrigin: 'MARCIE',
        name: '',
        giftType: '',
        giftId: '',
        sort: ''
      },
      formRules: {
        sysOrigin: commonRules,
        sort: commonRules,
        giftType: commonRules
      },
      gift: {
        id: '',
        giftPhoto: ''

      },
      submitLoading: false,
      listLoading: true
    }
  },
  computed: {
    description() {
      return `礼物必须配置(${this.standardConfig.giftQuantity})个，游戏才可进行！`
    },
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    standardInfo: {
      handler(newVal) {
        this.standardConfig = newVal
        this.listQuery.standardId = this.standardConfig.id
        this.renderData()
      },
      immediate: true
    },
    updateGiftData: {
      immediate: true,
      deep: true,
      handler(newVal) {
        Object.assign(this.formData, newVal)
        this.giftData = newVal
        this.getGiftList()
      }
    }
  },
  created() {
    if (!this.permissionsFirstSysOrigin) {
      return
    }
    this.listQuery.sysOrigin = this.permissionsFirstSysOrigin.value
    this.renderData()
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      getGameLuckyBoxGiftConfig(that.listQuery).then(res => {
        const { body } = res
        that.dataList = body
        that.isAdd = parseInt(that.standardConfig.giftQuantity) === parseInt(that.dataList.length)
        that.listLoading = false
      }).catch(er => {
        that.listLoading = false
      })
    },
    handleAdd() {
      this.innerEditVisible = true
      this.formData = {}
      this.photoUrl = ''
    },
    handleClose() {
      this.$emit('close')
    },
    handleSubmit() {
      const that = this
      that.$refs.dataForm.validate(valid => {
        if (valid) {
          that.submitLoading = true
          that.formData.standardId = that.listQuery.standardId
          addOrUpdateGiftConfig(that.formData).then(res => {
            that.submitLoading = false
            that.innerEditVisible = false
            that.renderData()
            this.$emit('success', 'create')
          }).catch(er => {
            that.submitLoading = false
            that.innerEditVisible = false
            console.error(er)
            this.$emit('fail')
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    },
    getGiftListInfo() {
      const that = this
      that.formData.giftId = ''
      that.photoUrl = ''
      that.getGiftList()
    },
    hanldeEidt(row) {
      this.innerEditVisible = true
      this.giftData = row
      this.formData = this.giftData
      this.photoUrl = this.giftData.photoUrl
    },
    showPhoto() {
      const that = this
      const item = this.gifts.find(item1 => item1.id === that.formData.giftId) || that.gift
      that.photoUrl = item.giftPhoto
    },
    getGiftList() {
      const that = this
      that.giftLoading = true
      listByTabV2({ 'sysOrigin': that.formData.sysOrigin, 'giftTab': that.formData.giftType }).then(res => {
        that.gifts = res.body || []
        that.giftLoading = false
        this.showPhoto()
      }).catch(er => {
        that.giftLoading = false
        console.error(er)
      })
    }
  }
}
</script>
<style scoped lang="scss">
.col-margin {
  margin-bottom: 20px;
}
img[src=""],img:not([src]){
            opacity: 0;
        }
</style>
