<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="item in tables" :key="item.name" :label="item.title" :name="item.component" />
      <component :is="activeName" />
    </el-tabs>
  </div>
</template>
<script>
import GameTeamPK from './team'
import GameRoomPK from './room'
export default {
  name: 'GamePK',
  components: { GameTeamPK, GameRoomPK },
  data() {
    return {
      activeName: 'GameRoomPK',
      tables: [
        {
          title: '房间PK',
          component: 'GameRoomPK'
        }, {
          title: '团队PK',
          component: 'GameTeamPK'
        }
      ]
    }
  }
}
</script>
