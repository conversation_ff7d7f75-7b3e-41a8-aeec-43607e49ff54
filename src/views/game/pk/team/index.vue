<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 200px"
        class="filter-item"
        collapse-tags
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <search-room-input placeholder="房间" @success="searchRoomSuccess" @fail="searchRoomFail" @load="loadSearchRoom" />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-alert type="success" :closable="false">
      <strong>注意:</strong> 每条数据的生命周期30天, 30天后记录将会永久清理
    </el-alert>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="房间" align="center" min-width="200">
        <template slot-scope="scope">
          <el-link @click="queryRoomDetails(scope.row.roomProfile.id)">
            {{ scope.row.roomProfile.roomName }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="MVP用户" align="center" min-width="200">
        <template slot-scope="scope">
          <user-table-exhibit v-if="scope.row.msvUserProfile != null" :user-profile="scope.row.msvUserProfile" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column prop="blueTeamScore" label="蓝队积分" align="center" min-width="80" />
      <el-table-column prop="redTeamScore" label="红队积分" align="center" min-width="80" />
      <el-table-column prop="createTime" label="创建时间" width="200" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
    </el-table>
    <div v-if="list && list.length > 0" style="text-align: center; margin-top:20px;">
      <el-button v-if="!notMore " size="mini" :disabled="listLoading" @click="clickLoadMore">加载更多</el-button>
      <span v-else>已加载全部</span>
    </div>

    <room-deatils-drawer
      v-if="roomDeatilsDrawerVisible"
      :room-id="roomId"
      @close="roomDeatilsDrawerVisible=false"
    />
  </div>
</template>

<script>

import RoomDeatilsDrawer from '@/components/data/RoomDeatilsDrawer'
import { listTeamPk } from '@/api/game'
import { pickerOptions } from '@/constant/el-const'
import { mapGetters } from 'vuex'
export default {
  name: 'GameTeamPK',
  components: { RoomDeatilsDrawer },
  data() {
    return {
      roomId: '',
      roomDeatilsDrawerVisible: false,
      dtailsDrawerVisible: false,
      thatRow: {},
      searchDisabled: false,
      pickerOptions,
      list: [],
      total: 0,
      listQuery: {
        limit: 20,
        roomId: '',
        sysOrigin: '',
        lastId: ''
      },
      listLoading: true,
      notMore: false
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        that.list = []
        that.listQuery.lastId = ''
      }
      that.listLoading = true
      listTeamPk(that.listQuery).then(res => {
        that.listLoading = false
        const { body } = res
        const list = body || []
        that.notMore = list.length <= 0
        that.list = that.list.concat(list)
        if (that.list && that.list.length > 0) {
          that.listQuery.lastId = that.list[that.list.length - 1].id
        }
      }).catch(er => {
        that.listLoading = false
        console.error(er)
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    clickLoadMore() {
      this.renderData()
    },
    queryRoomDetails(roomId) {
      this.roomDeatilsDrawerVisible = true
      this.roomId = roomId
    },
    loadSearchRoom() {
      this.searchDisabled = true
    },
    searchRoomSuccess(res) {
      this.searchDisabled = false
      if (!res) {
        return
      }
      this.listQuery.roomId = res.id
    },
    searchRoomFail() {
      this.listQuery.roomId = ''
      this.searchDisabled = false
    }
  }
}
</script>
