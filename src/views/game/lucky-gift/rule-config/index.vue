<template>
  <div class="lucky-gift">
    <div v-loading="submitLoading">
      <h2>幸运礼物规则配置</h2>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" class="rule-config" label-position="left">
        <!--<el-alert
          title="接收人是【主播】则目标与钻石分成比例如下;是【其他用户】则钻石分成比例如下"
          type="warning"
          :closable="false"
        />
        <el-form-item label-width="0px">
          <el-col class="col-margin">
            <el-form-item label="接收人分成比例" prop="giftShareRatio" label-width="135px">
              <el-input v-model.trim="form.giftShareRatio" type="text" class="gift-rule-input" placeholder="接收方分成比例" />
            </el-form-item>
          </el-col>
          <el-col style="width:20px">
            <span class="span-padding">%</span>
          </el-col>
        </el-form-item>-->
        <el-form-item label-width="0px">
          <el-col class="col-margin">
            <el-form-item label="Win图标" prop="winStart" label-width="135px">
              <el-input v-model.trim="form.winStart" type="text" class="gift-rule-input" />
            </el-form-item>
          </el-col>
          <el-col class="col-span">
            <span class="span-padding">(包含)~</span>
          </el-col>
          <el-col class="col-margin-two">
            <el-form-item prop="winEnd">
              <el-input v-model.trim="form.winEnd" type="text" class="gift-rule-input" />
            </el-form-item>
          </el-col>
          <el-col class="col-margin">
            <span class="span-padding">(包含)</span>
          </el-col>
        </el-form-item>
        <el-form-item label-width="0px">
          <el-col class="col-margin">
            <el-form-item label="BigWin图标" prop="bigWinStart" label-width="135px">
              <el-input v-model.trim="form.bigWinStart" type="text" class="gift-rule-input" />
            </el-form-item>
          </el-col>
          <el-col class="col-span">
            <span class="span-padding">(包含)~</span>
          </el-col>
          <el-col class="col-margin-two">
            <el-form-item prop="bigWinEnd">
              <el-input v-model.trim="form.bigWinEnd" type="text" class="gift-rule-input" />
            </el-form-item>
          </el-col>
          <el-col class="col-margin">
            <span class="span-padding">(包含)</span>
          </el-col>
        </el-form-item>
        <el-form-item label-width="0px">
          <el-col class="col-margin">
            <el-form-item label="SuperWin图标" prop="superStart" label-width="135px">
              <el-input v-model.trim="form.superStart" type="text" class="gift-rule-input" />
            </el-form-item>
          </el-col>
          <el-col class="col-span">
            <span class="span-padding">(包含)~</span>
          </el-col>
          <el-col class="col-margin-two">
            <el-form-item prop="superEnd">
              <el-input v-model.trim="form.superEnd" type="text" class="gift-rule-input" />
            </el-form-item>
          </el-col>
          <el-col class="col-span">
            <span class="span-padding">(包含)</span>
          </el-col>
        </el-form-item>
      </el-form>
      <div slot="footer" style="padding-left:20px">
        <el-button type="primary" @click="submitForm()">保存</el-button>
      </div>
    </div>
  </div>
</template>
<script>
import { addLuckyGiftRuleConfig, getLuckyGiftRuleConfig } from '@/api/game-lucky-gift-config'
import { mapGetters } from 'vuex'

export default {
  name: 'LuckyGiftRuleConfig',
  data() {
    function commonFormRules() {
      return [
        { required: true, message: '必填字段', trigger: 'blur' }
      ]
    }
    return {
      submitLoading: false,
      form: {
        id: '',
        giftShareRatio: '',
        winStart: '',
        winEnd: '',
        bigWinStart: '',
        bigWinEnd: '',
        superStart: '',
        superEnd: '',
        sysOrigin: 'HALAR'
      },
      ruleConfigQuery: {
        sysOrigin: 'HALAR'
      },
      rules: {
        giftShareRatio: commonFormRules(),
        winStart: commonFormRules(),
        winEnd: commonFormRules(),
        bigWinStart: commonFormRules(),
        bigWinEnd: commonFormRules(),
        superStart: commonFormRules(),
        superEnd: commonFormRules(),
        sysOrigin: commonFormRules()
      }
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.ruleConfigQuery.sysOrigin = querySystem.value
    that.ruleConfig()
  },
  methods: {
    ruleConfig() {
      const that = this
      that.submitLoading = true
      getLuckyGiftRuleConfig(that.ruleConfigQuery).then(res => {
        const { body } = res
        that.form = body
        that.submitLoading = false
      }).catch(er => {
        that.submitLoading = false
        console.error(er)
        that.$emit('fial', { error: er })
      })
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (valid) {
          that.submitLoading = true
          that.form.sysOrigin = that.ruleConfigQuery.sysOrigin
          addLuckyGiftRuleConfig(that.form).then(res => {
            that.submitLoading = false
            that.ruleConfig()
            that.$emit('success', { result: res })
          }).catch(er => {
            that.submitLoading = false
            console.error(er)
            that.$emit('fial', { error: er })
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    }
  }
}
</script>
<style scoped>
h2{
  padding-left: 20px;
  color: dodgerblue;
}
.span-padding{
  padding-left: 7px;
}
.rule-config{
  padding-left: 20px;
}
.gift-rule-input{
  width: 160px;
}
.col-span{
   width: 60px;
}
.col-margin{
  width: 300px;
}
.col-margin-two{
  width: 165px;
}
</style>
