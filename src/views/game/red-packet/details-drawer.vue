<template>
  <div class="game-red-packet-drawer">
    <el-drawer
      title="详情"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div class="game-red-packet-content">
        <div class="blockquote">发送人</div>
        <div class="content">
          <user-table-exhibit
            :user-profile="row.userProfile"
            :query-details="true"
          />
        </div>
        <div v-if="row.type == 'ROOM'" class="blockquote">发送房间</div>
        <div v-if="row.type == 'ROOM'" class="content">
          <div class="flex-l" style="padding-bottom: 10px;">
            <div class="room-avatar">
              <el-image
                :src="row.roomProfile.roomCover"
                style="width: 100%; height: 100%; border-radius: 100%; margin: auto;"
              >
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </el-image>
              <flag-icon
                class="flag-icon"
                :code="row.roomProfile.countryCode"
                :tooltip="row.roomProfile.countryName"
                size="18"
              />
            </div>
            <div class="nowrap-ellipsis room-name">
              <el-link @click="queryRoomDetails()"><a :title="row.roomProfile.roomName">{{ row.roomProfile.roomName }}</a></el-link>
            </div>
          </div>
        </div>
        <div class="blockquote">领取用户</div>
        <div class="content">
          <el-timeline>
            <el-timeline-item
              v-for="(item, index) in row.receiveUsers"
              :key="index"
              :timestamp="item.createTime"
            >
              <user-table-exhibit
                :user-profile="item.userProfile"
                :query-details="true"
              />
              <div class="receive-info">
                <el-tag size="mini">领取: {{ item.amount }}</el-tag>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
        <div class="blockquote">日志事件</div>
        <div class="content">
          <el-timeline>
            <el-timeline-item
              v-for="(item, index) in row.logs"
              :key="index"
              :timestamp="item.createTime | dateFormat"
            >
              {{ item.content }}
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-drawer>

    <room-deatils-drawer
      v-if="roomDeatilsDrawerVisible"
      :room-id="row.roomId"
      @close="roomDeatilsDrawerVisible = false"
    />
  </div>
</template>
<script>
import RoomDeatilsDrawer from '@/components/data/RoomDeatilsDrawer'
export default {
  name: 'GameRedPacketDetailsDrawer',
  components: { RoomDeatilsDrawer },
  props: {
    row: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      roomDeatilsDrawerVisible: false
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    queryRoomDetails() {
      this.roomDeatilsDrawerVisible = true
    }
  }
}
</script>
<style scoped lang="scss">
.game-red-packet-content {
  padding: 0px 10px;
  .el-tabs__nav-scroll .el-tabs__item {
    font-size: 13px;
    color: #303133;
    border: 1px sol;
  }
  .room-avatar {
    position: relative;
    width: 50px;
    height: 50px;
    margin: auto;
    flex-shrink: 0;
    .flag-icon {
      position: absolute;
      bottom: 0px;
    }
  }

  .room-name {
    width: 100%;
    text-align: left;
    padding-left: 10px;
  }
}
</style>
