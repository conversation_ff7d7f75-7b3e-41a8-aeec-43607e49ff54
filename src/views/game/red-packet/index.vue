<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="归属系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in permissionsSysOriginPlatforms"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;">
            <sys-origin-icon
              :icon="item.value"
              :desc="item.value"
            /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.type"
        placeholder="红包类型"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option label="房间内" :value="'ROOM'" />
        <el-option label="用户1V1" :value="'USER'" />
        <el-option label="家族群聊" :value="'FAMILY_GROUP_CHAT'" />
      </el-select>
      <div v-if="listQuery.type == 'ROOM'" class="filter-item">
        <search-room-input
          @success="searchRoomSuccess"
          @fail="searchRoomFail"
          @load="loadSearchRoom"
        />
      </div>
      <div v-if="listQuery.type == 'FAMILY_GROUP_CHAT'" class="filter-item">
        <account-input
          v-model="listQuery.relationId"
          :sys-origin="listQuery.sysOrigin"
          placeholder="发送用户"
        />
      </div>
      <div v-if="listQuery.type == 'USER'" class="filter-item">
        <account-input
          v-model="listQuery.relationId"
          :sys-origin="listQuery.sysOrigin"
          placeholder="发送用户"
        />
      </div>
      <div v-if="listQuery.type == 'USER'" class="filter-item">
        <account-input
          v-model="listQuery.receiverId"
          :sys-origin="listQuery.sysOrigin"
          placeholder="接收用户"
        />
      </div>
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="发送日期开始"
          end-placeholder="发送日期结束"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column type="index" width="50" label="No" />
      <el-table-column
        label="用户"
        align="center"
        show-overflow-tooltip
        min-width="220"
      >
        <template slot-scope="scope">
          <user-table-exhibit
            :user-profile="scope.row.userProfile"
            :query-details="true"
          />
        </template>
      </el-table-column>
      <el-table-column
        v-if="listQuery.type == 'USER'"
        label="接收用户"
        align="center"
        show-overflow-tooltip
        min-width="220"
      >
        <template slot-scope="scope">
          <user-table-exhibit
            :user-profile="scope.row.designatedReceiverUser"
            :query-details="true"
          />
        </template>
      </el-table-column>
      <el-table-column
        v-if="listQuery.type == 'ROOM'"
        label="房间"
        align="center"
        min-width="200"
      >
        <template slot-scope="scope">
          <div class="flex-l">
            <div class="room-avatar">
              <el-image
                :src="scope.row.roomProfile.roomCover"
                style="width: 100%; height: 100%; border-radius: 100%; margin: auto;"
              >
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </el-image>
              <flag-icon
                class="flag-icon"
                :code="scope.row.roomProfile.countryCode"
                :tooltip="scope.row.roomProfile.countryName"
                size="18"
              />
            </div>
            <div class="nowrap-ellipsis room-name">
              <el-link
                @click="queryRoomDetails(scope.row)"
              ><a :title="scope.row.roomProfile.roomName">{{
                scope.row.roomProfile.roomName
              }}</a></el-link>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="amount" label="金额" align="center" />
      <el-table-column prop="quantity" label="数量" align="center" />
      <el-table-column label="已领取" align="center">
        <template slot-scope="scope">
          {{ getReceiveUsersLength(scope.row) }}
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          <span v-if="getReceiveUsersLength(scope.row) === scope.row.quantity">
            {{ statusMap["NOT_RECEIVE"] || "-" }}
          </span>
          <span v-else>{{ statusMap[scope.row.status] || "-" }}</span>
        </template>
      </el-table-column>

      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
        width="200"
      >
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column
        prop="updateTime"
        label="修改时间"
        align="center"
        width="200"
      >
        <template slot-scope="scope">
          {{ scope.row.updateTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="100">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click.native="clickQueryDetails(scope.row)"
          >详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="listQuery.lastId" class="load-more">
      <span v-if="notData">已加载全部</span>
      <el-button
        v-else
        size="mini"
        :disabled="loadMoreLoading"
        :loading="loadMoreLoading"
        @click="clickLoadMore"
      >加载更多</el-button>
    </div>

    <game-red-packet-receiving-users-table
      v-if="gameRedPacketReceivingUsers"
      :red-packet-id="redPacketId"
      @close="gameRedPacketReceivingUsers = false"
    />

    <room-deatils-drawer
      v-if="roomDeatilsDrawerVisible"
      :room-id="thatRow.relationId"
      @close="roomDeatilsDrawerVisible = false"
    />

    <game-red-packet-details-drawer
      v-if="gameRedPacketDetailsDrawerVisible"
      :row="thatRow"
      @close="gameRedPacketDetailsDrawerVisible = false"
    />
  </div>
</template>

<script>
import { flowGameRedPacket } from '@/api/room'
import RoomDeatilsDrawer from '@/components/data/RoomDeatilsDrawer'
import GameRedPacketDetailsDrawer from './details-drawer'
import { pickerOptions } from '@/constant/el-const'
import { sysOriginPlatforms } from '@/constant/origin'
import { mapGetters } from 'vuex'

export default {
  name: 'GameRedPacketRecordTable',
  components: { RoomDeatilsDrawer, GameRedPacketDetailsDrawer },
  data() {
    return {
      gameRedPacketDetailsDrawerVisible: false,
      thatRow: {},
      sysOriginPlatforms,
      gameRedPacketReceivingUsers: false,
      roomDeatilsDrawerVisible: false,
      thatSelectedUserId: '',
      pickerOptions,
      list: [],
      notData: false,
      loadMoreLoading: false,
      checkList: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        sysOrigin: 'HALAR',
        relationId: '',
        startCreateDate: '',
        endCreateDate: '',
        lastId: '',
        type: 'ROOM',
        receiverId: ''
      },
      listLoading: true,
      rangeDate: '',
      redPacketId: '',
      searchLoading: false,
      searchDisabled: false,
      statusMap: {
        AVAILABLE: '等待领取',
        NOT_RECEIVE: '已抢完',
        REIMBURSE: '已处理退款',
        NOT_REIMBURSE: '已处理退款, 抽成没有可退'
      }
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startCreateDate = newVal[0]
          this.listQuery.endCreateDate = newVal[1]
          return
        }
        this.listQuery.startCreateDate = ''
        this.listQuery.endCreateDate = ''
      }
    }
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isReset, loadMore) {
      const that = this
      if (isReset === true) {
        that.list = []
        that.listQuery.cursor = 1
        that.listQuery.lastId = ''
      }
      that.loadMoreLoading = loadMore
      that.listLoading = !that.loadMoreLoading
      that.searchDisabled = true
      flowGameRedPacket(that.listQuery)
        .then(res => {
          that.listLoading = false
          that.loadMoreLoading = false
          that.searchDisabled = false
          const { body } = res
          const list = body || []
          that.notData = list.length <= 0
          if (!that.notData) {
            that.list = that.list.concat(list)
            that.listQuery.lastId = that.list[that.list.length - 1].id
          }
        })
        .catch(er => {
          that.searchDisabled = false
          that.listLoading = false
          that.loadMoreLoading = false
        })
    },
    handleSearch() {
      this.searchLoading = true
      this.renderData(true)
    },
    clickQueryDetails(row) {
      this.thatRow = row
      this.gameRedPacketDetailsDrawerVisible = true
    },
    loadSearchRoom() {
      this.searchDisabled = true
    },
    searchRoomSuccess(res) {
      this.searchDisabled = false
      if (!res) {
        return
      }
      this.listQuery.relationId = res.id
      if (this.listQuery.relationId) {
        this.renderData(true)
      }
    },
    searchRoomFail() {
      this.listQuery.relationId = ''
      this.searchDisabled = false
    },
    queryRoomDetails(row) {
      this.thatRow = row
      this.roomDeatilsDrawerVisible = true
    },
    getReceiveUsersLength(row) {
      return row.receiveUsers ? row.receiveUsers.length : 0
    },
    clickLoadMore() {
      const that = this
      that.loadMoreLoading = true
      that.renderData(false, true)
    }
  }
}
</script>
<style scoped lang="scss">
.room-avatar {
  position: relative;
  width: 50px;
  height: 50px;
  margin: auto;
  flex-shrink: 0;
  .flag-icon {
    position: absolute;
    bottom: 0px;
  }
}

.room-name {
  width: 100%;
  text-align: left;
  padding-left: 10px;
}

.load-more {
  padding: 20px;
  text-align: center;
}
.content {
  overflow: auto;
  padding: 10px 0px;
}
</style>
