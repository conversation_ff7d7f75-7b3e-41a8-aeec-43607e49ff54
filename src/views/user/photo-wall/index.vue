<template>
  <div class="app-container">
    <el-row v-loading="listLoading" :gutter="10">
      <el-col
        v-for="(item, index) in list"
        :key="index"
        :span="4"
        style="margin-bottom: 10px;"
      >
        <el-card :body-style="{ padding: '0px' }">
          <el-image
            style="width:100%;height:300px;"
            :src="item.resourceUrl"
            :preview-src-list="[item.resourceUrl]"
          />
          <div style="padding: 14px;">
            <div class="bottom">
              <div>创建日期：{{ item.createTime | dateFormat }}</div>
              <el-button
                type="text"
                class="button"
                @click="handlDel(item.id, index)"
              >删除照片</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <user-deatils-drawer
      v-if="userDeatilsDrawer"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawer = false"
    />
  </div>
</template>
<script>
import { getPotoWallTable, deletePhotoWall } from '@/api/app-user'
import Pagination from '@/components/Pagination'
export default {
  name: 'UserPhotoWall',
  components: { Pagination },
  data() {
    return {
      userDeatilsDrawer: false,
      thatSelectedUserId: '',
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: '',
        userNickname: '',
        cellphoneNumber: '',
        authType: '',
        originPlatform: '',
        originPhoneModel: '',
        userSex: '',
        userType: '',
        startTime: '',
        endTime: ''
      },
      listLoading: true
    }
  },
  created() {
    this.renderData()
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      getPotoWallTable(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handlDel(id, index) {
      const that = this
      deletePhotoWall(id)
        .then(res => {
          that.$opsMessage.success()
          that.list.splice(index, 1)
        })
        .catch(er => {
          that.$opsMessage.fail()
        })
    }
  }
}
</script>
<style scoped lang="scss">
.bottom {
  font-size: 14px;
}
</style>
