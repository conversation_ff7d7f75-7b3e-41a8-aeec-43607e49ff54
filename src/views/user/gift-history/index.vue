<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <div><account-input v-model="listQuery.sendUserId" placeholder="发送用户" type="USER" :sys-origin="listQuery.sysOrigin" /></div>
      </div>

      <div class="filter-item">
        <div><account-input v-model="listQuery.acceptUserId" placeholder="接收用户" type="USER" :sys-origin="listQuery.sysOrigin" /></div>
      </div>
      <el-input
        v-model.trim="listQuery.giftId"
        v-number
        placeholder="礼物ID"
        style="width: 200px;"
        class="filter-item"
      />
      <el-select
        v-if="showAllCondition"
        v-model="listQuery.giftValueType"
        placeholder="礼物类型"
        style="width:200px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option v-for="(item) in giftConfigTabs" :key="item.value" :label="item.name" :value="item.value" />
      </el-select>
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </div>
      <el-input
        v-if="showAllCondition"
        v-model.trim="listQuery.trackId"
        v-number
        placeholder="跟踪ID"
        style="width: 200px;"
        class="filter-item"
      />
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-checkbox v-model="showAllCondition" class="filter-item">显示所有条件</el-checkbox>
    </div>

    <el-alert type="warning" :closable="false">
      <div slot="title" class="filter-item">
        <el-tooltip class="item" effect="dark">
          <div slot="content">
            <p>什么时候会触发 “礼物总价值” 统计?</p>
            <p>* 用户ID && 接受人ID && 日期范围 <strong>不为空触发</strong></p>
            <p>礼物总价值是赠送后当礼物当前单价*数量=金币消耗总额 </p>
          </div>
          <i class="el-icon-question">
            <strong v-loading="totalCountLoading" element-loading-spinner="el-icon-loading"> 礼物总价值: {{ totalCount || '-' }}</strong>
          </i>
        </el-tooltip>
        、
        <el-tooltip class="item" effect="dark">
          <div slot="content">
            <p>什么时候会触发 “接收礼物数量” 统计?</p>
            <p>* 接受人ID && 礼物ID && 日期范围 <strong>不为空触发</strong></p>
            <p>用户接收了 </p>
          </div>
          <i class="el-icon-question">
            <strong v-loading="giftAcceptQuantityLoading" element-loading-spinner="el-icon-loading"> 接收礼物数量: {{ giftAcceptQuantity || '-' }}</strong>
          </i>
        </el-tooltip>
      </div>
      <strong>注意:</strong> 每条数据的生命周期30天, 30天后记录将会清理
    </el-alert>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="发送人" align="center" min-width="200">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" :show-sys-origin="true" />
          <div class="associate-block">
            <div class="associate-list">
              <div v-if="scope.row.roomProfile" class="block-item room-profile flex-c" @click="queryRoomDetails(scope.row)">
                <el-image
                  style="width: 24px; height: 24px; border-radius: 100%;cursor: pointer;"
                  :src="scope.row.roomProfile.roomCover"
                  fit="fill"
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline" />
                  </div>
                </el-image>
              </div>

              <div v-for="(item, index) in scope.row.acceptUsers" :key="index" class="block-item user-profile flex-c" @click="queryUserDetails(item)">
                <el-image
                  v-if="index<10"
                  style="width: 24px; height: 24px; border-radius: 100%;cursor: pointer;"
                  :src="item.userProfile.userAvatar"
                  fit="fill"
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline" />
                  </div>
                </el-image>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="giftValue.quantity" label="礼物数量" align="center" min-width="80">
        <template slot-scope="scope">
          <div class="flex-c">
            <el-image
              style="width: 30px; height: 30px"
              :src="scope.row.giftCover"
              fit="fill"
              :preview-src-list="[scope.row.giftCover]"
            />
            x {{ scope.row.giftValue.quantity }}
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="giftValue.userSize" label="接收人数" align="center" min-width="80" />
      <el-table-column prop="giftValue.actualAmount" label="消费金额" align="center" min-width="100" />
      <el-table-column prop="createTime" label="创建时间" align="center" width="200">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="60">
        <template slot-scope="scope">
          <el-button type="text" @click="clickQueryDetails(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="list && list.length > 0" style="text-align: center; margin-top:20px;">
      <el-button v-if="!notMore " size="mini" :disabled="listLoading" @click="clickLoadMore">加载更多</el-button>
      <span v-else>已加载全部</span>
    </div>

    <gift-history-details-drawer
      v-if="giftHistoryDetailsDrawerVisible"
      :row="thatRow"
      @close="giftHistoryDetailsDrawerVisible=false"
    />

    <room-deatils-drawer
      v-if="roomDeatilsDrawerVisible"
      :room-id="thatRow.originId"
      @close="roomDeatilsDrawerVisible=false"
    />

    <user-deatils-drawer
      v-if="userDeatilsDrawerVisible"
      :user-id="queryUserId"
      @close="userDeatilsDrawerVisible=false"
    />
  </div>
</template>

<script>

import { listGiftGiveAwayRunningWater, countGiftAmount, countGiftAcceptQuantity } from '@/api/gift'
import { mapGetters } from 'vuex'
import GiftHistoryDetailsDrawer from '@/components/data/GiftHistoryDetailsDrawer'
import RoomDeatilsDrawer from '@/components/data/RoomDeatilsDrawer'
import { giftConfigTabs } from '@/constant/type'
import { deepClone } from '@/utils'

export default {
  name: 'GiftHistory',
  components: { GiftHistoryDetailsDrawer, RoomDeatilsDrawer },
  data() {
    return {
      giftConfigTabs: deepClone(giftConfigTabs),
      showAllCondition: false,
      userDeatilsDrawerVisible: false,
      queryUserId: '',
      roomDeatilsDrawerVisible: false,
      thatRow: {},
      searchDisabled: false,
      giftHistoryDetailsDrawerVisible: false,
      list: [],
      rangeDate: [],
      listQuery: {
        limit: 20,
        sysOrigin: '',
        giftId: '',
        trackId: '',
        sendUserId: '',
        acceptUserId: '',
        startTime: '',
        endTime: '',
        lastId: '',
        giftValueType: ''
      },
      listLoading: true,
      clickUserId: '',
      totalCount: '',
      totalCountLoading: false,
      giftAcceptQuantity: '',
      giftAcceptQuantityLoading: false,
      notMore: false
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    }
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData(true)
    that.giftConfigTabs.push({ value: 'STAR', name: '周星礼物' })
  },
  methods: {
    renderData(isReset) {
      const that = this
      that.listLoading = true
      if (isReset === true) {
        that.list = []
        that.listQuery.lastId = ''
      }
      listGiftGiveAwayRunningWater(that.listQuery).then(res => {
        that.listLoading = false
        const { body } = res
        const list = body || []
        that.notMore = list.length <= 0
        that.list = that.list.concat(list)
        if (that.list && that.list.length > 0) {
          that.listQuery.lastId = that.list[that.list.length - 1].id
        }
      }).catch(er => {
        that.listLoading = false
      })

      that.totalCount = ''
      if (that.listQuery.sendUserId && that.listQuery.acceptUserId && that.listQuery.startTime && that.listQuery.endTime) {
        that.totalCountLoading = true
        countGiftAmount(that.listQuery).then(res => {
          that.totalCountLoading = false
          that.totalCount = res.body || ''
        }).catch(er => {
          that.totalCountLoading = false
        })
      }

      that.giftAcceptQuantity = ''
      if (that.listQuery.giftId && that.listQuery.acceptUserId && that.listQuery.startTime && that.listQuery.endTime) {
        that.giftAcceptQuantityLoading = true
        countGiftAcceptQuantity(that.listQuery).then(res => {
          that.giftAcceptQuantityLoading = false
          that.giftAcceptQuantity = res.body || ''
        }).catch(er => {
          that.giftAcceptQuantityLoading = false
        })
        return
      }
    },
    handleSearch() {
      this.renderData(true)
    },
    loadSearchUser() {
      this.searchDisabled = true
    },
    loadSearchAcceptUser() {
      this.searchDisabled = true
    },
    clickLoadMore() {
      this.renderData()
    },
    clickQueryDetails(row) {
      this.thatRow = row
      this.giftHistoryDetailsDrawerVisible = true
    },
    queryRoomDetails(row) {
      this.thatRow = row
      this.roomDeatilsDrawerVisible = true
    },
    queryUserDetails(row) {
      this.queryUserId = row.acceptUserId
      this.userDeatilsDrawerVisible = true
    }
  }
}
</script>
<style scoped lang="scss">
.associate-block {

  .associate-list {
    width: 100%;
    overflow: auto;
    > .block-item {
      float: left;
    }
  }
  .block-item {
    padding: 0px 5px;
  }
  .room-profile {
    border-right: 2px solid rgb(115, 133, 157);
  }
}
</style>

