<template>
  <div class="app-container">
    <div class="filter-container">
      <div class="filter-item">
        <div><account-input v-model="listQuery.userId" placeholder="用户id" type="USER" :sys-origin="listQuery.sysOrigin" /></div>
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>

    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="用户" align="center" min-width="100">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column prop="balance" label="余额" align="center" min-width="100" />
      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
        width="200"
      >
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
    <user-deatils-drawer
      v-if="userDeatilsDrawer"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawer=false"
    />
  </div>
</template>

<script>
import { pageUserDiamondBalance } from '@/api/app-user'
import { mapGetters } from 'vuex'
import Pagination from '@/components/Pagination'
import { pickerOptions } from '@/constant/el-const'
export default {
  name: 'UserSongCount',
  components: { Pagination },
  data() {
    return {
      list: [],
      total: 0,
      pickerOptions,
      regions: [],
      userDeatilsDrawer: false,
      thatSelectedUserId: '',
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: ''
      },
      listLoading: true
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData(true)
  },
  methods: {
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      pageUserDiamondBalance(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    changeSysOrigin() {
      this.handleSearch()
    },
    handleSearch() {
      this.renderData(true)
    },
    queryUserDetails(userId) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = userId
    }
  }
}
</script>
