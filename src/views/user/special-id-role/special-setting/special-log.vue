<template>
  <div class="special-log">
    <el-drawer
      title="账号日志"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      size="450px"
      :modal-append-to-body="false"
    >
      <div v-loading="listLoading" class="special-log-content">
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in list"
            :key="index"
            placement="top"
            :timestamp="item.createTime"
          >
            <div slot="dot">
              <el-tooltip class="item" effect="dark">
                <div slot="content">
                  <div>用户ID:{{ item.userBaseInfo.id }}</div>
                  <div>用户昵称:{{ item.userBaseInfo.userNickname }}</div>
                </div>
                <el-image
                  style="width: 30px; height: 30px; border-radius: 100%;position: absolute;left: -10px;z-index: 9;"
                  :src="item.userBaseInfo.userAvatar"
                  fit="fill"
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline" />
                  </div>
                </el-image>
              </el-tooltip>

            </div>
            {{ item.optUserNickname }} ,{{ item.eventDesc }}
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-drawer>
  </div>
</template>

<script>

import { listLatestLog } from '@/api/user-special-id'
export default {
  name: 'SpecialLog',
  props: {
    userId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      list: [],
      listLoading: false
    }
  },
  watch: {
    row: {
      handler(newVal) {
        this.renderData()
      },
      immediate: true
    }
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      listLatestLog(that.userId).then(res => {
        const { body } = res
        that.list = body || []
        that.listLoading = false
      }).catch(er => {
        that.listLoading = false
        console.error(er)
      })
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
