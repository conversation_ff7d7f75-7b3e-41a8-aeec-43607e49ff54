<template>
  <div class="user-special-id-form-edit">
    <el-drawer
      :title="textOptTitle"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >

      <div class="drawer-form">
        <el-form ref="form" :model="form" :rules="formRules" label-width="100px">
          <el-form-item label="系统" prop="sysOrigin">
            {{ sysOrigin }}
          </el-form-item>
          <el-form-item label="用户" prop="userId">
            <account-input v-model="form.userId" :sys-origin="sysOrigin" class="filter-item" placeholder="用户ID" />
          </el-form-item>
          <el-form-item label="新账号" prop="account">
            <el-input v-model.trim="form.account" placeholder="请输入新账号" class="input-with-select" maxlength="10" />
          </el-form-item>
          <el-form-item prop="expiredDays">
            <el-tooltip slot="label" class="item" effect="dark" placement="top-start">
              <div slot="content">
                <div>0.代表永久, 其他.有效范围1~3650; 更新方式:覆盖</div>
              </div>
              <i class="el-icon-question">有效天</i>
            </el-tooltip>
            <el-input v-model.trim="form.expiredDays" v-number min="0" max="3650" placeholder="请输入有效天数" class="input-with-select" maxlength="4" />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model.trim="form.remark" type="textarea" resize="none" :rows="3" placeholder="请输入备注" maxlength="100" show-word-limit />
          </el-form-item>
          <el-form-item label="自定义属性">
            <el-button type="text" @click="clickCustomizeField()">点击查看/设置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="drawer-footer">
        <el-button @click="handleClose()">取消</el-button>
        <el-button type="primary" :loading="submitLoading" :disabled="searchDisabled" @click="submitForm()">保存</el-button>
      </div>
    </el-drawer>

    <customize-field-dialog
      v-if="customizeFieldDialogVisable"
      :customize-field="form.customizeField"
      @close="customizeFieldDialogVisable=false"
      @submit="customizeFieldDialogSubmit"
    />
  </div>
</template>
<script>
import CustomizeFieldDialog from './customize-field-dialog'
import { deepClone } from '@/utils'
import { mapGetters } from 'vuex'
import { saveOrUpdate } from '@/api/user-special-id'
export default {
  components: { CustomizeFieldDialog },
  props: {
    row: {
      type: Object,
      require: false,
      default: () => {}
    },
    sysOrigin: {
      type: String,
      require: false,
      default: ''
    }
  },
  data() {
    const commonRules = [
      { required: true, message: '必填字段', trigger: 'blur' }
    ]

    return {
      customizeFieldDialogVisable: false,
      searchDisabled: false,
      userInfo: {},
      submitLoading: false,
      form: {
        id: '',
        sysOrigin: '',
        userId: '',
        account: '',
        expiredDays: '',
        customizeField: {},
        remark: ''
      },
      formRules: {
        userId: commonRules,
        account: commonRules,
        expiredDays: { required: true, message: '有效值0~3650', trigger: 'blur' }
      },
      sourceTypeList: [],
      selectType: {},
      isLoadSourceTypeList: false,
      listTypeLoading: false,
      badgeRestaurants: [{ 'value': '0', 'label': '永久' }]
    }
  },
  computed: {
    textOptTitle() {
      return this.row && this.row.id ? '修改' : '添加'
    },
    isUpdate() {
      return this.row != null && this.row.userSpecialId != null
    },
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    row: {
      handler(newVal) {
        if (!newVal) {
          return
        }
        this.form = deepClone(newVal.userSpecialId)
      },
      immediate: true
    }
  },
  methods: {
    clickCustomizeField() {
      this.customizeFieldDialogVisable = true
    },
    customizeFieldDialogSubmit(fieldObj) {
      this.customizeFieldDialogVisable = false
      this.form.customizeField = fieldObj
    },
    handleClose() {
      this.$emit('close')
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return
        }
        that.submitLoading = true
        if (!that.form.sysOrigin) {
          that.form.sysOrigin = that.sysOrigin
        }
        saveOrUpdate(that.form).then(res => {
          that.$message.success('操作成功')
          that.submitLoading = false
          that.$emit('success')
        }).catch(er => {
          that.submitLoading = false
          that.$emit('fail')
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">
.props-activity-reward-config-edit {
  .form-edit {
    max-height: 600px;
    overflow: auto;
    .dr-content {
      padding: 5px 0px 5px 20px;
    }
    .sort {
      border-radius: 50%;
      width: 30px;
      height: 30px;
      background: #f7f6f5;
      margin: auto;
      text-align: center;
      line-height: 29px;
      font-weight: bold;
    }
    .del {
      font-size: 30px;
      color: #F56C6C;
      cursor: pointer;
    }
    .save {
      font-size: 30px;
      color: #409EFF;
      cursor: pointer;
    }
  }
}
.my-autocomplete {
  li {
    line-height: normal;
    padding: 7px;

    .name {
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .addr {
      font-size: 12px;
      color: #b4b4b4;
    }

    .highlighted .addr {
      color: #ddd;
    }
  }
}
</style>
