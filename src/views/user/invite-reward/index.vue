<template>
  <div class="app-container">
    <div class="filter-container">
      <div class="filter-item">
        <account-input v-model="listQuery.userId" :sys-origin="listQuery.sysOrigin" placeholder="用户ID" />
      </div>
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="昵称" align="center">
        <template slot-scope="scope">
          <el-link v-if="scope.row.userBaseInfo && scope.row.userBaseInfo.userNickname" @click="queryUserDetails(scope.row.userBaseInfo.id)">
            {{ scope.row.userBaseInfo.userNickname }}
          </el-link>
        </template>
      </el-table-column>
      <!-- <el-table-column label="类型" align="center">
        <template slot-scope="scope">
          <div>
            <span v-if="scope.row.type === 0">收入</span>
            <span v-if="scope.row.type === 1">支出</span>
          </div>
        </template>
      </el-table-column> -->
      <el-table-column label="糖果数" align="center">
        <template slot-scope="scope">
          <div>
            <span class="font-danger">+{{ scope.row.quantity }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="origin" label="来源" align="center" />
      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          <div>
            <el-tag v-if="scope.row.flagReceive === true" type="success">已领取</el-tag>
            <el-tag v-if="scope.row.flagReceive === false" type="danger">未领取</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="200" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <user-deatils-drawer
      v-if="userDeatilsDrawerVisible"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawerVisible=false"
    />
  </div>
</template>

<script>

import { getUserInviteRewardRecord } from '@/api/invite-reward'
import { inviteRewardTypes } from '@/constant/type'
import Pagination from '@/components/Pagination'
import { pickerOptions } from '@/constant/el-const'

export default {
  name: 'InviteRewardRecord',
  components: { Pagination },
  data() {
    return {
      searchDisabled: false,
      pickerOptions,
      userDeatilsDrawerVisible: false,
      thatSelectedUserId: '',
      inviteRewardTypes,
      list: [],
      total: 0,
      rangeDate: [],
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: '',
        startTime: '',
        endTime: ''
      },
      listLoading: true,
      clickUserId: ''
    }
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    }
  },
  created() {
    this.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      getUserInviteRewardRecord(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    queryUserDetails(id) {
      this.userDeatilsDrawerVisible = true
      this.thatSelectedUserId = id
    }
  }
}
</script>
