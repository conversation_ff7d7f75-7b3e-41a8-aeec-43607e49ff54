<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane
        v-for="item in tables"
        :key="item.name"
        :label="item.title"
        :name="item.component"
      />
      <component :is="activeName" />
    </el-tabs>
  </div>
</template>
<script>
import IntegralStreamTimelyPreview from "./timely";
import IntegralStreamHistoryTimelyPreview from "./history";
export default {
  name: "IntegralStreamTimelyPreviewTabs",
  components: {
    IntegralStreamTimelyPreview,
    IntegralStreamHistoryTimelyPreview
  },
  data() {
    return {
      activeName: "IntegralStreamTimelyPreview",
      tables: [
        {
          title: "积分流水",
          component: "IntegralStreamTimelyPreview"
        } /* ,
        {
          title: '历史积分流水',
          component: 'IntegralStreamHistoryTimelyPreview'
        } */
      ]
    };
  }
};
</script>
