<!--积分记录表格-->
<template>
  <div class="app-container-timely">
    <div class="filter-containers">
      <div class="filter-item">
        <account-input
          v-model="listQuery.userId"
          :sys-origin="listQuery.sysOrigin"
          placeholder="用户ID"
        />
      </div>
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          :picker-options="pickerOptions"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </div>
      <el-select
        v-model="listQuery.origin"
        placeholder="积分来源"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option
          v-for="item in videoOrigin"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-button
        :loading="searchLoading"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-alert title="注意" type="warning" :closable="false">
      数据只会保留30天；
    </el-alert>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="昵称" align="center">
        <template slot-scope="scope">
          <el-link
            v-if="scope.row.userBaseInfo && scope.row.userBaseInfo.userNickname"
            @click="queryUserDetails(scope.row.userBaseInfo.id)"
          >
            {{ scope.row.userBaseInfo.userNickname }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="title" label="来源对象" align="center" />
      <el-table-column prop="originName" label="积分来源" align="center" />
      <el-table-column prop="quantity" label="获得积分" align="center">
        <template slot-scope="scope">
          <div :class="{ 'font-danger': scope.row.type === 0 }">
            {{ scope.row.type === 0 ? "+" : "-" }}{{ scope.row.quantity || 0 }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <user-deatils-drawer
      v-if="userDeatilsDrawerVisible"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawerVisible = false"
    />
  </div>
</template>

<script>
import { pageUserIntegralOriginStream } from '@/api/approval'
import Pagination from '@/components/Pagination'
import { videoOrigin } from '@/constant/user'
import { pickerOptions } from '@/constant/el-const'

export default {
  name: 'IntegralOriginHistoryTable',
  components: { Pagination },
  data() {
    return {
      searchDisabled: false,
      pickerOptions,
      userDeatilsDrawerVisible: false,
      thatSelectedUserId: '',
      list: [],
      userRegisterInfo: {},
      rangeDate: '',
      listQuery: {
        cursor: 1,
        limit: 20,
        startTime: '',
        endTime: '',
        origin: '',
        userId: ''
      },
      videoOrigin,
      total: 0,
      searchLoading: false,
      listLoading: false
    }
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    }
  },
  created() {
    this.renderData()
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      pageUserIntegralOriginStream(that.listQuery)
        .then(res => {
          const { body } = res
          that.total = body.total || 0
          that.list = body.records
          that.searchLoading = that.listLoading = false
        })
        .catch(er => {
          that.searchLoading = that.listLoading = false
        })
    },
    handleSearch() {
      this.searchLoading = true
      this.renderData()
    },
    queryUserDetails(id) {
      this.userDeatilsDrawerVisible = true
      this.thatSelectedUserId = id
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>

<style scoped lang="scss">
.filter-containers {
  padding-bottom: 10px;
  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}
.user_info {
  position: relative;
  top: -20px;
  .info {
    position: absolute;
    top: 20px;
    left: 60px;
    span {
      padding: 0px 10px;
    }
  }
}
</style>
