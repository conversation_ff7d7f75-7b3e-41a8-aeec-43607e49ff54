<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane v-if="buttonPermissions.includes('user:special:table:query:list')" label="靓号管理" name="SpecialSetting" />
      <el-tab-pane v-if="buttonPermissions.includes('user:special:log:table:query:list')" label="靓号操作日志" name="SpecialLogInfo" />
      <component :is="activeName" />
    </el-tabs>
  </div>
</template>
<script>
import SpecialSetting from './special-setting'
import SpecialLogInfo from './special-log-info'
import { mapGetters } from 'vuex'
export default {
  name: 'GiftConfig',
  components: { SpecialSetting, SpecialLogInfo },
  computed: {
    ...mapGetters([
      'buttonPermissions'
    ])
  },
  data() {
    return {
      activeName: 'SpecialSetting',
      tables: [
        {
          title: '靓号管理',
          component: 'SpecialSetting'
        },
        {
          title: '靓号操作日志',
          component: 'SpecialLogInfo'
        }
      ]
    }
  }
}
</script>
