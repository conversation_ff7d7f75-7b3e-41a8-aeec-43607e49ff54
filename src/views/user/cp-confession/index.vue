<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <account-input v-model="listQuery.userId" :sys-origin="listQuery.sysOrigin" placeholder="用户ID" />
      </div>
      <el-select
        v-model="listQuery.status"
        placeholder="状态"
        style="width:120px;"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="待审核" :value="'PENDING'" />
        <el-option label="已通过" :value="'PASS'" />
        <el-option label="不通过" :value="'NOT_PASS'" />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
      @cell-mouse-enter="handleMouseEnter"
    >
      <el-table-column prop="sysOrigin" label="系统" align="center" width="100">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column label="用户" align="center" min-width="100">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column label="告白对象" align="center" min-width="100">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.confessionUserProfile" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column prop="content" label="内容" align="center" min-width="100" />
      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          <div>
            <el-tag v-if="scope.row.status === 'PENDING'">待审核</el-tag>
            <el-tag v-if="scope.row.status === 'PASS'">已通过</el-tag>
            <el-tag v-else-if="scope.row.status === 'NOT_PASS'">不通过</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center" width="160">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" label="修改时间" align="center" width="160" />
      <el-table-column fixed="right" label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status === 'PENDING'" type="text" @click="auditSubmit(scope.row, true)">通过</el-button>
          <el-button v-if="scope.row.status === 'PENDING'" type="text" @click="auditSubmit(scope.row, false)">不通过</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
  </div>
</template>

<script>
import { userConfessionRecordPage, auditUserConfession } from '@/api/cp-cabin'
import Pagination from '@/components/Pagination'
import { mapGetters } from 'vuex'

export default {
  name: 'UserCpConfessionTable',
  components: { Pagination },
  data() {
    return {
      thatRow: null,
      rangeDate: [],
      userInfo: {},
      searchDisabled: false,
      thatSelectedUserId: '',
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: '',
        status: '',
        sysOrigin: 'HALAR'
      },
      formData: {
        id: '',
        status: ''
      },
      listLoading: true
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      userConfessionRecordPage(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      }).catch(er => {
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    handleMouseEnter(row) {
      this.thatRow = row
    },
    auditSubmit(row, type) {
      const that = this
      that.formData = {
        id: row.id,
        status: type ? 'PASS' : 'NOT_PASS'
      }
      that.listLoading = true
      auditUserConfession(that.formData).then(res => {
        that.$message.success('操作成功')
        that.listLoading = false
        that.renderData()
      }).catch(er => {
        that.listLoading = false
        that.$message.error('操作失败')
        console.error(er)
      })
    }
  }
}
</script>
