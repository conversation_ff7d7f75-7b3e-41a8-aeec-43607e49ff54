<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="changeSysOrigin"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <div><account-input v-model="listQuery.userId" placeholder="用户id" type="USER" :sys-origin="listQuery.sysOrigin" /></div>
      </div>
      <el-select
        v-model="listQuery.type"
        placeholder="类型"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="收入" :value="false" />
        <el-option label="支出" :value="true" />
      </el-select>
      <div class="filter-item">
        <el-autocomplete
          v-model="listQuery.origin"
          popper-class="my-autocomplete"
          :fetch-suggestions="querySearch"
          placeholder="请输入或选择内容"
          clearable
          @select="handleSelect"
          @clear="handleSelect"
        >
          <i
            slot="suffix"
            class="el-icon-edit el-input__icon"
          />
          <template slot-scope="{ item }">
            <span :label="item.name">{{ item.name }}</span>
            <div :key="item.value" :value="item.value" style="font-size:12px; color:#d8d0d0">{{ item.value }}</div>
          </template>
        </el-autocomplete>
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>

    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="用户" align="center" min-width="100">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column prop="balance" label="钻石余额" align="center" min-width="100" />
      <el-table-column prop="quantity" label="钻石" align="center" min-width="100" />
      <el-table-column prop="origin" label="来源" align="center" min-width="100" />
      <el-table-column prop="originDesc" label="来源描述" align="center" min-width="100" />
      <el-table-column prop="typeDesc" label="类型" align="center" min-width="100" />
      <el-table-column prop="remarks" label="备注" align="center" min-width="100" />
      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
        width="200"
      >
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
    <user-deatils-drawer
      v-if="userDeatilsDrawer"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawer=false"
    />
  </div>
</template>

<script>
import { pageUserDiamondRunWater } from '@/api/app-user'
import { mapGetters } from 'vuex'
import Pagination from '@/components/Pagination'
import { pickerOptions } from '@/constant/el-const'
import { diamondOrigins } from '@/constant/type'
export default {
  name: 'UserSongCount',
  components: { Pagination },
  data() {
    return {
      list: [],
      total: 0,
      pickerOptions,
      diamondOrigins,
      regions: [],
      restaurants: [],
      userDeatilsDrawer: false,
      thatSelectedUserId: '',
      listQuery: {
        cursor: 1,
        limit: 20,
        sysOrigin: 'HALAR',
        userId: '',
        origin: '',
        type: ''
      },
      listLoading: true
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData(true)
  },
  mounted() {
    this.restaurants = this.diamondOrigins
  },
  methods: {
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      pageUserDiamondRunWater(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    changeSysOrigin() {
      this.handleSearch()
    },
    handleSelect(item) {
      this.renderData(true)
    },
    handleSearch() {
      if (this.listQuery.origin !== '') {
        const origins = this.diamondOrigins.filter(this.createFilter(this.listQuery.origin))
        this.listQuery.origin = origins.length > 0 ? origins[0].value : this.listQuery.origin
      }
      this.renderData(true)
    },
    queryUserDetails(userId) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = userId
    },
    querySearch(queryString, cb) {
      var restaurants = diamondOrigins
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
      cb(results)
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) >= 0) || (restaurant.name.toLowerCase().indexOf(queryString.toLowerCase()) >= 0)
      }
    }
  }
}
</script>
