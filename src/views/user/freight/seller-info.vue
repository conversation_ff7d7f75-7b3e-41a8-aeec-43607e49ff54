<template>
  <el-dialog
    title="授权卖家数量"
    :visible="true"
    :before-close="handleClose"
    :close-on-click-modal="false"
    width="80%"
  >
    <div class="form-edit" style="margin-top: -30px;">
      <el-form ref="form" :model="form" :rules="formRules" label-width="145px">
        <div class="form-row" style="margin-top: 8px;">
          <el-form-item label="最多授权卖家数量" prop="sellerQuantity">
            <el-input v-model.trim="form.sellerQuantity" v-number placeholder="请输入人数" class="input-with-select" maxlength="10" style="width: 150px;" />
          </el-form-item>
          <div class="button-container">
            <el-button type="primary" :loading="submitLoading" @click="submitForm()">保存</el-button>
          </div>
        </div>
      </el-form>
    </div>

    <div class="content">
      <el-table
        v-loading="listLoading"
        :data="list"
        :before-close="handleClose"
        element-loading-text="Loading"
        fit
        highlight-current-row
        max-height="350px"
      >
        <el-table-column prop="sysOrigin" label="系统" align="center">
          <template slot-scope="scope">
            <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
          </template>
        </el-table-column>
        <el-table-column label="用户" align="center" min-width="100">
          <template slot-scope="scope">
            <user-table-exhibit :user-profile="scope.row.userBaseInfo" :query-details="true" />
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center" width="200">
          <template slot-scope="scope">
            {{ scope.row.createTime | dateFormat }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" align="center" width="140">
          <template slot-scope="scope">
            <el-button type="text" @click="showFreightSellerRuningWater(scope.row,true)">流水</el-button>
            <el-button type="text" @click="deleteFreightSeller(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="listQuery.cursor"
        :limit.sync="listQuery.limit"
        @pagination="renderData"
      />
      <freight-seller-running-water
        v-if="freightSellerRunningWaterVisible"
        :row="freightSeller"
        @close="freightSellerRunningWaterVisible=false"
      />
    </div>
  </el-dialog>
</template>

<script>
import { pageFreightSeller, updateSellerQuantity, removeFreightSeller } from '@/api/app-user'
import Pagination from '@/components/Pagination'
import FreightSellerRunningWater from './freight-seller-running-water.vue'
export default {
  name: 'SellerInfo',
  components: { Pagination, FreightSellerRunningWater },
  props: {
    row: {
      type: Object,
      required: true
    }
  },
  data() {
    const commonRules = [
      { required: true, message: '必填字段', trigger: 'blur' }
    ]
    return {
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        freightId: ''
      },
      listLoading: false,
      submitLoading: false,
      freightSellerRunningWaterVisible: false,
      freightSeller: {},
      form: {
        id: '',
        sellerQuantity: '',
        sysOrigin: 'HALAR'
      },
      formRules: {
        sellerQuantity: commonRules
      }
    }
  },
  watch: {
    row: {
      handler(newVal) {
        this.listQuery.freightId = newVal.id
        this.listQuery.sysOrigin = newVal.sysOrigin
        this.form = Object.assign({}, newVal)
        this.renderData()
      },
      immediate: true
    }
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      pageFreightSeller(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      }).catch(er => {
        that.listLoading = false
        console.error(er)
      })
    },
    handleClose() {
      this.$emit('close')
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return
        }
        that.submitLoading = true
        updateSellerQuantity(that.form).then(res => {
          that.$opsMessage.success()
          that.submitLoading = false
          that.$emit('success')
        }).catch(er => {
          that.submitLoading = false
          that.$emit('fail')
        })
      })
    },
    showFreightSellerRuningWater(row, type) {
      const that = this
      that.freightSellerRunningWaterVisible = true
      this.freightSeller = row
    },
    deleteFreightSeller(id) {
      const that = this
      that.$confirm('此操作将删除该卖家, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        removeFreightSeller(id).then(res => {
          that.renderData(true)
        })
      }).catch(() => {
        that.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">
.form-row {
  display: flex;
  align-items: center;
}

.button-container {
  margin-left: 30px;
      margin-bottom: 22px;
}
</style>
