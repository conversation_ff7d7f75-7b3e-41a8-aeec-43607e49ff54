<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;">
            <sys-origin-icon
              :icon="item.value"
              :desc="item.value"
            /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>

      <el-select
        v-model="listQuery.display"
        placeholder="显示状态"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="显示" :value="false" />
        <el-option label="不显示" :value="true" />
      </el-select>
      <el-select
        v-model="listQuery.dealer"
        placeholder="是否经销商"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="否" :value="false" />
        <el-option label="是" :value="true" />
      </el-select>

      <el-select
        v-model="listQuery.close"
        placeholder="状态"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="正常" :value="false" />
        <el-option label="关闭" :value="true" />
      </el-select>
      <!-- <el-input
        v-model.trim="listQuery.id"
        v-number
        placeholder="ID"
        style="width: 200px;"
        class="filter-item"
      /> -->
      <div class="filter-item">
        <account-input
          v-model="listQuery.userId"
          :sys-origin="listQuery.sysOrigin"
          placeholder="用户ID"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        添加
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
      @cell-mouse-enter="handleMouseEnter"
    >
      <el-table-column prop="sysOrigin" label="系统" align="center" width="80">
        <template slot-scope="scope">
          <sys-origin-icon
            :icon="scope.row.sysOrigin"
            :desc="scope.row.sysOrigin"
          />
        </template>
      </el-table-column>
      <el-table-column label="用户" align="center" min-width="200">
        <template slot-scope="scope">
          <user-table-exhibit
            :user-profile="scope.row.userBaseInfo"
            :query-details="true"
          />
        </template>
      </el-table-column>
      <el-table-column
        prop="earnPoints"
        label="获得总额"
        align="center"
        min-width="100"
      />
      <el-table-column
        prop="consumptionPoints"
        label="出货总"
        align="center"
        min-width="100"
      />
      <el-table-column
        prop="balance"
        label="余额"
        align="center"
        min-width="100"
      />
      <el-table-column
        width="200"
        label="显示/不显示"
        align="center"
        min-width="100"
      >
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.display"
            :active-value="false"
            :inactive-value="true"
            :disabled="scope.row.close"
            @change="showSwitchChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column
        width="200"
        label="正常/关闭"
        align="center"
        min-width="100"
      >
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.close"
            :active-value="false"
            :inactive-value="true"
            @change="handleSwitchChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column width="200" label="经销商" align="center" min-width="100">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.dealer"
            :active-value="true"
            :inactive-value="false"
            @change="switchChangeDealer(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="sellerQuantity" width="200" label="授权卖家数量" align="center" min-width="100">
        <template slot-scope="scope">
          <el-button type="text" @click="showSellerInfo(scope.row)"> {{ scope.row.sellerQuantity }}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="realSellerQuantity" width="200" label="已授权卖家数量" align="center" min-width="100">
        <template slot-scope="scope">
          {{ scope.row.realSellerQuantity }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
        width="200"
      >
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column
        prop="updateTime"
        label="修改时间"
        align="center"
        width="200"
      >
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="140">
        <template slot-scope="scope">
          <!-- <el-button type="text" @click="clickChip(scope.row)">发货</el-button> -->
          <el-button
            type="text"
            @click="showGoldInputBox(scope.row, true)"
          >发货</el-button>
          <el-button
            type="text"
            @click="showGoldInputBox(scope.row, false)"
          >扣款</el-button>
          <el-button
            type="text"
            @click="runningWaterVisible = true"
          >流水</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <form-edit
      v-if="formEditVisable"
      :row="thatRow"
      :sys-origin="listQuery.sysOrigin"
      @close="formEditClose"
      @success="formEditSuccess"
    />

    <running-water
      v-if="runningWaterVisible"
      :row="thatRow"
      @close="runningWaterVisible = false"
    />

    <seller-info
      v-if="showSellerInfoVisable"
      :row="sellerInfoEditParam"
      @close="sellerInfoClose"
    />

    <div class="gold-input-box">
      <el-dialog
        :title="type ? '发货' : '扣款'"
        :visible.sync="goldInputBoxVisible"
        width="400px"
      >
        <el-form
          ref="form"
          :rules="formRules"
          :model="formData"
          label-position="left"
          label-width="80px"
          style="width: 300px; margin-left:50px;"
        >
          <el-form-item label="金币" prop="earnPoints">
            <el-input
              v-model.trim="formData.earnPoints"
              :placeholder="'请输入' + (type ? '发货' : '扣款') + '金币数量'"
            />
          </el-form-item>
          <el-form-item v-if="type==true" label="金额" prop="amount">
            <el-input
              v-model.trim="formData.amount"
              placeholder="请输入金额"
            />
          </el-form-item>
          <el-form-item v-if="type==true" label="充值类型" prop="rechargeType">
            <el-select
              v-model.trim="formData.rechargeType"
              placeholder="充值类型"
              clearable
              style="width:100%;"
              class="filter-item"
            >
              <el-option label="CLIPSPAY-进货" :value="'CLIPSPAY-进货'" />
              <el-option label="Payoneer-进货" :value="'Payoneer-进货'" />
              <el-option label="Paypal-进货" :value="'Paypal-进货'" />
              <el-option label="工资-进货" :value="'工资-进货'" />
              <el-option label="银行卡兑换" :value="'银行卡兑换'" />
              <el-option label="USDT-进货" :value="'USDT-进货'" />
              <el-option label="金币补偿" :value="'金币补偿'" />
            </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model.trim="formData.remark" placeholder="请输入备注" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="hideGoldInputBox()">
            取消
          </el-button>
          <el-button
            v-loading="listLoading"
            type="primary"
            @click="goldSubmit()"
          >
            提交
          </el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  pageFreight,
  shipFreight,
  deductionFreight,
  switchStatusFreight,
  showStatusFreight,
  switchStatusDealer
} from '@/api/app-user'
import Pagination from '@/components/Pagination'
import FormEdit from './form-edit'
import RunningWater from './running-water'
import SellerInfo from './seller-info'
import { mapGetters } from 'vuex'

export default {
  name: 'UserPropsTable',
  components: { Pagination, FormEdit, RunningWater, SellerInfo },
  data() {
    const commonRules = [
      { required: true, message: '必填字段', trigger: 'blur' }
    ]

    return {
      runningWaterVisible: false,
      showSellerInfoVisable: false,
      sellerInfoEditParam: {},
      thatRow: null,
      userInfo: {},
      searchDisabled: false,
      goldInputBoxVisible: false,
      // true：发货  false：扣除
      type: false,
      thatSelectedUserId: '',
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        close: false,
        dealer: '',
        startTime: '',
        endTime: ''
      },
      formData: {
        userId: '',
        earnPoints: '',
        sysOrigin: '',
        amount: '',
        rechargeType: '',
        remark: ''
      },
      formRules: {
        userId: commonRules,
        earnPoints: {
          required: true,
          message: '必填字段不可为空',
          trigger: 'blur'
        },
        amount: commonRules,
        rechargeType: commonRules,
        sysOrigin: commonRules
      },
      listLoading: true,
      formEditVisable: false
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    handleImageUrl(row) {
      return row.propsCover ? row.propsCover : ''
    },
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      pageFreight(that.listQuery)
        .then(res => {
          const { body } = res
          that.total = body.total || 0
          that.list = body.records
          that.listLoading = false
        })
        .catch(er => {
          that.listLoading = false
        })
    },
    handleSearch() {
      this.renderData(true)
    },
    handleCreate() {
      this.thatRow = null
      this.formEditVisable = true
    },
    handleClose() {
      this.$emit('close')
    },
    handleMouseEnter(row) {
      this.thatRow = row
    },
    formEditClose() {
      this.formEditVisable = false
    },
    formEditSuccess() {
      this.formEditVisable = false
      this.renderData()
    },
    handleSwitchChange(row) {
      switchStatusFreight(row.id, row.close)
        .then(res => {})
        .catch(er => {
          row.close = !row.close
        })
    },
    showSwitchChange(row) {
      showStatusFreight(row.id, row.display)
        .then(res => {})
        .catch(er => {
          row.display = !row.display
        })
    },
    switchChangeDealer(row) {
      switchStatusDealer(row.id, row.dealer)
        .then(res => {})
        .catch(er => {
          row.dealer = !row.dealer
        })
    },
    showSellerInfo(row) {
      const that = this
      console.log('---->')
      that.showSellerInfoVisable = true
      this.sellerInfoEditParam = row
    },
    sellerInfoClose() {
      this.showSellerInfoVisable = false
      this.renderData()
    },
    showGoldInputBox(row, type) {
      const that = this
      that.type = type

      that.formData = {
        userId: row.userId,
        earnPoints: '',
        sysOrigin: row.sysOrigin,
        remark: ''
      }
      that.goldInputBoxVisible = true
    },
    hideGoldInputBox() {
      const that = this
      that.goldInputBoxVisible = false
    },
    goldSubmit() {
      const that = this

      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return
        }
        that.listLoading = true
        if (that.type) {
          shipFreight(that.formData)
            .then(res => {
              that.$opsMessage.success()
              that.goldInputBoxVisible = false
              that.listLoading = false
              that.renderData()
            })
            .catch(er => {
              that.listLoading = false
              that.$opsMessage.success()
              console.error(er)
            })
          return
        }

        deductionFreight(that.formData)
          .then(res => {
            that.$opsMessage.success()
            that.goldInputBoxVisible = false
            that.listLoading = false
            that.renderData()
          })
          .catch(er => {
            that.listLoading = false
            that.$opsMessage.success()
            console.error(er)
          })
      })
    }
  }
}
</script>
