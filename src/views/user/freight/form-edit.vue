<template>
  <div class="user-freight-form-edit">
    <el-dialog
      :title="textOptTitle"
      :visible="true"
      :before-close="handleClose"
      :close-on-click-modal="false"
      width="550px"
      top="20px"
    >
      <div class="form-edit">
        <el-alert
          style="margin-bottom: 10px;"
          title="不存在则创建用户，否则金币将会被追加到账户"
          type="warning"
          :closable="false"
        />
        <el-form ref="form" :model="form" :rules="formRules" label-width="110px" style="margin-right:50px;">
          <el-form-item label="系统" prop="sysOrigin">
            {{ sysOrigin }}
          </el-form-item>
          <el-form-item label="用户id" prop="userId">

            <account-input v-model="form.userId" :sys-origin="sysOrigin" placeholder="用户ID" />
          </el-form-item>
          <el-form-item label="金币" prop="earnPoints">
            <el-input v-model.trim="form.earnPoints" v-number placeholder="请输入发送金币" class="input-with-select" maxlength="10" />
          </el-form-item>
          <el-form-item label="金额" prop="amount">
            <el-input
              v-model.trim="form.amount"
              placeholder="请输入金额"
            />
          </el-form-item>
          <el-form-item label="充值类型" prop="rechargeType">
            <el-select
              v-model.trim="form.rechargeType"
              placeholder="充值类型"
              clearable
              style="width:100%;"
              class="filter-item"
            >
              <el-option label="CLIPSPAY-进货" :value="'CLIPSPAY-进货'" />
              <el-option label="Payoneer-进货" :value="'Payoneer-进货'" />
              <el-option label="Paypal-进货" :value="'Paypal-进货'" />
              <el-option label="工资-进货" :value="'工资-进货'" />
              <el-option label="银行卡兑换" :value="'银行卡兑换'" />
              <el-option label="USDT-进货" :value="'USDT-进货'" />
              <el-option label="金币补偿" :value="'金币补偿'" />
            </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model.trim="form.remark" placeholder="备注" class="input-with-select" maxlength="400" />
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer">
        <el-button @click="handleClose()">取消</el-button>
        <el-button type="primary" :loading="submitLoading" :disabled="searchDisabled" @click="submitForm()">保存</el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import { shipFreight } from '@/api/app-user'
import { deepClone } from '@/utils'
import { mapGetters } from 'vuex'
export default {
  props: {
    row: {
      type: Object,
      require: false,
      default: () => {}
    },
    sysOrigin: {
      type: String,
      require: false,
      default: ''
    }
  },
  data() {
    const commonRules = [
      { required: true, message: '必填字段', trigger: 'blur' }
    ]

    return {
      searchDisabled: false,
      userInfo: {},
      submitLoading: false,
      form: {
        id: '',
        userId: '',
        earnPoints: '',
        amount: '',
        rechargeType: '',
        sysOrigin: ''
      },
      formRules: {
        name: commonRules,
        userId: commonRules,
        amount: commonRules,
        rechargeType: commonRules,
        // earnPoints: { required: true, message: '必填字段，且必须大于0', validator: (rule, value, callback) => {
        //   if (!value || value <= 0) {
        //     callback(new Error())
        //     return
        //   }
        //   callback()
        // }, trigger: 'blur' },
        earnPoints: { required: true, message: '必填字段不可为空', trigger: 'blur' }
      },
      sourceTypeList: [],
      selectType: {},
      isLoadSourceTypeList: false,
      listTypeLoading: false,
      badgeRestaurants: [{ 'value': '0', 'label': '永久' }]
    }
  },
  computed: {
    textOptTitle() {
      return this.row && this.row.id ? '修改' : '添加'
    },
    isUpdate() {
      return this.row && this.row.id
    },
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    row: {
      handler(newVal) {
        if (!newVal) {
          return
        }
        const newForm = deepClone(newVal)
        newForm.tmpConfigList = []
        this.form = newForm
      },
      immediate: true
    }
  },
  methods: {
    submitItem(index) {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return
        }
        const item = that.form.tmpConfigList[0]
        const itemExt = item.ext
        that.form.rewardConfigList.push({
          type: item.type,
          content: item.content,
          detailType: item.clickType,
          quantity: item.quantity,
          cover: itemExt.selectVal.cover,
          sourceUrl: itemExt.selectVal.sourceUrl
        })
        that.form.tmpConfigList.splice(index, 1)
      })
    },
    handleClose() {
      this.$emit('close')
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return
        }
        that.submitLoading = true
        if (!that.form.sysOrigin) {
          that.form.sysOrigin = that.sysOrigin
        }
        shipFreight(that.form).then(res => {
          that.$opsMessage.success()
          that.submitLoading = false
          that.$emit('success')
        }).catch(er => {
          that.submitLoading = false
          that.$emit('fail')
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">
.props-activity-reward-config-edit {
  .form-edit {
    max-height: 600px;
    overflow: auto;
    .dr-content {
      padding: 5px 0px 5px 20px;
    }
    .sort {
      border-radius: 50%;
      width: 30px;
      height: 30px;
      background: #f7f6f5;
      margin: auto;
      text-align: center;
      line-height: 29px;
      font-weight: bold;
    }
    .del {
      font-size: 30px;
      color: #F56C6C;
      cursor: pointer;
    }
    .save {
      font-size: 30px;
      color: #409EFF;
      cursor: pointer;
    }
  }
}
.my-autocomplete {
  li {
    line-height: normal;
    padding: 7px;

    .name {
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .addr {
      font-size: 12px;
      color: #b4b4b4;
    }

    .highlighted .addr {
      color: #ddd;
    }
  }
}
</style>
