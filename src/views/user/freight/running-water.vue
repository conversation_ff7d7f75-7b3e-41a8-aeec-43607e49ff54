<template>
  <el-dialog
    title="货运流水"
    :visible="true"
    :before-close="handleClose"
    :close-on-click-modal="false"
    width="80%"
    top="20px"
  >
    <div class="content">
      <el-table
        v-loading="listLoading"
        :data="list"
        :before-close="handleClose"
        element-loading-text="Loading"
        fit
        highlight-current-row
        max-height="350px"
      >
        <el-table-column prop="sysOrigin" label="系统" align="center">
          <template slot-scope="scope">
            <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
          </template>
        </el-table-column>
        <el-table-column label="接收人" align="center" min-width="200">
          <template slot-scope="scope">
            <user-table-exhibit :user-profile="scope.row.acceptUser" :query-details="true" :tag-name="scope.row.operationUserNickname" />
          </template>
        </el-table-column>
        <el-table-column label="类型" align="center">
          <template slot-scope="scope">
            <div>
              <span v-if="scope.row.type === 0">收入</span>
              <span v-if="scope.row.type === 1">支出</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="数量" align="center">
          <template slot-scope="scope">
            <div>
              <span v-if="scope.row.type === 0" class="font-danger">+{{ scope.row.quantity }}</span>
              <span v-if="scope.row.type === 1">-{{ scope.row.quantity }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="balance" label="余额" align="center" />
        <el-table-column prop="originName" label="来源" align="center" />
        <el-table-column prop="remark" label="备注" align="center" min-width="200" />
        <el-table-column prop="createTime" label="创建时间" align="center" width="200">
          <template slot-scope="scope">
            {{ scope.row.createTime | dateFormat }}
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="listQuery.cursor"
        :limit.sync="listQuery.limit"
        @pagination="renderData"
      />
    </div>
  </el-dialog>
</template>

<script>
import { pageRunningWater } from '@/api/app-user'
import Pagination from '@/components/Pagination'
export default {
  name: 'GoldRunningWaterDrawer',
  components: { Pagination },
  props: {
    row: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: ''
      },
      listLoading: false
    }
  },
  watch: {
    row: {
      handler(newVal) {
        this.listQuery.userId = newVal.userId
        this.listQuery.sysOrigin = newVal.sysOrigin
        this.renderData()
      },
      immediate: true
    }
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      pageRunningWater(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      }).catch(er => {
        that.listLoading = false
        console.error(er)
      })
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
