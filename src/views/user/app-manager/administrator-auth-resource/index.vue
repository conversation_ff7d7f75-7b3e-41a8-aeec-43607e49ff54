<template>
  <div class="app-container-source">

    <div class="filter-container">
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        新增权限
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="resourceName" label="名称" align="center" />
      <el-table-column prop="auth" label="Key" align="center" />
      <el-table-column prop="groupName" label="分组" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.groupName === 'USER'">用户</span>
          <span v-if="scope.row.groupName === 'ROOM'">房间</span>
          <span v-if="scope.row.groupName === 'OTHER'">其它</span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="注册时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center">
        <template slot-scope="scope">
          <div>
            <el-button type="text" @click.native="handleUpdate(scope.row)">修改</el-button>
            <el-button type="text" @click.native="handlDel(scope.row.id)">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :title="textOptTitle"
      :visible.sync="formVisible"
      :before-close="handleClose"
      :close-on-click-modal="false"
      width="400px"
    >
      <div v-loading="submitLoading">
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-form-item label="名称" prop="resourceName">
            <el-input v-model.trim="form.resourceName" type="text" />
          </el-form-item>
          <el-form-item label="Key" prop="auth">
            <el-input v-model.trim="form.auth" type="text" />
          </el-form-item>
          <el-form-item label="分组" prop="groupName">
            <el-select
              v-model="form.groupName"
              placeholder="分组"
              clearable
              style="width:100%;"
              class="filter-item"
            >
              <el-option label="用户" :value="'USER'" />
              <el-option label="房间" :value="'ROOM'" />
              <el-option label="其它" :value="'OTHER'" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm()">保存</el-button>
            <el-button @click="handleClose()">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAdministratorAuthTable, deleteAdministratorAuth, addAdministratorAuth, updateAdministratorAuth } from '@/api/app-user'

function getFormData() {
  return {
    id: '',
    resourceName: '',
    auth: '',
    groupName: ''
  }
}

export default {
  name: 'AdministratorAuthTable',
  data() {
    return {
      thisRow: {},
      level: 0,
      formVisible: false,
      textOptTitle: '',
      form: getFormData(),
      submitLoading: false,
      list: [],
      total: 0,
      rules: {
        resourceName: [
          { required: true, message: '请填写名称', trigger: 'blur' }
        ],
        auth: [
          { required: true, message: '请填写Key', trigger: 'blur' }
        ],
        groupName: [
          { required: true, message: '请选择分组', trigger: 'blur' }
        ]
      },
      listLoading: true,
      searchLoading: false
    }
  },
  created() {
    this.renderData()
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      getAdministratorAuthTable().then(res => {
        const { body } = res
        that.list = body
        that.searchLoading = that.listLoading = false
      }).catch(er => {
        that.searchLoading = that.listLoading = false
      })
    },
    handleCreate() {
      this.textOptTitle = '新建'
      this.formVisible = true
    },
    handleUpdate(row) {
      this.textOptTitle = '修改'
      this.formVisible = true
      this.form = Object.assign(this.form, row)
    },
    handleClose() {
      // 去除校验
      this.$refs.form.clearValidate()
      this.formVisible = false
      this.resetForm()
    },
    resetForm() {
      this.form = getFormData()
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (valid) {
          that.submitLoading = true
          if (that.form.id) {
            updateAdministratorAuth(that.form).then(res => {
              that.submitLoading = false
              that.formVisible = false
              that.form = getFormData()
              that.renderData()
            }).catch(er => {
              that.submitLoading = false
              that.$emit('fial', er)
            })
            return
          }
          addAdministratorAuth(that.form).then(res => {
            that.submitLoading = false
            that.formVisible = false
            that.form = getFormData()
            that.renderData()
          }).catch(er => {
            that.submitLoading = false
            console.error(er)
            that.$emit('fial', er)
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    },
    handlDel(id) {
      const that = this
      this.$confirm('您确定要删除该权限吗', '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        deleteAdministratorAuth(id).then(res => {
          that.renderData()
          that.$opsMessage.success()
        }).catch(er => {
          that.$opsMessage.success()
        })
      }).catch(() => {})
    }
  }
}
</script>
<style scoped lang="scss">
.this-level {
  padding-bottom: 30px;
  display: flex;
  > div {
    width: 150px;
  }
}

</style>
