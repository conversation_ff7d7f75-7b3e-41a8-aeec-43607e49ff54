<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="item in tables" :key="item.name" :label="item.title" :name="item.component" />
      <component :is="activeName" />
    </el-tabs>
  </div>
</template>
<script>
import Administrator from './administrator'
import AdministratorAuthResource from './administrator-auth-resource'
export default {
  name: 'FamilyConfig',
  components: { Administrator, AdministratorAuthResource },
  data() {
    return {
      activeName: 'Administrator',
      tables: [
        {
          title: '管理员列表',
          component: 'Administrator'
        }, {
          title: '权限管理',
          component: 'AdministratorAuthResource'
        }
      ]
    }
  }
}
</script>
