<template>
  <div :class="'app-container'">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <account-input v-model="listQuery.userId" :sys-origin="listQuery.sysOrigin" placeholder="用户ID" />
      </div>

      <div class="filter-item">
        <el-date-picker
          v-model="listQuery.dateNumber"
          type="month"
          placeholder="选择日期"
          format="yyyy 年 MM 月"
          value-format="yyyyMM"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-alert
      v-if="isCalculateTotalAmount"
      v-loading="isCalculateTotalLoading"
      :title="'金额($): '+ calculateTotalValue"
      type="success"
      effect="dark"
      style="font-weight: bold;padding: 10px;"
      :closable="false"
    />
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="sysOrigin" label="系统" align="center" width="80">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column prop="regionCode" label="区域Code" align="center" min-width="80" />
      <el-table-column label="用户" align="center" min-width="80">
        <template slot-scope="scope">
          <div><user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" /></div>
        </template>
      </el-table-column>
      <el-table-column prop="dateNumber" label="目标所属年月" align="center" min-width="80" />
      <el-table-column prop="maxPolicyTarget" label="已完成最高政策等级目标" align="center" min-width="80" />
      <el-table-column prop="memberTarget" label="用户目标" align="center" min-width="80" />
      <el-table-column prop="surplusTarget" label="可兑换目标" align="center" min-width="80" />
      <el-table-column prop="subscriptionRatio" label="兑换比例(?/1$)" align="center" min-width="80" />
      <el-table-column prop="amountUsd" label="兑换金额$" align="center" min-width="80" />
      <el-table-column prop="createTime" label="创建时间" width="400" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
    </el-table>
    <div v-if="listQuery.lastId" class="load-more">
      <span v-if="notData">已加载全部</span>
      <el-button v-else size="mini" :disabled="loadMoreLoading" :loading="loadMoreLoading" @click="clickLoadMore">加载更多</el-button>
    </div>
  </div>
</template>

<script>
import { pageTeamExchangeTarget, calculateExchangeUsdTotalAmount } from '@/api/app-user-bank-balance'
import { mapGetters } from 'vuex'
export default {
  name: 'ExchangeTargetRecord',
  data() {
    return {
      thatSelectedUserId: '',
      thatSelectedAcceptUserId: '',
      searchDisabled: false,
      isCalculateTotalLoading: false,
      isCalculateTotalAmount: false,
      calculateTotalValue: '0',
      listQuery: {
        userId: '',
        sysOrigin: 'MARCIE',
        lastId: '',
        dateNumber: ''
      },
      listLoading: false,
      list: [],
      notData: false,
      loadMoreLoading: false
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isReset) {
      const that = this
      if (!that.listQuery.sysOrigin) {
        return
      }
      if (isReset === true) {
        that.list = []
        that.notData = false
        that.listQuery.lastId = ''
      }
      that.listLoading = true
      pageTeamExchangeTarget(that.listQuery).then(res => {
        that.listLoading = false
        that.loadMoreLoading = false
        const { body } = res
        const list = body || []
        that.notData = list.length <= 0
        if (!that.notData) {
          that.list = that.list.concat(list)
          that.listQuery.lastId = that.list[that.list.length - 1].id
        }
      }).catch(er => {
        that.listLoading = false
        that.loadMoreLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
      this.isShowAlertMoney()
    },
    clickLoadMore() {
      const that = this
      that.loadMoreLoading = true
      that.renderData()
    },
    isShowAlertMoney() {
      const that = this
      that.isCalculateTotalAmount = that.listQuery.sysOrigin !== ''
      // 计算额度
      if (that.isCalculateTotalAmount) {
        that.isCalculateTotalLoading = true
        calculateExchangeUsdTotalAmount(that.listQuery).then(res => {
          that.isCalculateTotalLoading = false
          const { body } = res
          that.calculateTotalValue = body
        }).catch(er => {
          that.isCalculateTotalLoading = false
        })
      }
    }
  }
}
</script>
<style scoped lang="scss">
  .user-css {
    display: flex;
    align-items: center;
    color: #909399;
    font-size: .15rem;
    line-height: .3rem;
    padding: 0.02rem;
    border-radius: 0.04rem;
  }
  .load-more {
    padding: 20px;
    text-align: center;
  }
</style>
