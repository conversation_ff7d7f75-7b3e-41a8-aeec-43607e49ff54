<template>
  <el-dialog
    title="用户银行账户流水"
    :visible="true"
    :before-close="handleClose"
    :close-on-click-modal="false"
    width="80%"
    top="20px"
  >
    <div class="content">
      <el-table
        v-loading="listLoading"
        :data="list"
        :before-close="handleClose"
        element-loading-text="Loading"
        fit
        highlight-current-row
        max-height="350px"
      >
        <el-table-column prop="sysOrigin" label="系统" align="center">
          <template slot-scope="scope">
            <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
          </template>
        </el-table-column>
        <el-table-column label="用户" align="center" min-width="80">
          <template slot-scope="scope">
            <div><user-table-exhibit :size="scope.row.userProfile ? 'mini' : 'small'" :user-profile="scope.row.userProfile" :query-details="true" /></div>
            <div v-if="scope.row.tmpUserProfile" class="attached">
              <user-table-exhibit size="mini" :user-profile="scope.row.tmpUserProfile" :query-details="true" :tag-name="getTransferDescriptionName(scope.row.event)" />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="typeName" label="类型" align="center" width="80" />
        <el-table-column label="数量" align="center" min-width="80">
          <template slot-scope="scope">
            <div>
              <span v-if="scope.row.type === 0" class="font-danger">+{{ scope.row.amount }}</span>
              <span v-if="scope.row.type === 1">-{{ scope.row.amount }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="balance" label="余额" align="center" />
        <el-table-column label="事件" align="center">
          <template slot-scope="scope">
            <div>{{ getEventDesc(scope.row.event) }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" align="center" min-width="200" />
        <el-table-column prop="createTime" label="创建时间" align="center" width="160">
          <template slot-scope="scope">
            {{ scope.row.createTime | dateFormat }}
          </template>
        </el-table-column>
      </el-table>

      <div v-if="listQuery.lastId" class="load-more">
        <span v-if="notData">已加载全部</span>
        <el-button v-else size="mini" :disabled="loadMoreLoading" :loading="loadMoreLoading" @click="clickLoadMore">加载更多</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { pageRunningWater } from '@/api/app-user-bank-balance'
import { userBankWaterEvent } from '@/constant/user'
export default {
  name: 'GoldRunningWaterDrawer',
  props: {
    row: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      userBankWaterEvent,
      listQuery: {
        limit: 20,
        userId: '',
        sysOrigin: '',
        lastId: ''
      },
      listLoading: false,
      list: [],
      notData: false,
      loadMoreLoading: false
    }
  },
  watch: {
    row: {
      handler(newVal) {
        this.listQuery.userId = newVal.id
        this.listQuery.sysOrigin = newVal.sysOrigin
        this.renderData()
      },
      immediate: true
    }
  },
  methods: {
    renderData(isReset) {
      const that = this
      if (!that.listQuery.sysOrigin) {
        return
      }
      if (isReset === true) {
        that.list = []
        that.notData = false
        that.listQuery.lastId = ''
      }
      that.listLoading = true
      pageRunningWater(that.listQuery).then(res => {
        that.listLoading = false
        that.loadMoreLoading = false
        const { body } = res
        const list = body || []
        that.notData = list.length <= 0
        if (!that.notData) {
          that.list = that.list.concat(list)
          that.listQuery.lastId = that.list[that.list.length - 1].id
        }
      }).catch(er => {
        that.listLoading = false
        that.loadMoreLoading = false
      })
    },
    getTransferDescriptionName(event) {
      if (event === 'TRANSFER') {
        return '收款人'
      }
      if (event === 'RECEIVE_TRANSFER') {
        return '付款人'
      }
      return ''
    },
    handleClose() {
      this.$emit('close')
    },
    clickLoadMore() {
      const that = this
      that.loadMoreLoading = true
      that.renderData()
    },
    getEventDesc(_key) {
      const that = this
      const result = that.userBankWaterEvent.filter(_event => _event.value === _key)
      if (!result) {
        return _key
      }
      return result[0].name
    }
  }
}
</script>
<style scoped lang="scss">
  .load-more {
    padding: 20px;
    text-align: center;
  }
</style>
