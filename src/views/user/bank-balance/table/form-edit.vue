<template>
  <div class="user-bank-form-edit">
    <el-dialog
      :title="'创建账户'"
      :visible="true"
      :before-close="handleClose"
      :close-on-click-modal="false"
      width="550px"
      top="20px"
    >
      <div class="form-edit">
        <el-form ref="form" :model="form" :rules="formRules" label-width="110px" style="margin-right:50px;">
          <el-form-item label="系统" prop="sysOrigin">
            {{ sysOrigin }}
          </el-form-item>
          <el-form-item label="用户id" prop="userId">

            <account-input v-model="form.userId" :sys-origin="sysOrigin" placeholder="用户ID" />
          </el-form-item>
          <el-form-item label="金额" prop="quantity">
            <el-input v-model.trim="form.quantity" placeholder="请输入发送金额" class="input-with-select" maxlength="10" />
          </el-form-item>
          <el-form-item label="对内备注" prop="remark">
            <el-input v-model.trim="form.remark" placeholder="对内备注" class="input-with-select" maxlength="10" />
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer">
        <el-button @click="handleClose()">取消</el-button>
        <el-button type="primary" :loading="submitLoading" :disabled="searchDisabled" @click="submitForm()">保存</el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import { createBankBalance } from '@/api/app-user-bank-balance'
import { deepClone } from '@/utils'
import { mapGetters } from 'vuex'
export default {
  props: {
    row: {
      type: Object,
      require: false,
      default: () => {}
    },
    sysOrigin: {
      type: String,
      require: false,
      default: ''
    }
  },
  data() {
    const commonRules = [
      { required: true, message: '必填字段', trigger: 'blur' }
    ]

    return {
      searchDisabled: false,
      userInfo: {},
      submitLoading: false,
      form: {
        userId: '',
        quantity: '',
        remark: ''
      },
      formRules: {
        userId: commonRules,
        // earnPoints: { required: true, message: '必填字段，且必须大于0', validator: (rule, value, callback) => {
        //   if (!value || value <= 0) {
        //     callback(new Error())
        //     return
        //   }
        //   callback()
        // }, trigger: 'blur' },
        quantity: { required: true, message: '必填字段不可为空', trigger: 'blur' }
      }
    }
  },
  computed: {
    textOptTitle() {
      return this.row && this.row.id ? '修改' : '添加'
    },
    isUpdate() {
      return this.row && this.row.id
    },
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    row: {
      handler(newVal) {
        if (!newVal) {
          return
        }
        const newForm = deepClone(newVal)
        this.form = newForm
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return
        }
        that.submitLoading = true
        createBankBalance(that.form).then(res => {
          that.$opsMessage.success()
          that.submitLoading = false
          that.$emit('success')
        }).catch(er => {
          that.submitLoading = false
          that.$emit('fail')
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">
.props-activity-reward-config-edit {
  .form-edit {
    max-height: 600px;
    overflow: auto;
    .dr-content {
      padding: 5px 0px 5px 20px;
    }
    .sort {
      border-radius: 50%;
      width: 30px;
      height: 30px;
      background: #f7f6f5;
      margin: auto;
      text-align: center;
      line-height: 29px;
      font-weight: bold;
    }
    .del {
      font-size: 30px;
      color: #F56C6C;
      cursor: pointer;
    }
    .save {
      font-size: 30px;
      color: #409EFF;
      cursor: pointer;
    }
  }
}
.my-autocomplete {
  li {
    line-height: normal;
    padding: 7px;

    .name {
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .addr {
      font-size: 12px;
      color: #b4b4b4;
    }

    .highlighted .addr {
      color: #ddd;
    }
  }
}
</style>
