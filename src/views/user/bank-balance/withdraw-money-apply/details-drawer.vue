<template>
  <div class="details-drawer">
    <el-drawer
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div slot="title" class="flex-l">
        <sys-origin-icon :icon="row.sysOrigin" size="18px" />&nbsp;提现申请详情
      </div>
      <div class="team-bill-settle">
        <div class="drawer-form">
          <div class="blockquote">提交人</div>
          <div class="content" style="display: flex;justify-content: flex-start;flex-direction: row;align-items: center;">
            <user-table-exhibit :user-profile="row.submitUser" :query-details="true" style="margin: 0.4rem" />
          </div>
          <div v-if="row.settlementResult && row.settlementResult.acceptBankCard">
            <div class="blockquote">银行卡 <i class="el-icon-document-copy cursor-pointer" @click="copyBankCard(row)" /></div>
            <div class="content">
              <div style="margin: 0.2rem auto;line-height: .6rem;">
                <el-tag effect="plain">卡号:{{ row.settlementResult.acceptBankCard.cardNo }}</el-tag>
                <el-tag effect="plain">收款人:{{ row.settlementResult.acceptBankCard.payee }}</el-tag>
                <el-tag effect="plain">银行:{{ row.settlementResult.acceptBankCard.cardName }}</el-tag>
              </div>
              <div v-if="(row.settlementResult.acceptBankCard.del || row.settlementResult.acceptBankCard.status !== 'PASS') && row.latestApprovalStatus === 'SUBMIT'" style="margin: 0.2rem auto;line-height: .6rem;">
                <el-tag type="danger">该银行卡可能已被用户删除或没有通审核,请核实.</el-tag>
              </div>
            </div>
          </div>
          <div class="blockquote">提现金额: <span style="color:#419eff;">{{ row.amount }}</span></div>
          <div class="blockquote">手续费(%) <span style="color:#419eff;">{{ row.serviceCharge }}</span></div>
          <div class="blockquote">实际提现金额: <span style="color:#419eff;">{{ row.actualAmount }}</span></div>
          <div class="blockquote">凭证</div>
          <div v-if="row.settlementResult && row.settlementResult.credential" class="content">
            <div style="display: flex;flex-direction: row;justify-content: space-around;">
              <div v-for="(item, index) in row.settlementResult.credential" :key="index">
                <el-image
                  style="width: 2.5rem;height: 2.5rem;"
                  :src="item.url"
                  :preview-src-list="[item.url]"
                />
              </div>
            </div>
          </div>
          <div class="blockquote">审批记录</div>
          <div class="content">
            <div class="block">
              <el-timeline>
                <el-timeline-item v-for="(item, index) in row.approvalProcesses" :key="index" :timestamp="item.createTime | dateFormat" placement="top">
                  <el-card>
                    <h4>{{ getStatusName(item.status) }}</h4>
                    <p>备注: {{ item.remark }}</p>
                  </el-card>
                </el-timeline-item>
              </el-timeline>
            </div>
          </div>

        </div>

        <div class="drawer-footer">
          <el-button @click="handleClose()">取消</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import { deepClone, copyText } from '@/utils'
import { bankCardType } from '@/constant/team-type'
export default {
  name: 'UserBankWithdrawMoneyApplyDetailsDrawer',
  props: {
    row: {
      type: Object,
      require: true,
      default: () => {}
    }
  },
  data() {
    return {
      bankCardType,
      certificateOne: '',
      certificateTwo: '',
      certificateThree: ''
    }
  },
  watch: {
    row: {
      handler(newVal) {
        if (newVal) {
          this.nativeRow = deepClone(newVal)
          if (this.nativeRow.settlementResult && this.nativeRow.settlementResult.credential) {
            const credentialList = this.nativeRow.settlementResult.credential || []
            if (credentialList.length > 0) {
              this.certificateOne = credentialList[0]
            }
            if (credentialList.length > 1) {
              this.certificateOne = credentialList[1]
            }
            if (credentialList.length > 2) {
              this.certificateOne = credentialList[2]
            }
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      if (this.submitLoading) {
        this.$opsMessage.warn('正在处理中!')
        return
      }
      this.$emit('close')
    },
    getStatusName(status) {
      if (status === 'SUBMIT') {
        return '用户提交'
      }
      if (status === 'PASS') {
        return '审核通过'
      }
      if (status === 'NOT_PASS') {
        return '审核不通过'
      }
      return '未知'
    },
    copyBankCard(row) {
      copyText(`卡号: ${row.settlementResult.acceptBankCard.cardNo || '?'}, 收款人: ${row.settlementResult.acceptBankCard.payee || '?'}, 银行: ${row.settlementResult.acceptBankCard.cardName || '?'} 
      `).then(() => {
        this.$opsMessage.success()
      }).catch(er => {
        this.$opsMessage.fail()
      })
    }
  }
}
</script>
<style scope.d lang="scss">
  .details-content {
      padding: 0px 10px;
      .content {
        margin-bottom: 10px;
      }

      .remark {
        width: 100%;
        overflow: auto;
        word-wrap: break-word;
        .nickname {
          font-weight: bold;
        }
      }
      .details-row {
        padding: 10px 0px;
        >.details-col {
          width: 50%;
        }
      }
      .policy {
        .policy-row {
          color: #333333;
          .title {
            padding: 5px 15px !important;
            background-color: transparent !important;
          }
          .policy-content {
            text-align: center;
            .policy-block {
              border: 1px solid #FFFFFF;
              width: 33.33%;
              padding: 10px;
              background-color: #F1F2F3;
              .label {
                padding-bottom: 5px;
              }
            }
          }

        }
      }
      .bank-card-list {
        .back-green {
          background-image: linear-gradient(to left, #018AA8 0%, #00B39F 100%);
        }
        .back-blue {
          background-image: linear-gradient(to left, #2D61CF 0%, #3893E6 100%);
        }
        .bank-card-item {
          color: #FFFFFF;
          margin-bottom: .2rem;
          border-radius: .2rem;
          padding: 10px;
          .card-info {
            .card-icon {
              height: 50px;
              width: 50px;
              flex-shrink: 0;
              img {
                width: 100%;
                height: 100%;
                border-radius: 100%;
              }
            }
            .profile {
              width: 100%;
              padding: 0px 10px;
              .payee {
                padding: 5px 0px;
              }
            }
          }
        }
      }
  }
  </style>
