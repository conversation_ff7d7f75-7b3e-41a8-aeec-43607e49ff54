<template>
  <div :class="'app-container'">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.bankCardType"
        placeholder="卡片类型"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option
          v-for="item in bankCardTypes"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <div class="filter-item">
        <account-input v-model="listQuery.submitUserId" :sys-origin="listQuery.sysOrigin" placeholder="提交用户ID" />
      </div>
      <el-select
        v-model="listQuery.acceptMethod"
        placeholder="类型"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="自己" :value="'ONESELF'" />
        <el-option label="代理" :value="'AGENT'" />
        <el-option label="BD" :value="'BD'" />
        <el-option label="其他" :value="'OTHER'" />
      </el-select>

      <el-select
        v-model="listQuery.latestApprovalStatus"
        placeholder="状态"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="用户提交" :value="'SUBMIT'" />
        <el-option label="通过" :value="'PASS'" />
        <el-option label="驳回" :value="'NOT_PASS'" />
      </el-select>
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="日期开始"
          end-placeholder="日期结束"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        v-if="buttonPermissions.includes('withdraw-money-apply:table:export')"
        class="filter-item"
        type="primary"
        icon="el-icon-download"
        @click="exprotOrder"
      >
        导出
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="sysOrigin" label="系统" align="center" width="80">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column label="提交用户" align="center" min-width="80">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.submitUser" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column prop="amount" label="提交金额" align="center" min-width="80" />
      <el-table-column label="类型" align="center" min-width="80" >
        <template slot-scope="scope">
          <span v-if="scope.row.amountType === 'SALARY_DIAMOND'">钻石</span>
          <span v-else>美金</span>
        </template>
      </el-table-column>
      <el-table-column prop="serviceCharge" label="手续费(%)" align="center" min-width="80" />
      <el-table-column prop="actualAmount" label="实际提现金额" align="center" min-width="80" />
      <el-table-column prop="acceptMethodName" label="接收方式" align="center" min-width="80" />
      <el-table-column prop="latestApprovalStatusName" label="最新审核状态" align="center" min-width="80" />
      <el-table-column prop="createTime" label="创建时间" align="center" width="160">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="140">
        <template slot-scope="scope">
          <el-button type="text" @click="clickDetailsDrawer(scope.row)">查看</el-button>
          <el-button v-if="scope.row.latestApprovalStatus === 'SUBMIT'" type="text" @click="clickApprovalDrawer(scope.row, scope.$index)">审核</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="listQuery.lastId" class="load-more">
      <span v-if="notData">已加载全部</span>
      <el-button v-else size="mini" :disabled="loadMoreLoading" :loading="loadMoreLoading" @click="clickLoadMore">加载更多</el-button>
    </div>
    <details-drawer
      v-if="detailsDrawerVisible"
      :row="thatRow"
      @close="detailsDrawerVisible=false"
    />
    <approval-drawer
      v-if="approvalDrawerVisible"
      :row="thatRow"
      @approvalClose="approvalClose"
      @close="approvalDrawerVisible=false"
    />

    <el-drawer
      title="导出条件"
      :visible="exportConditionVisible"
      :before-close="exportConditionClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div class="exprot-select">
        <div class="drawer-form">
          <el-form
            ref="exportQuery"
            :rules="exportQueryRules"
            :model="exportQuery"
            label-position="left"
            label-width="70px"
          >

            <el-form-item label="系统" prop="sysOrigin">
              <el-select
                v-model="exportQuery.sysOrigin"
                placeholder="系统"
                style="width: 100%;"
              >
                <el-option
                  v-for="item in permissionsSysOriginPlatforms"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
                  <span style="float: left;margin-left:10px">{{ item.label }}</span>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="类型" prop="bankCardType">
              <el-select
                v-model="exportQuery.bankCardType"
                placeholder="卡片类型"
                style="width: 100%;"
                clearable
              >
                <el-option
                  v-for="item in bankCardTypes"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="用户" prop="submitUserId">
              <account-input v-model="exportQuery.submitUserId" :sys-origin="exportQuery.sysOrigin || listQuery.sysOrigin" placeholder="提交用户ID" />
            </el-form-item>

            <el-form-item label="状态" prop="latestApprovalStatus">
              <el-select
                v-model="exportQuery.latestApprovalStatus"
                placeholder="状态"
                style="width: 100%;"
                clearable
                @change="handleSearch"
              >
                <el-option label="用户提交" :value="'SUBMIT'" />
                <el-option label="通过" :value="'PASS'" />
                <el-option label="驳回" :value="'NOT_PASS'" />
              </el-select>
            </el-form-item>

            <el-form-item label="时间" prop="startTime">
              <el-date-picker
                v-model="exportDate"
                style="width: 100%;"
                value-format="timestamp"
                type="datetimerange"
                :picker-options="pickerOptions"
                range-separator="至"
                start-placeholder="日期开始"
                end-placeholder="日期结束"
              />
            </el-form-item>

            <el-form-item label="数量" prop="limit">
              <el-input v-model="exportQuery.limit" v-number placeholder="导出数量" />
            </el-form-item>

          </el-form>
        </div>
        <div class="drawer-footer">
          <el-button @click="exportConditionClose()">关闭</el-button>
          <el-button type="primary" :disabled="exportLoading" :loading="exportLoading" @click="exportConditionSubmit()">导出</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { pageBankWithdrawMoneyApply, exportBankWithdrawMoneyApply } from '@/api/app-user-bank-balance'
import { pickerOptions } from '@/constant/el-const'
import { bankCardTypes } from '@/constant/type'
import { mapGetters } from 'vuex'
import DetailsDrawer from './details-drawer'
import ApprovalDrawer from './approval-drawer'
export default {
  name: 'UserBankWithdrawMoneyApply',
  components: { DetailsDrawer, ApprovalDrawer },
  data() {
    const commonRules = [
      { required: true, message: '必填字段不可为空', trigger: 'blur' }
    ]
    return {
      bankCardTypes,
      exportDate: [],
      exportLoading: false,
      exportQuery: {
        submitUserId: '',
        sysOrigin: '',
        latestApprovalStatus: '',
        startTime: '',
        endTime: '',
        bankCardType: '',
        limit: 1000
      },
      exportQueryRules: {
        sysOrigin: commonRules,
        startTime: commonRules,
        endTime: commonRules,
        limit: commonRules
      },
      exportConditionVisible: false,

      rangeDate: [],
      thatSelectedUserId: '',
      searchDisabled: false,
      detailsDrawerVisible: false,
      approvalDrawerVisible: false,
      pickerOptions,
      thatRow: {},
      clickApprovalIndex: 0,
      listQuery: {
        limit: 20,
        submitUserId: '',
        sysOrigin: 'MARCIE',
        acceptMethod: '',
        latestApprovalStatus: '',
        lastId: '',
        startTime: '',
        endTime: '',
        bankCardType: ''
      },
      listLoading: false,
      list: [],
      notData: false,
      loadMoreLoading: false
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms', 'buttonPermissions'])
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    },
    exportDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.exportQuery.startTime = newVal[0]
          this.exportQuery.endTime = newVal[1]
          return
        }
        this.exportQuery.startTime = ''
        this.exportQuery.endTime = ''
      }
    }
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isReset) {
      const that = this
      if (!that.listQuery.sysOrigin) {
        return
      }
      if (isReset === true) {
        that.list = []
        that.notData = false
        that.listQuery.lastId = ''
      }
      that.listLoading = true
      pageBankWithdrawMoneyApply(that.listQuery).then(res => {
        that.listLoading = false
        that.loadMoreLoading = false
        const { body } = res
        const list = body || []
        that.notData = list.length <= 0
        if (!that.notData) {
          that.list = that.list.concat(list)
          that.listQuery.lastId = that.list[that.list.length - 1].id
        }
      }).catch(er => {
        that.listLoading = false
        that.loadMoreLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    clickLoadMore() {
      const that = this
      that.loadMoreLoading = true
      that.renderData()
    },
    clickDetailsDrawer(details) {
      this.thatRow = details
      this.detailsDrawerVisible = true
    },
    clickApprovalDrawer(details, _index) {
      this.thatRow = details
      this.clickApprovalIndex = _index
      this.approvalDrawerVisible = true
    },
    approvalClose(_data) {
      this.list.splice(this.clickApprovalIndex, 1, _data)
      this.approvalDrawerVisible = false
    },
    exportConditionClose() {
      if (this.exportLoading) {
        this.$opsMessage.warn('正在执行导出, 请稍等~')
        return
      }
      this.exportConditionVisible = false
    },
    exprotOrder() {
      this.exportQuery.sysOrigin = this.listQuery.sysOrigin
      this.exportConditionVisible = true
    },
    exportConditionSubmit() {
      const that = this
      that.$refs.exportQuery.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.exportLoading = true
        exportBankWithdrawMoneyApply(that.exportQuery).then(res => {
          that.exportLoading = false
          that.exportConditionVisible = false
        }).catch(er => {
          that.exportLoading = false
          this.$opsMessage.fail('下载失败！')
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">
  .user-css {
    display: flex;
    align-items: center;
    color: #909399;
    font-size: .15rem;
    line-height: .3rem;
    padding: 0.02rem;
    border-radius: 0.04rem;
  }
  .load-more {
    padding: 20px;
    text-align: center;
  }
</style>
