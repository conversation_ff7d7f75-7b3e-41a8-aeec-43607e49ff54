<template>
  <div class="details-drawer">
    <el-drawer
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div slot="title" class="flex-l">
        <sys-origin-icon :icon="row.sysOrigin" size="18px" />&nbsp;审核提现申请
      </div>
      <div class="team-bill-settle">
        <div class="drawer-form">
          <div class="blockquote">提交人</div>
          <div class="content" style="display: flex;justify-content: flex-start;flex-direction: row;align-items: center;">
            <user-table-exhibit :user-profile="row.submitUser" :query-details="true" style="margin: 0.4rem" />
          </div>

          <div v-if="row.settlementResult && row.settlementResult.acceptBankCard">
            <div class="blockquote">银行卡 <i class="el-icon-document-copy cursor-pointer" @click="copyBankCard(row)" /></div>
            <div class="content">
              <div style="margin: 0.2rem auto;line-height: .6rem;">
                <el-tag effect="plain">卡号:{{ row.settlementResult.acceptBankCard.cardNo }}</el-tag>
                <el-tag effect="plain">收款人:{{ row.settlementResult.acceptBankCard.payee }}</el-tag>
                <el-tag effect="plain">银行:{{ row.settlementResult.acceptBankCard.cardName }}</el-tag>
              </div>
              <div v-if="row.settlementResult.acceptBankCard.del" style="margin: 0.2rem auto;line-height: .6rem;">
                <el-tag type="danger">该银行卡已被用户删除</el-tag>
              </div>
            </div>
          </div>
          <div class="blockquote">提现金额: <span style="color:#419eff;">{{ row.amount }}</span></div>
          <div class="blockquote">结算
            <el-tooltip class="item" effect="dark">
              <div slot="content">
                <p>* 转账凭证: 结算人员发送美元或其他币种后的记录凭证截图</p>
                <p>* 备注: 本次账单备注, 对外显示</p>
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </div>
          <el-form ref="form" :model="form" label-width="80px" style="margin-right: 15px;">

            <el-form-item prop="tmpExistsCertificate" label="凭证上传">
              <el-row>
                <el-col :span="8">
                  <upload-image
                    v-model="certificateOne"
                    :file-dir="$application.fileBucket.back"
                  />
                </el-col>
                <el-col :span="8">
                  <upload-image
                    v-model="certificateTwo"
                    :file-dir="$application.fileBucket.back"
                  />
                </el-col>
                <el-col :span="8">
                  <upload-image
                    v-model="certificateThree"
                    :file-dir="$application.fileBucket.back"
                  />
                </el-col>
                <el-col :span="24" style="margin-top: 10px;">
                  <upload-image
                    v-model="certificateFour"
                    :file-dir="$application.fileBucket.back"
                  />
                </el-col>
              </el-row>
            </el-form-item>

            <el-form-item label="对外备注">
              <el-input
                v-model="form.remark"
                type="textarea"
                placeholder="请输入对外备注"
                maxlength="100"
                show-word-limit
                resize="none"
                rows="5"
              />
            </el-form-item>
          </el-form>
        </div>

        <div class="drawer-footer">
          <el-button :disabled="submitLoading" @click="handleClose()">取消</el-button>
          <el-button :disabled="submitLoading" :loading="submitLoading" @click="submit('PASS')">通过</el-button>
          <el-button type="primary" :disabled="submitLoading" :loading="submitLoading" @click="submit('NOT_PASS')">驳回</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import { deepClone, copyText } from '@/utils'
import { approvalMoneyApply, getBankWithdrawMoneyApply } from '@/api/app-user-bank-balance'
import { bankCardType } from '@/constant/team-type'
export default {
  name: 'UserBankWithdrawMoneyApplyApprovalDrawer',
  props: {
    row: {
      type: Object,
      require: true,
      default: () => {}
    }
  },
  data() {
    return {
      bankCardType,
      submitLoading: false,
      certificateOne: '',
      certificateTwo: '',
      certificateThree: '',
      certificateFour: '',
      form: {
        id: '',
        credential: [],
        remark: '',
        approvalStatus: ''
      }
    }
  },
  watch: {
    row: {
      handler(newVal) {
        if (newVal) {
          this.nativeRow = deepClone(newVal)
          this.form.id = this.nativeRow.id
          this.form.approvalStatus = this.nativeRow.latestApprovalStatus
          this.form.remark = this.nativeRow.remark
          if (this.nativeRow.settlementResult && this.nativeRow.settlementResult.credential) {
            const credentialList = this.nativeRow.settlementResult.credential || []
            if (credentialList.length > 0) {
              this.certificateOne = credentialList[0]
            }
            if (credentialList.length > 1) {
              this.certificateTwo = credentialList[1]
            }
            if (credentialList.length > 2) {
              this.certificateThree = credentialList[2]
            }
            if (credentialList.length > 3) {
              this.certificateFour = credentialList[3]
            }
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    copyBankCard(row) {
      copyText(`卡号: ${row.settlementResult.acceptBankCard.cardNo || '?'}, 收款人: ${row.settlementResult.acceptBankCard.payee || '?'}, 银行: ${row.settlementResult.acceptBankCard.cardName || '?'} 
      `).then(() => {
        this.$opsMessage.success()
      }).catch(er => {
        this.$opsMessage.fail()
      })
    },
    handleClose() {
      if (this.submitLoading) {
        this.$opsMessage.warn('正在处理中!')
        return
      }
      this.$emit('close')
    },
    getStatusName(status) {
      if (status === 'SUBMIT') {
        return '用户提交'
      }
      if (status === 'PASS') {
        return '审核通过'
      }
      if (status === 'NOT_PASS') {
        return '审核不通过'
      }
      return '未知'
    },
    getBankCardTypeName(_type) {
      var _name = ''
      this.bankCardType.forEach(function(v, i) {
        if (v.value === _type) {
          _name = v.name
          return
        }
      })
      return _name
    },
    submit(approvalStatus) {
      const that = this

      that.form.approvalStatus = approvalStatus

      var certificateUrls = []
      if (that.certificateOne) {
        certificateUrls.push(that.certificateOne)
      }
      if (that.certificateTwo) {
        certificateUrls.push(that.certificateTwo)
      }
      if (that.certificateThree) {
        certificateUrls.push(that.certificateThree)
      }
      if (that.certificateFour) {
        certificateUrls.push(that.certificateFour)
      }
      if (certificateUrls.length <= 0 && approvalStatus === 'PASS') {
        this.$opsMessage.warn('请上传转账凭证, 请稍等!')
        return
      }
      that.form.credential = certificateUrls
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.submitLoading = true
        approvalMoneyApply(that.form).then(res => {
          getBankWithdrawMoneyApply(that.form.id).then(res => {
            that.submitLoading = false
            that.$opsMessage.success()
            this.$emit('approvalClose', res.body)
          }).catch(er => {
          })
        }).catch(er => {
          that.submitLoading = false
        })
      })
    }
  }
}
</script>
<style scope.d lang="scss">
  .details-content {
      padding: 0px 10px;
      .content {
        margin-bottom: 10px;
      }

      .remark {
        width: 100%;
        overflow: auto;
        word-wrap: break-word;
        .nickname {
          font-weight: bold;
        }
      }
      .details-row {
        padding: 10px 0px;
        >.details-col {
          width: 50%;
        }
      }
      .policy {
        .policy-row {
          color: #333333;
          .title {
            padding: 5px 15px !important;
            background-color: transparent !important;
          }
          .policy-content {
            text-align: center;
            .policy-block {
              border: 1px solid #FFFFFF;
              width: 33.33%;
              padding: 10px;
              background-color: #F1F2F3;
              .label {
                padding-bottom: 5px;
              }
            }
          }

        }
      }
      .bank-card-list {
        .back-green {
          background-image: linear-gradient(to left, #018AA8 0%, #00B39F 100%);
        }
        .back-blue {
          background-image: linear-gradient(to left, #2D61CF 0%, #3893E6 100%);
        }
        .bank-card-item {
          color: #FFFFFF;
          margin-bottom: .2rem;
          border-radius: .2rem;
          padding: 10px;
          .card-info {
            .card-icon {
              height: 50px;
              width: 50px;
              flex-shrink: 0;
              img {
                width: 100%;
                height: 100%;
                border-radius: 100%;
              }
            }
            .profile {
              width: 100%;
              padding: 0px 10px;
              .payee {
                padding: 5px 0px;
              }
            }
          }
        }
      }
  }
  </style>
