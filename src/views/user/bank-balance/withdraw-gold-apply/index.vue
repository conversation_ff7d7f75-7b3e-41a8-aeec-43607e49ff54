<template>
  <div :class="'app-container'">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;">
            <sys-origin-icon :icon="item.value" :desc="item.value"
          /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <account-input
          v-model="listQuery.submitUserId"
          :sys-origin="listQuery.sysOrigin"
          placeholder="提交用户ID"
        />
      </div>
      <div class="filter-item">
        <account-input
          v-model="listQuery.acceptUserId"
          :sys-origin="listQuery.sysOrigin"
          placeholder="接收用户ID"
        />
      </div>
      <el-select
        v-model="listQuery.acceptAccount"
        placeholder="类型"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="自己" :value="'GOLD'" />
        <el-option label="货运代理账户" :value="'FREIGHT'" />
      </el-select>

      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="日期开始"
          end-placeholder="日期结束"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="sysOrigin" label="系统" align="center" width="80">
        <template slot-scope="scope">
          <sys-origin-icon
            :icon="scope.row.sysOrigin"
            :desc="scope.row.sysOrigin"
          />
        </template>
      </el-table-column>
      <el-table-column label="提交用户" align="center" min-width="80">
        <template slot-scope="scope">
          <user-table-exhibit
            :user-profile="scope.row.submitUser"
            :query-details="true"
          />
        </template>
      </el-table-column>
      <el-table-column label="接收用户" align="center" min-width="80">
        <template slot-scope="scope">
          <user-table-exhibit
            :user-profile="scope.row.acceptUser"
            :query-details="true"
          />
        </template>
      </el-table-column>
      <el-table-column
        prop="acceptAccountName"
        label="接收方式"
        align="center"
        width="100"
      />
      <el-table-column
        prop="amount"
        label="提交金额"
        align="center"
        min-width="80"
      />
      <el-table-column
        prop="exchangeRate"
        label="兑换比率"
        align="center"
        min-width="80"
      />
      <el-table-column
        prop="goldQuantity"
        label="获得金币"
        align="center"
        min-width="80"
      />
      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
        width="160"
      >
        <template slot-scope="scope">
          创建日期：{{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
    </el-table>
    <div v-if="listQuery.lastId" class="load-more">
      <span v-if="notData">已加载全部</span>
      <el-button
        v-else
        size="mini"
        :disabled="loadMoreLoading"
        :loading="loadMoreLoading"
        @click="clickLoadMore"
        >加载更多</el-button
      >
    </div>
  </div>
</template>

<script>
import { pageBankWithdrawGoldApply } from "@/api/app-user-bank-balance";
import { pickerOptions } from "@/constant/el-const";
import { mapGetters } from "vuex";
export default {
  name: "UserBankWithdrawGoldApply",
  data() {
    return {
      rangeDate: [],
      thatSelectedUserId: "",
      thatSelectedAcceptUserId: "",
      searchDisabled: false,
      pickerOptions,
      listQuery: {
        limit: 20,
        acceptUserId: "",
        submitUserId: "",
        sysOrigin: "HALAR",
        acceptAccount: "",
        lastId: "",
        startTime: "",
        endTime: ""
      },
      listLoading: false,
      list: [],
      notData: false,
      loadMoreLoading: false
    };
  },
  computed: {
    ...mapGetters(["permissionsSysOriginPlatforms"])
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0];
          this.listQuery.endTime = newVal[1];
          return;
        }
        this.listQuery.startTime = "";
        this.listQuery.endTime = "";
      }
    }
  },
  created() {
    const that = this;
    const querySystem = this.permissionsSysOriginPlatforms[0];
    if (!querySystem) {
      return;
    }
    that.listQuery.sysOrigin = querySystem.value;
    that.renderData();
  },
  methods: {
    renderData(isReset) {
      const that = this;
      if (!that.listQuery.sysOrigin) {
        return;
      }
      if (isReset === true) {
        that.list = [];
        that.notData = false;
        that.listQuery.lastId = "";
      }
      that.listLoading = true;
      pageBankWithdrawGoldApply(that.listQuery)
        .then(res => {
          that.listLoading = false;
          that.loadMoreLoading = false;
          const { body } = res;
          const list = body || [];
          that.notData = list.length <= 0;
          if (!that.notData) {
            that.list = that.list.concat(list);
            that.listQuery.lastId = that.list[that.list.length - 1].id;
          }
        })
        .catch(er => {
          that.listLoading = false;
          that.loadMoreLoading = false;
        });
    },
    handleSearch() {
      this.renderData(true);
    },
    clickLoadMore() {
      const that = this;
      that.loadMoreLoading = true;
      that.renderData();
    }
  }
};
</script>
<style scoped lang="scss">
.user-css {
  display: flex;
  align-items: center;
  color: #909399;
  font-size: 0.15rem;
  line-height: 0.3rem;
  padding: 0.02rem;
  border-radius: 0.04rem;
}
.load-more {
  padding: 20px;
  text-align: center;
}
</style>
