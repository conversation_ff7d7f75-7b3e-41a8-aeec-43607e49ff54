<template>
  <div :class="'app-container'">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <account-input v-model="listQuery.userId" :sys-origin="listQuery.sysOrigin" placeholder="用户ID" />
      </div>
      <div class="filter-item">
        <el-date-picker
          v-model="listQuery.dateNumber"
          type="month"
          placeholder="选择日期"
          format="yyyy 年 MM 月"
          value-format="yyyyMM"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="sysOrigin" label="系统" align="center" width="80">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column label="用户" align="center">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column prop="dateNumber" label="年月" align="center" />
      <el-table-column prop="createTime" label="创建时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" label="修改时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.updateTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="clickDetailsDrawer(scope.row)">查看明细</el-button>
          <el-button type="text" @click="clickRemarkDrawer(scope.row)">查看备注</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
    <details-drawer
      v-if="detailsDrawerVisible"
      :row="thatRow"
      @close="detailsDrawerVisible=false"
    />
    <remark-details-drawer
      v-if="remarkDetailsDrawerVisible"
      :row="thatRow"
      @close="remarkDetailsDrawerVisible=false"
    />
  </div>
</template>

<script>
import { pageSalary } from '@/api/app-user-bank-balance'
import { pickerOptions } from '@/constant/el-const'
import Pagination from '@/components/Pagination'
import { mapGetters } from 'vuex'
import DetailsDrawer from './details-drawer'
import RemarkDetailsDrawer from './remark-details-drawer'
export default {
  name: 'AutoSalaryPayRecord',
  components: { DetailsDrawer, Pagination, RemarkDetailsDrawer },
  data() {
    return {
      thatSelectedUserId: '',
      searchDisabled: false,
      remarkDetailsDrawerVisible: false,
      detailsDrawerVisible: false,
      pickerOptions,
      thatRow: {},
      listQuery: {
        limit: 20,
        userId: '',
        sysOrigin: 'HALAR',
        dateNumber: ''
      },
      listLoading: false,
      list: [],
      total: 0
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      that.listLoading = true
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      pageSalary(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    clickLoadMore() {
      const that = this
      that.renderData()
    },
    clickDetailsDrawer(details) {
      this.thatRow = details
      this.detailsDrawerVisible = true
    },
    clickRemarkDrawer(details) {
      this.thatRow = details
      this.remarkDetailsDrawerVisible = true
    }
  }
}
</script>
<style scoped lang="scss">
  .user-css {
    display: flex;
    align-items: center;
    color: #909399;
    font-size: .15rem;
    line-height: .3rem;
    padding: 0.02rem;
    border-radius: 0.04rem;
  }
  .load-more {
    padding: 20px;
    text-align: center;
  }
</style>
