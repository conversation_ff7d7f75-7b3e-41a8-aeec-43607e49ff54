<template>
  <div class="details-drawer">
    <el-drawer
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      size="100%"
      direction="btt"
    >
      <div slot="title" class="flex-l">
        <sys-origin-icon :icon="row.sysOrigin" size="18px" />&nbsp;自动发送工资详情({{ dateNumber }})
      </div>
      <div>

        <el-table
          :data="list"
          fit
          highlight-current-row
        >
          <el-table-column label="系统" align="center" width="80">
            <template slot-scope="scope">
              <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
            </template>
          </el-table-column>
          <el-table-column prop="teamRegionName" label="区域" align="center" min-width="80" />
          <el-table-column prop="policyLevel" label="政策等级" align="center" width="160" />
          <el-table-column prop="target" label="用户目标" align="center" min-width="80" />
          <el-table-column prop="salary" label="工资" align="center" min-width="80" />
          <el-table-column label="应收工资用户" align="center" min-width="80">
            <template slot-scope="scope">
              <user-table-exhibit :user-profile="scope.row.anchor ? scope.row.teamMemberProfile : scope.row.teamOwnProfile" :query-details="true" />
            </template>
          </el-table-column>
          <el-table-column label="实际接收工资用户" align="center" min-width="80">
            <template slot-scope="scope">
              <user-table-exhibit :user-profile="scope.row.hostSalaryToAgent ? scope.row.teamOwnProfile : (scope.row.anchor ? scope.row.teamMemberProfile : scope.row.teamOwnProfile)" :query-details="true" />
            </template>
          </el-table-column>
          <el-table-column label="团长代收" align="center" min-width="80">
            <template slot-scope="scope">
              <span v-if="!scope.row.hostSalaryToAgent">否</span>
              <el-tag v-if="scope.row.hostSalaryToAgent" effect="plain">代收</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="已收回" align="center" min-width="80">
            <template slot-scope="scope">
              <span v-if="!scope.row.recycled">否</span>
              <el-tag v-if="scope.row.recycled" type="danger" effect="plain">已收回</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" align="center" width="160">
            <template slot-scope="scope">
              {{ scope.row.createTime | dateFormat }}
            </template>
          </el-table-column>
          <el-table-column prop="updateTime" label="修改时间" align="center" width="160">
            <template slot-scope="scope">
              {{ scope.row.updateTime | dateFormat }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="center" width="140">
            <template slot-scope="scope">
              <el-button type="text" @click="clickDetailsDrawer(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-drawer>
    <policy-details-drawer
      v-if="policyDetailsDrawerVisible"
      :row="thatRow"
      @close="policyDetailsDrawerVisible=false"
    />
  </div>
</template>
<script>
import { deepClone } from '@/utils'
import PolicyDetailsDrawer from './policy-details-drawer'
export default {
  name: 'AutomaticSalaryPaymentDetailsDrawer',
  components: { PolicyDetailsDrawer },
  props: {
    row: {
      type: Object,
      require: true,
      default: () => {}
    }
  },
  data() {
    return {
      list: [],
      thatRow: {},
      dateNumber: '',
      drawer: false,
      policyDetailsDrawerVisible: false
    }
  },
  watch: {
    row: {
      handler(newVal) {
        this.drawer = true
        if (newVal) {
          this.nativeRow = deepClone(newVal)
          if (this.nativeRow.salaryDetails) {
            this.list = this.nativeRow.salaryDetails || []
          }
          this.dateNumber = this.nativeRow.dateNumber
        }
      },
      immediate: true
    }
  },
  methods: {
    clickDetailsDrawer(details) {
      this.thatRow = details
      this.policyDetailsDrawerVisible = true
    },
    handleClose() {
      this.drawer = false
      this.$emit('close')
    }
  }
}
</script>
<style scope.d lang="scss">

  </style>
