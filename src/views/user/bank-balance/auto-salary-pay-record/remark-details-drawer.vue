<template>
  <div class="team-bill-drawer">
    <el-drawer
      title="详情"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div class="team-bill-content">

        <div v-if="row.remarks && row.remarks.length > 0">
          <div class="blockquote">内部备注</div>
          <div class="content">
            <el-timeline :reverse="true">
              <el-timeline-item
                v-for="(item, index) in row.remarks"
                :key="index"
                :timestamp="item.createTime"
              >
                <div class="remark">
                  {{ item.remark }}
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>
      </div>

    </el-drawer>
  </div>
</template>
<script>
export default {
  name: 'AutomaticSalaryPaymentRemarkDetailsDrawer',
  props: {
    row: {
      type: Object,
      required: true
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
  <style scope.d lang="scss">
  .team-bill-content {
      padding: 0px 10px;
      .content {
        margin-bottom: 10px;
      }

      .remark {
        width: 100%;
        overflow: auto;
        word-wrap: break-word;
        .nickname {
          font-weight: bold;
        }
      }
      .bill-row {
        padding: 10px 0px;
        >.bill-col {
          width: 50%;
        }
      }
      .policy {
        .policy-row {
          color: #333333;
          .title {
            padding: 5px 15px !important;
            background-color: transparent !important;
          }
          .policy-content {
            text-align: center;
            .policy-block {
              border: 1px solid #FFFFFF;
              width: 33.33%;
              padding: 10px;
              background-color: #F1F2F3;
              .label {
                padding-bottom: 5px;
              }
            }
          }

        }
      }
      .bank-card-list {
        .back-green {
          background-image: linear-gradient(to left, #018AA8 0%, #00B39F 100%);
        }
        .back-blue {
          background-image: linear-gradient(to left, #2D61CF 0%, #3893E6 100%);
        }
        .bank-card-item {
          color: #FFFFFF;
          margin-bottom: .2rem;
          border-radius: .2rem;
          padding: 10px;
          .card-info {
            .card-icon {
              height: 50px;
              width: 50px;
              flex-shrink: 0;
              img {
                width: 100%;
                height: 100%;
                border-radius: 100%;
              }
            }
            .profile {
              width: 100%;
              padding: 0px 10px;
              .payee {
                padding: 5px 0px;
              }
            }
          }
        }
      }
  }
  </style>

