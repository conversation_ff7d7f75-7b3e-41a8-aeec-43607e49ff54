<template>
  <div class="team-bill-drawer">
    <el-drawer
      title="详情"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div class="team-bill-content">

        <div class="blockquote">团长</div>
        <div class="content">
          <user-table-exhibit :user-profile="row.teamOwnProfile" :query-details="true" />
        </div>
        <div class="blockquote">成员</div>
        <div class="content">
          <user-table-exhibit :user-profile="row.teamMemberProfile" :query-details="true" />
        </div>
        <div v-if="row.recycled" class="blockquote">退款用户</div>
        <div v-if="row.recycled" class="content">
          <user-table-exhibit :user-profile="row.refundUser" :query-details="true" />
        </div>

        <div class="blockquote">账单信息</div>
        <div class="content">
          <div class="flex-b bill-row">
            <div class="left bill-col nowrap-ellipsis">
              工资凭据ID: {{ row.id }}
            </div>
            <div class="right bill-col nowrap-ellipsis">
              区域: {{ row.teamRegionName }}
            </div>
          </div>

          <div class="flex-b bill-row">
            <div class="left bill-col nowrap-ellipsis">
              工资($): {{ row.salary || 0 }}
            </div>
            <div class="right bill-col nowrap-ellipsis">
              工资接收方: {{ row.anchor ? '成员' : '团长' }}
            </div>
          </div>

          <div class="flex-b bill-row">
            <div class="left bill-col nowrap-ellipsis">
              是否团长代收: {{ row.hostSalaryToAgent ? '是' : '否' }}
            </div>
            <div class="right bill-col nowrap-ellipsis">
              是否已收回: {{ row.recycled ? '是' : '否' }}
            </div>
          </div>

          <div class="flex-b bill-row">
            <div class="left bill-col nowrap-ellipsis">
              实际收款账户: {{ row.hostSalaryToAgent ? '团长账户' : (row.anchor ? '成员账户' : '团长账户') }}
            </div>
            <div class="left bill-col nowrap-ellipsis">
              <el-tag v-if="row.recycled" type="danger" effect="plain">实际扣款账户: {{ row.hostSalaryToAgent ? '团长账户' : (row.anchor ? '成员账户' : '团长账户') }}</el-tag>
            </div>
          </div>

          <div class="bill-row">
            <div class="blockquote">备注</div>
            <div class="content">
              <el-timeline :reverse="true">
                <el-timeline-item
                  v-for="(item, index) in row.remarks"
                  :key="index"
                  :timestamp="item.createTime"
                >
                  <div class="remark">
                    {{ item.remark }}
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>
          </div>
        </div>

        <div>
          <div class="blockquote">政策: Lv.{{ row.policy.level }}</div>
          <div class="content">
            <div class="policy">
              <div v-if="row.policy" class="policy-row">
                <div class="policy-content flex-l flex-wrap">
                  <div class="policy-block">
                    <div class="label">Time(Hours)</div>
                    <div class="value">{{ row.policy.onlineTime }}</div>
                  </div>

                  <div class="policy-block">
                    <div class="label">GiftValue</div>
                    <div class="value">{{ row.policy.target }}</div>
                  </div>

                  <div class="policy-block">
                    <div class="label">MemberSalary</div>
                    <div class="value">{{ row.policy.memberSalary }}</div>
                  </div>

                  <div class="policy-block">
                    <div class="label">OwnSalary</div>
                    <div class="value">{{ row.policy.ownSalary }}</div>
                  </div>

                  <div class="policy-block">
                    <div class="label">TotalSalary</div>
                    <div class="value">{{ row.policy.totalSalary }}</div>
                  </div>

                </div>
                <div v-if="row.policy.propsRewards" class="props-reward">
                  <props-row :list="row.policy.propsRewards" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </el-drawer>
  </div>
</template>
<script>

import PropsRow from '@/components/data/PropsRow'
export default {
  name: 'AutomaticSalaryPaymentPolicyDetailsDrawer',
  components: { PropsRow },
  props: {
    row: {
      type: Object,
      required: true
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
  <style scope.d lang="scss">
  .team-bill-content {
      padding: 0px 10px;
      .content {
        margin-bottom: 10px;
      }

      .remark {
        width: 100%;
        overflow: auto;
        word-wrap: break-word;
        .nickname {
          font-weight: bold;
        }
      }
      .bill-row {
        padding: 10px 0px;
        >.bill-col {
          width: 50%;
        }
      }
      .policy {
        .policy-row {
          color: #333333;
          .title {
            padding: 5px 15px !important;
            background-color: transparent !important;
          }
          .policy-content {
            text-align: center;
            .policy-block {
              border: 1px solid #FFFFFF;
              width: 33.33%;
              padding: 10px;
              background-color: #F1F2F3;
              .label {
                padding-bottom: 5px;
              }
            }
          }

        }
      }
      .bank-card-list {
        .back-green {
          background-image: linear-gradient(to left, #018AA8 0%, #00B39F 100%);
        }
        .back-blue {
          background-image: linear-gradient(to left, #2D61CF 0%, #3893E6 100%);
        }
        .bank-card-item {
          color: #FFFFFF;
          margin-bottom: .2rem;
          border-radius: .2rem;
          padding: 10px;
          .card-info {
            .card-icon {
              height: 50px;
              width: 50px;
              flex-shrink: 0;
              img {
                width: 100%;
                height: 100%;
                border-radius: 100%;
              }
            }
            .profile {
              width: 100%;
              padding: 0px 10px;
              .payee {
                padding: 5px 0px;
              }
            }
          }
        }
      }
  }
  </style>

