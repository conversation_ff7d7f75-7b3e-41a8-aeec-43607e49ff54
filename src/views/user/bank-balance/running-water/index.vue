<template>
  <div :class="'app-container'">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <account-input v-model="listQuery.userId" :sys-origin="listQuery.sysOrigin" placeholder="用户ID" />
      </div>
      <el-select
        v-model="listQuery.type"
        placeholder="类型"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="收入" :value="0" />
        <el-option label="支出" :value="1" />
      </el-select>
      <el-select
        v-model="listQuery.event"
        placeholder="事件"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in userBankWaterEvent"
          :key="index"
          :label="item.name"
          :value="item.value"
        />
      </el-select>

      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="日期开始"
          end-placeholder="日期结束"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-alert
      v-if="isCalculateTotalAmount"
      v-loading="isCalculateTotalLoading"
      :title="'金额($): '+ calculateTotalValue"
      type="success"
      effect="dark"
      style="font-weight: bold;padding: 10px;"
      :closable="false"
    />
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="sysOrigin" label="系统" align="center" width="80">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column label="用户" align="center" min-width="80">
        <template slot-scope="scope">
          <div><user-table-exhibit :size="scope.row.userProfile ? 'mini' : 'small'" :user-profile="scope.row.userProfile" :query-details="true" /></div>
          <div v-if="scope.row.tmpUserProfile" class="attached">
            <user-table-exhibit size="mini" :user-profile="scope.row.tmpUserProfile" :query-details="true" :tag-name="getTransferDescriptionName(scope.row.event)" />
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="typeName" label="类型" align="center" width="80" />
      <el-table-column label="数量" align="center" min-width="80">
        <template slot-scope="scope">
          <div>
            <span v-if="scope.row.type === 0" class="font-danger">+{{ scope.row.amount }}</span>
            <span v-if="scope.row.type === 1">-{{ scope.row.amount }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="balance" label="余额" align="center" min-width="80" />
      <el-table-column label="事件" align="center">
        <template slot-scope="scope">
          <div>{{ getEventDesc(scope.row.event) }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" align="center" min-width="160" />
      <el-table-column prop="createUserOriginName" label="创建源" align="center" />
      <el-table-column prop="createTime" label="创建时间" align="center" width="160">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
    </el-table>
    <div v-if="listQuery.lastId" class="load-more">
      <span v-if="notData">已加载全部</span>
      <el-button v-else size="mini" :disabled="loadMoreLoading" :loading="loadMoreLoading" @click="clickLoadMore">加载更多</el-button>
    </div>
  </div>
</template>

<script>
import { pageRunningWater, calculateTotalAmount } from '@/api/app-user-bank-balance'
import { pickerOptions } from '@/constant/el-const'
import { mapGetters } from 'vuex'
import { userBankWaterEvent } from '@/constant/user'
export default {
  name: 'UserBankRunningWater',
  data() {
    return {
      userBankWaterEvent,
      rangeDate: [],
      thatSelectedUserId: '',
      thatSelectedAcceptUserId: '',
      searchDisabled: false,
      pickerOptions,
      isCalculateTotalLoading: false,
      isCalculateTotalAmount: false,
      calculateTotalValue: '0',
      listQuery: {
        limit: 20,
        userId: '',
        type: '',
        event: '',
        sysOrigin: 'MARCIE',
        lastId: '',
        startTime: '',
        endTime: ''
      },
      listLoading: false,
      list: [],
      notData: false,
      loadMoreLoading: false
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    }
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isReset) {
      const that = this
      if (!that.listQuery.sysOrigin) {
        return
      }
      if (isReset === true) {
        that.list = []
        that.notData = false
        that.listQuery.lastId = ''
      }
      that.listLoading = true
      pageRunningWater(that.listQuery).then(res => {
        that.listLoading = false
        that.loadMoreLoading = false
        const { body } = res
        const list = body || []
        that.notData = list.length <= 0
        if (!that.notData) {
          that.list = that.list.concat(list)
          that.listQuery.lastId = that.list[that.list.length - 1].id
        }
      }).catch(er => {
        that.listLoading = false
        that.loadMoreLoading = false
      })
      that.isShowAlertMoney()
    },
    getTransferDescriptionName(event) {
      if (event === 'TRANSFER') {
        return '收款人'
      }
      if (event === 'RECEIVE_TRANSFER') {
        return '付款人'
      }
      return ''
    },
    handleSearch() {
      this.renderData(true)
    },
    clickLoadMore() {
      const that = this
      that.loadMoreLoading = true
      that.renderData()
    },
    getEventDesc(_key) {
      const that = this
      const result = that.userBankWaterEvent.filter(_event => _event.value === _key)
      if (!result) {
        return _key
      }
      return result[0].name
    },
    isShowAlertMoney() {
      const that = this
      that.isCalculateTotalAmount = that.listQuery.sysOrigin !== '' && that.listQuery.type !== '' && that.listQuery.userId !== '' && that.listQuery.event !== '' && that.listQuery.startTime !== '' && that.listQuery.endTime !== ''
      // 计算额度
      if (that.isCalculateTotalAmount) {
        that.isCalculateTotalLoading = true
        calculateTotalAmount(that.listQuery).then(res => {
          that.isCalculateTotalLoading = false
          const { body } = res
          that.calculateTotalValue = body
        }).catch(er => {
          that.isCalculateTotalLoading = false
        })
      }
    }
  }
}
</script>
<style scoped lang="scss">
  .user-css {
    display: flex;
    align-items: center;
    color: #909399;
    font-size: .15rem;
    line-height: .3rem;
    padding: 0.02rem;
    border-radius: 0.04rem;
  }
  .load-more {
    padding: 20px;
    text-align: center;
  }
</style>
