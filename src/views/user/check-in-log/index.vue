<!--签到记录表格-->
<template>
  <div class="app-container">
    <div class="filter-containers">
      <div class="filter-item">
        <account-input v-model="listQuery.userId" placeholder="用户ID" />
      </div>
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="发送日期开始"
          end-placeholder="发送日期结束"
        />
      </div>
      <el-button
        :loading="searchLoading"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="userId" label="用户ID" align="center" />
      <el-table-column label="昵称" align="center">
        <template slot-scope="scope">
          <el-link v-if="scope.row.userNickname" @click="queryUserDetails(scope.row.userId)">{{ scope.row.userNickname }}</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="userSexName" label="性别" align="center" />
      <el-table-column prop="checkInNumber" label="连续签到天数" align="center" />
      <el-table-column label="签到奖励(道具/糖果数)" align="center">
        <template slot-scope="scope">
          <el-image
            v-if="scope.row.propsId"
            style="width: 50px; height: 50px"
            :src="scope.row.propsSourceRecord ? scope.row.propsSourceRecord.cover : ''"
          />
          <span v-else>{{ scope.row.quantity }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="签到时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
    <user-deatils-drawer
      v-if="userDeatilsDrawerVisible"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawerVisible=false"
    />
  </div>
</template>

<script>

import { pageUserCheckLog } from '@/api/approval'
import Pagination from '@/components/Pagination'
import { pickerOptions } from '@/constant/el-const'

export default {
  components: { Pagination },
  data() {
    return {
      pickerOptions,
      searchDisabled: false,
      list: [],
      userDeatilsDrawerVisible: false,
      thatSelectedUserId: '',
      userRegisterInfo: {},
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: '',
        startTime: '',
        endTime: ''
      },
      total: 0,
      searchLoading: false,
      listLoading: false,
      rangeDate: []
    }
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    }
  },
  created() {
    this.renderData()
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      pageUserCheckLog(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.searchLoading = that.listLoading = false
      }).catch(er => {
        that.searchLoading = that.listLoading = false
      })
    },

    handleSearch() {
      this.searchLoading = true
      this.renderData()
    },
    queryUserDetails(id) {
      this.userDeatilsDrawerVisible = true
      this.thatSelectedUserId = id
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>

<style scoped lang='scss'>
  .filter-containers {
    padding-bottom: 10px;
    float:left;

    .filter-item {
      display: inline-block;
      vertical-align: middle;
      margin-bottom: 10px;
    }
  }
</style>
