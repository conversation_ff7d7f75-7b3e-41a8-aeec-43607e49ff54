<template>
  <div class="app-container">
    <div class="filter-container">
      <div class="filter-item">
        <el-autocomplete
          v-model="listQuery.origin"
          popper-class="my-autocomplete"
          :fetch-suggestions="querySearch"
          placeholder="请输入或选择内容"
          clearable
          @select="handleSelect"
          @clear="handleSelect"
        >
          <i
            slot="suffix"
            class="el-icon-edit el-input__icon"
          />
          <template slot-scope="{ item }">
            <span :label="item.name">{{ item.name }}</span>
            <div :key="item.value" :value="item.value" style="font-size:12px; color:#d8d0d0">{{ item.value }}</div>
          </template>
        </el-autocomplete>
      </div>
      <el-input
        v-model.trim="listQuery.propsId"
        v-number
        placeholder="道具ID"
        style="width: 200px;"
        class="filter-item"
      />
      <div class="filter-item">
        <account-input v-model="listQuery.buyerId" placeholder="购买人ID" />
      </div>

      <div class="filter-item">
        <account-input v-model="listQuery.receiverId" placeholder="接收人ID" />
      </div>
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="创建日期开始"
          end-placeholder="创建日期结束"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>

    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <!-- <el-table-column prop="id" label="流水ID" align="center" /> -->
      <el-table-column label="购买人" align="center" min-width="200">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.buyerUserProfile" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column prop="propsCandy" label="金额" align="center" min-width="100" />
      <el-table-column label="接收人" align="center" min-width="200">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.receiverUserProfile" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column prop="propsOriginDesc" label="来源描述" align="center" min-width="200" />
      <el-table-column label="封面" align="center" min-width="80">
        <template slot-scope="scope">
          <el-image
            :src="handleImageUrl(scope.row)"
            style="width: 50px; height: 50px; margin: 0px 10px 10px 0px"
          />
        </template>
      </el-table-column>
      <el-table-column prop="propsId" label="道具ID" align="center" min-width="200" />
      <el-table-column prop="propsName" label="道具名称" align="center" min-width="200" />
      <el-table-column prop="createTime" label="创建时间" align="center" width="200">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <user-deatils-drawer
      v-if="userDeatilsDrawer"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawer=false"
    />

  </div>
</template>

<script>
import { userPropsTable } from '@/api/user'
import Pagination from '@/components/Pagination'
import { pickerOptions } from '@/constant/el-const'
import { propsOrigins } from '@/constant/type'
export default {
  name: 'UserPropsTable',
  components: { Pagination },
  data() {
    return {
      thatRow: {},
      userDeatilsDrawer: false,
      thatSelectedUserId: '',
      pickerOptions,
      propsOrigins,
      list: [],
      checkList: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        startCreateDate: '',
        endCreateDate: '',
        buyerAccount: '',
        receiverAccount: '',
        origin: ''
      },
      listLoading: true,
      rangeDate: ''
    }
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startCreateDate = newVal[0]
          this.listQuery.endCreateDate = newVal[1]
          return
        }
        this.listQuery.startCreateDate = ''
        this.listQuery.endCreateDate = ''
      }
    }
  },
  created() {
    this.renderData(true)
  },
  methods: {
    handleImageUrl(row) {
      return row.propsCover ? row.propsCover : ''
    },
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      userPropsTable(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    queryUserDetails(userId) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = userId
    },
    handleSearch() {
      if (this.listQuery.origin !== '') {
        const origins = this.propsOrigins.filter(this.createFilter(this.listQuery.origin))
        this.listQuery.origin = origins.length > 0 ? origins[0].value : this.listQuery.origin
      }
      this.renderData(true)
    },
    querySearch(queryString, cb) {
      var restaurants = this.propsOrigins
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
      cb(results)
    },
    handleSelect(item) {
      this.renderData(true)
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) >= 0) || (restaurant.name.toLowerCase().indexOf(queryString.toLowerCase()) >= 0)
      }
    }
  }
}
</script>
