<template>
  <div>
    <div class="filter-container">
      <account-input v-model="listQuery.userId" placeholder="用户ID" class="filter-item" />
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="badgeConfigId" label="徽章ID" align="center" />
      <el-table-column label="徽章图标" align="center">
        <template slot-scope="scope">
          <el-image
            style="width: 50px; height: 50px"
            :src="scope.row.selectUrl"
            :preview-src-list="[scope.row.selectUrl]"
          />
        </template>
      </el-table-column>
      <el-table-column prop="badgeName" label="徽章名称" align="center" />
      <el-table-column prop="typeName" label="徽章类型" align="center" />
      <el-table-column prop="createTime" label="创建时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button type="text" @click.native="handleRetrieveBadge(scope.row.badgeConfigId)">收回</el-button>
        </template>
      </el-table-column>
    </el-table>

  </div>
</template>

<script>
import { listUserNotAchieveBadge, retrieveBadge } from '@/api/badge'
export default {
  name: 'BadgeGive',
  data() {
    return {
      list: [],
      listQuery: {
        userId: ''
      },
      formVisible: false,
      textOptTitle: '',
      submitLoading: false
    }
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      that.$message({
        message: '查询中，请等待结果',
        type: 'warning'
      })
      listUserNotAchieveBadge(that.listQuery.userId).then(res => {
        that.listLoading = false
        const { body } = res
        that.list = body
      }).catch(er => {
        that.listLoading = false
      })
    },
    renderDataSuccess() {
      this.$message({
        message: 'Successful',
        type: 'success'
      })
      this.renderData()
    },
    handleSearch() {
      this.renderData()
    },
    // 收回
    handleRetrieveBadge(badgeId) {
      this.$confirm('确定收回当前用户的徽章吗？', '提示', {
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        this.$message({
          message: '收回中，请等待结果',
          type: 'warning'
        })
        retrieveBadge(badgeId, this.listQuery.userId).then((res) => {
          this.listLoading = false
          this.$message({
            message: '成功收回',
            type: 'success'
          })
          this.renderData()
        })
      }).catch(() => {

      })
    }
  }
}
</script>
