
<template>
  <el-dialog
    :title="textOptTitle"
    :visible="true"
    :before-close="handleClose"
    width="80%"
  >
    <div v-loading="submitLoading" class="source-edit">
      <div v-for="(item, index) in platformRows" :key="index">
        <el-card v-if="permissionsSysOriginCode.includes(item.sysOrigin.value)" class="box-card">
          <div slot="header" class="clearfix">
            <div style="display: inline-block;">
              <div class="flex-l">
                <div><sys-origin-icon :icon="item.sysOrigin.value" :desc="item.value" /></div>
                <div style="padding: 0rem 10px;">{{ item.sysOrigin.value }} 徽章资源</div>
              </div>
            </div>
          </div>
          <div class="edit-content-form">
            <el-form ref="form" label-width="110px" inline>
              <el-form-item prop="selectUrl">
                <el-upload
                  :disabled="item.selectUrlUpload.loading"
                  :file-list="item.selectUrlUpload.fileList"
                  :class="{'upload-but-hide': !isShowUpload(item, 'selectUrlUpload')}"
                  action=""
                  list-type="picture-card"
                  :http-request="file => uploadSelectUrl(file, item)"
                  :show-file-list="!isShowUpload(item, 'selectUrlUpload')"
                  :on-remove="(file, fileList) => handleSelectUrlFileRemove(item)"
                  accept="image/*"
                >
                  <i slot="default" v-loading="item.selectUrlUpload.loading" class="el-icon-plus" />
                </el-upload>
                <div class="el-upload__tip">选中图</div>
              </el-form-item>
              <el-form-item prop="notSelectUrl">
                <el-upload
                  :disabled="item.notSelectUrlUpload.loading"
                  :file-list="item.notSelectUrlUpload.fileList"
                  :class="{'upload-but-hide': !isShowUpload(item, 'notSelectUrlUpload')}"
                  action=""
                  list-type="picture-card"
                  :http-request="file => uploadNotSelectUrl(file, item)"
                  :show-file-list="!isShowUpload(item, 'notSelectUrlUpload')"
                  :on-remove="(file, fileList) => handleNotSelectUrlFileRemove(item)"
                  accept="image/*"
                >
                  <i slot="default" v-loading="item.notSelectUrlUpload.loading" class="el-icon-plus" />
                </el-upload>
                <div class="el-upload__tip">没有选中图</div>
              </el-form-item>
              <el-form-item prop="animationUrl">
                <el-upload
                  action=""
                  :http-request="file => svgaSourceUpload(file, item)"
                  :on-remove="(file, fileList) => svgaSourceUploadRemove(item)"
                  :file-list="item.svgaSourceUpload.fileList"
                  :limit="1"
                  :on-exceed="handleExceed"
                >
                  <el-button size="small" type="primary">点击上传</el-button>
                  <div slot="tip" class="el-upload__tip">svga资源文件</div>
                </el-upload>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { saveOrUpdateBadgePicture } from '@/api/badge'
import { sysOriginPlatforms } from '@/constant/origin'
import { deepClone, getElementUiUploadFile } from '@/utils'
import { mapGetters } from 'vuex'
function getFormData() {
  return {
    id: '',
    badgeConfigId: '',
    sysOrigin: '',
    selectUrl: '',
    notSelectUrl: '',
    animationUrl: ''
  }
}
export default {
  name: 'BadgeSource',
  props: {
    row: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      tableRow: {},
      platformRows: [],
      sysOriginPlatforms,

      // ---
      pushTextHistoryLoading: false,
      pushTextHistoryVisible: false,
      fileList: [],
      pushTextHistory: [],
      badgeList: [],
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        badgeName: '',
        badgeKey: ''
      },
      formVisible: false,
      textOptTitle: '',
      form: getFormData(),
      submitLoading: false,
      rules: {
        selectUrl: [
          { required: true, message: '请上传选中的图片', trigger: 'change' }
        ],
        notSelectUrl: [
          { required: true, message: '请上传没选中的图片', trigger: 'change' }
        ]
      },
      listLoading: true,
      uploadLoading: false,
      uploadUrlLoading: false
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms']),
    permissionsSysOriginCode() {
      if (!this.permissionsSysOriginPlatforms) {
        return []
      }
      return this.permissionsSysOriginPlatforms.map(item => item.value)
    },
    isUpdate() {
      return this.row && this.row.id
    },
    isShowSelectUrlUpload() {
      return !this.form.smallCover
    }
  },
  watch: {
    row: {
      handler(newVal) {
        if (!newVal) {
          return
        }
        this.tableRow = deepClone(newVal)
        this.platformRows = this.initPlatformRow(newVal.badgePictures)
      },
      immediate: true
    }
  },
  methods: {
    isShowUpload(item, typeKey) {
      return !item[typeKey].uploadUrl
    },
    uploadSelectUrl(file, item) {
      const that = this
      item.selectUrlUpload.loading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.other).then(res => {
        item.selectUrlUpload.uploadUrl = that.$getAccessImgUrl(res.name)
        that.changeSelectUrl(item)
      }).catch(er => {
        item.selectUrlUpload.loading = false
      })
    },
    handleSelectUrlFileRemove(item) {
      item.selectUrlUpload.uploadUrl = ''
      this.changeSelectUrl(item)
    },
    changeSelectUrl(item) {
      item.selectUrlUpload.loading = true
      saveOrUpdateBadgePicture({
        sysOrigin: item.sysOrigin.value,
        badgeConfigId: item.source.badgeConfigId,
        selectUrl: item.selectUrlUpload.uploadUrl
      }).then(res => {
        item.selectUrlUpload.loading = false
      }).catch(er => {
        item.selectUrlUpload.loading = false
        console.error(er)
      })
    },
    uploadNotSelectUrl(file, item) {
      const that = this
      item.notSelectUrlUpload.loading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.other).then(res => {
        item.notSelectUrlUpload.uploadUrl = that.$getAccessImgUrl(res.name)
        that.changeNotSelectUrl(item)
      }).catch(er => {
        item.notSelectUrlUpload.loading = false
      })
    },
    handleNotSelectUrlFileRemove(item) {
      item.notSelectUrlUpload.uploadUrl = ''
      this.changeNotSelectUrl(item)
    },
    changeNotSelectUrl(item) {
      item.notSelectUrlUpload.loading = true
      saveOrUpdateBadgePicture({
        sysOrigin: item.sysOrigin.value,
        badgeConfigId: item.source.badgeConfigId,
        notSelectUrl: item.notSelectUrlUpload.uploadUrl
      }).then(res => {
        item.notSelectUrlUpload.loading = false
      }).catch(er => {
        item.notSelectUrlUpload.loading = false
        console.error(er)
      })
    },
    svgaSourceUpload(file, item) {
      const that = this
      item.svgaSourceUpload.loading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.other).then(res => {
        item.svgaSourceUpload.uploadUrl = that.$getAccessImgUrl(res.name)
        that.changeSvgaSourcetUrl(item)
      }).catch(er => {
        item.svgaSourceUpload.loading = false
      })
    },
    svgaSourceUploadRemove(item) {
      item.svgaSourceUpload.loading = true
      item.svgaSourceUpload.uploadUrl = ''
      this.changeSvgaSourcetUrl(item)
    },
    changeSvgaSourcetUrl(item) {
      saveOrUpdateBadgePicture({
        sysOrigin: item.sysOrigin.value,
        badgeConfigId: item.source.badgeConfigId,
        animationUrl: item.svgaSourceUpload.uploadUrl
      }).then(res => {
        item.svgaSourceUpload.loading = false
      }).catch(er => {
        item.svgaSourceUpload.loading = false
        console.error(er)
      })
    },
    initPlatformRow(badgePictures) {
      const group = {}
      const tmpBadgePictures = badgePictures || []
      tmpBadgePictures.forEach(item => {
        group[item.sysOrigin] = item
      })
      return sysOriginPlatforms.map(item => {
        const source = group[item.value] || { sysOrigin: item, badgeConfigId: this.tableRow.id }
        return {
          sysOrigin: item,
          source,
          selectUrlUpload: {
            loading: false,
            fileList: getElementUiUploadFile(source.selectUrl),
            uploadUrl: source.selectUrl
          },
          notSelectUrlUpload: {
            loading: false,
            fileList: getElementUiUploadFile(source.notSelectUrl),
            uploadUrl: source.notSelectUrl
          },
          svgaSourceUpload: {
            loading: false,
            fileList: getElementUiUploadFile(source.animationUrl),
            uploadUrl: source.animationUrl
          }
        }
      })
    },
    handleClose() {
      this.$emit('close')
    },
    handleExceed(files, fileList) {
      this.$opsMessage.warn(`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    }
  }
}
</script>

<style scoped lang="scss">
.source-edit {
    height: 500px;
    overflow: auto;
    .box-card {
        margin-bottom: 10px;
    }
}

</style>
