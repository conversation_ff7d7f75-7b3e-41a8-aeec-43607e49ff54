<template>
  <div>
    <div class="filter-container">
      <account-input v-model="listQuery.userId" placeholder="用户ID" class="filter-item" />
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="id" label="徽章规则ID" align="center" />
      <el-table-column label="徽章图标" align="center">
        <template slot-scope="scope">
          <el-image
            style="width: 50px; height: 50px"
            :src="scope.row.selectUrl"
            :preview-src-list="[scope.row.selectUrl]"
          />
        </template>
      </el-table-column>
      <el-table-column prop="badgeName" label="徽章名称" align="center" />
      <el-table-column prop="typeName" label="徽章类型" align="center" />
      <el-table-column prop="badgeKey" label="徽章key" align="center" />
      <el-table-column prop="badgeLevel" label="等级" align="center" />
      <el-table-column prop="milestone" label="可领取里程碑" align="center" />
      <el-table-column prop="createTime" label="创建时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button v-if="scope.row.presented" type="text" @click.native="handleRetrieveBadge(scope.row.id)">收回</el-button>
          <el-button v-else-if="!scope.row.presented" type="text" @click.native="handleGiveBadge(scope.row.id)">赠送</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
  </div>
</template>

<script>
import { giveBadgeTable, giveBadge, retrieveBadge } from '@/api/badge'
import Pagination from '@/components/Pagination'
export default {
  name: 'BadgeGive',
  components: { Pagination },
  data() {
    return {
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: ''
      },
      formVisible: false,
      textOptTitle: '',
      submitLoading: false
    }
  },
  methods: {
    renderData() {
      const that = this
      if (!that.listQuery.userId) {
        that.$message({
          message: '请输入用户id',
          type: 'warning'
        })
        return
      }
      that.listLoading = true
      that.$message({
        message: '查询中，请等待结果',
        type: 'warning'
      })
      giveBadgeTable(that.listQuery).then(res => {
        that.listLoading = false
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
      }).catch(er => {
        that.listLoading = false
      })
    },
    renderDataSuccess() {
      this.$message({
        message: 'Successful',
        type: 'success'
      })
      this.renderData()
    },
    handleSearch() {
      this.renderData()
    },
    // 赠送
    handleGiveBadge(badgeId) {
      this.$confirm('确定赠送徽章给当前用户吗？', '提示', {
        type: 'warning'
      }).then(() => {
        this.$message({
          message: '赠送中，请等待结果',
          type: 'warning'
        })
        this.listLoading = true
        giveBadge(badgeId, this.listQuery.userId).then((res) => {
          this.listLoading = false
          this.$message({
            message: '赠送成功',
            type: 'success'
          })
          this.renderData()
        })
      }).catch(() => {

      })
    },
    // 收回
    handleRetrieveBadge(badgeId) {
      this.$confirm('确定收回当前用户徽章吗？', '提示', {
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        this.$message({
          message: '收回中，请等待结果',
          type: 'warning'
        })
        retrieveBadge(badgeId, this.listQuery.userId).then((res) => {
          this.listLoading = false
          this.$message({
            message: '成功收回',
            type: 'success'
          })
          this.renderData()
        })
      }).catch(() => {

      })
    }
  }
}
</script>
