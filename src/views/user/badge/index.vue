<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="item in tables" :key="item.name" :label="item.title" :name="item.component" />
      <component :is="activeName" />
    </el-tabs>
  </div>
</template>
<script>
import BadgeRule from './badge-rule'
import BadgeGive from './badge-give'
import BadgeRevoke from './badge-revoke'
import { mapGetters } from 'vuex'
export default {
  name: 'BadgeTab',
  components: { BadgeRule, BadgeGive, BadgeRevoke },
  data() {
    return {
      activeName: 'BadgeRule',
      tables: [
        {
          title: '徽章配置',
          component: 'BadgeRule'
        }
      ]
    }
  },
  computed: {
    ...mapGetters(['buttonPermissions'])
  },
  created() {
    if (this.buttonPermissions.includes('content:manager:badge:send:or:revoke')) {
      this.tables.push({
        title: '赠送or收回',
        component: 'BadgeGive'
      })
      this.tables.push({
        title: '收回',
        component: 'BadgeRevoke'
      })
    }
  }
}
</script>
