<template>
  <el-dialog
    title="用户银行账户流水"
    :visible="true"
    :before-close="handleClose"
    :close-on-click-modal="false"
    width="80%"
    top="20px"
  >
    <div class="content">
      <el-table
        v-loading="listLoading"
        :data="list"
        :before-close="handleClose"
        element-loading-text="Loading"
        fit
        highlight-current-row
        max-height="350px"
      >
        <el-table-column prop="sysOrigin" label="系统" align="center">
          <template slot-scope="scope">
            <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
          </template>
        </el-table-column>
        <el-table-column label="用户" align="center" min-width="80">
          <template slot-scope="scope">
            <div><user-table-exhibit :size="scope.row.userProfile ? 'mini' : 'small'" :user-profile="scope.row.userProfile" :query-details="true" /></div>
            <div v-if="scope.row.tmpUserProfile" class="attached">
              <user-table-exhibit size="mini" :user-profile="scope.row.tmpUserProfile" :query-details="true" :tag-name="getTransferDescriptionName(scope.row.event)" />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="typeName" label="类型" align="center" width="80" />
        <el-table-column label="数量" align="center" min-width="80">
          <template slot-scope="scope">
            <div>
              <span v-if="scope.row.type === 0" class="font-danger">+{{ scope.row.amount }}</span>
              <span v-if="scope.row.type === 1">-{{ scope.row.amount }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="balance" label="余额" align="center" />
        <el-table-column label="事件" align="center">
          <template slot-scope="scope">
            <div>{{ getEventDesc(scope.row.salaryEvent) }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" align="center" min-width="200" />
        <el-table-column prop="createTime" label="创建时间" align="center" width="160">
          <template slot-scope="scope">
            {{ scope.row.createTime | dateFormat }}
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="listQuery.cursor"
        :limit.sync="listQuery.limit"
        @pagination="renderData"
      />
    </div>
  </el-dialog>
</template>

<script>
import { pageRunningWater } from '@/api/app-user-salary-diamond-balance'
import Pagination from '@/components/Pagination'
import { userSalaryDiamondWaterEvent } from '@/constant/user'
export default {
  name: 'SalaryDiamondRunningWaterDrawer',
  components: { Pagination },
  props: {
    row: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      userSalaryDiamondWaterEvent,
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: '',
        sysOrigin: ''
      },
      listLoading: false,
      total: 0,
      list: []
    }
  },
  watch: {
    row: {
      handler(newVal) {
        this.listQuery.userId = newVal.userId
        this.listQuery.sysOrigin = newVal.sysOrigin
        this.renderData()
      },
      immediate: true
    }
  },
  methods: {
    renderData(isReset) {
      const that = this
      if (!that.listQuery.sysOrigin) {
        return
      }
      if (isReset === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      pageRunningWater(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      }).catch(er => {
        that.listLoading = false
      })
    },
    getTransferDescriptionName(event) {
      if (event === 'TRANSFER') {
        return '收款人'
      }
      if (event === 'RECEIVE_TRANSFER') {
        return '付款人'
      }
      return ''
    },
    handleClose() {
      this.$emit('close')
    },
    getEventDesc(_key) {
      const that = this
      const result = that.userSalaryDiamondWaterEvent.filter(_event => _event.value === _key)
      if (!result) {
        return _key
      }
      return result[0].name
    }
  }
}
</script>
<style scoped lang="scss">
  .load-more {
    padding: 20px;
    text-align: center;
  }
</style>
