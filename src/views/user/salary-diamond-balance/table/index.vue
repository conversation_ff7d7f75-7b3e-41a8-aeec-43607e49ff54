<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>

      <div class="filter-item">
        <account-input v-model="listQuery.userId" :sys-origin="listQuery.sysOrigin" placeholder="用户ID" />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        添加
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
      @cell-mouse-enter="handleMouseEnter"
    >
      <el-table-column type="index" width="50" label="No" />
      <el-table-column prop="sysOrigin" label="系统" align="center" width="80">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column label="用户" align="center" min-width="200">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column prop="earnPoints" label="获得总额" align="center" min-width="100" />
      <el-table-column prop="consumptionPoints" label="消费总额" align="center" min-width="100" />
      <el-table-column prop="balance" label="余额" align="center" min-width="100" />
      <el-table-column prop="createTime" label="创建时间" align="center" width="160">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" label="修改时间" align="center" width="160">
        <template slot-scope="scope">
          {{ scope.row.updateTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center">
        <template slot-scope="scope">
          <el-dropdown>
            <span class="el-dropdown-link">
              <i class="el-icon-more" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="showGoldInputBox(scope.row, true)">发送</el-dropdown-item>
              <el-dropdown-item @click.native="showGoldInputBox(scope.row, false)">扣除</el-dropdown-item>

              <el-dropdown-item @click.native="showTransferBox(scope.row)">转账</el-dropdown-item>
              <el-dropdown-item @click.native="clickRunningWater(scope.row)">流水</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
    <form-edit
      v-if="formEditVisable"
      :row="thatRow"
      :sys-origin="listQuery.sysOrigin"
      @close="formEditClose"
      @success="formEditSuccess"
    />
    <div class="gold-input-box">
      <el-dialog
        :title=" type ? '发送' : '扣除'"
        :visible="goldInputBoxVisible"
        width="400px"
      >
        <el-form
          ref="form"
          :rules="formRules"
          :model="formData"
          label-position="left"
          label-width="70px"
          style="width: 300px; margin-left:50px;"
        >
          <el-form-item label="金额" prop="quantity">
            <el-input
              v-model.trim="formData.quantity"
              :placeholder="'请输入' + (type ? '发送' : '扣除') + '工资钻石'"
            />
          </el-form-item>

          <el-form-item label="对内备注" prop="remark">
            <el-input
              v-model.trim="formData.remark"
              placeholder="请输入对内备注"
            />
          </el-form-item>

        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="hideGoldInputBox()">
            取消
          </el-button>
          <el-button
            v-loading="listLoading"
            type="primary"
            @click="goldSubmit()"
          >
            提交
          </el-button>
        </div>
      </el-dialog>
    </div>
    <form-edit
      v-if="formEditVisable"
      :row="thatRow"
      :sys-origin="listQuery.sysOrigin"
      @close="formEditClose"
      @success="formEditSuccess"
    />
    <running-water
      v-if="runningWaterVisible"
      :row="thatRow"
      @close="runningWaterVisible=false"
    />
    <div class="gold-input-box">
      <el-dialog
        title="转账"
        :visible="transferVisible"
        width="500px"
      >
        <el-form
          ref="form"
          :rules="transferFormRules"
          :model="transferFormData"
          label-position="left"
          label-width="70px"
          style="width: 400px; margin-left:50px;"
        >

          <el-form-item prop="acceptUserId" label="收款人">
            <account-input ref="transferAccountInput" v-model="transferFormData.acceptUserId" :sys-origin="transferFormData.sysOrigin" placeholder="用户账号" />
          </el-form-item>

          <el-form-item label="金额" prop="quantity">
            <el-input
              v-model.trim="transferFormData.quantity"
              :placeholder="'请输入转账工资钻石'"
            />
          </el-form-item>

          <el-form-item label="对内备注" prop="remark">
            <el-input
              v-model.trim="transferFormData.remark"
              placeholder="请输入对内备注"
            />
          </el-form-item>

        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="hideTransferBox()">
            取消
          </el-button>
          <el-button
            v-loading="listLoading"
            type="primary"
            @click="transferSubmit()"
          >
            提交
          </el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { pageSalaryDiamond, deductSalaryDiamond, sendSalaryDiamond, transferSalaryDiamond } from '@/api/app-user-salary-diamond-balance'
import Pagination from '@/components/Pagination'
import { mapGetters } from 'vuex'
import FormEdit from './form-edit'
import RunningWater from './running-water'

export default {
  name: 'UserSalaryDiamondBalance',
  components: { Pagination, FormEdit, RunningWater },
  data() {
    const commonRules = [
      { required: true, message: '必填字段', trigger: 'blur' }
    ]
    return {
      runningWaterVisible: false,
      thatRow: null,
      userInfo: {},
      transferVisible: false,
      searchDisabled: false,
      goldInputBoxVisible: false,
      // true：发送  false：扣除
      type: false,
      thatSelectedUserId: '',
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: '',
        sysOrigin: 'HALAR',
        startTime: '',
        endTime: ''
      },
      formData: {
        userId: '',
        quantity: '',
        sysOrigin: '',
        remark: ''
      },
      transferFormData: {
        userId: '',
        acceptUserId: '',
        quantity: '',
        sysOrigin: '',
        remark: ''
      },
      formRules: {
        userId: commonRules,
        quantity: { required: true, message: '必填字段不可为空', trigger: 'blur' },
        sysOrigin: commonRules
      },
      transferFormRules: {
        userId: commonRules,
        acceptUserId: commonRules,
        quantity: { required: true, message: '必填字段不可为空', trigger: 'blur' },
        sysOrigin: commonRules
      },
      listLoading: false,
      list: [],
      total: 0,
      formEditVisable: false
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isReset) {
      const that = this
      if (!that.listQuery.sysOrigin) {
        return
      }
      if (isReset === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      pageSalaryDiamond(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      }).catch(er => {
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    handleCreate() {
      this.thatRow = null
      this.formEditVisable = true
    },
    handleClose() {
      this.$emit('close')
    },
    clickRunningWater(details) {
      this.thatRow = details
      this.runningWaterVisible = true
    },
    handleMouseEnter(row) {
      this.thatRow = row
    },
    formEditClose() {
      this.formEditVisable = false
    },
    formEditSuccess() {
      this.formEditVisable = false
      this.renderData(true)
    },
    showGoldInputBox(row, type) {
      const that = this
      that.type = type

      that.formData = {
        userId: row.userId,
        quantity: '',
        sysOrigin: row.sysOrigin,
        remark: ''
      }
      that.goldInputBoxVisible = true
    },
    showTransferBox(row) {
      const that = this

      that.transferFormData = {
        userId: row.userId,
        acceptUserId: '',
        quantity: '',
        sysOrigin: row.sysOrigin,
        remark: ''
      }
      that.transferVisible = true
    },
    hideGoldInputBox() {
      const that = this
      that.goldInputBoxVisible = false
    },
    hideTransferBox() {
      const that = this
      that.transferVisible = false
    },
    goldSubmit() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return
        }
        that.listLoading = true
        if (that.type) {
          sendSalaryDiamond(that.formData).then(res => {
            //that.$opsMessage.success()
            that.goldInputBoxVisible = false
            that.listLoading = false
            that.renderData(true)
          }).catch(er => {
            that.listLoading = false
            //that.$opsMessage.success()
            console.error(er)
          })
          return
        }
        deductSalaryDiamond(that.formData).then(res => {
          //console.log(111);
          that.$opsMessage.success()
          that.goldInputBoxVisible = false
          that.listLoading = false
          that.renderData(true)
        }).catch(er => {
          that.listLoading = false
          //that.$opsMessage.success()
          console.error(er)
        })
      })
    },
    transferSubmit() {
      const that = this
      if(that.transferFormData.quantity <= 0) {
        that.$message.error('金额不能小于0')
        return
      }
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return
        }
        that.listLoading = true
        transferSalaryDiamond(that.transferFormData).then(res => {
          that.$opsMessage.success()
          that.transferVisible = false
          that.$refs.transferAccountInput.clearValue()
          that.listLoading = false
          that.renderData(true)
        }).catch(er => {
          that.listLoading = false
          console.error(er)
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">

.load-more {
  padding: 20px;
  text-align: center;
}
</style>
