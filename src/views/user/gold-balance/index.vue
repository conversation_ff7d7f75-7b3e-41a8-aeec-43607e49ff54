<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <div><account-input v-model="listQuery.userId" placeholder="用户ID" type="USER" :sys-origin="listQuery.sysOrigin" /></div>
      </div>

      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="用户" align="center" min-width="200">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" :show-sys-origin="true" />
        </template>
      </el-table-column>
      <el-table-column prop="balance" label="余额" align="center" min-width="100" />
      <!-- <el-table-column fixed="right" label="操作" align="center" width="60">
        <template slot-scope="scope">
          <el-button type="text" @click="clickQueryDetails(scope.row)">详情</el-button>
        </template>
      </el-table-column> -->
    </el-table>
  </div>
</template>

<script>

import { listUserCandyTop } from '@/api/app-user'
import { mapGetters } from 'vuex'

export default {
  name: 'GiftHistory',
  data() {
    return {
      list: [],
      rangeDate: [],
      listQuery: {
        limit: 200,
        sysOrigin: '',
        userId: ''
      },
      listLoading: true,
      clickUserId: '',
      totalCount: '',
      totalCountLoading: false,
      giftAcceptQuantity: '',
      giftAcceptQuantityLoading: false,
      notMore: false
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData(true)
  },
  methods: {
    renderData(isReset) {
      const that = this
      that.listLoading = true
      if (isReset === true) {
        that.list = []
      }
      listUserCandyTop(that.listQuery).then(res => {
        that.listLoading = false
        const { body } = res
        that.list = body || []
      }).catch(er => {
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    }
  }
}
</script>
  <style scoped lang="scss">

  </style>

