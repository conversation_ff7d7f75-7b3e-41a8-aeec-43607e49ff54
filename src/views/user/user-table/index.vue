<template>
  <div class="app-container">
    <div v-if="buttonPermissions.includes('user:table:query:list')">
      <div class="filter-container">
        <el-select
          v-model="listQuery.del"
          style="width: 120px"
          class="filter-item"
          @change="handleSearch"
        >
          <el-option label="未注销" value="0" />
          <el-option label="已注销" value="1" />
        </el-select>
        <el-select
          v-model="listQuery.sysOrigins"
          placeholder="系统"
          style="width: 200px"
          class="filter-item"
          multiple
          collapse-tags
        >
          <el-option
            v-for="item in permissionsSysOriginPlatforms"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
            <span style="float: left;">
              <sys-origin-icon
                :icon="item.value"
                :desc="item.value"
              /></span>
            <span style="float: left;margin-left:10px">{{ item.label }}</span>
          </el-option>
        </el-select>
        <el-select
          v-if="showAllCondition"
          v-model="listQuery.authType"
          placeholder="注册来源"
          clearable
          style="width: 120px"
          class="filter-item"
          @change="handleSearch"
        >
          <el-option
            v-for="item in registerOrigins"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
        <el-select
          v-if="showAllCondition"
          v-model="listQuery.originPlatform"
          placeholder="来源平台"
          clearable
          style="width: 120px"
          class="filter-item"
          @change="handleSearch"
        >
          <el-option
            v-for="item in originPlatforms"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>

        <el-select
          v-if="showAllCondition"
          v-model="listQuery.userSex"
          placeholder="性别"
          clearable
          style="width: 120px"
          class="filter-item"
          @change="handleUserSexChange"
        >
          <el-option
            v-for="item in genders"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
        <el-autocomplete
          v-if="showAllCondition"
          v-model="countryCodeStr"
          popper-class="my-autocomplete"
          :fetch-suggestions="querySearch"
          placeholder="国家"
          style="width: 200px"
          class="filter-item"
          clearable
          @clear="handleDelCountryCodeClick"
          @select="handleCountryCodeClick"
        >
          <i
            v-if="!countryCodeStr"
            slot="suffix"
            class="el-icon-edit el-input__icon"
          />
          <template slot-scope="{ item }">
            <div class="name">{{ item.phonePrefix + ' ' + item.countryName }}</div>
          </template>
        </el-autocomplete>
        <el-select
          v-if="showAllCondition"
          v-model="listQuery.userType"
          placeholder="账号类型"
          clearable
          style="width: 120px"
          class="filter-item"
          @change="handleSearch"
        >
          <el-option
            v-for="item in userTypes"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
        <div class="filter-item">
          <el-date-picker
            v-model="rangeDate"
            value-format="timestamp"
            type="datetimerange"
            :picker-options="pickerOptions"
            range-separator="至"
            start-placeholder="注册开始日期"
            end-placeholder="注册结束日期"
          />
        </div>
        <el-input
          v-model.trim="listQuery.userIds[0]"
          v-number
          placeholder="长UID"
          style="width: 200px;"
          class="filter-item"
        />
        <el-input
          v-model.trim="listQuery.account"
          placeholder="短账号"
          style="width: 200px;"
          class="filter-item"
        />
        <el-input
          v-model.trim="listQuery.deviceId"
          placeholder="设备ID"
          style="width: 200px;"
          class="filter-item"
        />
        <el-button
          :loading="searchLoading"
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleSearch"
        >
          搜索
        </el-button>
        <el-checkbox
          v-model="showAllCondition"
          class="filter-item"
        >显示所有条件</el-checkbox>
        <el-button
          v-if="buttonPermissions.includes('props:config:sales:total')"
          class="filter-item"
          type="primary"
          icon="el-icon-monitor"
          @click="clickUserRegistrationOverview"
        >
          用户注册情况
        </el-button>
      </div>

      <el-table v-loading="listLoading" :data="list" fit highlight-current-row>
        <el-table-column
          label="用户"
          align="center"
          show-overflow-tooltip
          min-width="220"
        >
          <template slot-scope="scope">
            <user-table-exhibit
              :user-profile="scope.row.userProfile"
              :query-details="
                buttonPermissions.includes('user:table:query:details')
              "
            />
          </template>
        </el-table-column>
        <el-table-column prop="goldBalance" label="金币余额" align="center">
          <template slot-scope="scope">
            <el-link
              v-if="buttonPermissions.includes('user:table:query:gold')"
              @click.native="handleRunningWater(scope.row)"
            >
              <a :title="scope.row.goldBalance">
                {{ scope.row.goldBalance || "0" }}
              </a>
            </el-link>
            <div v-else>
              {{ scope.row.goldBalance }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="diamondBalance" label="钻石余额" align="center">
          <template slot-scope="scope">
            <el-link
              v-if="buttonPermissions.includes('user:table:query:gold')"
              @click.native="handleDiamondRunningWater(scope.row)"
            >
              <a :title="scope.row.diamondBalance">
                {{ scope.row.diamondBalance || "0" }}
              </a>
            </el-link>
            <div v-else>
              {{ scope.row.diamondBalance }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="salaryDiamondBalance" label="工资钻石余额" align="center">
          <template slot-scope="scope">
            <el-link
              v-if="buttonPermissions.includes('user:table:query:gold')"
              @click.native="handleDiamondSalaryRunningWater(scope.row)"
            >
              <a :title="scope.row.salaryDiamondBalance">
                {{ scope.row.salaryDiamondBalance || "0" }}
              </a>
            </el-link>
            <div v-else>
              {{ scope.row.salaryDiamondBalance }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="账户" align="center">
          <template slot-scope="scope">
            <el-link
              v-if="
                buttonPermissions.includes('user:table:query:account:handler')
              "
              @click.native="accountStatus(scope.row)"
            >
              <a
                :title="scope.row.userProfile.accountStatusName"
                :class="getAccountStatusFontColor(scope.row.userProfile)"
              >
                {{ scope.row.userProfile.accountStatusName }}
              </a>
            </el-link>
            <div
              v-else
              :class="getAccountStatusFontColor(scope.row.userProfile)"
            >
              {{ scope.row.userProfile.accountStatusName }}
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="国家" align="center" width="60">
          <template slot-scope="scope">
            <flag-icon class="flag" :code="scope.row.userProfile.countryCode" :tooltip="scope.row.userProfile.countryName" size="30" />
          </template>
        </el-table-column> -->

        <el-table-column label="注册来源" align="center" width="120">
          <template slot-scope="scope">
            <sys-origin-icon
              :icon="
                scope.row.userProfile.sysOriginChild ||
                  scope.row.userProfile.originSys
              "
              :desc="
                scope.row.userProfile.originSys ||
                  scope.row.userProfile.sysOriginChild
              "
            />
            <platform-svg-icon
              v-if="scope.row.userRegisterInfo"
              :icon="scope.row.userRegisterInfo.originPlatform"
              :desc="'注册平台：' + originText(scope.row)"
            />
            <platform-svg-icon
              v-if="scope.row.userRegisterInfo"
              :icon="scope.row.userRegisterInfo.authType"
              :desc="'注册来源：' + scope.row.userRegisterInfo.authType"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="编号"
          align="center"
          show-overflow-tooltip
          min-width="200"
        >
          <template slot-scope="scope">
            <div
              class="cursor-pointer"
              @click="() => copyTextContent(scope.row.userProfile.id)"
            >
              用户ID：{{ scope.row.userProfile.id }}
            </div>
            <div
              class="cursor-pointer"
              @click="() => copyTextContent(scope.row.userProfile.id)"
            >
              国家ID：{{ scope.row.userProfile.countryId }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="时间"
          align="center"
          show-overflow-tooltip
          min-width="220"
        >
          <template slot-scope="scope">
            <div>
              创建时间：{{ scope.row.userProfile.createTime | dateFormat }}
            </div>
            <div>最近活跃：{{ scope.row.lastActiveTime | dateFormat }}</div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" align="center" width="50">
          <template slot-scope="scope">
            <el-dropdown>
              <span class="el-dropdown-link">
                <i class="el-icon-more" />
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-if="buttonPermissions.includes('user:table:query:details')"
                  @click.native="queryUserDetails(scope.row)"
                >资料详情</el-dropdown-item>
                <el-dropdown-item
                  v-if="buttonPermissions.includes('user:table:edit')"
                  @click.native="editUserForm(scope.row)"
                >编辑用户</el-dropdown-item>

                <el-dropdown-item
                  v-if="buttonPermissions.includes('user:table:query:gold')"
                  @click.native="handleRunningWater(scope.row)"
                >金币收支</el-dropdown-item>
                <el-dropdown-item
                  v-if="
                    buttonPermissions.includes('user:table:query:violation')
                  "
                  @click.native="clickViolationTableVisible(scope.row)"
                >违规记录</el-dropdown-item>

                <el-dropdown-item
                  v-if="
                    buttonPermissions.includes(
                      'user:table:edit:account:handler'
                    )
                  "
                  @click.native="accountHanle(scope.row)"
                >账号处理</el-dropdown-item>
                <el-dropdown-item
                  v-if="
                    buttonPermissions.includes(
                      'user:table:query:account:handler'
                    )
                  "
                  @click.native="accountStatus(scope.row)"
                >账号处理记录</el-dropdown-item>

                <el-dropdown-item
                  v-if="
                    buttonPermissions.includes('user:table:edit:reward:gold')
                  "
                  @click.native="editUserGoldCoinRewardForm(scope.row)"
                >金币奖励</el-dropdown-item>
                <el-dropdown-item
                  v-if="
                    buttonPermissions.includes('user:table:edit:deduction:gold')
                  "
                  @click.native="editUserGoldCoinDeductionForm(scope.row)"
                >金币扣除</el-dropdown-item>
                <el-dropdown-item
                  v-if="
                    buttonPermissions.includes('user:table:edit:reward:gold')
                  "
                  @click.native="editUserDiamondRewardForm(scope.row)"
                >钻石奖励</el-dropdown-item>
                <el-dropdown-item
                  v-if="
                    buttonPermissions.includes('user:table:edit:deduction:gold')
                  "
                  @click.native="editUserDiamondDeductionForm(scope.row)"
                >钻石扣除</el-dropdown-item>
                <!--                <el-dropdown-item
                  v-if="
                    buttonPermissions.includes(
                      'user:table:edit:reward:gameCoupon'
                    )
                  "
                  @click.native="editUserGameCouponRewardForm(scope.row)"
                  >游戏券奖励</el-dropdown-item
                >
                <el-dropdown-item
                  v-if="
                    buttonPermissions.includes(
                      'user:table:edit:deduction:gameCoupon'
                    )
                  "
                  @click.native="editUserGameCouponDeductionForm(scope.row)"
                  >游戏券扣除</el-dropdown-item
                >-->
                <el-dropdown-item
                  v-if="
                    buttonPermissions.includes('user:table:add:otherRecharge')
                  "
                  @click.native="addOtherRecharge(scope.row)"
                >添加充值记录</el-dropdown-item>
                <el-dropdown-item
                  v-if="
                    buttonPermissions.includes('user:table:query:otherRecharge')
                  "
                  @click.native="addOtherRechargeDialog(scope.row)"
                >线下充值记录</el-dropdown-item>
                <el-dropdown-item
                  v-if="buttonPermissions.includes('user:table:auth:info')"
                  @click.native="handleUserAuthForm(scope.row)"
                >认证信息</el-dropdown-item>
                <el-dropdown-item
                  v-if="buttonPermissions.includes('user:table:edit:bankCard')"
                  @click.native="handleBankCard(scope.row)"
                >银行卡</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        :total="9999999999999999999"
        :page.sync="listQuery.pageIndex"
        :limit.sync="listQuery.pageSize"
        layout="prev,next,sizes"
        @pagination="renderData"
      />

      <gold-running-water-dialog
        v-if="candyDialogVisible"
        :user-id="clickUserId"
        @close="candyDialogVisible = false"
      />

      <diamond-running-water-dialog
        v-if="diamondDialogVisible"
        :user-id="clickUserId"
        @close="diamondDialogVisible = false"
      />

      <diamond-salary-running-water-dialog
        v-if="diamondSalaryDialogVisible"
        :user-id="clickUserId"
        @close="diamondSalaryDialogVisible = false"
      />

      <user-deatils-drawer
        v-if="userDeatilsDrawer"
        :user-id="thatSelectedUserId"
        @close="userDeatilsDrawer = false"
      />

      <account-hanle
        v-if="accountHandleVisible"
        :user-id="thatSelectedUserId"
        @success="renderDataSuccess"
        @close="accountHandleVisible = false"
      />

      <user-video-violation-table-dialog
        v-if="violationTableVisible"
        :row="thisRow"
        @close="violationTableVisible = false"
      />

      <edit-user-form
        v-if="editUserFormVisible"
        :user-id="thatSelectedUserId"
        @close="editUserFormVisible = false"
        @success="renderDataSuccess"
      />

      <edit-user-gold-coin-deduction-form
        v-if="editUserGoldCoinDeductionFormVisible"
        :user-id="thatSelectedUserId"
        @close="editUserGoldCoinDeductionFormVisible = false"
        @success="renderDataSuccess"
      />

      <edit-user-gold-coin-reward-form
        v-if="editUserGoldCoinRewardFormVisible"
        :user-id="thatSelectedUserId"
        @close="editUserGoldCoinRewardFormVisible = false"
        @success="renderDataSuccess"
      />
      <edit-user-diamond-deduction-form
        v-if="editUserDiamondDeductionFormVisible"
        :user-id="thatSelectedUserId"
        @close="editUserDiamondDeductionFormVisible = false"
        @success="renderDataSuccess"
      />

      <edit-user-diamond-reward-form
        v-if="editUserDiamondRewardFormVisible"
        :user-id="thatSelectedUserId"
        @close="editUserDiamondRewardFormVisible = false"
        @success="renderDataSuccess"
      />

      <edit-user-game-coupon-reward-form
        v-if="editUserGameCouponRewardFormVisible"
        :user-id="thatSelectedUserId"
        @close="editUserGameCouponRewardFormVisible = false"
        @success="renderDataSuccess"
      />

      <edit-user-game-coupon-deduction-form
        v-if="editUserGameCouponDeductionFormVisible"
        :user-id="thatSelectedUserId"
        @close="editUserGameCouponDeductionFormVisible = false"
        @success="renderDataSuccess"
      />

      <user-account-status-log-drawer
        v-if="accountStatusLogTable"
        :row="thisRow"
        @close="accountStatusLogTable = false"
      />

      <add-other-recharge
        v-if="otherRechargeVisible"
        :user-id="thatSelectedUserId"
        :sys-origin="
          thisRow.userProfile
            ? thisRow.sysOriginChild || thisRow.userProfile.originSys
            : ''
        "
        @close="otherRechargeVisible = false"
        @success="otherRechargeSuccess"
      />

      <add-other-recharge-dialog
        v-if="otherRechargeDialogVisible"
        :user-id="thatSelectedUserId"
        :sys-origin="
          thisRow.userProfile
            ? thisRow.sysOriginChild || thisRow.userProfile.originSys
            : ''
        "
        @close="otherRechargeDialogVisible = false"
      />

      <user-auth-form
        v-if="userAuthFormVisible"
        :user-id="thatSelectedUserId"
        @close="userAuthFormVisible = false"
      />

      <bank-card-drawer
        v-if="bankCardVisible"
        :row="thisRow"
        @close="bankCardVisible = false"
      />
    </div>
    <user-registration-overview-charts-dialog
        v-if="UserRegistrationOverviewChartsDialogVisible"
        @close="UserRegistrationOverviewChartsDialogVisible=false"
      />
    <div
      v-else
      v-loading.lock="permissionValidationLoading"
      element-loading-background="#FFFFFF"
      element-loading-text="权限验证中..."
      style="text-align: center;"
    >
      抱歉您无权查看，请联系管理员开通查看权限
      【查看-用户列表，user:table:query:list】
    </div>
  </div>
</template>

<script>
import { getUserTable } from '@/api/app-user'
import { registerOrigins, originPlatforms } from '@/constant/origin'
import { genders, userTypes } from '@/constant/user'
import Pagination from '@/components/Pagination'
import PlatformSvgIcon from '@/components/PlatformSvgIcon'
import GoldRunningWaterDialog from '@/components/data/GoldRunningWaterDialog'
import DiamondRunningWaterDialog from '@/components/data/DiamondRunningWaterDialog'
import DiamondSalaryRunningWaterDialog from '@/components/data/DiamondSalaryRunningWaterDialog'
import AccountHanle from '@/components/data/AccountHanle'
import UserVideoViolationTableDialog from '@/components/data/UserVideoViolationTableDialog'
import EditUserForm from '@/components/data/EditUserForm'
import EditUserGoldCoinDeductionForm from '@/components/data/EditUserGoldCoinDeductionForm'
import EditUserGoldCoinRewardForm from '@/components/data/EditUserGoldCoinRewardForm'
import EditUserDiamondDeductionForm from '@/components/data/EditDiamondCoinDeductionForm'
import EditUserDiamondRewardForm from '@/components/data/EditUserDiamondRewardForm'
import EditUserGameCouponRewardForm from '@/components/data/EditUserGameCouponRewardForm'
import EditUserGameCouponDeductionForm from '@/components/data/EditUserGameCouponDeductionForm'
import UserAccountStatusLogDrawer from '@/components/data/UserAccountStatusLogDrawer'
import AddOtherRecharge from '@/components/data/AddOtherRecharge'
import AddOtherRechargeDialog from '@/components/data/AddOtherRechargeDialog'
import UserAuthForm from '@/components/data/UserAuthForm'
import BankCardDrawer from '@/components/data/UserInfo/BankCardDrawer'
import UserRegistrationOverviewChartsDialog from '@/components/data/UserRegistrationOverviewCharts/dialog'
import { copyText } from '@/utils'
import { pickerOptions } from '@/constant/el-const'
import { mapGetters } from 'vuex'
import { getCountryAlls } from '@/api/sys'

export default {
  name: 'AppUserTable',
  components: {
    Pagination,
    GoldRunningWaterDialog,
    DiamondRunningWaterDialog,
    DiamondSalaryRunningWaterDialog,
    PlatformSvgIcon,
    AccountHanle,
    UserVideoViolationTableDialog,
    EditUserForm,
    UserAccountStatusLogDrawer,
    EditUserGoldCoinDeductionForm,
    EditUserGoldCoinRewardForm,
    EditUserDiamondDeductionForm,
    EditUserDiamondRewardForm,
    EditUserGameCouponRewardForm,
    EditUserGameCouponDeductionForm,
    UserRegistrationOverviewChartsDialog,
    AddOtherRecharge,
    AddOtherRechargeDialog,
    UserAuthForm,
    BankCardDrawer
  },
  data() {
    return {
      UserRegistrationOverviewChartsDialogVisible: false,
      otherRechargeDialogVisible: false,
      otherRechargeVisible: false,
      showAllCondition: false,
      permissionValidationLoading: true,
      bankCardVisible: false,
      userAuthFormVisible: false,
      pickerOptions,
      thisRow: {},
      countryCodeStr: '',
      level: 0,
      marks: {
        0: 'L0',
        1: 'L1',
        2: 'L2',
        3: {
          style: {
            color: '#1989FA',
            width: '20px'
          },
          label: this.$createElement('strong', 'L3')
        }
      },
      levelSubmitLoading: false,
      accountHandleVisible: false,
      accountStatusLogTable: false,
      userDeatilsDrawer: false,
      thatSelectedUserId: '',
      candyDialogVisible: false,
      diamondDialogVisible: false,
      diamondSalaryDialogVisible: false,
      violationTableVisible: false,
      editUserFormVisible: false,
      editUserGoldCoinDeductionFormVisible: false,
      editUserGoldCoinRewardFormVisible: false,
      editUserDiamondRewardFormVisible: false,
      editUserDiamondDeductionFormVisible: false,
      editUserGameCouponRewardFormVisible: false,
      editUserGameCouponDeductionFormVisible: false,
      registerOrigins,
      originPlatforms,
      genders,
      userTypes,
      list: [],
      total: 0,
      listQuery: {
        pageIndex: 1,
        pageSize: 20,
        userIds: [],
        countryCode: '',
        authType: '',
        originPlatform: '',
        originPhoneModel: '',
        userSex: '',
        userType: '',
        startTime: '',
        endTime: '',
        startCreateDate: '',
        endCreateDate: '',
        del: '0',
        account: '',
        sysOrigins: [],
        deviceId: ''
      },
      allCountryList: [],
      rangeDate: '',
      listLoading: true,
      searchLoading: false,
      clickUserId: ''
    }
  },
  computed: {
    ...mapGetters([
      'buttonPermissions',
      'permissionsSysOriginPlatforms',
      'permissionsSysOriginPlatformAlls'
    ])
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startCreateDate = newVal[0]
          this.listQuery.endCreateDate = newVal[1]
          return
        }
        this.listQuery.startCreateDate = ''
        this.listQuery.endCreateDate = ''
      }
    }
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigins = [querySystem.value]
    that.renderData(true)
    // that.loadCountryAlls()
    setTimeout(() => {
      that.permissionValidationLoading = false
    }, 3 * 1000)
  },
  methods: {
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.listQuery.pageIndex = 1
        that.list = []
      }
      that.listLoading = true
      that.listQuery.userIds = that.listQuery.userIds.filter(item => item)
      that.listQuery.sysOrigins =
        that.listQuery.sysOrigins && that.listQuery.sysOrigins.length > 0
          ? that.listQuery.sysOrigins
          : that.permissionsSysOriginPlatformAlls.map(item => item.value)
      getUserTable(that.listQuery)
        .then(res => {
          const { body } = res
          that.list = body || []
          that.searchLoading = that.listLoading = false
        })
        .catch(er => {
          that.searchLoading = that.listLoading = false
        })
    },
    handleSearch() {
      this.searchLoading = true
      this.renderData(true)
    },
    querySearch(queryString, cb) {
      var restaurants = this.allCountryList
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    createFilter(queryString) {
      return (restaurant) => {
        if (restaurant.alphaTwo.toLowerCase().indexOf(queryString.toLowerCase()) === 0) {
          return true
        }
        if (restaurant.alphaThree.toLowerCase().indexOf(queryString.toLowerCase()) === 0) {
          return true
        }
        if (restaurant.countryName.toLowerCase().indexOf(queryString.toLowerCase()) === 0) {
          return true
        }
        if (restaurant.aliasName.toLowerCase().indexOf(queryString.toLowerCase()) === 0) {
          return true
        }
        return (String(restaurant.phonePrefix).indexOf(queryString.toLowerCase()) === 0)
      }
    },
    handleDelCountryCodeClick() {
      this.listQuery.countryCode = ''
      this.countryCodeStr = ''
      this.handleSearch()
    },
    handleCountryCodeClick(item) {
      this.listQuery.countryCode = item.alphaTwo
      this.countryCodeStr = item.phonePrefix + ' ' + item.countryName
      this.handleSearch()
    },
    loadCountryAlls() {
      const that = this
      getCountryAlls().then(res => {
        that.allCountryList = res.result || {}
      }).catch(er => {
      })
    },
    getAccountText(userInfo) {
      if (!userInfo) {
        return ''
      }
      const account = userInfo.account
      if (userInfo.ownSpecialId && userInfo.ownSpecialId.account) {
        return `${account} / ${userInfo.ownSpecialId.account}靓`
      }
      return account
    },
    queryUserDetails(row) {
      this.thatSelectedUserId = row.id
      this.userDeatilsDrawer = true
    },
    handleEidt(row) {},
    editUserForm(item) {
      this.thatSelectedUserId = item.id
      this.editUserFormVisible = true
    },
    editUserGoldCoinDeductionForm(item) {
      this.thatSelectedUserId = item.id
      this.editUserGoldCoinDeductionFormVisible = true
    },
    editUserGoldCoinRewardForm(item) {
      this.thatSelectedUserId = item.id
      this.editUserGoldCoinRewardFormVisible = true
    },
    editUserDiamondDeductionForm(item) {
      this.thatSelectedUserId = item.id
      this.editUserDiamondDeductionFormVisible = true
    },
    editUserDiamondRewardForm(item) {
      this.thatSelectedUserId = item.id
      this.editUserDiamondRewardFormVisible = true
    },
    editUserGameCouponRewardForm(item) {
      this.thatSelectedUserId = item.id
      this.editUserGameCouponRewardFormVisible = true
    },
    editUserGameCouponDeductionForm(item) {
      this.thatSelectedUserId = item.id
      this.editUserGameCouponDeductionFormVisible = true
    },
    handleBankCard(item) {
      this.thisRow = item
      this.bankCardVisible = true
    },
    addOtherRecharge(item) {
      this.thisRow = item
      this.thatSelectedUserId = item.id
      this.otherRechargeVisible = true
    },
    addOtherRechargeDialog(item) {
      this.thisRow = item
      this.thatSelectedUserId = item.id
      this.otherRechargeDialogVisible = true
    },
    otherRechargeSuccess() {
      this.otherRechargeVisible = false
      this.$opsMessage.success()
    },
    accountHandleSuccess(data) {
      this.$opsMessage.success()
      // this.renderData()
    },
    accountHanle(row) {
      this.thatSelectedUserId = row.id
      this.accountHandleVisible = true
    },
    accountStatus(item) {
      this.thisRow = item
      this.accountStatusLogTable = true
    },
    handleRunningWater(row) {
      this.clickUserId = row.id
      this.candyDialogVisible = true
    },
    handleDiamondRunningWater(row) {
      this.clickUserId = row.id
      this.diamondDialogVisible = true
    },
    handleDiamondSalaryRunningWater(row) {
      this.clickUserId = row.id
      this.diamondSalaryDialogVisible = true
    },
    handleUserAuthForm(row) {
      this.thatSelectedUserId = row.id
      this.userAuthFormVisible = true
    },
    clickViolationTableVisible(row) {
      this.thisRow = row
      this.violationTableVisible = true
    },
    copyTextContent(text) {
      const that = this
      copyText(text)
        .then(() => {
          that.$message({
            message: '复制成功',
            type: 'success'
          })
        })
        .catch(() => {
          that.$message({
            message: '复制失败',
            type: 'error'
          })
        })
    },
    originText(row) {
      const texts = []
      if (!row.userRegisterInfo) {
        return ''
      }
      const userRegisterInfo = row.userRegisterInfo
      if (userRegisterInfo.originPlatform) {
        texts.push(userRegisterInfo.originPlatform)
      }
      if (userRegisterInfo.originPhoneModel) {
        texts.push(userRegisterInfo.originPhoneModel)
      }
      return texts.join('，')
    },
    renderDataSuccess() {
      this.$message({
        message: 'Successful',
        type: 'success'
      })
      this.renderData()
    },
    handleMouseEnter(row) {
      this.thisRow = row
      this.thatSelectedUserId = row.id
    },
    getLevelName(gender, level) {
      if (gender !== 0) {
        return '-'
      }
      if (level === -1) {
        return 'L-1'
      }
      return `L${level}`
    },
    handleUserSexChange(value) {
      if (value === 0) {
        this.listQuery.maleLevel = ''
        return
      }
      if (value === 1) {
        this.listQuery.level = ''
        return
      }
      this.listQuery.maleLevel = ''
      this.listQuery.level = ''
    },
    getAccountStatusFontColor(user) {
      if (!user || !user.actualAccountStatus) {
        return 'info'
      }
      if (user.actualAccountStatus === 'NORMAL') {
        return 'font-success'
      }

      if (user.actualAccountStatus === 'FREEZE') {
        return 'font-warning'
      }
      return 'font-danger'
    },
    clickUserRegistrationOverview() {
      this.UserRegistrationOverviewChartsDialogVisible = true
    }
  }
}
</script>
<style scoped lang="scss">
.this-level {
  padding-bottom: 30px;
  display: flex;
  > div {
    width: 150px;
  }
}
</style>
