<template>
  <div class="app-container">

    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width:120px;"
        class="filter-item"
        @change="changeSysOrigin"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <account-input v-model="listQuery.userId" :sys-origin="listQuery.sysOrigin" placeholder="用户ID" />
      </div>
      <el-select
        v-model="listQuery.approveStatus"
        placeholder="状态"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="待处理" :value="0" />
        <el-option label="已处理" :value="1" />
      </el-select>
      <el-select
        v-model="listQuery.customType"
        placeholder="类型"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="专属定制" :value="'UPLOAD_GIFT'" />
        <el-option label="增加时长" :value="'INCREASE_TIME'" />
      </el-select>
      <el-select
        v-model="listQuery.region"
        v-loading="loading"
        placeholder="区域"
        style="width:120px;"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in regions"
          :key="index"
          :label="item.regionName"
          :value="item.id"
        />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
      @cell-mouse-enter="handleMouseEnter"
    >
      <el-table-column label="归属系统" align="center">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column label="用户" align="center" min-width="150">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.userBaseInfo" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column prop="regionNameStr" label="区域" align="center" />
      <el-table-column label="类型" align="center">
        <template slot-scope="scope">
          <div>
            <el-tag v-if="scope.row.customType === 'UPLOAD_GIFT'">专属定制</el-tag>
            <el-tag v-else-if="scope.row.customType === 'INCREASE_TIME'">增加时长</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="giftCode" label="礼物Code" align="center" />
      <el-table-column prop="quantity" label="天数" align="center" />
      <el-table-column width="200" label="待处理/已完成" align="center" min-width="100">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.approveStatus"
            :active-value="true"
            :inactive-value="false"
            @change="handleSwitchChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center" min-width="100">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="400">
        <template slot-scope="scope">
          <a class="resourceUrl" @click="downloadFile(scope.row.coverOneUrl)">下载图片1</a>
          <a class="resourceUrl" @click="downloadFile(scope.row.coverTwoUrl)">下载图片2</a>
          <a class="resourceUrl" @click="downloadFile(scope.row.coverThreeUrl)">下载图片3</a>
          <a class="resourceUrl" @click="downloadFile(scope.row.songSourceUrl)">下载歌曲</a>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
  </div>
</template>

<script>
import { pageUserCustomGift, switchStatus } from '@/api/user'
import Pagination from '@/components/Pagination'
import { sysOriginPlatforms } from '@/constant/origin'
import { mapGetters } from 'vuex'
import { regionConfigTable } from '@/api/sys'

export default {
  components: { Pagination },
  data() {
    return {
      thatRow: {},
      sysOriginPlatforms,
      loading: false,
      regions: [],
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        approveStatus: 0,
        sysOrigin: 'MARCIE',
        region: '',
        customType: '',
        userId: ''
      },
      textOptTitle: '',
      listLoading: true
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    this.listRegion()
    that.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        this.listQuery.cursor = 1
        this.listQuery.list = []
      }
      that.listLoading = true
      pageUserCustomGift(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    listRegion() {
      const that = this
      that.loading = true
      regionConfigTable({ 'sysOrigin': that.listQuery.sysOrigin }).then(res => {
        that.regions = res.body || []
        that.loading = false
      }).catch(er => {
        that.loading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    renderDataSuccess() {
      this.$message({
        message: '操作成功',
        type: 'success'
      })
      this.renderData()
    },
    changeSysOrigin() {
      this.listRegion()
      this.handleSearch()
    },
    queryUserDetails(row) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = row.id
    },
    handleMouseEnter(row) {
      this.thatRow = row
    },
    handleSwitchChange(row) {
      switchStatus(row.id, row.approveStatus)
        .then(res => {
          this.renderData()
        })
        .catch(er => {
          row.approveStatus = !row.approveStatus
        })
    },
    downloadFile(resource) {
      if (!resource) {
        this.$message({
          message: '资源用户未上传',
          type: 'error'
        })
        return
      }
      const fileUrl = resource
      const link = document.createElement('a')
      link.href = fileUrl
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }
}
</script>
<style scoped lang="scss">
.resourceUrl{
  color: rgb(72, 144, 247);
}
</style>
