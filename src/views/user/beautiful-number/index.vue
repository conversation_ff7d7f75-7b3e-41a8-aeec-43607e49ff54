<template>
  <div class="app-container">

    <div class="filter-container">
      <el-select
        v-model="listQuery.status"
        placeholder="类型"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="可售卖" :value="1" />
        <el-option label="不可售卖" :value="0" />
        <el-option label="已售出" :value="2" />
      </el-select>
      <el-input
        v-model.trim="listQuery.userAccount"
        placeholder="靓号"
        clearable
        style="width: 200px;"
        class="filter-item"
      />
      <el-cascader
        v-model="groups"
        :options="options"
        :props="{ expandTrigger: 'hover' }"
        style="width: 200px;top: -4px;"
        clearable
      />
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        新增
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="靓号" align="center" prop="userAccount" />
      <el-table-column prop="gold" label="金币" align="center" />
      <el-table-column prop="worth" label="价值(美元)" align="center" />
      <el-table-column prop="returnApp" label="是否可售卖" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.status === 1">可售卖</span>
          <span v-if="scope.row.status === 0">不可售卖</span>
          <span v-if="scope.row.status === 2">已售出</span>
        </template>
      </el-table-column>
      <el-table-column prop="tabName" label="Tab分组" align="center" />
      <el-table-column prop="typeName" label="二级类型" align="center" />
      <el-table-column prop="createTime" label="创建时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status !== 2 " type="text" @click.native="handleUpdate(scope.row)">修改</el-button>
          <el-button v-if="scope.row.status !== 2 " type="text" @click.native="handlDel(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
    <el-dialog
      :title="textOptTitle"
      :visible.sync="formVisible"
      :before-close="handleClose"
      width="400px"
    >
      <div v-loading="submitLoading">
        <el-form ref="form" :model="form" :rules="rules" label-width="110px">
          <el-form-item label="靓号" prop="userAccount">
            <el-input v-model.trim="form.userAccount" type="text" :disabled="form.id !== ''" />
          </el-form-item>
          <el-form-item label="分组" prop="SysGroup">
            <el-cascader
              v-model="form.SysGroup"
              :options="options"
              :props="{ expandTrigger: 'hover' }"
              style="width:100%;"
            />
          </el-form-item>
          <el-form-item label="售卖类型" prop="status">
            <el-select
              v-model="form.status"
              placeholder="售卖类型"
              clearable
              style="width:100%;"
              class="filter-item"
            >
              <el-option label="不可售卖" :value="0" />
              <el-option label="可售卖" :value="1" />
            </el-select>
          </el-form-item>
          <el-form-item label="金币" prop="gold">
            <el-input v-model.trim="form.gold" type="number" />
          </el-form-item>
          <el-form-item label="价值(美元)" prop="worth">
            <el-input v-model.trim="form.worth" type="number" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm()">保存</el-button>
            <el-button @click="handleClose()">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { numberTable, updateNumber, deleteNumber, addNumber } from '@/api/number'
import Pagination from '@/components/Pagination'

function getFormData() {
  return {
    id: '',
    userAccount: '',
    status: '',
    gold: '',
    worth: '',
    SysGroup: [],
    tabs: []
  }
}
export default {
  components: { Pagination },
  data() {
    return {
      pushTextHistoryLoading: false,
      pushTextHistoryVisible: false,
      pushTextHistory: [],
      list: [],
      checkList: [],
      groups: [],
      options: [
        { label: 'Top', value: 'TOP', children: [{ label: 'UniqueIDs', value: 'UNIQUE_ID' }] },
        { label: 'Five-Digit', value: 'FIVE', children: [{ label: 'UniqueIDs', value: 'UNIQUE_ID' }] },
        { label: 'Pairs', value: 'PAIRS', children: [
          { label: 'AABB', value: 'AABB' },
          { label: 'ABAB', value: 'ABAB' },
          { label: 'ABABAB', value: 'ABABAB' },
          { label: 'ABCABC', value: 'ABCABC' },
          { label: 'ABCCBA', value: 'ABCCBA' },
          { label: 'AABBCC', value: 'AABBCC' },
          { label: 'AAABBB', value: 'AAABBB' }
        ] },
        { label: 'Repeationg', value: 'REPEATIONG', children: [
          { label: 'AABB', value: 'AABB' },
          { label: 'ABAB', value: 'ABAB' },
          { label: 'ABABAB', value: 'ABABAB' },
          { label: 'ABCABC', value: 'ABCABC' },
          { label: 'ABCCBA', value: 'ABCCBA' },
          { label: 'AABBCC', value: 'AABBCC' },
          { label: 'AAABBB', value: 'AAABBB' }
        ] },
        { label: 'Consecutive', value: 'CONSECUTIVE', children: [
          { label: 'ABCD', value: 'ABCD' },
          { label: 'ABCDE', value: 'ABCDE' },
          { label: 'ABCDEF', value: 'ABCDEF' },
          { label: 'ABCDEFG', value: 'ABCDEFG' }
        ] }
      ],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        userAccount: '',
        status: '',
        tabName: '',
        typeName: ''
      },
      formVisible: false,
      textOptTitle: '',
      form: getFormData(),
      submitLoading: false,
      rules: {
        userAccount: [
          { required: true, message: '请填写靓号', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择售卖类型', trigger: 'blur' }
        ],
        gold: [
          { required: true, message: '请填写金币', trigger: 'blur' }
        ],
        worth: [
          { required: true, message: '请填写价值', trigger: 'blur' }
        ],
        SysGroup: [{ type: 'array', required: true, message: '请选择分组', trigger: 'change' }]
      },
      listLoading: true,
      uploadLoading: false,
      uploadUrlLoading: false
    }
  },
  created() {
    this.renderData()
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      that.listQuery.tabName = ''
      that.listQuery.typeName = ''
      if (that.groups !== undefined || that.groups.length > 0) {
        that.listQuery.tabName = that.groups[0]
        that.listQuery.typeName = that.groups[1]
      }
      numberTable(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData()
    },
    renderDataSuccess() {
      this.$message({
        message: 'Successful',
        type: 'success'
      })
      this.renderData()
    },
    queryUserDetails(row) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = row.id
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (valid) {
          that.submitLoading = true
          const tabParams = []
          tabParams.push({
            tabName: that.form.SysGroup[0],
            typeName: that.form.SysGroup[1]
          })
          that.form.tabs = tabParams
          if (that.form.id) {
            updateNumber(that.form).then(res => {
              that.submitLoading = false
              that.formVisible = false
              that.form = getFormData()
              that.checkList = []
              that.renderData()
            }).catch(er => {
              that.submitLoading = false
              that.$emit('fial', er)
            })
            return
          }
          addNumber(that.form).then(res => {
            that.submitLoading = false
            that.formVisible = false
            that.form = getFormData()
            that.checkList = []
            that.renderData()
          }).catch(er => {
            that.submitLoading = false
            console.error(er)
            that.$emit('fial', er)
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    },
    // 删除
    handlDel(id) {
      this.$confirm('确认删除吗？', '提示', {
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        deleteNumber(id).then((res) => {
          this.listLoading = false
          this.$message({
            message: '删除成功',
            type: 'success'
          })
          this.renderData()
        })
      }).catch(() => {

      })
    },
    handleClose() {
      // 去除校验
      this.$refs.form.clearValidate()
      // this.form = getFormData()
      this.checkList = []
      this.formVisible = false
    },
    handleCreate() {
      this.formVisible = true
      this.textOptTitle = '添加'
      this.form = getFormData()
    },
    handleUpdate(row) {
      this.textOptTitle = '修改'
      this.formVisible = true
      this.form = Object.assign(this.form, row)
      debugger
      if (row.tabName !== '' && row.typeName !== '') {
        this.checkList.push(
          row.tabName,
          row.typeName
        )
      }
      this.form.SysGroup = this.checkList
    }
  }
}
</script>
