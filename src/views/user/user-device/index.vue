<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="归属系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in permissionsSysOriginPlatforms"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;">
            <sys-origin-icon
              :icon="item.value"
              :desc="item.value"
            /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <account-input
        v-model="listQuery.userId"
        :sys-origin="listQuery.sysOrigin"
        class="filter-item"
        placeholder="用户ID"
      />
      <el-input
        v-model.trim="listQuery.imei"
        placeholder="设备号"
        style="width: 200px;"
        class="filter-item"
      />
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="注册开始日期"
          end-placeholder="注册结束日期"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="id" label="ID" align="center" width="200" />
      <el-table-column prop="sysOrigin" label="系统" align="center" min-width="50">
        <template slot-scope="scope">
          <sys-origin-icon
            :icon="scope.row.sysOrigin"
            :desc="scope.row.sysOrigin"
          />
        </template>
      </el-table-column>
      <el-table-column label="用户" align="center" min-width="200">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.userId"
            type="text"
            @click="queryUserDetails(scope.row.userId)"
          >{{ scope.row.userNickname }}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="registerNumber" label="注册数量" align="center" min-width="80" />
      <el-table-column prop="ip" label="最近登录IP" align="center" min-width="200" />
      <el-table-column prop="imei" label="设备号" align="center" min-width="80">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="copyContent(scope.row.imei)"
          >点击复制</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="requestClient" label="请求平台" align="center" min-width="80" />
      <el-table-column prop="phoneModel" label="手机型号" align="center" min-width="80" />
      <el-table-column prop="phoneSysVersion" label="操作系统" align="center" min-width="80" />
      <el-table-column prop="appVersion" label="App版本" align="center" min-width="80" />
      <el-table-column prop="createTime" label="创建时间" align="center" width="200">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" label="修改时间" align="center" width="200">
        <template slot-scope="scope">
          {{ scope.row.updateTime | dateFormat }}
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <user-deatils-drawer
      v-if="userDeatilsDrawer"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawer = false"
    />
  </div>
</template>

<script>
import { getUserDeviceTable } from '@/api/app-user'
import Pagination from '@/components/Pagination'
import { pickerOptions } from '@/constant/el-const'
import { sysOriginPlatforms } from '@/constant/origin'
import { copyText } from '@/utils'
import { mapGetters } from 'vuex'
export default {
  name: 'UserDevice',
  components: { Pagination },
  data() {
    return {
      pickerOptions,
      rangeDate: '',
      thatRow: {},
      sysOriginPlatforms,
      userDeatilsDrawer: false,
      list: [],
      checkList: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        sysOrigin: '',
        startTime: '',
        endTime: ''
      },
      listLoading: true,
      searchDisabled: false
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    }
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      getUserDeviceTable(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    queryUserDetails(userId) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = userId
    },
    copyContent(text) {
      const that = this
      copyText(text).then(() => {
        that.$opsMessage.success()
      }).catch(er => {
        that.$opsMessage.fail()
      })
    }
  }
}
</script>
