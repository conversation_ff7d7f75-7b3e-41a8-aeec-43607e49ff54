<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="item in tables" :key="item.name" :label="item.title" :name="item.component" />
      <component :is="activeName" />
    </el-tabs>
  </div>
</template>
<script>
import NewPushForm from './new-push-form'
import PushLog from './push-log'
import PusTask from './push-task'
export default {
  name: 'MessagePush',
  components: { NewPushForm, PushLog, PusTask },
  data() {
    return {
      activeName: 'NewPushForm',
      tables: [
        {
          title: '新建Push',
          component: 'NewPushForm'
        }, {
          title: 'Push日志',
          component: 'PushLog'
        }, {
          title: '推送任务',
          component: 'PusTask'
        }
      ]
    }
  }
}
</script>
