<template>
  <div class="push-form">
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-form-item prop="title" label="Push标题">
        <el-input v-model="form.title" placeholder="最多输入200字" maxlength="200" />
      </el-form-item>
      <el-form-item prop="content" label="Push内容">
        <el-input v-model="form.content" type="textarea" placeholder="最多输入500字" maxlength="1000" />
      </el-form-item>
      <el-form-item label="系统" prop="sysOrigin">
        <el-select v-model="form.sysOrigin">
          <el-option
            v-for="item in sysOriginPlatforms"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="客户端" prop="platform">
        <el-select v-model="form.platform" clearable placeholder="全部">
          <el-option
            v-for="item in appPlatforms"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="推送平台" prop="deviceType">
        <el-select v-model="form.deviceType">
          <el-option
            v-for="item in deviceTypes"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="语言" prop="language">
        <el-select v-model="form.language" clearable placeholder="请选择语言">
          <el-option label="阿拉伯语" value="ar" />
          <el-option label="土耳其语" value="tr" />
          <el-option label="其他语言" value="en" />
        </el-select>
      </el-form-item>
      <el-form-item label="业务场景" prop="businessScene">
        <el-select v-model="form.businessScene">
          <el-option label="官方通知" value="OFFICIAL_MESSAGE_NOTICE" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="Push承接" prop="link">
        <el-input v-model="form.link" placeholder="请输入内容" />
      </el-form-item> -->
      <el-form-item label="定投UID" prop="fixedUserIds">
        <el-input v-model.trim="form.fixedUserIds" type="textarea" placeholder="请输入定投用户ID多个以逗号分隔 123,321" @blur="fixedUserIdBlur" @input="fixedUserIdKeyup" />
        <div class="font-info">{{ inputFixedNumber }}/100 定投会忽略 "语言" 过滤条件参数</div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">提交</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { appPlatforms, deviceTypes, sysOriginPlatforms } from '@/constant/origin'
import { validPositiveNumber } from '@/utils/validate'
import { newPush } from '@/api/message'

export default {
  data() {
    return {
      appPlatforms,
      deviceTypes,
      sysOriginPlatforms,
      form: {
        title: '',
        content: '',
        platform: '',
        businessScene: 'OFFICIAL_MESSAGE_NOTICE',
        link: '',
        fixedUserIds: '',
        deviceType: '',
        sysOrigin: '',
        language: ''
      },
      rules: {
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' },
          { min: 3, max: 200, message: '长度在 3 到 200 个字符', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入内容', trigger: 'blur' },
          { min: 3, max: 500, message: '长度在 3 到 500 个字符', trigger: 'blur' }
        ],
        sysOrigin: { required: true, message: '请选择系统', trigger: 'blur' },
        deviceType: { required: true, message: '请选择推送平台', trigger: 'blur' }
      },
      inputFixedNumber: 0
    }
  },
  methods: {
    onSubmit() {
      const that = this
      that.$refs.form.validate((valid) => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.$confirm('是否确认提交发送?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          newPush(that.form).then(res => {
            that.$message({
              type: 'success',
              message: 'Successful'
            })
            that.resetForm()
          }).catch(er => {
            that.$opsMessage.success()
          })
        }).catch(() => {
          that.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
      })
    },
    resetForm() {
      this.inputFixedNumber = 0
      this.$refs.form.resetFields()
    },
    fixedUserIdBlur(value) {
      const list = this.filterUserIds(this.form.fixedUserIds.split(','), 100)
      this.form.fixedUserIds = list.join(',')
      this.inputFixedNumber = list.length
    },
    filterUserIds(contentArrays, maxSize) {
      const inputNumber = contentArrays.length
      var list = []
      for (let index = 0; index < inputNumber; index++) {
        const item = contentArrays[index]
        if (validPositiveNumber(item)) {
          list.push(item)
        }
        if (list.length === maxSize) {
          break
        }
      }
      return list
    },
    fixedUserIdKeyup(value) {
      if (!value) {
        this.inputFixedNumber = 0
        return
      }
      var contentArrays = value.split(',')
      if (contentArrays.length > 100) {
        var list = this.filterUserIds(contentArrays, 100)
        this.form.fixedUserIds = list.join(',')
        this.inputFixedNumber = list.length
        return
      }
      this.inputFixedNumber = contentArrays.length
    }
  }
}
</script>
