<template>
  <div class="app-container-push-log">
    <div class="filter-container">
      <!--
      <el-select
        v-model="listQuery.pushBusinessScene"
        placeholder="业务类型"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option
          v-for="item in messageBusinessScene"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select> -->
      <el-select
        v-model="listQuery.pushStatus"
        placeholder="发送状态"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option
          v-for="item in pushStatus"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>

      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option
          v-for="item in sysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="listQuery.platform"
        placeholder="手机系统"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option
          v-for="item in appPlatforms"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="listQuery.deviceType"
        placeholder="推送平台"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option
          v-for="item in deviceTypes"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>

      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="sendUserName" label="发送者" align="center">
        <template slot-scope="scope">
          {{ scope.row.sendUserName || "系统" }}
        </template>
      </el-table-column>
      <el-table-column prop="sysOrigin" label="系统" align="center">
        <template slot-scope="scope">
          <sys-origin-icon
            :icon="scope.row.sysOrigin"
            :desc="scope.row.sysOrigin"
          />
        </template>
      </el-table-column>
      <el-table-column prop="platform" label="平台" align="center" />
      <el-table-column prop="deviceType" label="推送平台" align="center" />
      <el-table-column prop="businessScene" label="业务场景" align="center" />
      <!-- <el-table-column prop="pushUserType" label="用户类型" align="center" /> -->
      <el-table-column prop="title" label="标题" align="center" />
      <el-table-column prop="content" label="文案" align="center" />
      <el-table-column prop="pushStatus" label="状态" align="center" />
      <el-table-column prop="successQuantity" label="成功人数" align="center" />
      <el-table-column prop="failQuantity" label="失败人数" align="center" />
      <el-table-column prop="createTime" label="创建时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
  </div>
</template>

<script>
import { messageBusinessScene, userTypes } from '@/constant/type'
import {
  appPlatforms,
  deviceTypes,
  sysOriginPlatforms
} from '@/constant/origin'
import { pushLogTable } from '@/api/message'
import Pagination from '@/components/Pagination'
import { pickerOptions } from '@/constant/el-const'

export default {
  components: { Pagination },
  data() {
    return {
      pickerOptions,
      appPlatforms,
      deviceTypes,
      sysOriginPlatforms,
      messageBusinessScene,
      userTypes,
      pushStatus: [{ value: 0, name: '成功' }, { value: 1, name: '失败' }],
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        pushType: '',
        pushBusinessScene: '',
        pushUserType: '',
        pushStatus: '',
        startTime: '',
        endTime: '',
        sysOrigin: '',
        platform: '',
        deviceType: ''
      },
      rangeDate: '',
      listLoading: true
    }
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    }
  },
  created() {
    this.renderData()
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      pushLogTable(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData()
    },
    queryUserDetails(row) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = row.id
    }
  }
}
</script>
