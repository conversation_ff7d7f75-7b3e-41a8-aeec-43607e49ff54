<template>

  <el-dialog
    title="房间成员详情"
    :visible="true"
    width="70%"
    :before-close="handleClose"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <div class="filter-container">
      <div class="filter-item">
        <account-input v-model="listQuery.userId" :sys-origin="sysOrigin" placeholder="用户ID" />
      </div>
      <el-select
        v-model="listQuery.roomRole"
        placeholder="角色"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in roomRoles"
          :key="index"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
      height="500"
    >
      <el-table-column label="用户" align="center" min-width="200">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" />
        </template>
      </el-table-column>

      <el-table-column label="房间角色" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.roomRole === 'HOMEOWNER'"> 房主 </span>
          <span v-if="scope.row.roomRole === 'ADMIN'"> 管理员 </span>
          <span v-if="scope.row.roomRole === 'MEMBER'"> 成员 </span>
        </template>
      </el-table-column>

      <el-table-column prop="createTime" label="注册时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
    <user-deatils-drawer
      v-if="userDeatilsDrawer"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawer=false"
    />

  </el-dialog>
</template>

<script>

import { getRoomUserInfo } from '@/api/room'
import Pagination from '@/components/Pagination'
import { roomRoles } from '@/constant/type'

export default {
  components: { Pagination },
  props: {
    roomId: {
      type: String,
      required: true
    },
    sysOrigin: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      list: [],
      roomRoles,
      userRegisterInfo: {},
      thatSelectedUserId: '',
      userDeatilsDrawer: false,
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: '',
        roomId: '',
        roomRole: ''
      },
      total: 0,
      searchLoading: false,
      listLoading: false,
      searchDisabled: false
    }
  },
  watch: {
    roomId: {
      immediate: true,
      deep: true,
      handler(newVal) {
        this.listQuery.roomId = newVal
        this.renderData()
      }
    }
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      getRoomUserInfo(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.searchLoading = that.listLoading = false
      }).catch(er => {
        that.searchLoading = that.listLoading = false
      })
    },
    queryUserDetails(userId) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = userId
    },
    handleSearch() {
      this.searchLoading = true
      this.renderData()
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>

<style scoped lang='scss'>
  .filter-containers {
    padding-bottom: 10px;
    float:right;

    .filter-item {
      display: inline-block;
      vertical-align: middle;
      margin-bottom: 10px;
    }
  }
  .user_info {
    position: relative;
    top: -20px;
    .info {
      position: absolute;
      top: 20px;
      left: 20px;
      span {
        padding: 0px 10px;
      }
    }
  }
</style>
