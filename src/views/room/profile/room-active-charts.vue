<template>
  <el-dialog
    title="房间活跃指标"
    :visible="true"
    top="50px"
    width="70%"
    :before-close="handleClose"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <div class="filter-container">
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="创建日期开始"
          end-placeholder="创建日期结束"
          :clearable="false"
          :editable="false"
          @change="rangeDateChange"
        />
      </div>
    </div>

    <div v-loading="listLoading" class="charts">
      <div id="charts" ref="charts" :style="'width: 100%;height:400px;'" />
    </div>

  </el-dialog>
</template>

<script>

import { listRoomActiveIndex } from '@/api/statistics'
import { formatDate } from '@/utils'
import { pickerOptions } from '@/constant/el-const'
import { recentDate } from '@/utils'

export default {
  props: {
    roomId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      pickerOptions,
      list: [],
      listLoading: false,
      listQuery: {
        roomId: '',
        startTime: '',
        endTime: ''
      },
      charts: null,
      rangeDate: []
    }
  },
  watch: {
    roomId: {
      immediate: true,
      deep: true,
      handler(newVal) {
        this.listQuery.roomId = newVal
        this.initRangeDate()
        this.renderData()
      }
    }
  },
  mounted() {
    const that = this
    that.$nextTick(() => {
      that.charts = that.$echarts.init(that.$refs.charts)
      that.renderCharts()
      window.addEventListener('resize', () => {
        that.charts.resize()
      })
    })
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      that.listQuery.startTime = that.rangeDate[0]
      that.listQuery.endTime = that.rangeDate[1]
      listRoomActiveIndex(that.listQuery).then(res => {
        that.listLoading = false
        that.list = res.body || []
        that.renderCharts()
      }).catch(er => {
        that.listLoading = false
      })
    },
    processChartsData() {
      const that = this
      const chartsData = {
        onlieUser: [],
        senGiftUser: [],
        sendGiftAmount: []
      }
      const onlieUser = chartsData.onlieUser
      const senGiftUser = chartsData.senGiftUser
      const sendGiftAmount = chartsData.sendGiftAmount
      if (that.list.length > 0) {
        that.list.forEach(item => {
          const roomActiveIndex = item.roomActiveIndex || {}
          const date = formatDate(item.createTime, 'yyyy-MM-dd HH:mm')
          onlieUser.push([date, (roomActiveIndex.onlineUserQuantity || 0)])
          senGiftUser.push([date, (roomActiveIndex.sendGiftUserQuantity || 0)])
          sendGiftAmount.push([date, (roomActiveIndex.sendGiftAmount || 0)])
        })
      }
      return chartsData
    },
    renderCharts() {
      const that = this
      const thatDayData = that.processChartsData()
      that.charts.setOption({
        color: ['#66b3ff', '#ce90e8', '#ff9c6e', '#5cdbd3'],
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(50,50,50,0.5)',
          axisPointer: {
            type: 'cross',
            label: {
              lineStyle: { color: '#009688' },
              crossStyle: { color: '#008acd' },
              shadowStyle: { color: 'rgba(200,200,200,0.2)' }
            }
          }
        },
        grid: {
          x: 20,
          y: 50,
          x2: 20,
          y2: 0,
          containLabel: true,
          borderColor: '#eee'
        },
        toolbox: { color: ['#1e90ff', '#1e90ff', '#1e90ff', '#1e90ff'], effectiveColor: '#ff4500' },
        xAxis: [
          {
            show: false,
            type: 'category',
            boundaryGap: false,
            splitArea: {
              show: true,
              areaStyle: { color: ['rgba(250,250,250,0.1)', 'rgba(200,200,200,0.1)'] }
            },
            axisLabel: {
              rotate: 38
            },
            axisLine: { lineStyle: { color: '#b7bdc7' }},
            splitLine: { lineStyle: { color: ['#eee'] }},
            splitNumber: 24
          }
        ],
        yAxis: [{
          type: 'value',
          axisTick: { show: true, length: 0 },
          splitNumber: 5,
          splitLine: { lineStyle: { color: ['#eee'] }},
          axisLine: { lineStyle: { color: '#b7bdc7' }}
        }],
        legend: {
          y: 10,
          textStyle: { color: '#8e929b' }
        },
        series: [
          {
            name: '在线用户',
            type: 'line',
            stack: '在线用户',
            smooth: 0.6,
            symbol: 'none',
            symbolSize: 10,
            data: thatDayData.onlieUser,
            areaStyle: {}
          },
          {
            name: '送礼用户',
            type: 'line',
            stack: '送礼用户',
            smooth: 0.6,
            symbol: 'none',
            symbolSize: 10,
            data: thatDayData.senGiftUser,
            areaStyle: {}
          },
          {
            name: '礼物价值',
            type: 'line',
            stack: '礼物价值',
            smooth: 0.6,
            symbol: 'none',
            symbolSize: 10,
            data: thatDayData.sendGiftAmount,
            areaStyle: {}
          }]
      }, true)
    },
    handleClose() {
      this.$emit('close')
    },
    rangeDateChange(val) {
      if (!val || val.length <= 0) {
        this.initRangeDate()
      }

      this.renderData()
    },
    initRangeDate() {
      this.rangeDate = []
      this.rangeDate.push(new Date(recentDate(-2, '/')).getTime())
      this.rangeDate.push(new Date().getTime())
    }
  }
}
</script>

<style scoped lang='scss'>
  .filter-containers {
    padding-bottom: 10px;
    float:right;

    .filter-item {
      display: inline-block;
      vertical-align: middle;
      margin-bottom: 10px;
    }
  }
  .user_info {
    position: relative;
    top: -20px;
    .info {
      position: absolute;
      top: 20px;
      left: 20px;
      span {
        padding: 0px 10px;
      }
    }
  }
</style>
