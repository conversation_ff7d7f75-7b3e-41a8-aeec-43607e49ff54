<template>

  <el-dialog
    title="房间访客记录"
    :visible="true"
    width="70%"
    :before-close="handleClose"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <div class="filter-container">
      <div class="filter-item">
        <account-input v-model="listQuery.userId" placeholder="用户ID" :sys-origin="sysOrigin" />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
      height="500"
    >
      <el-table-column label="用户" align="left" min-width="200">
        <template slot-scope="scope">
          <user-table-exhibit
            :user-profile="scope.row.userProfile"
          />
        </template>
      </el-table-column>

      <el-table-column prop="createTime" label="浏览时间" align="center" width="200">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <user-deatils-drawer
      v-if="userDeatilsDrawer"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawer=false"
    />

  </el-dialog>
</template>

<script>

import { listRecentVisitors } from '@/api/room'
import Pagination from '@/components/Pagination'

export default {
  components: { Pagination },
  props: {
    sysOrigin: {
      type: String,
      required: true
    },
    roomId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      list: [],
      userDeatilsDrawer: false,
      userRegisterInfo: {},
      thatSelectedUserId: '',
      listQuery: {
        cursor: 1,
        limit: 20,
        roomId: '',
        userId: ''
      },
      total: 0,
      searchLoading: false,
      listLoading: false,
      searchDisabled: false
    }
  },
  watch: {
    roomId: {
      immediate: true,
      deep: true,
      handler(newVal) {
        this.listQuery.roomId = newVal
        this.renderData()
      }
    }
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      listRecentVisitors(that.listQuery).then(res => {
        that.total = res.body.length || 0
        that.list = res.body || []
        that.searchLoading = that.listLoading = false
      }).catch(er => {
        that.searchLoading = that.listLoading = false
      })
    },
    handleSearch() {
      this.searchLoading = true
      this.renderData()
    },
    queryUserDetails(userId) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = userId
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>

<style scoped lang='scss'>
  .filter-containers {
    padding-bottom: 10px;
    float:right;

    .filter-item {
      display: inline-block;
      vertical-align: middle;
      margin-bottom: 10px;
    }
  }
  .user_info {
    position: relative;
    top: -20px;
    .info {
      position: absolute;
      top: 20px;
      left: 20px;
      span {
        padding: 0px 10px;
      }
    }
  }
</style>
