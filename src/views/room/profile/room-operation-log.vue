<template>
  <div class="room-operation-log-drawer">
    <el-drawer
      title="房间操作日志"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      size="450px"
      :modal-append-to-body="false"
    >
      <div class="deatils-content">
        <div class="filter-container">
          <el-select
            v-model="listQuery.businessCode"
            placeholder="业务CODE"
            style="width: 100%;"
            class="filter-item"
            clearable
            @change="renderDate"
          >
            <el-option
              v-for="(item, index) in logEvents"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div v-loading="listLoading">
          <el-timeline>
            <el-timeline-item
              v-for="(item, index) in list"
              :key="index"
              placement="top"
              :timestamp="item.createTime"
            >
              <div class="title"> {{ item.apiDesc }}<el-tag style="margin:0px 10px;" size="mini">expiredTime:{{ item.expiredTime }}</el-tag></div>
              <div class="request-data">
                <json-editor v-model="item.jsonCmd" :read-only="true" />
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>

import { listApiOperationLog } from '@/api/tools'
import { apiRequestLogs } from '@/constant/type'
import JsonEditor from '@/components/JsonEditor'
export default {
  components: { JsonEditor },
  props: {
    roomId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      list: [],
      listQuery: {
        businessContent: '',
        businessCode: ''
      },
      listLoading: false,
      logEvents: apiRequestLogs
    }
  },
  watch: {
    roomId: {
      immediate: true,
      handler(newVal) {
        if (!newVal) {
          return
        }
        this.listQuery.businessContent = newVal
        this.renderDate()
      }
    }
  },
  methods: {
    renderDate() {
      const that = this
      that.list = []
      that.listLoading = true
      listApiOperationLog(that.listQuery).then(res => {
        that.listLoading = false
        const list = res.body || []
        list.forEach(element => {
          element.jsonCmd = element.cmd ? JSON.parse(element.cmd) : '{}'
        })
        that.list = list
      }).catch(er => {
        that.listLoading = false
      })
    },
    handleClose() {
      this.$emit('close')
    },
    handleBaseInfoSuccess(data) {
      this.baseInfo = data
    }
  }
}
</script>
<style scoped lang="scss">
.room-operation-log-drawer {
    .deatils-content {
        padding: 0px 10px;
        .request-data {
          margin-top: 10px;
        }
    }
}

</style>
