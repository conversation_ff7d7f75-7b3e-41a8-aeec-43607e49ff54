<template>
  <div class="app-container">

    <div class="filter-container">
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        新增
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="主题背景图" align="center">
        <template slot-scope="scope">
          <el-image
            style="width: 60px; height: 80px"
            :src="scope.row.themeBack"
            :preview-src-list="[scope.row.themeBack]"
          />
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          <div>
            <span v-if="scope.row.showcase === false">下架</span>
            <span v-if="scope.row.showcase === true">上架</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="themeMoney" label="主题金额" align="center" />
      <el-table-column prop="createTime" label="创建时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="100">
        <template slot-scope="scope">
          <el-button type="text" @click.native="handleUpdate(scope.row)">修改</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
    <el-dialog
      :title="textOptTitle"
      :visible.sync="formVisible"
      :before-close="handleClose"
      width="400px"
    >
      <div v-loading="submitLoading">
        <el-form ref="form" :model="form" :rules="rules" label-width="110px">
          <el-form-item label="主题背景图" prop="themeBack">
            <upload-image
              v-model="form.themeBack"
              :file-dir="$application.fileBucket.back"
            />
          </el-form-item>
          <el-form-item label="状态" prop="showcase">
            <el-select
              v-model="form.showcase"
              placeholder="状态"
              clearable
              style="width:100%;"
              class="filter-item"
            >
              <el-option label="上架" :value="true" />
              <el-option label="下架" :value="false" />
            </el-select>
          </el-form-item>

          <el-form-item label="主题金额" prop="themeMoney">
            <el-input v-model.trim="form.themeMoney" type="number" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm()">保存</el-button>
            <el-button @click="handleClose()">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { themeTable, updateRoomTheme, deleteRoomTheme, addRoomTheme } from '@/api/room-theme'
import Pagination from '@/components/Pagination'

export default {
  components: { Pagination },
  data() {
    return {
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20
      },
      formVisible: false,
      textOptTitle: '',
      // fileList: [],
      form: {
        id: '',
        themeBack: '',
        showcase: '',
        themeMoney: ''
      },
      submitLoading: false,
      rules: {
        themeBack: [
          { required: true, message: '请填写主题背景图', trigger: 'change' }
        ],
        showcase: [
          { required: true, message: '请填写状态', trigger: 'blur' }
        ],
        themeMoney: [
          { required: true, message: '请填写主题金额', trigger: 'blur' }
        ]
      },
      listLoading: true
    }
  },
  created() {
    this.renderData()
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      themeTable(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        // that.fileList = []
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData()
    },
    clearForm() {
      this.form.id = ''
      this.form.themeBack = ''
      this.form.showcase = ''
      this.form.themeMoney = ''
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (valid) {
          that.submitLoading = true
          if (that.form.id) {
            updateRoomTheme(that.form).then(res => {
              that.submitLoading = false
              that.formVisible = false
              that.clearForm()
              that.renderData()
            }).catch(er => {
              that.submitLoading = false
              console.error(er)
              this.$emit('fail')
            })
            return
          }
          addRoomTheme(that.form).then(res => {
            that.submitLoading = false
            that.formVisible = false
            that.clearForm()
            that.renderData()
          }).catch(er => {
            that.submitLoading = false
            console.error(er)
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    },
    // 删除
    handlDel(id) {
      this.$confirm('确认删除吗？', '提示', {
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        deleteRoomTheme(id).then((res) => {
          this.listLoading = false
          this.$message({
            message: '删除成功',
            type: 'success'
          })
          this.renderData()
        })
      }).catch(() => {

      })
    },
    handleClose() {
      this.formVisible = false
    },
    handleCreate() {
      this.formVisible = true
      this.textOptTitle = '添加'
      this.clearForm()
      console.log(' this.form', this.form)
    },
    handleUpdate(row) {
      this.textOptTitle = '修改'
      this.formVisible = true
      this.clearForm()
      this.form = Object.assign(this.form, row)
    }
  }
}
</script>
