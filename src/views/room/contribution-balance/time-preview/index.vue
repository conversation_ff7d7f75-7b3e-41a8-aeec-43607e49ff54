<template>
  <div class="app-container-quality">
    <div class="filter-container">
      <div class="filter-item">
        <search-room-input @success="searchRoomSuccess" @fail="searchRoomFail" @load="loadSearchRoom" />
      </div>
      <div class="filter-item">
        <el-select
          v-model="listQuery.type"
          placeholder="类型"
          @change="changeType()"
        >
          <el-option v-for="item in types" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </div>
      <div v-if="listQuery.type === 'DAILY'" class="filter-item">
        <el-date-picker
          v-model="listQuery.dateNumber"
          type="date"
          value-format="yyyyMMdd"
          placeholder="选择日期"
        />
      </div>
      <div v-if="listQuery.type === 'WEEKLY'" class="filter-item">
        <el-date-picker
          v-model="listQuery.dateNumber"
          type="week"
          value-format="yyyyMMdd"
          format="yyyy-MM-dd"
          placeholder="选择周"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="房间" align="center">
        <template slot-scope="scope">
          <div v-if="scope.row.roomProfile" class="room-profile flex-l">
            <div class="avatar" style="margin: auto 0.2rem;">
              <el-image
                style="width:1.5rem;height: 1.5rem;border-radius: .2rem;"
                :src="scope.row.roomProfile.roomCover"
                :preview-src-list="[scope.row.roomProfile.roomCover]"
              >
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </el-image>
            </div>
            <div class="info nowrap-ellipsis">
              <div class="nickname">
                <el-link v-if="scope.row.roomProfile.roomName" @click="queryRoomDetails(scope.row.roomProfile.id)">
                  {{ scope.row.roomProfile.roomName }}
                </el-link>
              </div>
              <div class="desc">
                {{ scope.row.roomProfile.roomAccount }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="totalQuantity" label="数量" align="center" />
      <el-table-column v-if="listQuery.type === 'DAILY'" prop="dateNumber" label="时间" align="center" />
      <el-table-column v-if="listQuery.type === 'WEEKLY'" prop="dateNumber" label="时间(周一至周日)" align="center" />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <room-deatils-drawer
      v-if="roomDeatilsDrawerVisible"
      :room-id="roomId"
      @close="roomDeatilsDrawerVisible=false"
    />
  </div>
</template>

<script>

import { roomTimeContributionBalanceTable } from '@/api/room'
import Pagination from '@/components/Pagination'
import RoomDeatilsDrawer from '@/components/data/RoomDeatilsDrawer'

export default {
  name: 'TimePreview',
  components: { Pagination, RoomDeatilsDrawer },
  data() {
    return {
      searchDisabled: false,
      roomDeatilsDrawerVisible: false,
      list: [],
      total: 0,
      roomId: '',
      types: [{
        name: '天',
        value: 'DAILY'
      }, {
        name: '周',
        value: 'WEEKLY'
      }],
      listQuery: {
        cursor: 1,
        limit: 20,
        roomId: '',
        type: 'DAILY',
        dateNumber: ''
      },
      listLoading: true
    }
  },
  created() {
    this.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      roomTimeContributionBalanceTable(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    changeType() {
      this.listQuery.dateNumber = ''
      this.handleSearch()
    },
    handleSearch() {
      this.renderData(true)
    },
    queryRoomDetails(roomId) {
      this.roomDeatilsDrawerVisible = true
      this.roomId = roomId
    },
    loadSearchRoom() {
      this.searchDisabled = true
    },
    searchRoomSuccess(res) {
      this.searchDisabled = false
      if (!res) {
        return
      }
      this.listQuery.roomId = res.id
    },
    searchRoomFail() {
      this.listQuery.roomId = ''
      this.searchDisabled = false
    }
  }
}
</script>
