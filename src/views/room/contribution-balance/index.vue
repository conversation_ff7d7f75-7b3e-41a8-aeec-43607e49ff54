<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="item in tables" :key="item.name" :label="item.title" :name="item.component" />
      <component :is="activeName" />
    </el-tabs>
  </div>
</template>
<script>
import TotalPreview from './total-preview'
import TimePreview from './time-preview'
export default {
  name: 'StatisticsQuailtyUsersIndex',
  components: { TotalPreview, TimePreview },
  data() {
    return {
      activeName: 'TotalPreview',
      tables: [
        {
          title: '总预览',
          component: 'TotalPreview'
        },
        {
          title: '时间段预览',
          component: 'TimePreview'
        }
      ]
    }
  }
}
</script>
