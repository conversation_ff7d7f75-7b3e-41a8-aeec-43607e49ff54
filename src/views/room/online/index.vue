<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="归属系统"
        style="width: 120px"
        class="filter-item"
        @change="changeSysOrigin"
      >
        <el-option
          v-for="(item, index) in permissionsSysOriginPlatforms"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>

      <div class="filter-item">
        <select-system-region
          v-if=" listQuery.sysOrigin === 'ASWAT'"
          ref="regionSelectPolicy"
          v-model="listQuery.region"
          :sys-origin="listQuery.sysOrigin"
          placeholder="请选择区域"
        />
      </div>

      <el-input
        v-model.trim="listQuery.roomId"
        v-number
        placeholder="房间ID"
        style="width: 200px;"
        class="filter-item"
      />
      <el-input
        v-model.trim="listQuery.roomAccount"
        v-number
        placeholder="房间Account"
        style="width: 200px;"
        class="filter-item"
      />
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>

    </div>
    <div v-loading="listLoading">
      <el-row :gutter="10">
        <el-col v-for="item in list" :key="item.id" class="row" :xs="24" :sm="8" :md="6" :lg="6" :xl="4">
          <el-card class="box-card">
            <div slot="header">
              <div class="header-user">
                <div class="user-info">
                  <div class="imgs flex-l">
                    <avatar :url="item.profile.roomCover" size="mini" />
                    <el-tag size="mini" style="margin: 0px 10px;">{{ item.activeRoom.region }}</el-tag>
                    <el-tag v-if=" item.activeRoom.hot" type="danger" size="mini">热门</el-tag>
                  </div>
                </div>
                <div class="operaiton">
                  <el-dropdown>
                    <span class="el-dropdown-link">
                      操作<i class="el-icon-arrow-down el-icon--right" />
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item @click.native="clickCallMember(item)">在线成员</el-dropdown-item>
                      <el-dropdown-item @click.native="clickAccountHandle(item)">账号处理</el-dropdown-item>
                      <el-dropdown-item @click.native="clickQueryRoomDetails(item)">房间详情</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </div>
            </div>
            <div class="card-body">
              <div class="nowrap-ellipsis">名称：<a :title="item.profile.roomName">{{ item.profile.roomName }}</a></div>
              <div class="nowrap-ellipsis">ID：<a :title="item.profile.id">{{ item.profile.id }}</a></div>
              <div>
                <el-row>
                  <el-col :span="12">
                    <div class="nowrap-ellipsis">
                      账号：<a :title="item.profile.roomAccount">{{ item.profile.roomAccount }}</a>
                    </div>
                  </el-col>
                  <el-col v-if="item.profile.setting && item.profile.setting.password" :span="12">
                    <div class="nowrap-ellipsis">密码：<a :title="item.profile.setting">{{ item.profile.setting.password }}</a></div>
                  </el-col>
                </el-row>
              </div>
              <div class="nowrap-ellipsis">公告：<a :title="item.profile.roomDesc">{{ item.profile.roomDesc }}</a></div>
              <flag-icon class="user-flag" :code="item.activeRoom.countryCode" :tooltip="item.activeRoom.countryName" size="24" />
              <div v-if="item.activeRoom.familyProfile" class="family-info nowrap-ellipsis flex-l">
                家族：  <avatar :url="item.activeRoom.familyProfile.familyAvatar" size="mini" />
                &nbsp;[{{ item.activeRoom.familyProfile.familyAccount }}] <a :title="item.activeRoom.familyProfile.familyName ">{{ item.activeRoom.familyProfile.familyName }}</a>
              </div>
              <div v-else class=" nowrap-ellipsis">
                家族： 无
              </div>
            </div>
          </el-card></el-col>
      </el-row>
    </div>

    <online-member
      v-if="historyMemberVisable"
      :session-id="activeSessionId"
      @close="historyMemberVisable=false"
    />

    <account-hanle
      v-if="accountHandleVisible"
      :user-id="activeUserId"
      @success="accountHandleSuccess"
      @close="accountHandleVisible=false"
    />

    <room-deatils-drawer
      v-if="roomDeatilsDrawerVisible"
      :room-id="activeRoomId"
      @close="roomDeatilsDrawerVisible=false"
    />

    <user-deatils-drawer
      v-if="userDeatilsDrawerVisible"
      :user-id="activeUserId"
      @close="userDeatilsDrawerVisible=false"
    />
  </div>
</template>

<script>

import { onlineRoom } from '@/api/room-voice'
import OnlineMember from './online-member'
import AccountHanle from '@/components/data/AccountHanle'
import { sysOriginPlatforms } from '@/constant/origin'
import { mapGetters } from 'vuex'
import RoomDeatilsDrawer from '@/components/data/RoomDeatilsDrawer'

export default {
  name: 'RoomOnline',
  components: { OnlineMember, AccountHanle, RoomDeatilsDrawer },
  data() {
    return {
      activeRoomId: '',
      roomDeatilsDrawerVisible: false,
      sysOriginPlatforms,
      userDeatilsDrawerVisible: false,
      accountHandleVisible: false,
      historyMemberVisable: false,
      listLoading: false,
      activeSessionId: '',
      activeUserId: '',
      listQuery: {
        roomAccount: '',
        sysOrigin: '',
        roomId: '',
        limit: 200,
        region: ''
      },
      list: []
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        that.list = []
      }
      that.listLoading = true
      onlineRoom(that.listQuery).then(res => {
        that.listLoading = false
        that.list = res.body || []
      }).catch(er => {
        console.error(er)
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    changeSysOrigin() {
      this.$refs.regionSelectPolicy.clearValue()
      if (this.listQuery.region !== 'ASWAT') {
        this.listQuery.region = ''
      }
      this.renderData()
    },
    clickCallMember(item) {
      const that = this
      that.activeSessionId = item.profile.id
      that.historyMemberVisable = true
    },
    clickAccountHandle(item) {
      this.activeUserId = item.profile.userId
      this.accountHandleVisible = true
    },
    clickQueryRoomDetails(item) {
      this.activeRoomId = item.profile.id
      this.roomDeatilsDrawerVisible = true
    },
    accountHandleSuccess(data) {
      this.$message({
        message: 'Successful',
        type: 'success'
      })
    },
    clickUserDetails(item) {
      this.activeUserId = item.userId
      this.userDeatilsDrawerVisible = true
    }
  }
}
</script>
<style scoped lang="scss">
.row {
  margin-bottom: 10px;
}
.header-user {
   position: relative;
  .infos{
    position: absolute;
    left: 40px;
    top: 8px;
    right: 45px;
  }
  .operaiton {
    position:absolute;
    right: 0px;
    top: 8px;
  }
}
.border-top-red {
  border-top: 1px solid red;
}
.border-top-yello {
  border-top: 1px solid yellow;
}
.card-body {
  position: relative;
  line-height: 30px;
  color: #666;
  .line {
    display: flex;
    .line-item {
      width: 100%;
      text-align: left;
    }
  }
  .user-flag {
    position: absolute;
    right: -15px;
    bottom:  -30px;
  }
}
.archives-details {
  height: 90px;
}
.not-upload-call {
  text-align: center;
  line-height: 90px;
  background-color: #fbfbfb;
}

.nowrap-ellipsis {
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
}
.anchors-col {
  height: 30px;
}

</style>
