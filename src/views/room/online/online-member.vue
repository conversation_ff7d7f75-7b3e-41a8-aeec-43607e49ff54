<template>
  <div class="live-broadcast-history-member">
    <el-drawer
      title="成员信息"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      size="450px"
      :modal-append-to-body="false"
    >
      <div class="online-member-content">
        <div v-for="(item, index) in listAudience" :key="'t_'+ index" class="time-card">
          <h4 class="user-info">
            <div class="imgs"><avatar :url="item.userAvatar" :gender="item.gender" size="mini" /> </div>
            <div class="infos" @click="clickUserDetails(item)"><gender :gender="item.gender" :gender-name="item.genderName" :desc="item.userNickname" /></div>
          </h4>
          <div class="account-handle"><el-button type="text" size="mini" @click="clickAccountHandle(item)">账号处理</el-button></div>
          <div slot="dot"><flag-icon class="time-dot" :code="item.countryCode" :tooltip="item.countryName" size="26" /></div>
        </div>
        <el-divider class="cursor-pointer">
          <div v-if="notData === true">已加载全部</div>
          <div v-else v-loading="listLoading" @click="loadMore">点击加载更多</div>
        </el-divider>
      </div>
    </el-drawer>

    <user-deatils-drawer
      v-if="userDeatilsDrawerVisible"
      :user-id="activeUserId"
      @close="userDeatilsDrawerVisible=false"
    />

    <account-hanle
      v-if="accountHandleVisible"
      :user-id="activeUserId"
      @success="accountHandleSuccess"
      @close="accountHandleVisible=false"
    />
  </div>
</template>

<script>

import { flowMember } from '@/api/room-voice'
import AccountHanle from '@/components/data/AccountHanle'

export default {
  name: 'LiveBroadcastOnlineMember',
  components: { AccountHanle },
  props: {
    sessionId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      accountHandleVisible: false,
      userDeatilsDrawerVisible: false,
      activeUserId: '',
      listLoading: false,
      listQuery: {
        sessionId: '',
        lastId: ''
      },
      listAudience: [],
      notData: false,
      videoRealTimeCharts: null,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > new Date()
        }
      }
    }
  },
  watch: {
    sessionId: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal) {
          this.listQuery.sessionId = newVal
          return
        }
      }
    }
  },
  created() {
    this.renderMemberData()
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    renderMemberData() {
      const that = this
      that.listLoading = true
      flowMember(that.listQuery).then(res => {
        that.listLoading = false
        const result = res.body || []
        if (result.length === 0) {
          that.notData = true
          return
        }
        if (!that.listAudience || that.listAudience.length === 0) {
          that.listAudience = result
          return
        }
        that.listAudience = that.listAudience.concat(result)
      }).catch(er => {
        console.error(er)
        that.listLoading = false
      })
    },
    loadMore() {
      if (this.notData === true || !this.listAudience || this.listAudience.length === 0) {
        return
      }
      this.listQuery.lastId = this.listAudience[this.listAudience.length - 1].timingId

      if (!this.listQuery.lastId) {
        return
      }
      this.renderMemberData()
    },
    clickUserDetails(item) {
      this.activeUserId = item.userId
      this.userDeatilsDrawerVisible = true
    },
    clickAccountHandle(item) {
      this.activeUserId = item.userId
      this.accountHandleVisible = true
    },
    accountHandleSuccess(data) {
      this.$message({
        message: 'Successful',
        type: 'success'
      })
    }
  }
}
</script>
<style scoped lang="scss">
.time-card {
  position: relative;
  padding: 5px;
  border-radius: 5px;
  -webkit-box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  .user-info {
    .infos{
      position: absolute;
      top: 30px;
      left: 70px;
      right: 100px;
    }
  }
}
.time-dot{
    position: absolute;
    top: 26px;
    left: 40px;
}
.account-handle {
  position: absolute;
  top: 26px;
  right: 10px;
}
</style>
