<template>
  <div class="app-container">

    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width:120px;"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>

      <div class="filter-item">
        <account-input v-model="listQuery.anchorUserId" :sys-origin="listQuery.sysOrigin" placeholder="主播ID" />
      </div>
      <div class="filter-item">
        <account-input v-model="listQuery.refunderUserId" :sys-origin="listQuery.sysOrigin" placeholder="退款人ID" />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
      @cell-mouse-enter="handleMouseEnter"
    >

      <el-table-column label="主播信息" align="center" show-overflow-tooltip width="220">
        <template slot-scope="scope">
          <div class="box-header flex-l">
            <div class="avatar">
              <avatar :url="scope.row.anchorUserProfile.userAvatar" :gender="scope.row.anchorUserProfile.userSex" />
            </div>
            <div class="info nowrap-ellipsis" style="text-align: left;">
              <div class="nickname">
                <el-link @click="queryUserDetails(scope.row.anchorUserProfile.id)"><a :title="scope.row.anchorUserProfile.userNickname"> {{ scope.row.anchorUserProfile.userNickname }} </a></el-link>
              </div>
              <div class="sex-account">
                <gender :gender="scope.row.anchorUserProfile.userSex" :gender-name="scope.row.anchorUserProfile.userSexName" :desc="getAccountText(scope.row.anchorUserProfile)" />
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="退款人信息" align="center" show-overflow-tooltip width="220">
        <template slot-scope="scope">
          <div class="box-header flex-l">
            <div class="avatar">
              <avatar :url="scope.row.refundUserProfile.userAvatar" :gender="scope.row.refundUserProfile.userSex" />
            </div>
            <div class="info nowrap-ellipsis" style="text-align: left;">
              <div class="nickname">
                <el-link @click="queryUserDetails(scope.row.refundUserProfile.id)"><a :title="scope.row.refundUserProfile.userNickname"> {{ scope.row.refundUserProfile.userNickname }} </a></el-link>
              </div>
              <div class="sex-account">
                <gender :gender="scope.row.refundUserProfile.userSex" :gender-name="scope.row.refundUserProfile.userSexName" :desc="getAccountText(scope.row.refundUserProfile)" />
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="扣除目标" prop="trackRecord.candyQuantity" align="center" />
      <el-table-column label="扣除日期" prop="trackRecord.years" align="center" />
      <el-table-column label="创建时间" prop="trackRecord.createTime" align="center">
        <template slot-scope="scope">
          {{ scope.row.trackRecord.createTime | dateFormat }}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <user-deatils-drawer
      v-if="userDeatilsDrawer"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawer=false"
    />

  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { mapGetters } from 'vuex'
import { refundAnchorTrackRecordPage } from '@/api/sys'
export default {
  components: { Pagination },
  data() {
    return {
      thatRow: {},
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        anchorUserId: '',
        refunderUserId: '',
        sysOrigin: 'TIM_CHAT'
      },
      listLoading: true,
      searchDisabled: false,
      thatSelectedUserId: '',
      userDeatilsDrawer: false
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        this.listQuery.cursor = 1
        this.listQuery.list = []
      }
      that.listLoading = true
      refundAnchorTrackRecordPage(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    getAccountText(userInfo) {
      if (!userInfo) {
        return ''
      }
      const account = userInfo.account
      if (userInfo.ownSpecialId && userInfo.ownSpecialId.account) {
        return `${account} / ${userInfo.ownSpecialId.account}靓`
      }
      return account
    },
    handleSearch() {
      this.renderData(true)
    },
    renderDataSuccess() {
      this.$message({
        message: 'Successful',
        type: 'success'
      })
      this.renderData()
    },
    queryUserDetails(userId) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = userId
    },
    handleMouseEnter(row) {
      this.thatRow = row
    }
  }
}
</script>
<style scoped lang="scss">
.popover-content {
  max-width: 300px;
  line-height: 20px;
}
</style>
