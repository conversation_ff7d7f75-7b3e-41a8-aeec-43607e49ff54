<template>
  <div class="app-container">
    <div class="filter-container">
      <div class="filter-item">
        <account-input v-model="listQuery.userId" placeholder="用户ID" />
      </div>
      <div class="filter-item">
        <el-input
          v-model.trim="listQuery.deviceId"
          placeholder="设备ID"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="平台" prop="appPlatform" align="center" width="60">
        <template slot-scope="scope">
          <platform-svg-icon :icon="scope.row.appPlatform" />
        </template>
      </el-table-column>
      <el-table-column label="用户" align="center" min-width="200">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column label="设备ID" prop="deviceId" align="center" min-width="200" />
      <el-table-column label="IP" prop="ip" align="center" min-width="100">
        <template slot-scope="scope">
          <a :href="`https://www.ip138.com/iplookup.asp?ip=${scope.row.ip}&action=2`" target="__blank">{{ scope.row.ip }}</a>
        </template>
      </el-table-column>
      <el-table-column label="登录方式" prop="loginTypeName" align="center" min-width="100" />
      <el-table-column label="App版本" prop="appVersion" align="center" min-width="100" />
      <el-table-column prop="createTime" label="登录时间" width="200" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="60" align="center">
        <template slot-scope="scope">
          <el-dropdown>
            <span class="el-dropdown-link">
              <i class="el-icon-more" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="clickViolationTableVisible(scope.row)">违规记录</el-dropdown-item>
              <el-dropdown-item @click.native="accountStatus(scope.row)">账号处理记录</el-dropdown-item>
              <el-dropdown-item @click.native="accountHanle(scope.row)">账号处理</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <user-video-violation-table-dialog
      v-if="violationTableVisible"
      :row="thisRow"
      @close="violationTableVisible = false"
    />

    <user-account-status-log-drawer
      v-if="accountStatusLogTable"
      :row="thisRow"
      @close="accountStatusLogTable=false"
    />

    <account-hanle
      v-if="accountHandleVisible"
      :user-id="thatSelectedUserId"
      @success="renderDataSuccess"
      @close="accountHandleVisible=false"
    />

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
  </div>
</template>

<script>

import { loginLoggerPage } from '@/api/sys'
import Pagination from '@/components/Pagination'
import PlatformSvgIcon from '@/components/PlatformSvgIcon'
import UserVideoViolationTableDialog from '@/components/data/UserVideoViolationTableDialog'
import UserAccountStatusLogDrawer from '@/components/data/UserAccountStatusLogDrawer'
import AccountHanle from '@/components/data/AccountHanle'
export default {
  name: 'CandyPurchasing',
  components: { Pagination, PlatformSvgIcon, UserVideoViolationTableDialog, UserAccountStatusLogDrawer, AccountHanle },
  data() {
    return {
      accountStatusLogTable: false,
      thisRow: {},
      violationTableVisible: '',
      thatSelectedUserId: '',
      list: [],
      total: 0,
      rangeDate: [],
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: '',
        deviceId: ''
      },
      listLoading: true,
      clickUserId: '',
      searchDisabled: false
    }
  },
  created() {
    this.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      loginLoggerPage(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    clickViolationTableVisible(row) {
      this.thisRow = row
      this.violationTableVisible = true
    },
    accountStatus(item) {
      this.thisRow = item
      this.accountStatusLogTable = true
    },
    accountHanle(row) {
      this.thatSelectedUserId = row.id
      this.accountHandleVisible = true
    },
    renderDataSuccess() {
      this.$message({
        message: 'Successful',
        type: 'success'
      })
      this.renderData()
    }
  }
}
</script>
