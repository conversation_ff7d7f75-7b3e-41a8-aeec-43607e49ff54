<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="归属系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in permissionsSysOriginPlatforms"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <account-input v-model="listQuery.userId" :sys-origin="listQuery.sysOrigin" placeholder="被拉黑用户ID" />
      </div>
      <div class="filter-item">
        <account-input v-model="listQuery.createUserId" :sys-origin="listQuery.sysOrigin" placeholder="操作用户ID" />
      </div>
      <div class="filter-item">
        <account-input v-model="listQuery.roomId" :sys-origin="listQuery.sysOrigin" placeholder="房间账号" type="ROOM" />
      </div>
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="发送日期开始"
          end-placeholder="发送日期结束"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>

    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="操作用户" align="center" show-overflow-tooltip min-width="220">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" :tag-name="scope.row.createUserRoles" />
        </template>
      </el-table-column>
      <el-table-column label="被拉黑用户" align="center" show-overflow-tooltip min-width="220">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.beUserProfile" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column label="房间" align="center" min-width="200">
        <template slot-scope="scope">
          <div class="room-profile flex-l">
            <div class="avatar">
              <el-image
                style="width: 100%; height: 100%;"
                :src="scope.row.roomProfile.roomCover"
                :preview-src-list="[scope.row.roomProfile.roomCover]"
              >
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </el-image>
            </div>
            <div class="info nowrap-ellipsis">
              <div class="nickname">
                <el-link v-if="scope.row.roomProfile.roomName" @click="queryRoomDetails(scope.row)">
                  {{ scope.row.roomProfile.roomName }}
                </el-link>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="expiredTime" label="过期时间" align="center" width="200">
        <template slot-scope="scope">
          {{ scope.row.expiredTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center" width="200">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
    </el-table>
    <div v-if="listQuery.lastId" class="load-more">
      <span v-if="notData">已加载全部</span>
      <el-button v-else size="mini" :disabled="loadMoreLoading" :loading="loadMoreLoading" @click="clickLoadMore">加载更多</el-button>
    </div>

    <room-deatils-drawer
      v-if="roomDeatilsDrawerVisible"
      :room-id="thatRow.roomId"
      @close="roomDeatilsDrawerVisible=false"
    />

  </div>
</template>

<script>
import { getRoomBlacklist } from '@/api/room'
import RoomDeatilsDrawer from '@/components/data/RoomDeatilsDrawer'
import { pickerOptions } from '@/constant/el-const'
import { sysOriginPlatforms } from '@/constant/origin'
import { mapGetters } from 'vuex'

export default {
  components: { RoomDeatilsDrawer },
  data() {
    return {
      thatRow: {},
      sysOriginPlatforms,
      roomDeatilsDrawerVisible: false,
      thatSelectedUserId: '',
      pickerOptions,
      list: [],
      notData: false,
      loadMoreLoading: false,
      checkList: [],
      total: 0,
      listQuery: {
        size: 20,
        sysOrigin: '',
        roomId: '',
        lastId: '',
        userId: '',
        createUserId: '',
        startTime: '',
        endTime: ''
      },
      listLoading: true,
      rangeDate: '',
      redPacketId: '',
      searchLoading: false
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    }
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.list = []
        that.listQuery.cursor = 1
        that.listQuery.lastId = ''
      }
      that.listLoading = true
      getRoomBlacklist(that.listQuery).then(res => {
        that.listLoading = false
        that.loadMoreLoading = false
        const { body } = res
        const list = body || []
        that.notData = list.length <= 0
        if (!that.notData) {
          that.list = that.list.concat(list)
          that.listQuery.lastId = that.list[that.list.length - 1].timingId
        }
      }).catch(er => {
        that.listLoading = false
        that.loadMoreLoading = false
      })
    },
    handleSearch() {
      this.searchLoading = true
      this.renderData(true)
    },
    queryRoomDetails(row) {
      this.thatRow = row
      this.roomDeatilsDrawerVisible = true
    },
    getReceiveUsersLength(row) {
      return row.receiveUsers ? row.receiveUsers.length : 0
    },
    clickLoadMore() {
      const that = this
      that.loadMoreLoading = true
      that.renderData()
    }
  }
}
</script>
<style scoped lang="scss">
.room-avatar {
  position: relative;
  width: 50px;
  height: 50px;
  margin: auto;
  flex-shrink: 0;
  .flag-icon {
    position: absolute;
    bottom: 0px;
  }
}

.room-name {
  width: 100%;
  text-align: left;
  padding-left: 10px;
}

.load-more {
  padding: 20px;
  text-align: center;
}
.content {
  overflow: auto;
  padding: 10px 0px;
}
.room-profile {
  text-align: left;
  .avatar {
    width: 50px;
    height: 50px;
    border-radius: 100%;
    overflow: hidden;
    flex-shrink: 0;
  }
  .info {
    padding: 0px 5px;
    width: 100%;
    > div {
      line-height: 30px;
    }
  }
 }
</style>
