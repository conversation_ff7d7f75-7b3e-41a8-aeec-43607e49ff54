<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-autocomplete
        v-model="countryCodeStr"
        popper-class="my-autocomplete"
        :fetch-suggestions="querySearch"
        placeholder="国家"
        style="width: 200px"
        class="filter-item"
        clearable
        @clear="handleDelCountryCodeClick"
        @select="handleCountryCodeClick"
      >
        <i
          v-if="!countryCodeStr"
          slot="suffix"
          class="el-icon-edit el-input__icon"
        />
        <template slot-scope="{ item }">
          <div class="name">{{ item.phonePrefix + ' ' + item.countryName }}</div>
        </template>
      </el-autocomplete>
      <div class="filter-item">
        <account-input v-model="listQuery.userId" :sys-origin="listQuery.sysOrigin" placeholder="邀请用户" />
      </div>
      <div class="filter-item">
        <account-input v-model="listQuery.inviteUserId" :sys-origin="listQuery.sysOrigin" placeholder="被邀请用户" />
      </div>
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="发送日期开始"
          end-placeholder="发送日期结束"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>

    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="邀请用户" align="center" show-overflow-tooltip min-width="220">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column label="被邀请用户" align="center" show-overflow-tooltip min-width="220">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.beUserProfile" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column prop="commission" label="奖金" align="center" min-width="100" />
      <el-table-column prop="deviceId" label="设备号" align="center" min-width="200" />
      <el-table-column prop="createTime" label="创建时间" align="center" width="200">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

  </div>
</template>

<script>
import { pageUserInviteRegister, getCountryAlls } from '@/api/sys'
import { pickerOptions } from '@/constant/el-const'
import Pagination from '@/components/Pagination'
import { mapGetters } from 'vuex'

export default {
  components: { Pagination },
  data() {
    return {
      pickerOptions,
      list: [],
      total: 0,
      countryCodeStr: '',
      allCountryList: [],
      listQuery: {
        cursor: 1,
        limit: 20,
        sysOrigin: '',
        userId: '',
        inviteUserId: '',
        startTime: '',
        endTime: ''
      },
      listLoading: true,
      rangeDate: []
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    }
  },
  created() {
    const that = this
    that.loadCountryAlls()
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },

  methods: {
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      pageUserInviteRegister(that.listQuery).then(res => {
        that.listLoading = false
        const { body } = res
        that.list = body.records || []
        that.total = body.total || 0
      }).catch(er => {
        that.listLoading = false
      })
    },
    querySearch(queryString, cb) {
      var restaurants = this.allCountryList
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    createFilter(queryString) {
      return (restaurant) => {
        if (restaurant.alphaTwo.toLowerCase().indexOf(queryString.toLowerCase()) === 0) {
          return true
        }
        if (restaurant.alphaThree.toLowerCase().indexOf(queryString.toLowerCase()) === 0) {
          return true
        }
        if (restaurant.countryName.toLowerCase().indexOf(queryString.toLowerCase()) === 0) {
          return true
        }
        if (restaurant.aliasName.toLowerCase().indexOf(queryString.toLowerCase()) === 0) {
          return true
        }
        return (String(restaurant.phonePrefix).indexOf(queryString.toLowerCase()) === 0)
      }
    },
    loadCountryAlls() {
      const that = this
      getCountryAlls().then(res => {
        that.allCountryList = res.body || {}
      }).catch(er => {
      })
    },
    handleDelCountryCodeClick() {
      this.listQuery.countryCode = ''
      this.countryCodeStr = ''
      this.handleSearch()
    },
    handleCountryCodeClick(item) {
      this.listQuery.countryCode = item.alphaTwo
      this.countryCodeStr = item.phonePrefix + ' ' + item.countryName
      this.handleSearch()
    },
    handleSearch() {
      this.renderData(true)
    }
  }
}
</script>
<style scoped lang="scss">
.room-avatar {
  position: relative;
  width: 50px;
  height: 50px;
  margin: auto;
  flex-shrink: 0;
  .flag-icon {
    position: absolute;
    bottom: 0px;
  }
}

.room-name {
  width: 100%;
  text-align: left;
  padding-left: 10px;
}

.load-more {
  padding: 20px;
  text-align: center;
}
.content {
  overflow: auto;
  padding: 10px 0px;
}
</style>
