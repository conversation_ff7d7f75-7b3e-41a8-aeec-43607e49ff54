<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.status"
        placeholder="状态"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in status"
          :key="index"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <div class="filter-item">
        <account-input v-model="listQuery.sendApplyUserId" placeholder="发送用户ID" />
      </div>
      <div class="filter-item">
        <account-input v-model="listQuery.acceptApplyUserId" placeholder="接收用户ID" />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="发送用户" align="center" min-width="200">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.sendApplyUser" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column label="接收用户" align="center" min-width="200">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.acceptApplyUser" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column label="金额" prop="applyConsumeGold" align="center" min-width="100" />
      <el-table-column label="状态" prop="ip" align="center" min-width="100">
        <template slot-scope="scope">
          {{ statusMap[scope.row.status] ? statusMap[scope.row.status].name : '?' }}
        </template>
      </el-table-column>
      <el-table-column label="时间" width="200" align="center">
        <template slot-scope="scope">
          <div>创建:{{ scope.row.createTime | dateFormat }}</div>
          <div>修改:{{ scope.row.updateTime | dateFormat }}</div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
  </div>
</template>

<script>

import { pageCpApply } from '@/api/table'
import Pagination from '@/components/Pagination'
import { toMap } from '@/utils'
export default {
  name: 'CandyPurchasing',
  components: { Pagination },
  data() {
    return {
      list: [],
      total: 0,
      rangeDate: [],
      listQuery: {
        cursor: 1,
        limit: 20,
        sendApplyUserId: '',
        acceptApplyUserId: '',
        status: ''
      },
      listLoading: true,
      status: [
        { value: 'WAIT', name: '等待' },
        { value: 'AGREE', name: '同意' },
        { value: 'DISMISS', name: '解散' },
        { value: 'REFUSE', name: '拒绝' },
        { value: 'REIMBURSE', name: '退款' }
      ],
      statusMap: {}
    }
  },
  created() {
    this.statusMap = toMap(this.status, 'value')
    this.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      pageCpApply(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    renderDataSuccess() {
      this.$message({
        message: 'Successful',
        type: 'success'
      })
      this.renderData()
    }
  }
}
</script>
