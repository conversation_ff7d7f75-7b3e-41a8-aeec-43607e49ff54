<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <div><account-input v-model="listQuery.userId" placeholder="用户id" type="USER" :sys-origin="listQuery.sysOrigin" /></div>
      </div>
      <el-select
        v-model="listQuery.operateType"
        placeholder="操作类型"
        style="width:120px;"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="设置密码" :value="'ADD_PASSWORD'" />
        <el-option label="修改密码" :value="'UPDATE_PASSWORD'" />
      </el-select>
      <el-select
        v-model="listQuery.businessType"
        placeholder="业务类型"
        style="width:120px;"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="支付密码" :value="'PAY_PASSWORD'" />
        <el-option label="账号登录密码" :value="'ACCOUNT_PASSWORD'" />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>

    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="sysOrigin" label="系统" align="center" width="100">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column label="用户" align="center" min-width="100">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column prop="imei" label="设备" align="center" width="300" />
      <el-table-column label="操作类型" align="center">
        <template slot-scope="scope">
          <div>
            <el-tag v-if="scope.row.operateType === 'ADD_PASSWORD'">设置密码</el-tag>
            <el-tag v-else-if="scope.row.operateType === 'UPDATE_PASSWORD'">修改密码</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="业务类型" align="center">
        <template slot-scope="scope">
          <div>
            <el-tag v-if="scope.row.businessType === 'PAY_PASSWORD'">支付密码</el-tag>
            <el-tag v-else-if="scope.row.businessType === 'ACCOUNT_PASSWORD'">账号登录密码</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center" width="200">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>

    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <user-deatils-drawer
      v-if="userDeatilsDrawer"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawer=false"
    />

  </div>
</template>

<script>
import { userPasswordLog } from '@/api/user'
import { mapGetters } from 'vuex'
import Pagination from '@/components/Pagination'
import { pickerOptions } from '@/constant/el-const'
export default {
  name: 'UserPropsMikeType',
  components: { Pagination },
  data() {
    return {
      thatRow: {},
      userDeatilsDrawer: false,
      thatSelectedUserId: '',
      pickerOptions,
      list: [],
      checkList: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: '',
        sysOrigin: 'MARCIE',
        operateType: '',
        businessType: ''
      },
      listLoading: true
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData(true)
  },
  methods: {
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      userPasswordLog(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    queryUserDetails(userId) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = userId
    },
    handleSearch() {
      this.renderData(true)
    },
    querySearch(queryString, cb) {
      var restaurants = this.propsOrigins
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
      cb(results)
    },
    handleSelect(item) {
      this.renderData(true)
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) >= 0) || (restaurant.name.toLowerCase().indexOf(queryString.toLowerCase()) >= 0)
      }
    }
  }
}
</script>
