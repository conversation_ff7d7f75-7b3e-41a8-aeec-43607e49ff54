<template>
  <div class="app-container">
    <div class="filter-container">
      <div class="filter-item">
        <el-select
          v-model="listQuery.businessCode"
          placeholder="业务CODE"
          style="width: 100%;"
          @change="handleSearch"
        >
          <el-option
            v-for="(item, index) in apiRequestLogs"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <div class="filter-item">
        <el-input v-model="listQuery.businessContent" placeholder="业务内容" />
      </div>
      <div class="filter-item">
        <el-input v-model="listQuery.cmdRegex" placeholder="业务命令匹配" />
      </div>
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="发送日期开始"
          end-placeholder="发送日期结束"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>

    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="日志内容" align="left" show-overflow-tooltip min-width="220">
        <template slot-scope="scope">
          <el-tag size="mini">用户:</el-tag>{{ scope.row.requestUserId }},
          <el-tag size="mini">内容:</el-tag>{{ scope.row.cmd }}
        </template>
      </el-table-column>

      <el-table-column prop="createTime" label="创建时间" align="center" width="200">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right" width="100">
        <template slot-scope="scope">
          <el-button type="text" @click="clickDetails(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="listQuery.lastId" class="load-more">
      <span v-if="notData">已加载全部</span>
      <el-button v-else size="mini" :disabled="loadMoreLoading" :loading="loadMoreLoading" @click="clickLoadMore">加载更多</el-button>
    </div>

    <room-deatils-drawer
      v-if="roomDeatilsDrawerVisible"
      :room-id="thatRow.roomId"
      @close="roomDeatilsDrawerVisible=false"
    />

    <el-dialog
      title="命令内容"
      :visible.sync="detailsVisible"
      width="600px"
      :before-close="handleClose"
      top="20px"
    >
      <div>
        <json-editor v-model="jsonCmd" :read-only="true" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listApiOperationLog } from '@/api/tools'
import RoomDeatilsDrawer from '@/components/data/RoomDeatilsDrawer'
import { pickerOptions } from '@/constant/el-const'
import { sysOriginPlatforms } from '@/constant/origin'
import { mapGetters } from 'vuex'
import { apiRequestLogs } from '@/constant/type'
import JsonEditor from '@/components/JsonEditor'

export default {
  components: { RoomDeatilsDrawer, JsonEditor },
  data() {
    return {
      jsonCmd: '',
      detailsVisible: false,
      apiRequestLogs,
      thatRow: {},
      sysOriginPlatforms,
      roomDeatilsDrawerVisible: false,
      thatSelectedUserId: '',
      pickerOptions,
      list: [],
      notData: false,
      loadMoreLoading: false,
      checkList: [],
      total: 0,
      listQuery: {
        size: 20,
        businessCode: '',
        businessContent: '',
        startTime: '',
        endTime: '',
        cmdRegex: ''
      },
      listLoading: true,
      rangeDate: '',
      redPacketId: '',
      searchLoading: false,
      searchDisabled: false
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    }
  },
  created() {
    const that = this
    that.listQuery.businessCode = that.apiRequestLogs[0].value
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    clickDetails(row) {
      this.jsonCmd = row.cmd ? JSON.parse(row.cmd) : '{}'
      this.detailsVisible = true
    },
    handleClose() {
      this.detailsVisible = false
    },
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.list = []
        that.listQuery.cursor = 1
        that.listQuery.lastId = ''
      }
      that.listLoading = true
      listApiOperationLog(that.listQuery).then(res => {
        that.listLoading = false
        that.loadMoreLoading = false
        const { body } = res
        const list = body || []
        that.notData = list.length <= 0
        if (!that.notData) {
          that.list = that.list.concat(list)
          that.listQuery.lastId = that.list[that.list.length - 1].id
        }
      }).catch(er => {
        that.listLoading = false
        that.loadMoreLoading = false
      })
    },
    handleSearch() {
      this.searchLoading = true
      this.renderData(true)
    },
    loadSearchRoom() {
      this.searchDisabled = true
    },
    searchRoomSuccess(res) {
      this.searchDisabled = false
      if (!res) {
        return
      }
      this.listQuery.roomId = res.id
      if (this.listQuery.roomId) {
        this.renderData(true)
      }
    },
    searchRoomFail() {
      this.listQuery.roomId = ''
      this.searchDisabled = false
    },
    queryRoomDetails(row) {
      this.thatRow = row
      this.roomDeatilsDrawerVisible = true
    },
    getReceiveUsersLength(row) {
      return row.receiveUsers ? row.receiveUsers.length : 0
    },
    clickLoadMore() {
      const that = this
      that.loadMoreLoading = true
      that.renderData()
    }
  }
}
</script>
<style scoped lang="scss">
.room-avatar {
  position: relative;
  width: 50px;
  height: 50px;
  margin: auto;
  flex-shrink: 0;
  .flag-icon {
    position: absolute;
    bottom: 0px;
  }
}

.room-name {
  width: 100%;
  text-align: left;
  padding-left: 10px;
}

.load-more {
  padding: 20px;
  text-align: center;
}
.content {
  overflow: auto;
  padding: 10px 0px;
}
.room-profile {
  text-align: left;
  .avatar {
    width: 50px;
    height: 50px;
    border-radius: 100%;
    overflow: hidden;
    flex-shrink: 0;
  }
  .info {
    padding: 0px 5px;
    width: 100%;
    > div {
      line-height: 30px;
    }
  }
 }
</style>
