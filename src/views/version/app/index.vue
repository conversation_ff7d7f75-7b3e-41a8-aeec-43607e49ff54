<template>
  <div class="app-container">

    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatformAlls"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.platform"
        style="width: 120px"
        class="filter-item"
        placeholder="平台"
        @change="changeSelectPlatform"
      >
        <el-option
          v-for="(item, index) in platformOrigins"
          :key="index"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-if="selectedPlatform && selectedPlatform.channels && selectedPlatform.channels .length > 0"
        v-model="listQuery.channel"
        placeholder="渠道"
        style="width: 200px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in selectedPlatform.channels"
          :key="index"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-button
        :loading="searchLoading"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-edit"
        @click="handleAdd"
      >
        添加
      </el-button>

      <el-input
        v-if="buildVersionInputVsiible"
        ref="saveTagInput"
        v-model="buildVersion"
        v-number
        class="filter-item input-new-tag"
        @blur="handleInputConfirm"
      />
      <el-button
        v-else
        v-loading="buildVersionLoading"
        class="filter-item button-new-tag"
        type="info"
        plain
        @click="showBuildVersionInput"
      > BuildVersion:{{ buildVersion }}
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
      @cell-mouse-enter="handleMouseEnter"
    >
      <el-table-column prop="platform" label="平台" align="center" min-width="100px">
        <template slot-scope="scope">
          {{ scope.row.platform }}<span v-if="scope.row.channel">/{{ scope.row.channel }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="version" label="版本" align="center" min-width="100px" />
      <el-table-column prop="buildVersion" label="编译版本" align="center" min-width="80px" />
      <el-table-column label="强更" align="center">
        <template slot-scope="scope">
          {{ scope.row.forceUpdate === true ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column label="审核中" align="center">
        <template slot-scope="scope">
          {{ scope.row.review === true ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column label="app类型" align="center">
        <template slot-scope="scope">
          {{ scope.row.appType === 'CHAT' ? '语聊' : '朝拜' }}
        </template>
      </el-table-column>
      <el-table-column label="补丁" align="center">
        <template slot-scope="scope">
          {{ scope.row.patch === true ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column prop="updateDescribe" label="版本描述" align="center">
        <template slot-scope="scope">
          <div @click="copyTextContent(scope.row.updateDescribe)">
            <el-tooltip class="item" effect="dark" placement="top-start">
              <div slot="content" class="tooltip-content"><span v-html="formatUpdateDescribe(scope.row.updateDescribe)" /></div>
              <div class="el-icon-info cursor-pointer" />
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="apkSize" label="安装包大小" align="center" />
      <el-table-column label="下载链接" align="center" width="100">
        <template slot-scope="scope">
          <el-button type="text" @click="copyTextContent(scope.row.downloadUrl)">点击复制</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center" width="200">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="100">
        <template slot-scope="scope">
          <el-dropdown>
            <span class="el-dropdown-link">
              <i class="el-icon-more" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="hanldeEidt(scope.row)">编辑</el-dropdown-item>
              <el-dropdown-item @click.native="removeAppVersion(scope.row.id)">删除</el-dropdown-item>
              <el-dropdown-item @click.native="clickSetServerBuildVersion(scope.row)">服务强更</el-dropdown-item>
              <el-dropdown-item @click.native="handleUpdateDescribeList(scope.row)">更新描述</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
    <app-version-edit
      v-if="appVersionEditVisible"
      :update-data="updateData"
      :sys-origin="listQuery.sysOrigin"
      @close="appVersionEditVisible = false"
      @success="appVersionEditSuccess"
    />

    <app-update-content
      v-if="updateDescribeVisible"
      :version-id="thatSelectedVersionId"
      @close="updateDescribeVisible=false"
    />

  </div>
</template>

<script>
import { getAppVersionTable, delAppVersion, addServerBuildVersion, getServerBuildVersion } from '@/api/app-manager'
import Pagination from '@/components/Pagination'
import { copyText } from '@/utils'
import AppVersionEdit from '@/views/version/app/app-version-edit'
import { mapGetters } from 'vuex'
import AppUpdateContent from '@/views/version/app/app-update-content.vue'
import { platformOrigins } from '@/constant/type'

export default {
  name: 'AppUserTable',
  components: {
    Pagination,
    AppVersionEdit,
    AppUpdateContent
  },
  data() {
    return {
      platformOrigins,
      selectedPlatform: {},
      appVersionEditVisible: false,
      updateDescribeVisible: false,
      updateData: null,
      thisRow: {},
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        platform: 'iOS',
        sysOrigin: '',
        channel: ''
      },
      rangeDate: '',
      thatSelectedVersionId: '',
      listLoading: false,
      searchLoading: false,
      clickUserId: '',
      buildPlatform: 'iOS',
      buildVersion: 0,
      buildVersionInputVsiible: false,
      buildVersionLoading: false
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatformAlls', 'permissionsFirstSysOrigin'])
  },
  created() {
    if (!this.permissionsFirstSysOrigin) {
      return
    }
    this.listQuery.sysOrigin = this.permissionsFirstSysOrigin.value
    this.changeSelectPlatform(this.listQuery.platform)
    this.getBuildVersion()
  },
  methods: {
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.listQuery.cursor = 1
      }
      if (that.listLoading === true) {
        return
      }
      that.listLoading = true
      getAppVersionTable(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.searchLoading = that.listLoading = false
      }).catch(er => {
        that.searchLoading = that.listLoading = false
      })
    },
    changeSelectPlatform(val) {
      this.selectedPlatform = {}
      this.listQuery.channel = ''
      this.selectedPlatform = this.platformOrigins.filter(item => item.value === val)[0] || {}
      if (this.selectedPlatform.channels && this.selectedPlatform.channels.length > 0) {
        this.listQuery.channel = this.selectedPlatform.channels[0].value
      }
      this.handleSearch()
    },
    getBuildVersion() {
      const that = this
      that.buildVersion = 0
      that.buildVersionLoading = true
      getServerBuildVersion({ sysOrigin: that.listQuery.sysOrigin, platform: that.listQuery.platform, channel: that.listQuery.channel }).then(res => {
        that.buildVersionLoading = false
        that.buildVersion = res.body || 0
      }).catch(er => {
        that.buildVersionLoading = false
      })
    },
    handleSearch() {
      this.searchLoading = true
      this.renderData(true)
      this.getBuildVersion()
      this.buildPlatform = this.listQuery.platform
    },
    handleUpdateDescribeList(row) {
      this.thatSelectedVersionId = String(row.id)
      this.updateDescribeVisible = true
    },
    copyTextContent(text) {
      const that = this
      copyText(text).then(() => {
        that.$message({
          message: '复制成功',
          type: 'success'
        })
      }).catch(() => {
        that.$message({
          message: '复制失败',
          type: 'error'
        })
      })
    },
    handleAdd() {
      this.appVersionEditVisible = true
      this.updateData = null
    },
    hanldeEidt(row) {
      this.appVersionEditVisible = true
      this.updateData = row
    },
    handleMouseEnter(row) {
      this.thisRow = row
      this.thatSelectedUserId = row.id
    },
    appVersionEditSuccess(event) {
      this.updateData = null
      this.appVersionEditVisible = false
      this.renderData(event === 'create')
    },
    removeAppVersion(id) {
      const that = this
      this.$confirm('是否确认删除', '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        delAppVersion(id).then(res => {
          that.renderData()
        })
      }).catch(() => {})
    },
    handleInputConfirm() {
      this.setServerBuildVersion({ sysOrigin: this.listQuery.sysOrigin,
        platform: this.listQuery.platform,
        channel: this.listQuery.channel,
        buildVersion: this.buildVersion
      })
    },
    showBuildVersionInput() {
      this.buildVersionInputVsiible = true
    },
    clickSetServerBuildVersion(row) {
      this.setServerBuildVersion({ sysOrigin: row.sysOrigin,
        platform: row.platform,
        channel: row.channel,
        buildVersion: row.buildVersion
      })
    },
    setServerBuildVersion(params) {
      const that = this
      that.buildVersionInputVsiible = false
      this.$confirm('服务器将拦截小于当前buildVersion请求，10分钟内生效是否继续？', '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        addServerBuildVersion(params).then(res => {
          that.buildVersion = params.buildVersion
          that.$opsMessage.success()
        })
      }).catch(() => {})
    },
    formatUpdateDescribe(text) {
      text = text || ''
      return text.replace(/\\n/g, '<br/>')
    }
  }
}
</script>
<style scoped lang="scss">
.this-level {
  padding-bottom: 30px;
  display: flex;
  > div {
    width: 150px;
  }
}
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
