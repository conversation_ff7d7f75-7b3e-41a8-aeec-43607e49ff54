<template>
  <el-dialog
    title="描述列表"
    :visible="true"
    :before-close="handleClose"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    width="80%"
  >
    <div class="app-container">
      <div class="filter-container">
        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-edit"
          @click="handleCreate"
        >
          新增描述
        </el-button>
      </div>
      <el-table
        v-loading="listLoading"
        :data="list"
        :before-close="handleClose"
        element-loading-text="Loading"
        fit
        highlight-current-row
        max-height="350px"
      >
        <el-table-column label="ID" prop="id" align="center" />
        <el-table-column label="语言" prop="language" align="center">
          <template slot-scope="scope">
            <span v-for="item in language" :key="item.value">
              <span v-if="item.value === scope.row.language">{{
                item.name
              }}</span>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="描述" prop="updateDescribe" align="center" />
        <el-table-column label="朝拜描述" prop="updateWorshipDescribe" align="center" />
        <el-table-column prop="createTime" label="创建时间" align="center">
          <template slot-scope="scope">
            {{ scope.row.createTime | dateFormat }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" align="center" width="120">
          <template slot-scope="scope">
            <el-button
              type="text"
              @click.native="handleUpdate(scope.row)"
            >修改</el-button>
            <el-button
              type="text"
              @click.native="removeDescription(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="listQuery.cursor"
        :limit.sync="listQuery.limit"
        @pagination="renderData"
      />

      <el-dialog
        :title="textOptTitle"
        :visible.sync="formVisible"
        :before-close="handleDescriptionClose"
        :close-on-click-modal="false"
        :modal="false"
        append-to-bod="true"
        width="450px"
      >
        <div v-loading="submitLoading">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="100px"
            style="margin-left: 15px;"
          >
            <el-form-item label="语言" prop="language">
              <el-select
                v-model="form.language"
                placeholder="语言"
                clearable
                style="width: 100%"
                class="filter-item"
                :disabled="form.id !== ''"
              >
                <el-option
                  v-for="item in language"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="更新描述" prop="updateDescribe">
              <el-input
                v-model.trim="form.updateDescribe"
                :autosize="{ minRows: 5, maxRows: 8 }"
                type="textarea"
                placeholder="请输入更新描述"
              />
            </el-form-item>
            <el-form-item label="更新朝拜描述" prop="updateWorshipDescribe">
              <el-input
                v-model.trim="form.updateWorshipDescribe"
                :autosize="{ minRows: 5, maxRows: 8 }"
                type="textarea"
                placeholder="请输入更新朝拜描述"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="submitForm()">保存</el-button>
              <el-button @click="handleDescriptionClose()">取消</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-dialog>
    </div>
  </el-dialog>
</template>

<script>
import {
  getAppVersionDescriptionTable,
  addAppVersionDescription,
  updateAppVersionDescription,
  delAppVersionDescription
} from '@/api/app-manager'
import Pagination from '@/components/Pagination'
import { language } from '@/constant/type'
function getFormData() {
  return {
    id: '',
    versionId: '',
    updateDescribe: '',
    updateWorshipDescribe: '',
    language: ''
  }
}
export default {
  name: 'AppUpdateContent',
  components: { Pagination },
  props: {
    versionId: {
      type: String,
      required: true
    }
  },
  data() {
    const validateUpdateDescribe = (rule, value, callback) => {
      if (!value || value.trim().length === 0) {
        callback(new Error('必填项不可为空'))
      } else if (value && value.length > 500) {
        callback(new Error('最大 500 字符'))
      } else {
        callback()
      }
    }
    return {
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        versionId: ''
      },
      language,
      submitLoading: false,
      listLoading: false,
      searchDisabled: false,
      textOptTitle: '',
      formVisible: false,
      form: getFormData(),
      rules: {
        language: [
          { required: true, message: '必填项不可为空', trigger: 'blur' }
        ],
        updateDescribe: [
          { required: true, trigger: 'blur', validator: validateUpdateDescribe }
        ]
      }
    }
  },
  watch: {
    versionId: {
      handler(newVal) {
        this.listQuery.versionId = newVal
        this.renderData()
      },
      immediate: true
    }
  },
  methods: {
    renderData() {
      const that = this
      if (!that.listQuery.versionId) {
        return
      }
      that.listLoading = true
      getAppVersionDescriptionTable(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleCreate() {
      this.textOptTitle = '新增描述'
      this.formVisible = true
      this.form = getFormData()
    },
    handleUpdate(row) {
      this.textOptTitle = '修改描述'
      this.formVisible = true
      this.form.id = row.id
      this.form.versionId = row.versionId
      this.form.updateDescribe = row.updateDescribe
      this.form.updateWorshipDescribe = row.updateWorshipDescribe
      this.form.language = row.language
    },
    handleDescriptionClose() {
      this.formVisible = false
      this.resetForm()
    },
    resetForm() {
      this.form = getFormData()
    },
    handleClose() {
      this.$emit('close')
    },
    handleSearch() {
      this.renderData(true)
    },
    loadSearchUser() {
      this.searchDisabled = true
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (valid) {
          that.submitLoading = true
          that.form.versionId = that.listQuery.versionId
          if (this.form.id === '') {
            addAppVersionDescription(this.form)
              .then(res => {
                if (res.status) {
                  that.$opsMessage.success()
                  that.submitLoading = false
                  that.formVisible = false
                  that.resetForm()
                  that.renderData(true)
                } else {
                  that.$opsMessage.fail(res.errorMsg)
                  that.submitLoading = false
                }
              })
              .catch(er => {
                that.submitLoading = false
                console.error(er)
                this.$emit('fail')
              })
          } else {
            updateAppVersionDescription(this.form)
              .then(res => {
                if (res.status) {
                  that.$opsMessage.success()
                  that.submitLoading = false
                  that.formVisible = false
                  that.resetForm()
                  that.renderData(true)
                } else {
                  that.$opsMessage.fail(res.errorMsg)
                  that.submitLoading = false
                }
              })
              .catch(er => {
                that.submitLoading = false
                console.error(er)
                this.$emit('fail')
              })
          }
        } else {
          console.error('error submit!!')
          return false
        }
      })
    },
    removeDescription(item) {
      const that = this
      that
        .$confirm('确定删除描述吗？', '提示', {
          type: 'warning'
        })
        .then(() => {
          // that.$message({
          //   message: '处理中，请等待结果',
          //   type: 'warning'
          // })
          that.listLoading = true
          delAppVersionDescription(item.id)
            .then(res => {
              that.listLoading = false
              that.$message({
                message: '删除成功',
                type: 'success'
              })
              that.renderData()
            })
            .catch(er => {
              console.error(er)
              that.listLoading = false
            })
        })
        .catch(er => {
          console.error(er)
          that.listLoading = false
        })
    }
  }
}
</script>
<style scoped>
.v-modal {
  z-index: 0;
}
</style>
