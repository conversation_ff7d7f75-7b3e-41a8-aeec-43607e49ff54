<template>
  <div class="edit-role">
    <el-dialog
      :title="title"
      :visible="true"
      width="550px"
      top="50px"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <div style="height: 500px;overflow: auto;">
        <el-form
          ref="dataForm"
          :rules="rules"
          :model="formData"
          style="width: 400px; margin-left:50px;"
        >
          <el-form-item label="平台" prop="platform">
            <el-select
              v-model="formData.platform"
              :disabled="!isAdd"
              clearable
              style="width: 100%"
              class="filter-item"
              @change="changeSelectPlatform"
            >
              <el-option
                v-for="(item, index) in platformOrigins"
                :key="index"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="渠道" prop="channel">
            <el-select
              v-model="formData.channel"
              :disabled="!isAdd"
              clearable
              style="width: 100%"
              class="filter-item"
            >
              <el-option
                v-for="(item, index) in selectedPlatform.channels"
                :key="index"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="版本号" prop="version">
            <el-input v-model.trim="formData.version" placeholder="请输入版本号" />
          </el-form-item>
          <el-form-item label="编译版本号" prop="buildVersion">
            <el-input v-model="formData.buildVersion" v-number placeholder="请输入编译版本号" />
          </el-form-item>
          <el-form-item label="强更" prop="forceUpdate">
            <el-select
              v-model="formData.forceUpdate"
              clearable
              style="width: 100%"
              class="filter-item"
            >
              <el-option label="否" :value="false" />
              <el-option label="是" :value="true" />
            </el-select>
          </el-form-item>
          <el-form-item label="app类型" prop="appType">
            <el-select
              v-model="formData.appType"
              clearable
              style="width: 100%"
              class="filter-item"
            >
              <el-option label="语聊" :value="'CHAT'" />
              <el-option label="朝拜" :value="'WORSHIP'" />
            </el-select>
          </el-form-item>
          <el-form-item label="审核中" prop="review">
            <el-select
              v-model="formData.review"
              clearable
              style="width: 100%"
              class="filter-item"
            >
              <el-option label="否" :value="false" />
              <el-option label="是" :value="true" />
            </el-select>
          </el-form-item>
          <el-form-item label="补丁" prop="patch">
            <el-select
              v-model="formData.patch"
              clearable
              style="width: 100%"
              class="filter-item"
            >
              <el-option label="否" :value="false" />
              <el-option label="是" :value="true" />
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="更新描述" prop="updateDescribe">
            <el-input
              v-model.trim="formData.updateDescribe"
              :autosize="{ minRows: 2, maxRows: 4 }"
              type="textarea"
              placeholder="请输入更新描述"
            />
          </el-form-item> -->
          <el-form-item label="安装包大小" prop="apkSize">
            <el-input v-model.trim="formData.apkSize" placeholder="请输入安装包大小" />
          </el-form-item>
          <el-form-item label="下载链接" prop="downloadUrl">
            <el-input v-model.trim="formData.downloadUrl" placeholder="请输入下载链接" />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">
          取消
        </el-button>
        <el-button
          :loading="submitLoading"
          type="primary"
          @click="handleSubmit"
        >
          提交
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import { platformOrigins } from '@/constant/type'
import { addAppVersion, updateAppVersion } from '@/api/app-manager'
export default {
  name: 'AppVersionEdit',
  props: {
    updateData: {
      type: Object,
      default: null
    },
    sysOrigin: {
      type: String,
      default: null
    }
  },
  data() {
    // const validateUpdateDescribe = (rule, value, callback) => {
    //   if (!value || value.trim().length === 0) {
    //     callback(new Error('必填项不可为空'))
    //   } else if (value && value.length > 500) {
    //     callback(new Error('最大 500 字符'))
    //   } else {
    //     callback()
    //   }
    // }
    return {
      platformOrigins,
      selectedPlatform: {},
      formData: {
        id: '',
        platform: '',
        channel: '',
        version: '',
        appType: 'CHAT',
        buildVersion: '',
        forceUpdate: true,
        review: false,
        patch: false,
        updateDescribe: '',
        apkSize: '',
        downloadUrl: '',
        sysOrigin: ''
      },
      rules: {
        platform: [
          { required: true, message: '必填项不可为空', trigger: 'blur' }
        ],
        channel: [
          { required: true, message: '必填项不可为空', trigger: 'blur' }
        ],
        version: [{ required: true, message: '必填项不可为空', trigger: 'blur' }],
        buildVersion: [{ required: true, message: '必填项不可为空', trigger: 'blur' }],
        forceUpdate: [{ required: true, message: '必填项不可为空', trigger: 'blur' }],
        review: [{ required: true, message: '必填项不可为空', trigger: 'blur' }],
        // updateDescribe: [{ required: true, trigger: 'blur', validator: validateUpdateDescribe }],
        apkSize: [{ required: true, message: '必填项不可为空', trigger: 'blur' }],
        downloadUrl: [{ required: true, message: '必填项不可为空', trigger: 'blur' }],
        patch: [{ required: true, message: '必填项不可为空', trigger: 'blur' }]
      },
      submitLoading: false
    }
  },
  computed: {
    isAdd() {
      return this.updateData === null
    },
    title() {
      return this.isAdd ? `添加(${this.sysOrigin})` : `修改(${this.updateData.sysOrigin})`
    }
  },
  watch: {
    updateData: {
      immediate: true,
      deep: true,
      handler(newVal) {
        Object.assign(this.formData, newVal)
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    changeSelectPlatform(val) {
      this.selectedPlatform = {}
      this.formData.channel = ''
      this.selectedPlatform = this.platformOrigins.filter(item => item.value === val)[0] || {}
    },
    handleSubmit() {
      const that = this
      that.$refs.dataForm.validate(valid => {
        if (valid) {
          that.formData.updateDescribe = 'App version update'
          that.submitLoading = true
          if (that.isAdd) {
            that.formData.sysOrigin = that.sysOrigin
            addAppVersion(that.formData).then(res => {
              that.submitLoading = false
              this.$emit('success', 'create')
            }).catch(er => {
              that.submitLoading = false
              console.error(er)
              this.$emit('fail')
            })
            return
          }
          updateAppVersion(that.formData).then(res => {
            that.submitLoading = false
            this.$emit('success', 'update')
          }).catch(er => {
            that.submitLoading = false
            console.error(er)
            this.$emit('fail')
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    }
  }
}
</script>
