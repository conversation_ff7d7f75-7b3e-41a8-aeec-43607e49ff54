<template>
  <div class="app-lottery">
    <el-row v-loading="formLoading" style="height: 100%;">
      <el-col class="lottery-body flex-c" :md="14" :xs="24">
        <LuckyWheel
          :key="luckyWheelIndex"
          ref="luckyWheel"
          class="luck-conf"
          :width="form.diameter"
          :height="form.diameter"
          :blocks="form.blocks"
          :prizes="form.prizes"
          :buttons="form.buttons"
          :default-config="form.defaultConfig"
          :default-style="form.defaultStyle"
          @start="startCallBack"
          @end="endCallBack"
        />
      </el-col>
      <el-col class="lottery-body flex-c" :md="4" :xs="24">
        <div class="in-stock">
          <div v-for="(item,index) in form.prizes" :key="index" class="flex-l">
            <img :src="item.imgs[0].src" width="40" height="40">
            <div style="width: 40%;padding: 0px 5px;" class="font-info">
              库存: {{ item.unlimited ? '不限' : item.inStock }}
            </div>
            <div style="width: 40%;padding: 0px 5px;" class="font-danger">
              消耗:  {{ item.consumeInStock || '0' }}
            </div>
          </div>
        </div>
      </el-col>
      <el-col class="lottery-conf" :md="6" :xs="24">
        <el-form ref="form" class="form" :model="form" label-width="100px">
          <div class="card bottom-line">
            <div class="title">属性</div>
            <div class="content">
              <el-form-item label="归属系统">
                <el-select
                  v-model="listQuery.sysOrigin"
                  placeholder="系统"
                  style="width: 100%"
                  class="filter-item"
                  @change="loadLotteryConf"
                >
                  <el-option
                    v-for="item in permissionsSysOriginPlatforms"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
                    <span style="float: left;margin-left:10px">{{ item.label }}</span>
                  </el-option>
                </el-select>

              </el-form-item>
              <el-form-item label="启动消费">
                <el-input v-model="form.startConsume" v-number />
              </el-form-item>
              <el-form-item label="抽奖类型">
                <el-radio-group v-model="form.type">
                  <el-radio :label="0">转盘</el-radio>
                  <el-radio :label="1" :disabled="true">九宫格</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="转盘直径">
                <el-input v-model="form.diameter" @change="renderLuckyWheel" />
              </el-form-item>
            </div>
          </div>
          <div class="card bottom-line">
            <div class="title">
              <el-row>
                <el-col :span="21">背景</el-col>
                <el-col :span="3"><el-button type="text" @click="addBlocks"><i class="el-icon-circle-plus" />添加</el-button></el-col>
              </el-row>
            </div>
            <div class="content">
              <el-tabs v-model="blocksIndex" type="border-card" closable @tab-remove="removeBlocks">
                <el-tab-pane v-for="(item, index) in form.blocks" :key="index" :name="String(index)">
                  <span slot="label">{{ index }}</span>
                  <el-form-item label="内间距">
                    <el-input v-model="item.padding" />
                  </el-form-item>
                  <el-form-item label="背景颜色">
                    <el-color-picker v-model="item.background" />
                  </el-form-item>

                  <div class="card">
                    <div class="title">
                      <el-row>
                        <el-col :span="21">图片</el-col>
                        <el-col :span="3"><el-button type="text" @click="addBlocksImg(item)"><i class="el-icon-circle-plus" />添加</el-button></el-col>
                      </el-row>
                    </div>
                    <div :key="attributeIndex" class="content">
                      <el-tabs v-if="item.imgs.length > 0" v-model="item.imageIndex" type="border-card" closable @tab-remove="index => removeImg(item, index)">
                        <el-tab-pane v-for="(imgItem, imgIndex) in item.imgs" :key="imgIndex" :name="String(imgIndex)">
                          <span slot="label">{{ imgIndex }}</span>
                          <el-form-item label="图片链接">
                            <el-input v-model="imgItem.src" />
                          </el-form-item>
                          <el-form-item label="顶部间距">
                            <el-input v-model="imgItem.top" />
                          </el-form-item>
                          <el-form-item label="图片宽">
                            <el-input v-model="imgItem.width" />
                          </el-form-item>
                          <el-form-item label="图片高">
                            <el-input v-model="imgItem.height" />
                          </el-form-item>
                          <el-form-item label="背景转动">
                            <el-radio-group v-model="imgItem.rotate">
                              <el-radio :label="true">是</el-radio>
                              <el-radio :label="false">否</el-radio>
                            </el-radio-group>
                          </el-form-item>
                        </el-tab-pane>
                      </el-tabs>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </div>
          <div class="card bottom-line">
            <div class="title">
              <el-row>
                <el-col :span="21">奖品</el-col>
                <el-col :span="3"><el-button type="text" @click="addPrize"><i class="el-icon-circle-plus" />添加</el-button></el-col>
              </el-row>
            </div>
            <div :key="attributeIndex" class="content">
              <el-tabs v-model="prizesIndex" type="border-card" closable @tab-remove="removePrize">
                <el-tab-pane v-for="(item, index) in form.prizes" :key="index" :name="String(index)">
                  <span slot="label">{{ index }}</span>
                  <el-form-item label="奖品编号">
                    <el-input v-model="item.code" />
                  </el-form-item>
                  <el-form-item label="中奖概率">
                    <el-input v-model="item.probability" />
                  </el-form-item>
                  <el-form-item label="库存数量">
                    <el-col :span="4" style="text-align: center;">
                      <el-checkbox v-model="item.unlimited">不限</el-checkbox>
                    </el-col>
                    <el-col v-if="!item.unlimited" :span="10" style="text-align: center;">
                      库存: {{ item.inStock }}
                      <i class="el-icon-edit cursor-pointer" style="color:#1890ff;" @click="clickInStockEdit(item)" />
                    </el-col>
                    <el-col v-if="!item.unlimited" :span="10" style="text-align: center;">
                      消费: {{ item.consumeInStock }}
                      <i class="el-icon-refresh-left cursor-pointer" style="color:#1890ff;" @click="clickRestConsumeInStock(item)" />
                    </el-col>
                    <div v-if="item.inStock < item.consumeInStock" class="font-danger">
                      ”库存 &lt; 消费“ 点击保存系统会清理消费数重新计算
                    </div>
                  </el-form-item>
                  <el-form-item label="背景颜色">
                    <el-color-picker v-model="item.background" @change="renderLuckyWheel()" />
                  </el-form-item>
                  <el-form-item label="奖品关联">
                    <el-button type="text" @click="clickSelectPrize(item)"><i class="el-icon-circle-plus" />选择奖品</el-button>
                    <div v-if="item.prize.content" class="select-prize flex-l">
                      <img :src="item.prize.cover" width="100px" height="100px">
                      <div class="content" style="padding:10px;color:#999999;">
                        <div>内容: {{ item.prize.content }}</div>
                        <div>数量: {{ item.prize.quantity }} {{ item.prize.unit }}</div>
                        <div>
                          <el-popover
                            placement="bottom"
                            title="选择设置图片位置"
                            trigger="click"
                          >
                            <div class="content">
                              <el-button v-for="(itemImg, itemImgIndex) in item.imgs" :key="itemImgIndex" @click="clickSetPrizeCover(item, itemImg)">
                                {{ itemImgIndex }}
                              </el-button>
                            </div>
                            <el-button slot="reference" type="text">设置图片</el-button>
                          </el-popover>

                          <el-popover
                            placement="bottom"
                            title="选择设置文字"
                            trigger="click"
                          >
                            <div class="content">
                              <el-button v-for="(itemFont, itemFontIndex) in item.fonts" :key="itemFontIndex" @click="clickSetPrizeFont(item, itemFont)">
                                {{ itemFontIndex }}
                              </el-button>
                            </div>
                            <el-button slot="reference" type="text">设置文字</el-button>
                          </el-popover>
                        </div>
                      </div>
                    </div>
                  </el-form-item>
                  <div class="card">
                    <div class="title">
                      <el-row>
                        <el-col :span="21">文字</el-col>
                        <el-col :span="3"><el-button type="text" @click="addFont(item)"><i class="el-icon-circle-plus" />添加</el-button></el-col>
                      </el-row>
                    </div>
                    <div class="content">
                      <el-tabs v-if="item.fonts.length > 0" v-model="item.fontIndex" type="border-card" closable @tab-remove="index => removeFont(item, index)">
                        <el-tab-pane v-for="(fontItem, fontIndex) in item.fonts" :key="fontIndex" :name="String(fontIndex)">
                          <span slot="label">{{ fontIndex }}</span>
                          <el-form-item label="字体内容">
                            <el-input v-model="fontItem.text" />
                          </el-form-item>
                          <el-form-item label="顶部间距">
                            <el-input v-model="fontItem.top" />
                          </el-form-item>
                          <el-form-item label="字体颜色">
                            <el-color-picker v-model="fontItem.fontColor" />
                          </el-form-item>
                          <el-form-item label="字体大小">
                            <el-input v-model="fontItem.fontSize" />
                          </el-form-item>
                          <el-form-item label="字体样式">
                            <el-input v-model="fontItem.fontStyle" />
                          </el-form-item>
                          <el-form-item label="字体粗细">
                            <el-select v-model="fontItem.fontWeight" style="width:100%;">
                              <el-option
                                v-for="val in fontWeights"
                                :key="val"
                                :label="val"
                                :value="val"
                              />
                            </el-select>
                          </el-form-item>
                          <el-form-item label="字体行高">
                            <el-input v-model="fontItem.lineHeight" />
                          </el-form-item>
                          <el-form-item label="自动换行">
                            <el-radio-group v-model="fontItem.wordWrap">
                              <el-radio :label="true">是</el-radio>
                              <el-radio :label="false">否</el-radio>
                            </el-radio-group>
                          </el-form-item>
                          <el-form-item label="换行宽度">
                            <el-input v-model="fontItem.lengthLimit" />
                          </el-form-item>
                        </el-tab-pane>
                      </el-tabs>
                    </div>
                  </div>
                  <div class="card">
                    <div class="title">
                      <el-row>
                        <el-col :span="21">图片</el-col>
                        <el-col :span="3"><el-button type="text" @click="addImg(item)"><i class="el-icon-circle-plus" />添加</el-button></el-col>
                      </el-row>
                    </div>
                    <div class="content">
                      <el-tabs v-if="item.imgs.length > 0" v-model="item.imageIndex" type="border-card" closable @tab-remove="index => removeImg(item, index)">
                        <el-tab-pane v-for="(imgItem, imgIndex) in item.imgs" :key="imgIndex" :name="String(imgIndex)">
                          <span slot="label">{{ imgIndex }}</span>
                          <el-form-item label="图片链接">
                            <el-input v-model="imgItem.src" />
                          </el-form-item>
                          <el-form-item label="顶部间距">
                            <el-input v-model="imgItem.top" />
                          </el-form-item>
                          <el-form-item label="图片宽">
                            <el-input v-model="imgItem.width" />
                          </el-form-item>
                          <el-form-item label="图片高">
                            <el-input v-model="imgItem.height" />
                          </el-form-item>
                        </el-tab-pane>
                      </el-tabs>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </div>

          <div class="card bottom-line">
            <div class="title">
              <el-row>
                <el-col :span="21">按钮</el-col>
                <el-col :span="3"><el-button type="text" @click="addButton"><i class="el-icon-circle-plus" />添加</el-button></el-col>
              </el-row>
            </div>
            <div :key="attributeIndex" class="content">
              <el-tabs v-model="buttonIndex" type="border-card" closable @tab-remove="removeButton">
                <el-tab-pane v-for="(item, index) in form.buttons" :key="index" :name="String(index)">
                  <span slot="label">{{ index }}</span>
                  <el-form-item label="按钮半径">
                    <el-input v-model="item.radius" />
                  </el-form-item>
                  <el-form-item label="是否显示指针">
                    <el-radio-group v-model="item.pointer">
                      <el-radio :label="true">是</el-radio>
                      <el-radio :label="false">否</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="背景颜色">
                    <el-color-picker v-model="item.background" />
                  </el-form-item>
                  <div class="card">
                    <div class="title">
                      <el-row>
                        <el-col :span="21">文字</el-col>
                        <el-col :span="3"><el-button type="text" @click="addFont(item)"><i class="el-icon-circle-plus" />添加</el-button></el-col>
                      </el-row>
                    </div>
                    <div class="content">
                      <el-tabs v-if="item.fonts.length > 0" v-model="item.fontIndex" type="border-card" closable @tab-remove="index => removeFont(item, index)">
                        <el-tab-pane v-for="(fontItem, fontIndex) in item.fonts" :key="fontIndex" :name="String(fontIndex)">
                          <span slot="label">{{ fontIndex }}</span>
                          <el-form-item label="字体内容">
                            <el-input v-model="fontItem.text" />
                          </el-form-item>
                          <el-form-item label="顶部间距">
                            <el-input v-model="fontItem.top" />
                          </el-form-item>
                          <el-form-item label="字体颜色">
                            <el-color-picker v-model="fontItem.fontColor" />
                          </el-form-item>
                          <el-form-item label="字体大小">
                            <el-input v-model="fontItem.fontSize" />
                          </el-form-item>
                          <el-form-item label="字体样式">
                            <el-input v-model="fontItem.fontStyle" />
                          </el-form-item>
                          <el-form-item label="字体粗细">
                            <el-select v-model="fontItem.fontWeight" style="width:100%;">
                              <el-option
                                v-for="val in fontWeights"
                                :key="val"
                                :label="val"
                                :value="val"
                              />
                            </el-select>
                          </el-form-item>
                          <el-form-item label="字体行高">
                            <el-input v-model="fontItem.lineHeight" />
                          </el-form-item>
                          <el-form-item label="自动换行">
                            <el-radio-group v-model="fontItem.wordWrap">
                              <el-radio :label="true">是</el-radio>
                              <el-radio :label="false">否</el-radio>
                            </el-radio-group>
                          </el-form-item>
                          <el-form-item label="换行宽度">
                            <el-input v-model="fontItem.lengthLimit" />
                          </el-form-item>
                        </el-tab-pane>
                      </el-tabs>
                    </div>
                  </div>
                  <div class="card">
                    <div class="title">
                      <el-row>
                        <el-col :span="21">图片</el-col>
                        <el-col :span="3"><el-button type="text" @click="addImg(item)"><i class="el-icon-circle-plus" />添加</el-button></el-col>
                      </el-row>
                    </div>
                    <div class="content">
                      <el-tabs v-if="item.imgs.length > 0" v-model="item.imageIndex" type="border-card" closable @tab-remove="index => removeImg(item, index)">
                        <el-tab-pane v-for="(imgItem, imgIndex) in item.imgs" :key="imgIndex" :name="String(imgIndex)">
                          <span slot="label">{{ imgIndex }}</span>
                          <el-form-item label="图片链接">
                            <el-input v-model="imgItem.src" />
                          </el-form-item>
                          <el-form-item label="顶部间距">
                            <el-input v-model="imgItem.top" />
                          </el-form-item>
                          <el-form-item label="图片宽">
                            <el-input v-model="imgItem.width" />
                          </el-form-item>
                          <el-form-item label="图片高">
                            <el-input v-model="imgItem.height" />
                          </el-form-item>
                        </el-tab-pane>
                      </el-tabs>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </div>
          <div class="card">
            <div class="title">默认配置</div>
            <div class="content">
              <el-form-item label="扇形间隙">
                <el-input-number v-model="form.defaultConfig.gutter" :min="0" style="width:100%;" />
              </el-form-item>
              <el-form-item label="停止范围">
                <el-slider v-model="form.defaultConfig.stopRange" />
              </el-form-item>
              <el-form-item label="偏移角度">
                <el-input-number v-model="form.defaultConfig.offsetDegree" :min="0" style="width:100%;" />
              </el-form-item>
              <el-form-item label="速度">
                <el-input-number v-model="form.defaultConfig.speed" :min="0" style="width:100%;" />
              </el-form-item>
              <el-form-item label="开始旋转时间">
                <el-input-number v-model="form.defaultConfig.accelerationTime" :min="0" style="width:100%;" />
              </el-form-item>
              <el-form-item label="缓慢停止时间">
                <el-input-number v-model="form.defaultConfig.decelerationTime" :min="0" style="width:100%;" />
              </el-form-item>
            </div>
          </div>
          <div class="card">
            <div class="title">默认样式</div>
            <div class="content">
              <el-form-item label="背景颜色">
                <el-color-picker v-model="form.defaultStyle.background" />
              </el-form-item>
              <el-form-item label="字体颜色">
                <el-input v-model="form.defaultStyle.fontColor" />
              </el-form-item>
              <el-form-item label="字体大小">
                <el-input v-model="form.defaultStyle.fontSize" />
              </el-form-item>
              <el-form-item label="字体样式">
                <el-input v-model="form.defaultStyle.fontStyle" />
              </el-form-item>
              <el-form-item label="字体粗细">
                <el-select v-model="form.defaultStyle.fontWeight" style="width:100%;">
                  <el-option
                    v-for="item in fontWeights"
                    :key="item"
                    :label="item"
                    :value="item"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="字体行高">
                <el-input v-model="form.defaultStyle.lineHeight" />
              </el-form-item>
              <el-form-item label="自动换行">
                <el-radio-group v-model="form.defaultStyle.wordWrap">
                  <el-radio :label="true">是</el-radio>
                  <el-radio :label="false">否</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="换行宽度">
                <el-input v-model="form.defaultStyle.lengthLimit" />
              </el-form-item>
            </div>
          </div>
        </el-form>
        <div class="operation-but">
          <el-button :loading="formLoading" @click="loadLotteryConf">重置</el-button>
          <el-button @click="copyForm">复制</el-button>
          <el-button @click="importForm">导入</el-button>
          <el-button type="primary" :loading="formSubmitLoading" @click="clickSubmit">保存</el-button>
        </div>
      </el-col>
    </el-row>

    <select-prize v-if="visibleSelectPrize" :sys-origin="listQuery.sysOrigin" @select="selectPrize" @close="visibleSelectPrize=false" />
  </div>
</template>

<script>
import { copyText } from '@/utils'
import { mapGetters } from 'vuex'
import SelectPrize from './select-prize.vue'
import { getLotteryConf, addLotteryConf } from '@/api/sys'
export default {
  name: 'LotteryConf',
  components: { SelectPrize },
  data() {
    return {
      visibleSelectPrize: false,
      thisClickPrizeItem: {},
      listQuery: {
        sysOrigin: ''
      },
      luckyWheelIndex: 0,
      attributeIndex: 0,
      blocksIndex: '0',
      prizesIndex: '0',
      buttonIndex: '0',
      fontWeights: [300, 400, 500, 600, 700],
      formLoading: false,
      formSubmitLoading: false,
      form: {}
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    that.form = that.getDefaultConf()
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.loadLotteryConf()
  },
  methods: {
    getDefaultConf() {
      const form = {
        type: 0,
        startConsume: 3000,
        diameter: '300px',
        blocks: [
          {
            padding: '13px',
            background: '#d64737',
            imgs: [],
            // 扩展属性
            imageIndex: '0'
          }
        ],
        prizes: [],
        buttons: [
          {
            radius: '50%',
            pointer: false,
            background: '#d64737',
            fonts: [],
            imgs: []
          },
          {
            radius: '45%',
            pointer: false,
            background: '#ffffff',
            fonts: [],
            imgs: []
          },
          {
            radius: '41%',
            pointer: true,
            background: '#f6c66f',
            fonts: [],
            imgs: []
          },
          {
            radius: '35%',
            pointer: false,
            background: '#ffdea0',
            fonts: [],
            imgs: []
          }
        ],
        defaultConfig: {
          gutter: '',
          stopRange: 0,
          offsetDegree: 0,
          speed: 20,
          accelerationTime: 2500,
          decelerationTime: 2500
        },
        defaultStyle: {
          background: '',
          fontColor: '#000',
          fontSize: '14px',
          fontStyle: 'sans-serif',
          fontWeight: '400',
          lineHeight: '14px',
          wordWrap: true,
          lengthLimit: '90%'
        }
      }

      for (let index = 0; index < 6; index++) {
        const prize = this.getEmptyPrizeObj()
        prize.background = index % 2 === 0 ? '#f8d384' : '#f9e3bb'
        prize.fonts[0].text = index + 1 + ' 元红包'
        form.prizes.push(prize)
      }
      return form
    },
    loadLotteryConf() {
      const that = this
      that.formLoading = true
      getLotteryConf(that.listQuery).then(res => {
        that.formLoading = false
        that.form = res.body || that.getDefaultConf()
        that.renderLuckyWheel()
      }).catch(er => {
        that.formLoading = false
        console.error(er)
      })
    },
    importForm() {
      const that = this
      that.$prompt('JSON配置信息', '导入配置', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false
      }).then(({ value }) => {
        that.form = JSON.parse(value)
        that.renderLuckyWheel()
      }).catch(() => {
      })
    },
    copyForm() {
      const that = this
      copyText(JSON.stringify(that.form)).then(() => {
        that.$opsMessage.success()
      }).catch(() => {
        that.$opsMessage.fail()
      })
    },
    clickSubmit() {
      const that = this
      that.formSubmitLoading = true
      addLotteryConf({
        sysOrigin: that.listQuery.sysOrigin,
        body: that.form
      }).then(res => {
        that.formSubmitLoading = false
        that.$opsMessage.success()
      }).catch(er => {
        that.formSubmitLoading = false
        that.$opsMessage.fail()
        console.error(er)
      })
    },
    startCallBack() {
      this.$refs.luckyWheel.play()
      setTimeout(() => {
        this.$refs.luckyWheel.stop(Math.random() * 8 >> 0)
      }, 3000)
    },
    endCallBack(prize) {
    },
    getEmptyBaseImageObj() {
      return {
        src: 'http://img.sugartimeapp.com/avatar/16D2030D-E863-4406-9DB3-68658F8F7175.png',
        top: '0',
        width: '50%',
        height: '50%'
      }
    },
    getEmptyImageObj() {
      return {
        src: 'http://img.sugartimeapp.com/avatar/16D2030D-E863-4406-9DB3-68658F8F7175.png',
        top: '0',
        width: '100%',
        height: '100%',
        rotate: false
      }
    },
    getEmptyPrizeObj() {
      return {
        background: '',
        fonts: [this.getEmptyFontObj()],
        imgs: [{ src: 'http://img.sugartimeapp.com/svga_cover/manager-3c9372f3-1d15-482d-9cb7-e58e6852dbcb.png', top: '35%', width: '30%', height: '' }],
        // 扩展属性
        fontIndex: '0',
        imageIndex: '0',
        probability: 0,
        inStock: 0,
        consumeInStock: 0,
        unlimited: false,
        prize: {
          content: '',
          cover: '',
          propsType: '',
          propsDetailType: '',
          quantity: 0
        }
      }
    },
    getEmptyFontObj() {
      return {
        text: 'New text',
        top: '18%',
        fontColor: '',
        fontSize: '14px',
        fontStyle: 'sans-serif',
        fontWeight: '',
        lineHeight: '14px',
        wordWrap: true,
        lengthLimit: ''
      }
    },
    renderLuckyWheel() {
      this.luckyWheelIndex++
    },
    renderAttributeIndex() {
      this.attributeIndex++
    },
    addBlocks() {
      if (this.form.blocks.length > 10) {
        this.$opsMessage.warn('有点多了,哥哥!')
        return
      }
      this.form.blocks.push({
        padding: '13px',
        background: '#d64737',
        imgs: []
      })
      this.form.blocks = Object.assign([], this.form.blocks)
      this.blocksIndex = String(this.form.blocks.length - 1)
    },
    removeBlocks(index) {
      if (this.form.blocks.length === 1) {
        this.$opsMessage.warn('求求你做个人,留下一个吧!')
        return
      }
      this.form.blocks.splice(index, 1)
      this.blocksIndex = String(this.form.blocks.length - 1)
    },
    addPrize() {
      if (this.form.prizes.length > 10) {
        this.$opsMessage.warn('有点多了,哥哥!')
        return
      }
      const prize = this.getEmptyPrizeObj()
      prize.background = `#${parseInt(Math.random() * 1000000)}`
      this.form.prizes.push(prize)
      this.form.prizes = Object.assign([], this.form.prizes)
      this.prizesIndex = String(this.form.prizes.length - 1)
    },
    removePrize(index) {
      if (this.form.prizes.length === 1) {
        this.$opsMessage.warn('求求你做个人,留下一个吧!')
        return
      }
      this.form.prizes.splice(index, 1)
      this.prizesIndex = String(this.form.prizes.length - 1)
    },
    addButton() {
      if (this.form.buttons.length > 10) {
        this.$opsMessage.warn('有点多了,哥哥!')
        return
      }
      this.form.buttons.push(this.getEmptyPrizeObj())
      this.form.buttons = Object.assign([], this.form.buttons)
      this.buttonIndex = String(this.form.buttons.length - 1)
    },
    removeButton(index) {
      if (this.form.buttons.length === 1) {
        this.$opsMessage.warn('求求你做个人,留下一个吧!')
        return
      }
      this.form.buttons.splice(index, 1)
      this.buttonIndex = String(this.form.buttons.length - 1)
    },
    addBlocksImg(item) {
      if (item.imgs.length > 10) {
        this.$opsMessage.warn('有点多了,哥哥!')
        return
      }
      item.imgs.push(this.getEmptyImageObj())
      item.imgs = Object.assign([], item.imgs)
      item.imageIndex = String(item.imgs.length > 0 ? item.imgs.length - 1 : 0)
      if (item.imgs.length === 1) {
        this.renderAttributeIndex()
      }
    },
    addImg(item) {
      if (item.imgs.length > 10) {
        this.$opsMessage.warn('有点多了,哥哥!')
        return
      }
      item.imgs.push(this.getEmptyBaseImageObj())
      item.imgs = Object.assign([], item.imgs)
      item.imageIndex = String(item.imgs.length > 0 ? item.imgs.length - 1 : 0)
      if (item.imgs.length === 1) {
        this.renderAttributeIndex()
      }
    },
    removeImg(item, index) {
      item.imgs.splice(index, 1)
      item.imageIndex = String(item.imgs.length > 0 ? item.imgs.length - 1 : 0)
      if (item.imgs.length === 0) {
        this.renderAttributeIndex()
      }
    },
    addFont(item, index) {
      if (item.fonts.length > 10) {
        this.$opsMessage.warn('有点多了,哥哥!')
        return
      }
      item.fonts.push(this.getEmptyFontObj())
      item.fonts = Object.assign([], item.fonts)
      item.fontIndex = String(item.fonts.length > 0 ? item.fonts.length - 1 : 0)
      if (item.fonts.length === 1) {
        this.renderAttributeIndex()
      }
    },
    removeFont(item, index) {
      item.fonts.splice(index, 1)
      item.fontIndex = String(item.fonts.length > 0 ? item.fonts.length - 1 : 0)
      if (item.fonts.length === 0) {
        this.renderAttributeIndex()
      }
    },
    selectPrize(val) {
      this.thisClickPrizeItem.prize = val
      this.visibleSelectPrize = false
    },
    clickSelectPrize(item) {
      this.thisClickPrizeItem = item
      this.visibleSelectPrize = true
    },
    clickSetPrizeCover(item, itemImg) {
      itemImg.src = item.prize.cover
    },
    clickSetPrizeFont(item, itemFont) {
      itemFont.text = `${item.prize.quantity || ''} ${item.prize.unit || ''}`
    },
    clickInStockEdit(item) {
      const that = this
      that.$prompt('请输入库存数量', '库存编辑', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^\d+$/,
        inputErrorMessage: '请输入正整数',
        closeOnClickModal: false
      }).then(({ value }) => {
        item.inStock = value
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消输入'
        })
      })
    },
    clickRestConsumeInStock(item) {
      const that = this
      that.$confirm('是否重置消费库存', '重置', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message({
          type: 'success',
          message: '重置成功!'
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消重置'
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.app-lottery {
  position: absolute;
  overflow: auto;
  left: 0;
  top: 50px;
  right: 0px;
  bottom: 0px;
  .form {
    height: 100%;
    overflow: auto;
  }
  .in-stock {
    width: 100%;
    position:absolute;
    top: 0px;
    padding: 15px;
    line-height: 30px;
  }
  .lottery-body{
    height: 100%;
    position: relative;
    background-color: rgba(231, 236, 236, 0.418);
    .luck-conf {
      margin: auto;
    }
  }
  .lottery-conf {
    position: relative;
    height: 100%;
    .bottom-line {
      border-bottom: 1px solid rgb(240, 235, 235);
    }
    .card {
      padding: 20px;
      .title {
        font-size: 22px;
        font-weight: bold;
      }
      .content {
        padding: 20px 0px;
      }
    }
  }
  .operation-but{
    background-color: rgb(245, 248, 248);
    padding: 10px;
    position: absolute;
    bottom: 0px;
    right: 0px;
    left: 0px;
    z-index: 100;
    text-align: center;
  }
}

</style>
