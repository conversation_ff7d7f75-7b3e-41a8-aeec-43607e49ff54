<template>
  <div class="select-prize">
    <el-dialog
      title="选择奖品"
      :visible="true"
      :before-close="handleClose"
      :close-on-click-modal="false"
      width="600px"
      top="20px"
    >
      <div class="form-edit">
        <el-form ref="form" :model="form" :rules="formRules" label-width="110px" style="margin-right:50px;">
          <el-form-item label="选择类型" prop="propsType">
            <el-select v-model="form.propsType" :disabled="listLoading" placeholder="请选择" style="width:100%;" @change="changePropsType">
              <el-option-group
                v-for="group in propsTypeSelectGroup"
                :key="group.label"
                :label="group.label"
              >
                <el-option
                  v-for="item in group.options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-option-group>
            </el-select>
          </el-form-item>

          <el-form-item v-if="!isInput(form.propsType)" label="选择类型" prop="content">
            <div v-if="selectProps.cover" class="payer-source">
              <div class="paler-icon">
                <svgaplayer
                  type="popover"
                  :url="selectProps.svgaUrl || ''"
                />
              </div>
              <img :src="selectProps.cover" alt="cover" style="width: 100px;height:100px;">
            </div>
            <props-source-select-popover
              v-loading="listLoading"
              :data="list"
              :property="selectPopoverProperty"
              :visible="visiblePopover"
              @select="selectPropsSourcePopover"
            />
          </el-form-item>

          <el-form-item label="数量" prop="quantity">
            <el-input v-model="form.quantity" v-number placeholder="数量" />
          </el-form-item>

        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="handleClose()">取消</el-button>
        <el-button type="primary" @click="submitForm()">确定</el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>

import { listGiftBySysOrigin } from '@/api/gift'
import { listBadgePictureBySysOrigin } from '@/api/badge'
import { listSysOriginTypeList, listNotFamilyBySysOriginType } from '@/api/props'
import { listGroupBySysOrigin } from '@/api/sys-emoji'
import PropsSourceSelectPopover from '@/components/data/PropsSourceSelectPopover'

export default {
  components: { PropsSourceSelectPopover },
  props: {
    sysOrigin: {
      type: String,
      return: true,
      default: ''
    }
  },
  data() {
    const commonRules = [
      { required: true, message: '必填字段不可为空', trigger: 'blur' }
    ]
    return {
      visiblePopover: false,
      selectPopoverProperty: {
        label: 'label',
        cover: 'cover',
        svgaUrl: 'svgaUrl',
        value: 'value'
      },
      form: {
        propsType: '',
        content: '',
        quantity: '',
        cover: ''
      },
      formRules: {
        propsType: commonRules,
        content: commonRules,
        quantity: commonRules
      },
      list: [],
      listLoading: false,
      selectProps: {},
      propsTypeSelectGroup: [
        {
          label: '常用',
          options: [
            { value: 'GIFT', label: '礼物', type: 'GIFT' }
          ]
        },

        {
          label: '道具',
          options: [
            { value: 'AVATAR_FRAME', label: '头像框', type: 'PROPS' },
            { value: 'RIDE', label: '座驾', type: 'PROPS' },
            { value: 'NOBLE_VIP', label: '贵族', type: 'PROPS' },
            { value: 'THEME', label: '房间背景', type: 'PROPS' },
            { value: 'EMOJI', label: '表情包', type: 'EMOJI' }

          ]
        },

        {
          label: '徽章',
          options: [
            { value: 'BADGE', label: '用户徽章', type: 'BADGE' },
            { value: 'ROOM_BADGE', label: '房间徽章', type: 'BADGE' }
          ]
        },
        {
          label: '货币',
          options: [
            { value: 'GOLD', label: '金币', type: 'GOLD' },
            { value: 'DIAMOND', label: '钻石', type: 'DIAMOND' }
          ]
        }
      ],
      propsTypeData: {
        'AVATAR_FRAME': {
          loadData: false,
          list: []
        },
        'RIDE': {
          loadData: false,
          list: []
        },
        'NOBLE_VIP': {
          loadData: false,
          list: []
        },
        'THEME': {
          loadData: false,
          list: []
        },
        'GIFT': {
          loadData: false,
          list: []
        },
        'BADGE': {
          loadData: false,
          list: []
        },
        'ROOM_BADGE': {
          loadData: false,
          list: []
        },
        'EMOJI': {
          loadData: false,
          list: []
        }
      },
      selectPropsTypeVal: {}
    }
  },
  methods: {
    selectPropsSourcePopover(item) {
      this.selectProps = item
      this.form.content = item.value
    },
    getPropsTypeVal(val) {
      for (let index = 0; index < this.propsTypeSelectGroup.length; index++) {
        const options = this.propsTypeSelectGroup[index].options
        for (let oIndex = 0; oIndex < options.length; oIndex++) {
          if (options[oIndex].value === val) {
            return options[oIndex]
          }
        }
      }
      return {}
    },
    changePropsType(val) {
      this.selectPropsTypeVal = this.getPropsTypeVal(val)
      this.selectProps = {}
      this.form.content = ''
      if (this.isInput(val)) {
        return
      }
      this.loadSourceData(val)
    },
    isInput(propsType) {
      return propsType === 'GOLD' || propsType === 'DIAMOND'
    },
    loadSourceData(type) {
      const that = this
      const propsTypeData = that.propsTypeData[type]
      if (propsTypeData.loadData === true) {
        that.list = propsTypeData.list
        return
      }

      that.listLoading = true
      if (type === 'GIFT') {
        listGiftBySysOrigin(that.sysOrigin).then(res => {
          that.listLoading = false
          propsTypeData.loadData = true
          const list = res.body || []
          that.list = propsTypeData.list = list.map(item => {
            return {
              label: item.giftName,
              cover: item.giftPhoto,
              svgaUrl: item.giftSourceUrl,
              value: item.id
            }
          })
        }).catch(er => {
          that.listLoading = false
          console.error(er)
        })
        return
      }

      const badgeType = that.getBadgeType(type)
      if (badgeType) {
        listBadgePictureBySysOrigin(that.sysOrigin, badgeType).then(res => {
          that.listLoading = false
          propsTypeData.loadData = true
          const list = res.body || []

          that.list = propsTypeData.list = list.map(item => {
            return {
              label: item.badgeName,
              cover: item.selectUrl,
              svgaUrl: item.animationUrl,
              value: item.badgeConfigId
            }
          })
        }).catch(er => {
          that.listLoading = false
          console.error(er)
        })
        return
      }

      if (type === 'NOBLE_VIP') {
        listNotFamilyBySysOriginType(that.sysOrigin, 'NOBLE_VIP').then(res => {
          that.listLoading = false
          propsTypeData.loadData = true
          const list = res.body || []
          that.list = that.propsTypeData[type].list = list.map(item => {
            return {
              label: item.name,
              cover: item.cover,
              svgaUrl: item.sourceUrl,
              value: item.id
            }
          })
        }).catch(er => {
          that.listLoading = false
          console.error(er)
        })
        return
      }

      if (type === 'EMOJI') {
        listGroupBySysOrigin(that.sysOrigin).then(res => {
          that.listLoading = false
          propsTypeData.loadData = true
          const list = res.body || []
          that.list = propsTypeData.list = list.map(item => {
            return {
              label: item.groupName,
              cover: item.cover,
              svgaUrl: '',
              value: item.id
            }
          })
        }).catch(er => {
          that.listLoading = false
          console.error(er)
        })
        return
      }

      listSysOriginTypeList(that.sysOrigin, type).then(res => {
        that.listLoading = false
        propsTypeData.loadData = true
        const list = res.body || []
        that.list = that.propsTypeData[type].list = list.map(item => {
          return {
            label: item.name,
            cover: item.cover,
            svgaUrl: item.sourceUrl,
            value: item.id
          }
        })
      }).catch(er => {
        that.listLoading = false
        console.error(er)
      })
    },
    getBadgeType(type) {
      if (this.isUserBadge(type)) {
        return 'ACTIVITY'
      }

      if (this.isRoomBadge(type)) {
        return 'ROOM_ACHIEVEMENT'
      }
      return null
    },
    isUserBadge(type) {
      return type === 'BADGE'
    },
    isRoomBadge(type) {
      return type === 'ROOM_BADGE'
    },
    handleClose() {
      this.$emit('close')
    },
    getCover() {
      if (this.form.propsType === 'GOLD') {
        return this.$application.goldIconUrl
      }

      if (this.form.propsType === 'DIAMOND') {
        return this.$application.diamondIconUrl
      }

      return this.selectProps.cover || ''
    },
    submitForm() {
      const that = this
      if (that.isInput(that.form.propsType)) {
        that.form.content = that.form.quantity
      }
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return
        }
        that.$emit('select', {
          propsType: that.selectPropsTypeVal.type,
          propsDetailType: that.selectPropsTypeVal.val,
          content: that.form.content,
          cover: that.getCover(),
          quantity: that.form.quantity,
          unit: that.isInput(that.form.propsType) || that.form.propsType === 'GIFT' ? '' : 'D'
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">
.props-activity-reward-config-edit {
  .form-edit {
    max-height: 600px;
    overflow: auto;
    .dr-content {
      padding: 5px 0px 5px 20px;
    }
    .sort {
      border-radius: 50%;
      width: 30px;
      height: 30px;
      background: #f7f6f5;
      margin: auto;
      text-align: center;
      line-height: 29px;
      font-weight: bold;
    }
    .del {
      font-size: 30px;
      color: #F56C6C;
      cursor: pointer;
    }
    .save {
      font-size: 30px;
      color: #409EFF;
      cursor: pointer;
    }
  }
}
.my-autocomplete {
  li {
    line-height: normal;
    padding: 7px;

    .name {
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .addr {
      font-size: 12px;
      color: #b4b4b4;
    }

    .highlighted .addr {
      color: #ddd;
    }
  }
}
</style>
