<template>
  <el-dialog
    :title="'礼包配置('+ sysOriginName +')'"
    :visible="true"
    width="45%"
    top="50px"
    :before-close="handleClose"
  >
    <div class="launch-content">
      <el-form ref="form" :model="form" label-width="80px" label-position="left">
        <div class="content-box block-content">
          <el-form-item label="金币">
            <el-input v-model="form.gold" v-number placeholder="请输入金币数量" />
          </el-form-item>
        </div>

        <div class="content-box block-content">
          <el-form-item label="礼 包 配 置">
            <div class="launch-type" />

            <div v-loading="listLoading" class="main">
              <div class="gift-pack-list">
                <el-row v-for="(v,i) in form.giftPackConfigParams" :key="i" class="gift-pack-item sponsor">
                  <div v-if="v.type=='GIFT'" class="content content-box">
                    <div class="gift-pack-common-no">
                      <div class="gift-pack-label">
                        <span>礼物</span>
                      </div>
                    </div>
                    <el-row :gutter="10">
                      <el-col :span="17">
                        <el-form-item
                          :prop="'giftPackConfigParams.' + i + '.relevanceId'"
                          :rules="{
                            required: true, message: '请选择礼物', trigger: 'change'
                          }"
                        >
                          <el-select
                            v-model="v.relevanceId"
                            placeholder="请选择礼物"
                            style="width:100%"
                            filterable
                          >
                            <el-option
                              v-for="item in allGiftList"
                              :key="item.id"
                              :label="`${item.giftName}/${item.giftCandy}`"
                              :value="item.id"
                            >
                              <span>{{ item.giftName }}/{{ item.giftCandy }}</span>
                              <img :src="item.giftPhoto" style="height:32px;float: right">
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="7">
                        <el-form-item
                          :prop="'giftPackConfigParams.' + i + '.giftQuantity'"
                          :rules="{
                            required: true, message: '必填参数', trigger: 'blur'
                          }"
                        >
                          <span><el-input v-model="v.giftQuantity" style="width: 100%;" placeholder="礼物数量" /></span>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-tooltip content="删除该礼物" placement="top" style="margin-left: 15px;" class="delete-icon-wrapper">
                      <i class="el-icon-close" @click="deleteNode(i)" />
                    </el-tooltip>
                  </div>

                  <div v-if="v.type=='AVATAR_FRAME'" class="content content-box">
                    <div class="gift-pack-common-no">
                      <div class="gift-pack-label">
                        <span>头像框</span>
                      </div>
                    </div>
                    <el-row :gutter="10">
                      <el-col :span="17">
                        <el-form-item
                          :prop="'giftPackConfigParams.' + i + '.relevanceId'"
                          :rules="{
                            required: true, message: '必填参数', trigger: 'change'
                          }"
                        >
                          <el-select
                            v-model="v.relevanceId"
                            placeholder="请选择头像框"
                            style="width:100%"
                            filterable
                          >
                            <el-option
                              v-for="item in allAvatarList"
                              :key="item.id"
                              :label="`${item.name}/${item.amount}`"
                              :value="item.id"
                            >
                              <span>{{ item.name }}/{{ item.amount }} </span>
                              <img :src="item.cover" style="height:32px;float: right">
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="7">
                        <el-form-item
                          :prop="'giftPackConfigParams.' + i + '.effectDuration'"
                          :rules="{
                            required: true, message: '必填参数', trigger: 'blur'
                          }"
                        >
                          <span><el-input v-model="v.effectDuration" style="width: 100%" placeholder="必填参数" /> </span>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-tooltip content="删除该头像框" placement="top" style="margin-left: 13px;" class="delete-icon-wrapper">
                      <i class="el-icon-close" @click="deleteNode(i)" />
                    </el-tooltip>
                  </div>

                  <div v-if="v.type=='RIDE'" class="content content-box">
                    <div class="gift-pack-common-no">
                      <div class="gift-pack-label">
                        <span>座驾</span>
                      </div>
                    </div>
                    <el-row :gutter="10">
                      <el-col :span="17">
                        <el-form-item
                          :prop="'giftPackConfigParams.' + i + '.relevanceId'"
                          :rules="{
                            required: true, message: '请选择座驾', trigger: 'change'
                          }"
                        >
                          <el-select
                            v-model="v.relevanceId"
                            placeholder="请选择座驾"
                            style="width:100%"
                            filterable
                          >
                            <el-option
                              v-for="item in allRideList"
                              :key="item.id"
                              :label="`${item.name}/${item.amount}`"
                              :value="item.id"
                            >
                              <span>{{ item.name }}/{{ item.amount }}</span>
                              <img :src="item.cover" style="height:32px;float: right">
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="7">
                        <el-form-item
                          :prop="'giftPackConfigParams.' + i + '.effectDuration'"
                          :rules="{
                            required: true, message: '必填参数', trigger: 'blur'
                          }"
                        >
                          <span><el-input v-model="v.effectDuration" style="width: 100%" placeholder="必填参数" /> </span>
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-tooltip content="删除该座驾" placement="top" style="margin-left: 13px;" class="delete-icon-wrapper">
                      <i class="el-icon-close" @click="deleteNode(i)" />
                    </el-tooltip>
                  </div>
                </el-row>

              </div>
              <el-row>
                <el-col>
                  <!-- <el-button type="text" @click="addGift"><i class="el-icon-circle-plus" />添加礼物</el-button> -->
                  <el-button type="text" @click="addAvatar"><i class="el-icon-circle-plus" />添加头像框</el-button>
                  <el-button type="text" @click="addRide"><i class="el-icon-circle-plus" />添加座驾</el-button>
                </el-col>
              </el-row>
            </div>
          </el-form-item>
        </div>
        <div class="content-box block-content">
          <el-form-item>
            <el-button type="primary" style="margin: 0px 0px 0px 100px;" @click="submit('form')">提交</el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>
  </el-dialog>
</template>
<script>

import { getGiftPackInfo, giftPackConfig } from '@/api/gift-pack'
import { listGiftBySysOrigin } from '@/api/gift'
import { listSysOriginTypeList } from '@/api/props'
import { sysOriginPlatforms } from '@/constant/origin'
export default {
  name: 'GiftPackConfig',
  props: {
    packId: {
      type: String,
      default: ''
    },
    sysOrigin: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      listLoading: false,
      allGiftList: [],
      allAvatarList: [],
      allRideList: [],
      form: {
        giftPackId: '',
        gold: '',
        giftPackConfigParams: []
      },
      sysOriginPlatformMap: {}
    }
  },
  computed: {
    sysOriginName() {
      const origin = this.sysOriginPlatformMap[this.sysOrigin]
      return origin ? origin.label : ''
    }
  },
  watch: {
    packId: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal) {
          this.form.giftPackId = newVal
          this.giftContent()
          this.avatarContent()
          this.rideContent()
          this.renderData()
        }
      }
    }
  },
  created() {
    const that = this
    sysOriginPlatforms.forEach(item => {
      that.sysOriginPlatformMap[item.value] = item
    })
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    renderData() {
      const that = this
      that.listLoading = true
      getGiftPackInfo(that.form.giftPackId).then(res => {
        const { body } = res
        that.listLoading = false
        that.form.gold = body.gold || ''
        that.form.giftPackConfigParams = body.giftPackConfigParams || []
      }).catch(er => {
        console.error(er)
        that.listLoading = false
      })
    },
    submit(formName) {
      const that = this
      this.$refs[formName].validate((valid) => {
        if (valid) {
          that.listLoading = true
          giftPackConfig(that.form).then(res => {
            that.listLoading = false
            that.$emit('success', Object.assign({}, that.form))
            that.handleClose()
          }).catch(err => {
            that.listLoading = false
            that.$emit('fial', err)
            that.handleClose()
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    },
    addGift() { // 添加礼物
      this.form.giftPackConfigParams.push({ relevanceId: '', type: 'GIFT', giftQuantity: 1, effectDuration: 0 })
    },
    addAvatar() { // 添加头像框
      this.form.giftPackConfigParams.push({ relevanceId: '', type: 'AVATAR_FRAME', giftQuantity: 0, effectDuration: 1 })
    },
    addRide() { // 添加座驾
      this.form.giftPackConfigParams.push({ relevanceId: '', type: 'RIDE', giftQuantity: 0, effectDuration: 1 })
    },
    deleteNode(i) { // 删除节点
      this.form.giftPackConfigParams.splice(i, 1)
    },
    giftContent() {
      const that = this
      listGiftBySysOrigin(that.sysOrigin).then(res => {
        that.allGiftList = res.body || []
      }).catch(er => {
        console.error(er)
      })
    },
    avatarContent() {
      const that = this
      listSysOriginTypeList(that.sysOrigin, 'AVATAR_FRAME').then(res => {
        that.allAvatarList = res.body || []
      }).catch(er => {
        console.error(er)
      })
    },
    rideContent() {
      const that = this
      listSysOriginTypeList(that.sysOrigin, 'RIDE').then(res => {
        that.allRideList = res.body || []
      }).catch(er => {
        console.error(er)
      })
    },
    accountHandleSuccess(data) {
      this.$message({
        message: 'Successful',
        type: 'success'
      })
    }
  }
}
</script>

<style scoped>
  .app-container{
    padding-top: 20px;
  }
  .launch-content {
    max-height: 700px;
    overflow: auto;
  }

  .content-box {
    padding: 30px;
  }

  .block-content {
    margin-bottom: 5px;
    background: #fff;
    box-shadow: 0 2px 4px 0 #e5e7ea;
    border-radius: 2px;
  }

  .main .gift-pack-list .gift-pack-item {
    margin-bottom: 20px;
    background-color: #fff;
    border: 1px solid #e5e7ea;
    border-radius: 2px;
  }

  .main .gift-pack-list .gift-pack-item .content {
    position: relative;
    padding: 20px 45px;
  }

  .gift-pack-common-no {
    position: absolute;
    top: 0;
    left: -6px;
    white-space: nowrap;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    height: 30px;
  }

  .gift-pack-common-no .gift-pack-label {
    line-height: 18px;
    padding: 0 2px;
    border-radius: 2px;
    background: linear-gradient(315deg, #737d91, #959caa);
  }

  .gift-pack-common-no .gift-pack-label span {
    display: inline-block;
    font-size: 12px;
    color: #fff;
    transform: scale(.8);
  }

  .el-col button, .el-upload button {
    font-size: 15.5px;
  }

  .el-col button i, .el-upload button i {
    margin-right: 5px;
  }
  .menu-bar .back-button {
    float: left;
    margin-top: 18px;
    line-height: 14px;
    padding-right: 15px;
  }
  .menu-bar .back-button .el-button {
    padding: 0;
    color: #4c596e;
  }
  .menu-bar .button-group {
    position: absolute;
    top: 11px;
    right: 30px;
  }

  .delete-icon-wrapper {
    position: absolute;
    right: 0;
    top: 0;
    font-size: 24px;
    overflow: hidden;
    color: #7f8997;
    cursor: pointer;
  }
  .gift-pack-list .el-form-item__label{
    color: #001330 !important;
  }
</style>
