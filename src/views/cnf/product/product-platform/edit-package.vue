<template>
  <div class="edit-package">
    <el-drawer
      title="关联平台包名"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      size="450px"
      :modal-append-to-body="false"
    >
      <div v-loading="queryLoading" class="content">
        <div class="desc">
          <div class="flex-l col">
            <div><strong>ID:</strong> {{ row.productId }}</div>
            <div><strong>平台:</strong> {{ row.platform }}</div>
            <div><strong>单价:</strong> {{ row.unitPrice }}</div>
          </div>
        </div>
        <el-form ref="form" v-loading="submitLoading" :model="form" label-width="80px" style="padding:0px 10px;">
          <el-form-item v-for="(item,index) in form.packages" :key="index" :label="item.payPlatform">
            <el-input v-model.trim="item.productPackage" placeholder="请输入包名" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :loading="submitLoading" @click="submit()">提交</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>
  </div>
</template>
<script>

import { listConfigPackageByCnfId, addOrUpdatePackageBatch } from '@/api/product'
export default {
  props: {
    row: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      queryLoading: false,
      productPackages: [],
      submitLoading: false,
      form: {
        packages: []
      },
      packageMap: {
        'iOS': [
          { id: '', productCnfId: '', productPackage: '', payPlatform: 'Apple' }
        ],
        'Android': [
          { id: '', productCnfId: '', productPackage: '', payPlatform: 'Google' },
          { id: '', productCnfId: '', productPackage: '', payPlatform: 'Huawei' }
        ]
      }
    }
  },
  watch: {
    row: {
      immediate: true,
      handler(newValue) {
        if (!newValue) {
          return
        }

        this.form.packages = this.packageMap[this.row.platform]
        this.form.packages.forEach(item => {
          item.productCnfId = newValue.id
        })
        this.loadProcutConfigPackage(newValue.id)
      }
    }
  },
  methods: {
    loadProcutConfigPackage(productCnfId) {
      const that = this
      that.queryLoading = true
      listConfigPackageByCnfId(productCnfId).then(res => {
        that.queryLoading = false
        that.productPackages = res.body || []
        that.form.packages = Object.assign(that.form.packages, that.productPackages)
      }).catch(er => {
        that.queryLoading = false
      })
    },
    handleClose() {
      this.$emit('close')
    },
    handleClick() {

    },
    handleBaseInfoSuccess(data) {
      this.baseInfo = data
    },
    submit() {
      const that = this
      if (that.submitLoading) {
        return
      }
      that.submitLoading = true
      addOrUpdatePackageBatch({
        sysOrigin: that.row.sysOrigin,
        packages: that.form.packages
      }).then(res => {
        that.submitLoading = false
        that.$opsMessage.success()
      }).catch(er => {
        that.submitLoading = false
        that.$opsMessage.fail()
      })
    }
  }
}
</script>
<style scoped lang="scss">
.edit-package {
  .content {
    .desc {
      padding: 0px 20px 30px 30px;
      line-height: 30px;
      .col > div {
        margin-right: 20px;
      }
    }
  }
}
</style>
