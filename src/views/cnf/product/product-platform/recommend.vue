<template>
  <el-dialog
    title="提示"
    :visible="true"
    width="500px"
    :before-close="handleClose"
  >
    <div>{{ prompt }}</div>
    <span slot="footer" class="dialog-footer">
      <el-button size="small" @click="handleClose">取消</el-button>
      <el-dropdown v-if="isRecommend" size="small">
        <el-button size="small" type="primary" :disabled="!id || !productId" :loading="submitLoading">
          推荐操作<i class="el-icon-arrow-down el-icon--right" />
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item @click.native="handleCurrentPlatform()">仅当前平台</el-dropdown-item>
          <el-dropdown-item @click.native="handleAllPlatform()">同步所有平台</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>

      <el-dropdown v-if="isCancelRecommend" size="small">
        <el-button size="small" type="primary" :disabled="!platform" :loading="submitLoading">
          推荐操作<i class="el-icon-arrow-down el-icon--right" />
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item @click.native="handleCancelRecommend(false)">仅当前平台</el-dropdown-item>
          <el-dropdown-item @click.native="handleCancelRecommend(true)">同步所有平台</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </span>
  </el-dialog>
</template>

<script>
import { singleRecommend, syncAllRecommend, cancelRecommend } from '@/api/product'
export default {
  name: 'ProductRecommend',
  props: {
    // 1.推荐 2.取消推荐
    type: {
      type: Number,
      require: true,
      default: 1
    },
    prompt: {
      type: String,
      require: true,
      default: ''
    },
    id: {
      type: String,
      require: true,
      default: ''
    },
    productId: {
      type: String,
      require: true,
      default: ''
    },
    platform: {
      type: String,
      require: true,
      default: ''
    },
    sysOrigin: {
      type: String,
      required: true
    },
    productType: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      submitLoading: false
    }
  },
  computed: {
    isRecommend() {
      return this.type === 1
    },
    isCancelRecommend() {
      return this.type === 2
    }
  },
  created() {
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    handleCurrentPlatform() {
      const that = this
      that.submitLoading = true
      singleRecommend({
        id: that.id,
        platform: that.platform,
        productId: that.productId,
        sysOrigin: that.sysOrigin,
        productType: that.productType
      }).then(res => {
        that.submitLoading = false
        that.$emit('success', 'singleRecommend')
      }).catch(er => {
        console.error(er)
        that.submitLoading = false
        that.$emit('fail', 'singleRecommend')
      })
    },
    handleAllPlatform() {
      const that = this
      that.submitLoading = true
      syncAllRecommend({
        productId: that.productId,
        sysOrigin: that.sysOrigin,
        productType: that.productType
      }).then(res => {
        that.submitLoading = false
        that.$emit('success', 'syncAllRecommend')
      }).catch(er => {
        console.error(er)
        that.submitLoading = false
        that.$emit('fail', 'syncAllRecommend')
      })
    },
    handleCancelRecommend(syncAll) {
      const that = this
      that.submitLoading = true
      cancelRecommend({
        platform: that.platform,
        syncAll,
        productId: that.productId,
        sysOrigin: that.sysOrigin,
        productType: that.productType
      }).then(res => {
        that.submitLoading = false
        that.$emit('success', 'cancelRecommendAll')
      }).catch(er => {
        that.submitLoading = false
        that.$emit('fail', 'cancelRecommendAll')
      })
    }
  }
}
</script>
