<template>
  <div class="app-container-product-pool">

    <div class="filter-container">
      <el-input
        v-model.trim="listQuery.productId"
        placeholder="产品ID"
        style="width: 200px;"
        class="filter-item"
      />

      <el-select
        v-model="listQuery.groupName"
        clearable
        placeholder="分组名称"
        style="width: 120px"
        class="filter-item"
      >
        <el-option
          v-for="item in productConfigGroupNames"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>

      <el-select
        v-model="listQuery.productType"
        placeholder="产品类型"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option
          v-for="item in productTypes"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>

      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        添加
      </el-button>
    </div>
    <el-alert
      title="注意"
      type="warning"
      :closable="false"
    >
      产品创建后不可修改变更，可选择删除创建新的产品；如果产品被其他平台引用需要删除关联后才可移除产品池
    </el-alert>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
      @cell-mouse-enter="handleMouseEnter"
    >
      <el-table-column label="产品id" prop="productId" align="center" />
      <el-table-column label="描述" prop="productDesc" align="center" />
      <el-table-column label="分组" prop="groupName" align="center" width="100" />
      <el-table-column label="单价" prop="unitPrice" align="center" width="100" />
      <el-table-column prop="createTime" width="200" label="创建时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="100">
        <template slot-scope="scope">
          <div class="operation">
            <el-button type="text" @click.native="handlDel(scope.row.productId)">删除</el-button>
            <!-- <el-dropdown>
              <span class="el-dropdown-link">
                更多
              </span>
              <el-dropdown-menu slot="dropdown">

              </el-dropdown-menu>
            </el-dropdown> -->
          </div>
        </template>
      </el-table-column>
    </el-table>

    <cnf-product-pool-edit
      v-if="cnfProductPoolVisible"
      :edit-id="thisRow.id"
      @close="cnfProductPoolVisible = false;"
      @success="renderData()"
    />

  </div>
</template>

<script>
import { listProductPool, delProductPool } from '@/api/product'
import { productConfigGroupNames, productTypes, platformOrigins, productTags, productPositions } from '@/constant/type'
import CnfProductPoolEdit from './edit'
export default {
  name: 'ProductPool',
  components: { CnfProductPoolEdit },
  data() {
    return {
      thisRow: {},
      cnfProductPoolVisible: false,
      clickRow: {},
      list: [],
      listQuery: {
        productId: '',
        groupName: '',
        showcase: true,
        productType: ''
      },
      productConfigGroupNameMap: {},
      productConfigTypeMap: {},
      productConfigGroupNames,
      productTags,
      productTypes,
      productPositions,
      platformOrigins,
      listLoading: true
    }
  },
  created() {
    const that = this
    that.renderData()
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      listProductPool(that.listQuery).then(res => {
        that.listLoading = false
        const { body } = res
        that.list = body || []
      })
    },
    handleSearch() {
      this.renderData()
    },
    handleCreate() {
      this.cnfProductPoolVisible = true
    },
    handlDel(productId) {
      const that = this
      that.searchLoading = that.listLoading = false
      this.$confirm('您确定要删除该产品配置吗？', '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        delProductPool(productId).then(res => {
          that.renderData()
          that.$opsMessage.success()
        }).catch(er => {
        })
      }).catch(() => {})
    },
    handleMouseEnter(row) {
      this.thisRow = row
    }
  }
}
</script>
