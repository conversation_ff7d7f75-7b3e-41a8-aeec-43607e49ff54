<template>
  <div class="cnf-product-edit">

    <el-dialog
      title="添加产品配置"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="550px"
      top="50px"
    >
      <div v-loading="submitLoading" style="height: 500px;overflow: auto;">
        <el-form
          ref="form"
          style="width: 400px; margin-left:50px;"
          :model="form"
          :rules="rules"
        >
          <el-form-item label="CODE" prop="productId">
            <el-input v-model.trim="form.productId" type="text" />
          </el-form-item>
          <el-form-item label="描述" prop="productDesc">
            <el-input v-model.trim="form.productDesc" placeholder="请输入产品描述" />
          </el-form-item>
          <el-form-item label="分组" prop="groupName">
            <el-select
              v-model="form.groupName"
              placeholder="分组名称"
              class="filter-item"
              style="width:100%;"
            >
              <el-option
                v-for="item in productConfigGroupNames"
                :key="item.value"
                :value="item.value"
                :label="item.name"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="产品类型" prop="productType">
            <el-select
              v-model="form.productType"
              placeholder="产品类型"
              class="filter-item"
              style="width:100%;"
            >
              <el-option
                v-for="item in productTypes"
                :key="item.value"
                :value="item.value"
                :label="item.name"
              />
            </el-select>
            <el-form-item label="产品单价" prop="unitPrice">
              <el-input v-model.trim="form.unitPrice" oninput="value=value.replace(/[^\d^\.]/g,'')" type="text" />
            </el-form-item>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm()">保存</el-button>
        <el-button @click="handleClose()">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addProductPool } from '@/api/product'
import { productConfigGroupNames, productTypes } from '@/constant/type'

export default {
  name: 'CnfProductPoolEdit',
  data() {
    var commonRequiredParams = [{ required: true, message: '必填参数不可为空', trigger: 'blur' }]
    return {
      productConfigGroupNames,
      productTypes,
      form: {
        id: '',
        productId: '',
        productDesc: '',
        unitPrice: '',
        groupName: '',
        productType: ''
      },
      submitLoading: false,
      rules: {
        productId: commonRequiredParams,
        productType: commonRequiredParams,
        productDesc: commonRequiredParams,
        groupName: commonRequiredParams,
        unitPrice: commonRequiredParams
      }
    }
  },
  created() {
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('submit error!')
          return
        }
        that.submitLoading = true
        addProductPool(that.form).then(res => {
          that.submitLoading = false
          that.handleClose()
          that.$emit('success')
        }).catch(er => {
          that.submitLoading = false
          console.error(er)
          that.$emit('fail')
        })
      })

      return
    }
  }
}
</script>
