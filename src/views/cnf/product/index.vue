<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="item in tables" :key="item.name" :label="item.title" :name="item.component" />
      <component :is="activeName" />
    </el-tabs>
  </div>
</template>
<script>
import ProductPool from './product-pool'
import ProductPlatform from './product-platform'
import GiftPack from './gift-pack'
export default {
  name: 'ConfProdcut',
  components: { ProductPool, ProductPlatform, GiftPack },
  data() {
    return {
      activeName: 'ProductPool',
      tables: [
        {
          title: '产品池',
          component: 'ProductPool'
        },
        {
          title: '平台产品',
          component: 'ProductPlatform'
        },
        {
          title: '礼包配置',
          component: 'GiftPack'
        }
      ]
    }
  }
}
</script>
