<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="changeSysOrigin"
      >
        <el-option
          v-for="(item, index) in permissionsSysOriginPlatforms"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.platform"
        placeholder="支付平台"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in platformOriginsV2"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="listQuery.showcase"
        placeholder="状态"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in productConfigShowcase"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="listQuery.region"
        v-loading="loading"
        placeholder="区域"
        style="width:120px;"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in regions"
          :key="index"
          :label="item.regionName"
          :value="item.id"
        />
      </el-select>
      <el-button
        :loading="searchLoading"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>

      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        添加
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="sysOrigin" label="系统" align="center">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column prop="productPackage" label="包名" align="center" width="250" />
      <el-table-column prop="unitPrice" label="产品单价" align="center" />
      <el-table-column prop="obtainCandy" label="获得糖果数量" align="center" />
      <el-table-column prop="rewardCandy" label="奖励糖果" align="center" />
      <el-table-column prop="platform" label="平台" align="center" />
      <el-table-column prop="regionNameStr" label="区域" align="center" />
      <el-table-column prop="description" label="描述" align="center" />
      <el-table-column prop="sort" label="序号" align="center" />
      <el-table-column prop="createTime" label="创建时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center">
        <template slot-scope="scope">
          <div>
            <el-button type="text" @click.native="handlUpdate(scope.row)">修改</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :title="textOptTitle"
      :visible.sync="formVisible"
      :before-close="handleClose"
      :close-on-click-modal="false"
      width="40%"
    >
      <div v-loading="submitLoading">
        <el-form ref="form" :model="form" label-width="110px" :rules="rules">
          <el-form-item label="平台" prop="platform">
            <el-select v-model="form.platform" placeholder="支付平台" style="width:100%;">
              <el-option
                v-for="item in platformOriginsV2"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item prop="regionList" label="区域">
            <el-select v-model="form.regionList" multiple placeholder="请选择" style="width:100%;">
              <el-option
                v-for="(item, index) in regions"
                :key="index"
                :label="item.regionName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="showcase">
            <el-select
              v-model="form.showcase"
              placeholder="状态"
              class="filter-item"
              style="width:100%;"
            >
              <el-option
                v-for="item in productConfigShowcase"
                :key="item.value"
                :value="item.value"
                :label="item.name"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="包名" prop="productPackage">
            <el-input v-model.trim="form.productPackage" />
          </el-form-item>
          <el-form-item label="产品单价" prop="unitPrice">
            <el-input v-model.trim="form.unitPrice" type="number" />
          </el-form-item>
          <el-form-item label="获得糖果数量" prop="obtainCandy">
            <el-input v-model.trim="form.obtainCandy" v-number />
          </el-form-item>
          <el-form-item label="奖励糖果" prop="rewardCandy">
            <el-input v-model.trim="form.rewardCandy" v-number />
          </el-form-item>
          <el-form-item label="描述" prop="description">
            <el-input v-model.trim="form.description" />
          </el-form-item>
          <el-form-item label="顺序" prop="sort">
            <el-input v-model.trim="form.sort" type="number" :placeholder="'数字越小越靠前(升序排列)'" />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="submitForm()">保存</el-button>
            <el-button @click="handleClose()">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { productConfigShowcase, platformOriginsV2 } from '@/constant/type'
import { regionConfigTable } from '@/api/sys'
import { listProduct, addProduct, updateProduct } from '@/api/product'
import { mapGetters } from 'vuex'

function getFormData() {
  return {
    platform: '',
    sysOrigin: '',
    rewardCandy: '',
    obtainCandy: '',
    unitPrice: '',
    productPackage: '',
    description: '',
    showcase: '',
    sort: '',
    id: '',
    regionList: []
  }
}
export default {
  name: 'ProductConfigV2',
  data() {
    return {
      platformOriginsV2,
      productConfigShowcase,
      thisRow: {},
      formVisible: false,
      textOptTitle: '',
      regions: [],
      form: getFormData(),
      submitLoading: false,
      loading: false,
      list: [],
      listQuery: {
        sysOrigin: 'MARCIE',
        showcase: true,
        platform: 'AppStore',
        region: ''
      },
      listLoading: true,
      searchLoading: false,
      rules: {
        sysOrigin: [
          { required: true, message: '请选择系统', trigger: 'blur' }
        ],
        showcase: [
          { required: true, message: '请选择状态', trigger: 'blur' }
        ],
        regionList: [
          { required: true, message: '请选择区域', trigger: 'blur' }
        ],
        productPackage: [
          { required: true, message: '请输入包名', trigger: 'blur' }
        ],
        unitPrice: [
          { required: true, message: '请输入单价', trigger: 'blur' }
        ],
        obtainCandy: [
          { required: true, message: '请输入获得糖果数', trigger: 'blur' }
        ],
        platform: [
          { required: true, message: '请选择支付平台', trigger: 'blur' }
        ],
        maxManager: [
          { required: true, message: '最大管理员数必须填写', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '序号必须填写', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.listRegion()
    that.renderData()
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      listProduct(that.listQuery).then(res => {
        const { body } = res
        that.list = body
        that.searchLoading = that.listLoading = false
      }).catch(er => {
        that.searchLoading = that.listLoading = false
      })
    },
    listRegion() {
      const that = this
      that.loading = true
      regionConfigTable({ 'sysOrigin': that.listQuery.sysOrigin }).then(res => {
        that.regions = res.body || []
        that.loading = false
      }).catch(er => {
        that.loading = false
      })
    },
    handleSearch() {
      this.searchLoading = true
      this.renderData(true)
    },
    handleCreate() {
      this.textOptTitle = '新增内购产品'
      this.formVisible = true
      this.form = getFormData()
    },
    handlUpdate(row) {
      this.textOptTitle = '修改内购产品'
      this.formVisible = true
      this.form = Object.assign(this.form, row)
    },
    handleClose() {
      this.formVisible = false
      this.resetForm()
    },
    resetForm() {
      this.form = getFormData()
    },
    changeSysOrigin() {
      this.listRegion()
      this.handleSearch()
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (valid) {
          that.submitLoading = true
          that.form.sysOrigin = that.listQuery.sysOrigin
          if (!that.form.id) {
            addProduct(that.form).then(res => {
              that.$opsMessage.success()
              that.submitLoading = false
              that.formVisible = false
              that.resetForm()
              that.renderData(true)
            }).catch(er => {
              that.submitLoading = false
              console.error(er)
              this.$emit('fail')
            })
            return
          }

          updateProduct(that.form).then(res => {
            that.$opsMessage.success()
            that.submitLoading = false
            that.formVisible = false
            that.resetForm()
            that.renderData(true)
          }).catch(er => {
            that.submitLoading = false
            console.error(er)
            this.$emit('fail')
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.this-level {
  padding-bottom: 30px;
  display: flex;
  > div {
    width: 150px;
  }
}

</style>
