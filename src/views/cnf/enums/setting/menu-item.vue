<template>
  <el-menu-item v-if="!item.children || item.children.length <= 0" :index="item.value" @click="selectMenu(item.value)">
    <span slot="title">{{ item.name }}</span>
  </el-menu-item>
  <el-submenu v-else :index="item.value">
    <template slot="title">{{ item.name }}</template>
    <el-menu-item v-for="(cItem, cIndex) in item.children" :key="cIndex" :index="cItem.value" @click="selectMenu(item.value + ',' + cItem.value)">{{ cItem.name }}</el-menu-item>
  </el-submenu>
</template>

<script>
export default {
  name: 'MenuItem',
  props: {
    item: {
      type: Object,
      default: null
    }
  },
  methods: {
    selectMenu(value) {
      this.$emit('select', value)
    }
  }
}
</script>
