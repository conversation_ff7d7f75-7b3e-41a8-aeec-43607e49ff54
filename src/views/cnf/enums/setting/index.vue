<template>
  <div class="app-container">
    <el-container>
      <el-header class="setting-header">
        <sticky :sticky-top="50">
          <div class="tools">
            <!-- <div class="tool-item origin">
              <el-select
                v-model="sysOrigin"
                placeholder="系统"
                style="width: 120px;margin-top:10px;"
                class="filter-item"
              >
                <el-option
                  v-for="item in sysOriginPlatforms"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
                  <span style="float: left;margin-left:10px">{{ item.label }}</span>
                </el-option>
              </el-select>
            </div> -->
            <div class="tool-item">
              <el-menu
                :default-active="activeComponent"
                mode="horizontal"
                background-color="#D9DEE4"
                text-color="#303133"
                menu-trigger="click"
                active-text-color="rgb(86, 129, 216)"
              >
                <menu-item v-for="(item, index) in menus" :key="index" :item="item" @select="value => activeComponent = value" />
              </el-menu>
            </div>
          </div>

        </sticky>
      </el-header>
      <el-main class="setting-main">
        <div class="content">
          <enums-setting-config :group="activeComponent" />
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import EnumsSettingConfig from './config'
import { enumConfigGroupNames } from '@/constant/type'
import Sticky from '@/components/Sticky'
import MenuItem from './menu-item.vue'
export default {
  name: 'Setting',
  components: { EnumsSettingConfig, Sticky, MenuItem },
  data() {
    const getDefaultActiveComponent = () => {
      const first = enumConfigGroupNames[0]
      if (!first) {
        return ''
      }
      return first.children && first.children.length > 0
        ? first.value + ',' + first.children[0].value : first.value
    }
    return {
      activeComponent: getDefaultActiveComponent(),
      menus: enumConfigGroupNames
    }
  }
}
</script>
<style scoped lang="scss">
.setting-header {
    border-right: solid 1px #e6e6e6;
    background: #D9DEE4;
    .el-menu {
        border: none;
    }
}

.setting-main {
    padding: 10PX;
}

.tools {
  background: #D9DEE4;
  display: flex;
  .tool-item {
    width: 100%;
  }
  .tool-item.origin {
    width:150px;
    text-align: center;
  }
}
</style>
