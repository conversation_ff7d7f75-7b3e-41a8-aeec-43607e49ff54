<template>
  <div v-loading="loading" class="team-info">
    <el-button v-if="isDrag" v-loading="isDragLoading" type="danger" style="margin-left: 40px;" @click.native="handleSort()">
      保存排序
    </el-button>
    <ul class="setting-list">
      <li v-for="(item,index) in list" :key="index" class="item">
        <div class="list-left ">
          <a :title="isNotNull(item.description) ? item.description : item.title" @click="copyTextContent(item.name)">
            <i class="el-icon-question" style="cursor: pointer;" />
            <strong>{{ item.title }}</strong>
          </a>
        </div>
        <div class="list-right">

          <el-switch
            v-if="dataTypeBool(item)"
            v-model="item.val"
            :disabled="item.inoperable"
            active-value="true"
            inactive-value="false"
            @change="(v)=> boolSwaitchChange(v,item)"
          />

          <el-button v-else type="text" :disabled="item.inoperable" @click="handleEdit(item)">编辑</el-button>

        </div>
        <div class="list-center">
          {{ dataTypeConvertVal(item) }}
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import { copyText } from '@/utils'
import { getEnumConfigByGroup, updateConfigInfo, updateSort } from '@/api/table'
import { enumConfigDataTypesExpression } from '@/constant/type'
import Sortable from 'sortablejs'

export default {
  name: 'EnumsSettingConfig',
  props: {
    group: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      isDragLoading: false,
      isDrag: false,
      loading: false,
      list: []
    }
  },
  watch: {
    group: {
      handler(newVal) {
        if (newVal) {
          this.renderData()
          this.isDrag = false
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.rowDrop()
  },
  methods: {
    renderData() {
      const that = this
      that.loading = true
      getEnumConfigByGroup(that.group).then(res => {
        const { body } = res
        that.loading = false
        that.list = body.result || []
      }).catch(er => {
        that.loading = false
      })
    },
    isNotNull(text) {
      return text && text.trim().length > 0
    },
    dataTypeBool(item) {
      return item && item.dataType === 'bool'
    },
    dataTypeConvertVal(item) {
      const that = this
      if (that.dataTypeBool(item)) {
        if (item.val === 'true') {
          return '开'
        }
        if (item.val === 'false') {
          return '关'
        }
      }
      return item.val
    },
    handleEdit(item) {
      const that = this
      const dataTypeExpression = enumConfigDataTypesExpression[item.dataType]
      if (!dataTypeExpression) {
        that.$opsMessage.fail('未找到相关定义数据类型，请联系管理员！')
        return
      }
      that.$prompt(`请输入：${item.description}`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: dataTypeExpression.rex,
        inputErrorMessage: `数据类型错误: ${dataTypeExpression.msg}`
      }).then(({ value }) => {
        updateConfigInfo({ id: item.id, val: value, name: item.name }).then(res => {
          that.renderData()
        })
      }).catch(() => {
      })
    },
    boolSwaitchChange(value, item) {
      const that = this
      updateConfigInfo({ id: item.id, val: value, name: item.name }).then(res => {
        that.renderData()
      })
    },
    rowDrop() {
      const that = this
      Sortable.create(document.querySelector('.team-info .setting-list'), {
        ghostClass: 'simple-el-drop-table',
        onEnd: function(event) {
          that.list.splice(event.newIndex, 0, that.list.splice(event.oldIndex, 1)[0])
          const newArray = that.list.slice(0)
          that.list = []
          that.$nextTick(() => {
            that.list = newArray
          })
          that.isDrag = true
        }
      })
    },
    handleSort() {
      const that = this
      const list = that.list
      if (list.length <= 0) {
        return
      }
      const params = []
      const size = list.length
      for (let index = 0; index < size; index++) {
        const data = list[index]
        params.push({
          id: data.id,
          sort: size - index
        })
      }

      that.isDragLoading = true
      updateSort(params).then(res => {
        that.isDragLoading = false
        that.isDrag = false
      }).catch(er => {
        that.isDragLoading = false
        console.error('error', er)
      })
    },
    copyTextContent(text) {
      const that = this
      copyText(text).then(() => {
        that.$message({
          message: '复制成功',
          type: 'success'
        })
      }).catch(() => {
        that.$message({
          message: '复制失败',
          type: 'error'
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">
ul,li {
    list-style: none;
}
.team-info {
    font-size: 14px;
    .setting-list {
        .item {
            padding: 33px;
            border-top: 1px dashed #e1e6eb;
            .list-left {
                width: 140px;
                float: left !important;
            }
            .list-center {
                overflow: hidden;
                padding: 0px 20px;
            }
            .list-right {
                width: 190px;
                text-align: center;
                float: right !important;
            }
        }
    }
}
</style>
