<template>
  <div class="push-form">
    <el-form ref="form" :model="form" label-width="100px">
      <el-form-item prop="sysOrigin" label="系统">
        <el-select
          v-model="form.sysOrigin"
          placeholder="归属系统"
          style="width: 120px"
          class="filter-item"
          @change="handleSearch"
        >
          <el-option
            v-for="(item, index) in permissionsSysOriginPlatforms"
            :key="index"
            :label="item.label"
            :value="item.value"
          >
            <span style="float: left;">
              <sys-origin-icon
                :icon="item.value"
                :desc="item.value"
              /></span>
            <span style="float: left;margin-left:10px">{{ item.label }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="giftId" label="礼物ID">
        <el-input v-model="form.giftId" type="number" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { getPairCpGiveGiftId, pushPairCpGiveGiftId } from '@/api/gift'
import { mapGetters } from 'vuex'
export default {
  name: 'CP',
  data() {
    return {
      form: {
        giftId: '',
        sysOrigin: ''
      }
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.form.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      getPairCpGiveGiftId(that.form.sysOrigin).then(res => {
        const { body } = res
        that.form.giftId = body
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData()
    },
    onSubmit() {
      const that = this
      that.$refs.form.validate((valid) => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        if (that.form.sysOrigin === '') {
          that.$opsMessage.fail('请输选择系统')
          return
        }
        if (that.form.giftId === '') {
          that.$opsMessage.fail('请输入礼物ID')
          return
        }
        that.$confirm('是否确定修改配置值?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          pushPairCpGiveGiftId(that.form.giftId, that.form.sysOrigin).then(res => {
            that.$message({
              type: 'success',
              message: 'Successful'
            })
            that.renderData()
          }).catch(er => {
            // that.$opsMessage.success()
          })
        }).catch(() => {
          that.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">
  .msg-text {
    color: #54c988;
    background-color: #f6f5f5;
  }
</style>
