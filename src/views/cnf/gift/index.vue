<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="item in tables" :key="item.name" :label="item.title" :name="item.component" />
      <component :is="activeName" />
    </el-tabs>
  </div>
</template>
<script>
import Setting from './setting'
import WeekStar from './week-star'
import CP from './cp'
export default {
  name: 'GiftConfig',
  components: { Setting, WeekStar, CP },
  data() {
    return {
      activeName: 'Setting',
      tables: [
        {
          title: '礼物配置',
          component: 'Setting'
        },
        {
          title: '周星礼物配置',
          component: 'WeekStar'
        },
        {
          title: 'CP组合赠送礼物',
          component: 'CP'
        }
      ]
    }
  }
}
</script>
