<template>
  <div class="gift-config-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="归属系统"
        style="width: 120px"
        class="filter-item"
        @change="changeSysOrigin"
      >
        <el-option
          v-for="(item, index) in permissionsSysOriginPlatforms"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.sortType"
        placeholder="排序方式"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option v-for="item in sortTypes" :key="item.value" :label="item.name" :value="item.value" />
      </el-select>
      <el-select
        v-model="listQuery.del"
        placeholder="上/下架"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option v-for="item in showcaseTypes" :key="item.value" :label="item.name" :value="item.value" />
      </el-select>

      <el-select
        v-model="listQuery.giftTab"
        placeholder="类型"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in giftConfigTabs"
          :key="index"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <div v-if="listQuery.giftTab==='EXCLUSIVE'" class="filter-item">
        <div><account-input v-model="listQuery.userId" placeholder="用户id" type="USER" :sys-origin="listQuery.sysOrigin" /></div>
      </div>
      <el-select
        v-model="listQuery.region"
        v-loading="loading"
        placeholder="区域"
        style="width:120px;"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in regions"
          :key="index"
          :label="item.regionName"
          :value="item.id"
        />
      </el-select>
      <el-select
        v-model="listQuery.type"
        placeholder="收费类型"
        style="width:120px;"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="金币" :value="'GOLD'" />
        <el-option label="免费" :value="'FREE'" />
        <el-option label="钻石" :value="'DIAMOND'" />
      </el-select>
      <el-input
        v-model.trim="listQuery.giftId"
        placeholder="礼物ID"
        clearable
        style="width: 200px;"
        class="filter-item"
      />
      <el-input
        v-model.trim="listQuery.giftName"
        placeholder="礼物名称"
        clearable
        style="width: 200px;"
        class="filter-item"
      />

      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </div>

      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        新增
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="ID" width="200" align="center" prop="id" />
      <el-table-column label="Code" align="center" prop="giftCode" />
      <el-table-column label="封面" align="center">
        <template slot-scope="scope">
          <el-image
            style="width: 50px; height: 50px"
            :src="scope.row.giftPhoto"
            :preview-src-list="[scope.row.giftPhoto]"
          />
        </template>
      </el-table-column>
      <el-table-column prop="giftName" label="名称" align="center" />
      <el-table-column prop="giftCandy" label="金币" align="center" />
      <el-table-column prop="giftIntegral" label="积分" align="center" />
      <el-table-column prop="regionNameStr" label="区域" align="center" />
      <el-table-column prop="typeName" label="类型" align="center" />
      <el-table-column prop="sort" label="排序权重" align="center" />
      <el-table-column width="100" label="上/下架" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.del"
            :active-value="false"
            :inactive-value="true"
            @change="handleSwitchChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" min-width="170" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column label="专属礼物归属人" align="center" min-width="150">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.userBaseInfo" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="80">
        <template slot-scope="scope">
          <el-dropdown>
            <span class="el-dropdown-link">
              <i class="el-icon-more" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item type="text" @click.native="handleUpdate(scope.row)">编辑</el-dropdown-item>
              <el-dropdown-item v-if="scope.row.giftPhoto" type="text" @click.native="copyContent(scope.row.giftPhoto)">图片地址&nbsp;<i class="el-icon-document-copy" /></el-dropdown-item>
              <el-dropdown-item v-if="scope.row.giftSourceUrl" type="text" @click.native="copyContent(scope.row.giftSourceUrl)">动画地址&nbsp;<i class="el-icon-document-copy" /></el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <gift-edit
      v-if="formVisible"
      :event="giftEditEvent"
      :select-param="giftEditParam"
      @close="handleClose"
      @success="giftEditSuccess"
      @fail="giftEditFail"
    />

  </div>
</template>

<script>
import { giftTable, switchDelStatus } from '@/api/gift'
import { regionConfigTable } from '@/api/sys'
import Pagination from '@/components/Pagination'
import { giftConfigTabs } from '@/constant/type'
import GiftEdit from './edit'
import { mapGetters } from 'vuex'
import { copyText } from '@/utils'

export default {
  components: { Pagination, GiftEdit },
  data() {
    return {
      sortTypes: [
        { value: 'CREATE_TIME_DESC', name: '创建时间降序' },
        { value: 'AMOUNT_DESC', name: '金额降序' },
        { value: 'SORT_DESC', name: '权重降序' }
      ],
      showcaseTypes: [
        { value: true, name: '下架' },
        { value: false, name: '上架' }
      ],
      giftEditParam: {},
      giftConfigTabs,
      giftEditEvent: 'ADD',
      rangeDate: '',
      loading: false,
      pushTextHistoryLoading: false,
      pushTextHistoryVisible: false,
      disabledTranslate: false,
      fileList: [],
      pushTextHistory: [],
      list: [],
      delarr: [],
      total: 0,
      regions: [],
      listQuery: {
        cursor: 1,
        limit: 20,
        startCreateDate: '',
        endCreateDate: '',
        giftName: '',
        sysOrigin: 'HALAR',
        giftTab: '',
        giftId: '',
        userId: '',
        del: false,
        type: '',
        region: '',
        sortType: 'CREATE_TIME_DESC'
      },
      formVisible: false,
      listLoading: true
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startCreateDate = newVal[0]
          this.listQuery.endCreateDate = newVal[1]
          return
        }
        this.listQuery.startCreateDate = ''
        this.listQuery.endCreateDate = ''
      }
    }
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.listRegion()
    that.renderData()
  },
  methods: {
    copyContent(text) {
      copyText(text).then(() => {
        this.$message.success('复制成功')
      }).catch(er => {
        this.$message.error('复制失败')
      })
    },
    changeSysOrigin() {
      this.listRegion()
      this.handleSearch()
    },
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        that.listQuery.cursor = 1
        that.list = []
      }
      that.listLoading = true
      giftTable(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    listRegion() {
      const that = this
      that.loading = true
      regionConfigTable({ 'sysOrigin': that.listQuery.sysOrigin }).then(res => {
        that.regions = res.body || []
        that.loading = false
      }).catch(er => {
        that.loading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    queryUserDetails(row) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = row.id
    },

    handleClose() {
      this.formVisible = false
    },
    handleCreate() {
      this.giftEditEvent = 'ADD'
      this.giftEditParam = {}
      this.formVisible = true
    },
    handleUpdate(row) {
      this.giftEditEvent = 'UPDATE'
      this.formVisible = true
      this.giftEditParam = row
    },
    giftEditSuccess(res) {
      this.$message.success('操作成功')
      this.formVisible = false
      if (res.event === 'UPDATE') {
        this.renderData()
      }
    },
    giftEditFail() {
      this.$message.success('操作失败')
    },
    handleSwitchChange(row) {
      switchDelStatus(row.id, row.del)
        .then((res) => {})
        .catch(er => {
          row.del = !row.del
        })
    }
  }
}
</script>
