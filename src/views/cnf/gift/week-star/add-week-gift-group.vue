<template>
  <div class="gift-edit">
    <el-dialog
      title="添加周星礼物组"
      :visible="true"
      :before-close="handleClose"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      width="450px"
    >
      <div v-loading="submitLoading">
        <el-alert
          title="注意：周星正常运行切换最少需要配置3组礼物"
          type="warning"
          :closable="false"
          style="margin-bottom:10px"
        />
        <el-form ref="form" :model="form" :rules="rules" label-width="110px" style="margin-right:50px;">

          <el-form-item v-loading="changeSysOriginLoading" label="归属系统" prop="sysOrigin">
            <el-select
              v-model="form.sysOrigin"
              placeholder="归属系统"
              style="width: 100%"
              @change="changeSysOrigin"
            >
              <el-option
                v-for="item in permissionsSysOriginPlatforms"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
                <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
                <span style="float: left;margin-left:10px">{{ item.label }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="礼物1" prop="giftOne">
            <el-select
              v-model="form.giftOne"
              placeholder="礼物类型"
              style="width:100%;"
            >
              <el-option v-for="(item, index) in selectGifts" :key="index" :label="item.giftName" :value="item.id" :disabled="isDisable(item.id)">
                <div class="gift-select">
                  <img :src="item.giftPhoto" alt="gift">
                  <span>金币:{{ item.giftCandy }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="礼物2" prop="giftTwo">
            <el-select
              v-model="form.giftTwo"
              placeholder="礼物类型"
              style="width:100%;"
            >
              <el-option v-for="(item, index) in selectGifts" :key="index" :label="item.giftName" :value="item.id" :disabled="isDisable(item.id)">
                <div class="gift-select">
                  <img :src="item.giftPhoto" alt="gift">
                  <span>金币:{{ item.giftCandy }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="礼物3" prop="giftThree">
            <el-select
              v-model="form.giftThree"
              placeholder="礼物类型"
              style="width:100%;"
            >
              <el-option v-for="(item, index) in selectGifts" :key="index" :label="item.giftName" :value="item.id" :disabled="isDisable(item.id)">
                <div class="gift-select">
                  <img :src="item.giftPhoto" alt="gift">
                  <span>金币:{{ item.giftCandy }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" style="text-align: center;">
          <el-button @click="handleClose()">取消</el-button>
          <el-button type="primary" @click="submitForm()">保存</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { listGiftBySysOrigin } from '@/api/gift'
import { addWeekStarGroup } from '@/api/sys-dictionary'
import { mapGetters } from 'vuex'

export default {
  name: 'AddWeekGiftGroup',
  data() {
    function commonFormRules() {
      return [
        { required: true, message: '必填字段不可为空', trigger: 'blur' }
      ]
    }
    return {
      submitLoading: false,
      changeSysOriginLoading: false,
      sysOriginGiftMap: {},
      selectGifts: [],
      form: {
        sysOrigin: '',
        giftOne: '',
        giftTwo: '',
        giftThree: ''
      },
      rules: {
        sysOrigin: commonFormRules(),
        giftOne: commonFormRules(),
        giftTwo: commonFormRules(),
        giftThree: commonFormRules()
      }
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  methods: {
    isDisable(val) {
      return this.form.giftOne === val || this.form.giftTwo === val || this.form.giftThree === val
    },
    handleClose() {
      this.$emit('close')
    },
    changeSysOrigin(value) {
      const that = this
      that.form.giftOne = ''
      that.form.giftTwo = ''
      that.form.giftThree = ''
      const sysOriginGift = that.sysOriginGiftMap[value]
      if (sysOriginGift && sysOriginGift.length > 0) {
        that.selectGifts = sysOriginGift
        return
      }
      that.loadBySysOrigin(value)
    },
    loadBySysOrigin(sysOrigin) {
      const that = this
      that.changeSysOriginLoading = true
      listGiftBySysOrigin(sysOrigin).then(res => {
        that.changeSysOriginLoading = false
        that.selectGifts = res.body || []
        that.sysOriginGiftMap[sysOrigin] = that.selectGifts
      }).catch(er => {
        that.changeSysOriginLoading = false
      })
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (valid) {
          that.submitLoading = true
          addWeekStarGroup({
            sysOrigin: that.form.sysOrigin,
            giftIds: [that.form.giftOne, that.form.giftTwo, that.form.giftThree]
          }).then(res => {
            that.submitLoading = false
            that.$emit('success', res)
          }).catch(er => {
            that.submitLoading = false
            console.error(er)
            that.$emit('fial', er)
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.gift-select {
    img {
        width: 30px;
        height: 30px;
        border: 30px;
        vertical-align: middle;
        margin-right: 10px;
    }
}
</style>
