<template>
  <div class="gift-config-week-star">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="归属系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in permissionsSysOriginPlatforms"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;">
            <sys-origin-icon
              :icon="item.value"
              :desc="item.value"
            /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        新增
      </el-button>
      <el-alert
        title="注意：周星正常运行切换最少需要配置3组礼物"
        type="warning"
        :closable="false"
      />
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="编号" align="center" prop="id" width="200" />
      <el-table-column
        label="归属系统"
        align="center"
        prop="sysOrigin"
        width="200"
      >
        <template slot-scope="scope">
          <sys-origin-icon
            :icon="scope.row.sysOrigin"
            :desc="scope.row.sysOrigin"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="展示次数"
        align="center"
        prop="displayNumber"
        width="100"
      />
      <el-table-column label="礼物信息" align="center">
        <template slot-scope="scope">
          <el-row>
            <el-col
              v-for="(item, index) in scope.row.sysGiftConfigs"
              :key="index"
              :span="8"
            >
              <el-image
                style="width: 50px; height: 50px"
                :src="item.giftPhoto"
                :preview-src-list="[item.giftPhoto]"
              />
              <div>{{ item.giftCandy }}</div>
            </el-col>
          </el-row>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          <div v-if="scope.row.type === 'THIS_WEEK'">
            当前
          </div>
          <div v-else-if="scope.row.type === 'LAST_WEEK'">
            上周
          </div>
          <div v-else-if="scope.row.type === 'NEXT_WEEK'">
            下周
          </div>
          <div v-else>
            -
          </div>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button
            type="text"
            :disabled="scope.row.type !== 'NONE' || list.length <= 3"
            @click.native="handlDel(scope.row.id)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <add-week-gift-group
      v-if="formVisible"
      @close="formVisible = false"
      @success="addSuccess"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import AddWeekGiftGroup from './add-week-gift-group'
import { getWeekStarGroupPage, delWeekStarGroup } from '@/api/sys-dictionary'
import { mapGetters } from 'vuex'

export default {
  components: { Pagination, AddWeekGiftGroup },
  data() {
    return {
      formVisible: false,
      listLoading: true,
      list: [],
      delarr: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        sysOrigin: 'TIM_CHAT'
      }
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    }
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        that.listQuery.cursor = 1
        that.list = []
      }
      that.listLoading = true
      getWeekStarGroupPage(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData()
    },
    renderDataSuccess() {
      this.$message({
        message: 'Successful',
        type: 'success'
      })
      this.renderData()
    },
    queryUserDetails(row) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = row.id
    },
    // 删除
    handlDel(id) {
      this.$confirm('确认删除吗？', '提示', {
        type: 'warning'
      })
        .then(() => {
          this.listLoading = true
          delWeekStarGroup(id).then(res => {
            this.listLoading = false
            this.$opsMessage.success()
            this.renderData()
          })
        })
        .catch(() => {})
    },
    handleCreate() {
      this.formVisible = true
    },
    addSuccess() {
      this.$opsMessage.success()
      this.formVisible = false
      this.renderData()
    }
  }
}
</script>
