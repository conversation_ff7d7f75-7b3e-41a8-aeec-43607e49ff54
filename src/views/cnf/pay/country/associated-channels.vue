<template>
  <div class="associated-channels">
    <el-dialog
      title="关联渠道2"
      :visible="true"
      :before-close="handleClose"
      :close-on-click-modal="false"
      width="80%"
      top="20px"
    >
      <div class="form-edit">
        <div v-loading="loading" class="operation-but">
          <div class="flex-r">
            <el-image
              style="width: 30px; height: 18px;margin: 0px 5px;"
              :src="row.country.nationalFlag"
              :preview-src-list="[row.country.nationalFlag]"
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline" />
              </div>
            </el-image>
            {{ row.country.aliasName }}、
            国家货币: {{ row.currency }}、
            美元汇率: {{ row.usdExchangeRate }}
            <div style="margin-left: 20px;">
              <transfer-channel
                :selected-channels="channelCodes"
                :pay-country-id="payCountryId"
                @hide="hideTransferChannel"
                @change="handleChange"
              />
            </div>
          </div>
        </div>
        <el-form ref="form" label-position="top">
          <el-collapse v-if="list && list.length > 0" v-model="activeNames">
            <el-collapse-item v-for="(item,index) in list" :key="index" :name="item.channelCode">
              <template slot="title">
                <div class="flex-l" style="width: 100%;">
                  <img class="channel-icon" :src="item.channel.channelIcon" alt="icon" width="30" height="30">
                  <div style="width: 100%;" class="channel-name nowrap-ellipsis">{{ item.channel.channelName }}</div>
                  <!-- <div>
                    <el-button type="text" style="color: #F56C6C;" @click.stop="clickDeleteChannel"><i class="el-icon-delete" />删除</el-button>
                  </div> -->
                </div>
              </template>
              <div v-for="(aItem,aIndex) in item.countryChannels" :key="aIndex" class="channel-item">
                <el-col :md="4">
                  <div class="flex-l input-content">
                    <img class="channel-icon" :src="aItem.payFactory.factoryIcon" alt="icon" width="30" height="30">
                    <div class="channel-name nowrap-ellipsis">{{ aItem.payFactory.factoryName }}</div>
                  </div>
                </el-col>
                <el-col :md="20">
                  <el-col :md="8" class="channel-item-form">
                    <el-form-item label="最小额度">
                      <el-input v-model.trim="aItem.countryChannelDetails.factoryMinLimit" :disabled="aItem.disabled" placeholder="最小限额" class="input-with-select" maxlength="10" @change="()=> changeFactoryVal(aItem, 'factoryMinLimit')">
                        <template slot="append">USD</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :md="8" class="channel-item-form">
                    <el-form-item label="最大额度">
                      <el-input v-model.trim="aItem.countryChannelDetails.factoryMaxLimit" :disabled="aItem.disabled" placeholder="最小限额" class="input-with-select" maxlength="10" @change="()=> changeFactoryVal(aItem, 'factoryMaxLimit')">
                        <template slot="append">USD</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :md="8" class="channel-item-form">
                    <el-form-item label="每日限额">
                      <el-input v-model.trim="aItem.countryChannelDetails.factoryDailyLimit" :disabled="aItem.disabled" placeholder="每日限额" class="input-with-select" maxlength="10" @change="()=> changeFactoryVal(aItem, 'factoryDailyLimit')">
                        <template slot="append">USD</template>
                      </el-input>
                    </el-form-item>
                  </el-col>

                  <el-col :md="8" class="channel-item-form">
                    <el-form-item label="第三方渠道CODE">
                      <el-input v-model="aItem.countryChannelDetails.factoryChannel" :disabled="aItem.disabled" placeholder="请输入第三方厂商渠道CODE" @change="(val)=>changeFactoryVal(aItem, 'factoryChannel')" />
                    </el-form-item>
                  </el-col>
                  <el-col :md="8" class="channel-item-form">
                    <el-form-item label="货币小数点(暂时没用)">
                      <el-input v-model="aItem.countryChannelDetails.factoryCurrencyPoint" :disabled="aItem.disabled" placeholder="货币小数点,默认2位" @change="(val)=>changeFactoryVal(aItem, 'factoryCurrencyPoint')" />
                    </el-form-item>
                  </el-col>
                  <el-col :md="8" class="channel-item-form">
                    <el-form-item label="是否启动">
                      <el-switch
                        v-model="aItem.countryChannelDetails.shelf"
                        :disabled="aItem.disabled"
                        active-text="开"
                        inactive-text="关"
                        @change="changeFactoryVal(aItem, 'shelf')"
                      />
                    </el-form-item>
                  </el-col>
                </el-col>
              </div>

            </el-collapse-item>
          </el-collapse>

        </el-form>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { listPayCountryChannel, addBatchCountyAssociateChannel, delBatchPayCountryChannel, addOrUpdateCountyChannelDetails } from '@/api/sys-pay'
import TransferChannel from './../comp/transfer-channel'
export default {
  components: { TransferChannel },
  props: {
    row: {
      type: Object,
      require: false,
      default: () => {}
    }
  },
  data() {
    return {
      activeNames: 'PAER_MAX',
      loading: false,
      list: [],
      channelCodes: [],
      payCountryId: ''
    }
  },
  computed: {
  },
  watch: {
    row: {
      handler(newVal) {
        if (!newVal) {
          return
        }
        this.payCountryId = newVal.id
        this.renderData()
      },
      immediate: true
    }
  },
  methods: {
    changeFactoryVal(item, field) {
      console.log('item', item)
      if (field === 'factoryMinLimit' ||
      field === 'factoryMaxLimit' ||
      field === 'factoryDailyLimit') {
        const regx = /^\d{1,8}(\.\d{0,2})?$/
        if (!regx.test(item.countryChannelDetails[field])) {
          this.$opsMessage.fail('输入支持范围0 ~ 99999999最多2位小数')
          return
        }
      }
      if (field === 'factoryCurrencyPoint') {
        if (item.factoryCurrencyPoint < 0 || item.factoryCurrencyPoint > 6) {
          this.$opsMessage.fail('货币小数点范围0 ~ 6')
          return
        }
      }
      item.disabled = !item.countryChannelDetails.id
      item.loading = true
      addOrUpdateCountyChannelDetails(item.countryChannelDetails).then(res => {
        item.loading = false
        item.disabled = false
        item.countryChannelDetails.id = res.body.id
      }).catch(er => {
        item.disabled = false
        item.loading = false
        console.error(er)
      })
    },
    hideTransferChannel(isChange) {
      if (isChange) {
        this.renderData()
      }
    },
    handleChange(value, direction, movedKeys) {
      const that = this
      if (direction === 'right') {
        addBatchCountyAssociateChannel({
          payCountryId: that.payCountryId,
          channelCodes: movedKeys
        }).then(res => {
          that.$opsMessage.success()
        }).catch(er => {
          console.error(er)
        })
        return
      }
      delBatchPayCountryChannel({
        payCountryId: that.payCountryId,
        channelCodes: movedKeys
      }).then(res => {
        that.$opsMessage.success()
      }).catch(er => {
        console.error(er)
      })
    },
    renderData() {
      this.loadListPayCountryChannel(this.payCountryId)
    },
    loadListPayCountryChannel(payCountryId) {
      const that = this
      that.loading = true
      listPayCountryChannel(payCountryId).then(res => {
        that.loading = false
        const list = res.body || []
        list.forEach(item => {
          item.countryChannels.forEach(cItem => {
            cItem.loading = false
            cItem.disabled = false
          })
        })
        that.list = list
        that.channelCodes = list.map(item => item.associated.channelCode)
      }).catch(er => {
        that.loading = false
        console.error(er)
      })
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
<style lang="scss">
  .associated-channels {
    .el-switch__label  {
      color: #333333;
    }
  }
</style>
<style scoped lang="scss">
.associated-channels {
  .form-edit {
    max-height: 70vh;
    overflow: auto;
    .operation-but {
        text-align: right;
        padding: 0px 20px;
        position: absolute;
        width: 100%;
        top: 45px;
        left: 0px;
        right: 0px;
        z-index: 999;

    }
    .channel-name {
      padding: 0px 5px;
    }
    .channel-icon {
        border-radius: 50%;
    }
    .col {
        padding: 0px 10px;
    }
    .margin-top-10 {
        margin-top: 10px;
    }
    .channel-item + .channel-item {
      margin-top: 10px;
    }
    .channel-item {
        background-color: #828f98;
        padding: 10px;
        border-radius: 5px;
        overflow: hidden;
        .channel-item-form {
          padding: 0px 5px;
        }
    }
  }
 .input-content {
   margin-top: 5px;
   color: #333333;
 }
}

</style>
