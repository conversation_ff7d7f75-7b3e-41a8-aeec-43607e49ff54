<template>
  <div class="associated-channels">
    <el-dialog
      title="国家选择"
      :visible="true"
      :before-close="handleClose"
      :close-on-click-modal="false"
      width="700px"
      top="50px"
    >
      <div class="form-edit">
        <el-transfer
          v-model="countryValues"
          v-loading="countryValuesLoading"
          style="text-align: left; display: inline-block"
          filterable
          :titles="['可选', '已选']"
          :format="{
            noChecked: '${total}',
            hasChecked: '${checked}/${total}'
          }"
          :button-texts="['删除','开通']"
          :props="{key: 'id', label: 'aliasName'}"
          :data="countrys"
          @change="handleChange"
        >
          <div slot-scope="{ option }">
            <div class="flex-l">
              <el-image
                style="width: 28px; height: 18px;margin: 0px 2px;"
                :src="option.nationalFlag"
              >
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </el-image>
              <div>{{ option.aliasName || option.countryName }}</div>
            </div>
          </div>
        </el-transfer>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import { getCountryAlls } from '@/api/sys'
import { addPayOpenCountyBatch, delPayOpenCountyBatch, listPayOpenCountry } from '@/api/sys-pay'
import { deepClone } from '@/utils'
export default {
  props: {
    selectedCountrys: {
      type: Array,
      require: false,
      default: () => []
    }
  },
  data() {
    return {
      countryValues: [],
      countryLoading: false,
      countrys: [],
      countryValuesLoading: false
    }
  },
  computed: {
    isUpdate() {
      return this.row && this.row.id
    }
  },
  watch: {
    row: {
      handler(newVal) {
        if (!newVal) {
          return
        }
        const newForm = deepClone(newVal)
        newForm.tmpConfigList = []
        this.form = newForm
      },
      immediate: true
    }
  },
  created() {
    this.loadListPayOpenCountry()
    this.loadCountrys()
  },
  methods: {
    loadListPayOpenCountry() {
      const that = this
      that.countryValuesLoading = true
      listPayOpenCountry().then(res => {
        that.countryValuesLoading = false
        const list = res.body || []
        that.countryValues = list.map(item => {
          return item.countryId
        })
      }).catch(er => {
        that.countryValuesLoading = false
        console.error(er)
      })
    },
    handleChange(value, direction, movedKeys) {
      const that = this
      if (movedKeys.length <= 0) {
        that.$opsMessage.fail()
        return
      }
      if (direction === 'right') {
        addPayOpenCountyBatch(movedKeys.map(item => {
          return {
            countryId: item,
            currency: '',
            usdExchangeRate: 0,
            shelf: 0
          }
        })).then(res => {
          that.$opsMessage.success()
        }).catch(er => {
          console.error(er)
        })
        return
      }
      delPayOpenCountyBatch(movedKeys).then(res => {
        that.$opsMessage.success()
      }).catch(er => {
        console.error(er)
      })
    },
    getSelectedParams() {

    },
    loadCountrys() {
      const that = this
      that.countryLoading = true
      getCountryAlls().then(res => {
        that.countryLoading = false
        const countryList = res.body || []
        that.countrys = countryList
        // countryList.forEach(item => {
        //   that.countrys.push({
        //     key: item.id,
        //     label: item.aliasName,
        //     disabled: false
        //   })
        // })
      }).catch(er => {
        that.countryLoading = false
        console.error(er)
      })
    },
    //
    clickDeleteChannel() {

    },
    submitItem(index) {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return
        }
        const item = that.form.tmpConfigList[0]
        const itemExt = item.ext
        that.form.rewardConfigList.push({
          type: item.type,
          content: item.content,
          detailType: item.clickType,
          quantity: item.quantity,
          cover: itemExt.selectVal.cover,
          sourceUrl: itemExt.selectVal.sourceUrl
        })
        that.form.tmpConfigList.splice(index, 1)
      })
    },
    handleClose() {
      this.$emit('close')
    },
    loadSearchUser() {
      this.searchDisabled = true
    },
    searchUserSuccess(res) {
      this.searchDisabled = false
      this.userInfo = res
      if (!res) {
        return
      }
      this.form.userId = res.id
    },
    searchUserFail() {
      this.searchDisabled = false
      this.form.userId = ''
    }
  }
}
</script>
<style scoped lang="scss">
.form-edit {
  text-align: center;
}
</style>
