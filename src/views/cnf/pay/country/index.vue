<template>
  <div class="app-container">

    <div class="filter-container">
      <el-select
        v-model="listQuery.shelf"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index ) in shelfStatus"
          :key="index"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="createCountry"
      >
        添加
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        刷新
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column width="200" label="国家信息" align="center">
        <template slot-scope="scope">
          <el-image
            style="width: 45px; height: 30px"
            :src="scope.row.country.nationalFlag"
            :preview-src-list="[scope.row.country.nationalFlag]"
          >
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline" />
            </div>
          </el-image>
        </template>
      </el-table-column>
      <el-table-column width="200" label="国家名称" prop="country.aliasName" align="center" />
      <el-table-column label="国家货币" prop="currency" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="clickInputCurrency(scope.row)">
            <span v-if="!scope.row.currency">点击输入货币</span>
            <span v-else>{{ scope.row.currency }}</span>
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="美元概率(?=$)" prop="usdExchangeRate" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="clickUpdateRate(scope.row)">
            <span v-if="!scope.row.usdExchangeRate">点击入汇率</span>
            <span v-else>{{ scope.row.usdExchangeRate }}</span>
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="展示权重" prop="sort" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="clickInputUpdateSort(scope.row)">
            <span v-if="!scope.row.sort">点击输入权重</span>
            <span v-else>{{ scope.row.sort }}</span>
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="上/下架" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.shelf"
            :active-value="true"
            :inactive-value="false"
            @change="handleSwitchChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="100">
        <template slot-scope="scope">
          <el-button type="text" @click="clickAssociateChannel(scope.row)">关联渠道</el-button>
          <!-- <el-button type="text" @click="clickAssociateChannelDetails(scope.row)">详情</el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <associated-channels
      v-if="formAssociatedChannelVisible"
      :row="selectedRow"
      @close="formAssociatedChannelVisible=false;selectedRow={}"
    />
    <country-select
      v-if="countrySelectVisible"
      @close="countrySelectClose"
    />
  </div>
</template>
<script>
import { pagePayOpenCounty, updatePayOpenCountyShelf, updatePayOpenCountryExchangeRate, updatePayOpenCountryCurrent, updatePayOpenCountrySort } from '@/api/sys-pay'
import Pagination from '@/components/Pagination'
import AssociatedChannels from './associated-channels'
import CountrySelect from './country-select'
export default {
  name: 'CountryCodeInfo',
  components: { Pagination, AssociatedChannels, CountrySelect },
  data() {
    return {
      shelfStatus: [
        { value: true, name: '上架' },
        { value: false, name: '下架' }
      ],
      countrySelectVisible: false,
      formAssociatedChannelVisible: false,
      selectedRow: {},
      //
      formVisible: false,
      textOptTitle: '',
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 30,
        shelf: true,
        countryId: '',
        id: ''
      },
      listLoading: true,
      uploadLoading: false
    }
  },
  created() {
    this.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      that.listLoading = true
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      pagePayOpenCounty(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    createCountry() {
      this.countrySelectVisible = true
    },
    countrySelectClose() {
      this.countrySelectVisible = false
      this.renderData()
    },
    //
    handleClose() {
      // 去除校验
      this.$refs.form.clearValidate()
      this.formVisible = false
    },
    clickInputUpdateSort(row) {
      const that = this
      that.$prompt('请输入新的权重数', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^\d{1,5}$/,
        inputErrorMessage: '范围0~99999小数最多两位',
        closeOnClickModal: false
      }).then(({ value }) => {
        updatePayOpenCountrySort(row.id, value).then(res => {
          that.$opsMessage.success()
          that.renderData()
        }).catch(er => {
          console.error(er)
          that.$opsMessage.fail()
        })
      }).catch(() => {
      })
    },
    clickInputCurrency(row) {
      const that = this
      that.$prompt('请输入新的国家货币Code', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false
      }).then(({ value }) => {
        updatePayOpenCountryCurrent(row.id, value).then(res => {
          that.$opsMessage.success()
          that.renderData()
        }).catch(er => {
          console.error(er)
          that.$opsMessage.fail()
        })
      }).catch(() => {
      })
    },
    clickUpdateRate(row) {
      const that = this
      that.$prompt('请输入新的美元汇率', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^\d{1,5}(\.\d{0,8})?$/,
        inputErrorMessage: '范围0~99999小数最多八位',
        closeOnClickModal: false
      }).then(({ value }) => {
        updatePayOpenCountryExchangeRate(row.id, value).then(res => {
          that.$opsMessage.success()
          that.renderData()
        }).catch(er => {
          console.error(er)
          that.$opsMessage.fail()
        })
      }).catch(() => {
      })
    },
    clickAssociateChannel(row) {
      const that = this
      that.selectedRow = row
      that.formAssociatedChannelVisible = true
    },
    clickAssociateChannelDetails() {

    },
    handleSwitchChange(row) {
      updatePayOpenCountyShelf(row.id, row.shelf)
        .then((res) => {})
        .catch(er => {
          row.shelf = !row.shelf
        })
    }
  }
}
</script>
<style scoped lang="scss">
  .bottom {
    color: #666;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 25px;
    .line {
      display: flex;
      >div {
        width: 100%;
      }
    }
  }

</style>
