<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="changeSysOrigin"
      >
        <el-option
          v-for="(item, index) in permissionsSysOriginPlatforms"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.region"
        v-loading="loading"
        placeholder="区域"
        style="width:120px;"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in regions"
          :key="index"
          :label="item.regionName"
          :value="item.id"
        />
      </el-select>
      <el-select
        v-model="listQuery.showcase"
        placeholder="状态"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in productConfigShowcase"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-button
        :loading="loading"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>

      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        添加
      </el-button>
    </div>
    <el-table
      v-loading="loading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="sysOrigin" label="系统" align="center">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column prop="regionName" label="区域" align="center" width="250" />
      <el-table-column prop="relationDesc" label="关联数据" align="center" />
      <el-table-column prop="showcase" label="状态" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.showcase ? '已上架' : '已下架' }}</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center">
        <template slot-scope="scope">
          <div>
            <el-button type="text" @click.native="handlUpdate(scope.row)">修改</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
    <el-dialog
      :title="textOptTitle"
      :visible.sync="formVisible"
      :before-close="handleClose"
      :close-on-click-modal="false"
      width="40%"
    >
      <div v-loading="submitLoading">
        <el-form ref="form" :model="form" label-width="110px" :rules="rules">
          <el-form-item prop="region" label="区域">
            <el-select v-model="form.regionId" placeholder="请选择" style="width:100%;">
              <el-option
                v-for="(item, index) in regions"
                :key="index"
                :label="item.regionName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-if="form.id" prop="relationIds" label="开通国家">
            <el-select v-model="form.relationIds" placeholder="请选择" style="width:100%;">
              <el-option
                v-for="(item, index) in countryList"
                :key="index"
                :label="item.country.countryName"
                :value="item.id"
              >
                <span style="float: left;">
                  <img :src="item.country.nationalFlag" style="width: 28px; height: 18px;margin: 0px 2px;">
                </span>
                <span style="float: left;margin-left:10px">{{ item.country.countryName }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="!form.id" prop="relationIds" label="开通国家">
            <el-select v-model="form.relationIds" multiple placeholder="请选择" style="width:100%;">
              <el-option
                v-for="(item, index) in countryList"
                :key="index"
                :label="item.country.countryName"
                :value="item.id"
              >
                <span style="float: left;">
                  <img :src="item.country.nationalFlag" style="width: 28px; height: 18px;margin: 0px 2px;">
                </span>
                <span style="float: left;margin-left:10px">{{ item.country.countryName }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="showcase">
            <el-select
              v-model="form.showcase"
              placeholder="状态"
              class="filter-item"
              style="width:100%;"
            >
              <el-option
                v-for="item in productConfigShowcase"
                :key="item.value"
                :value="item.value"
                :label="item.name"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm()">保存</el-button>
            <el-button @click="handleClose()">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { productConfigShowcase } from '@/constant/type'
import { listPayOpenCountry } from '@/api/sys-pay'
import Pagination from '@/components/Pagination'
import { regionRelationTable, updateRegionRelation, addRegionRelation, regionConfigTable } from '@/api/sys'
import { mapGetters } from 'vuex'

function getFormData() {
  return {
    id: '',
    sysOrigin: '',
    regionId: '',
    groupType: 'OPEN_PAY_COUNTRY',
    relationIds: [],
    showcase: true
  }
}
export default {
  name: 'RegionRelation',
  components: { Pagination },
  data() {
    return {
      productConfigShowcase,
      loadingCountry: false,
      thisRow: {},
      formVisible: false,
      textOptTitle: '',
      regions: [],
      countryList: [],
      form: getFormData(),
      submitLoading: false,
      loading: false,
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        sysOrigin: 'HALAR',
        groupType: 'OPEN_PAY_COUNTRY',
        relationId: '',
        region: '',
        showcase: true
      },
      rules: {
        relationIds: [
          { required: true, message: '请选择关联数据', trigger: 'blur' }
        ],
        regionId: [
          { required: true, message: '请选择区域', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    that.listRegion()
    that.loadOpenCountry()
    that.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      that.loading = true
      regionRelationTable(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.loading = false
      }).catch(er => {
        that.loading = false
        console.error(er)
      })
    },
    loadOpenCountry() {
      const that = this
      that.loading = true
      listPayOpenCountry().then(res => {
        that.loading = false
        that.countryList = res.body || []
      }).catch(er => {
        that.loading = false
        console.error(er)
      })
    },
    listRegion() {
      const that = this
      that.loading = true
      regionConfigTable({ 'sysOrigin': that.listQuery.sysOrigin }).then(res => {
        that.regions = res.body || []
        that.loading = false
      }).catch(er => {
        that.loading = false
      })
    },
    handleSearch() {
      this.loading = false
      this.renderData(true)
    },
    handleCreate() {
      this.textOptTitle = '新增内购产品'
      this.formVisible = true
      this.form.relationIds = []
      this.form = getFormData()
    },
    handlUpdate(row) {
      this.textOptTitle = '修改内购产品'
      this.formVisible = true
      this.form = Object.assign(this.form, row)
      this.form.relationIds = row.relationId
    },
    handleClose() {
      this.formVisible = false
      this.resetForm()
    },
    resetForm() {
      this.form = getFormData()
    },
    changeSysOrigin() {
      this.listRegion()
      this.handleSearch()
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (valid) {
          that.submitLoading = true
          that.form.sysOrigin = that.listQuery.sysOrigin
          if (!that.form.id) {
            addRegionRelation(that.form).then(res => {
              that.$opsMessage.success()
              that.submitLoading = false
              that.formVisible = false
              that.resetForm()
              that.renderData(true)
            }).catch(er => {
              that.submitLoading = false
              console.error(er)
              this.$emit('fail')
            })
            return
          }
          that.form.relationIds = [that.form.relationIds]
          updateRegionRelation(that.form).then(res => {
            that.$opsMessage.success()
            that.submitLoading = false
            that.formVisible = false
            that.resetForm()
            that.renderData(false)
          }).catch(er => {
            that.submitLoading = false
            console.error(er)
            this.$emit('fail')
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.this-level {
  padding-bottom: 30px;
  display: flex;
  > div {
    width: 150px;
  }
}

</style>
