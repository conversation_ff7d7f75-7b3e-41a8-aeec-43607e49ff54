<template>
  <div class="associated-channel">
    <el-dialog
      title="关联渠道"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="70%"
      top="50px"
    >
      <div class="content">
        <div class="operation-but">
          <transfer-channel
            :selected-channels="channelCodes"
            :factory-code="factoryCode"
            @hide="hideTransferChannel"
            @change="handleChange"
          />
        </div>
        <el-table
          v-loading="channelLoading"
          :data="channels"
          fit
          highlight-current-row
        >
          <el-table-column label="渠道Code" prop="payChannel.channelCode" min-width="100" align="center" show-overflow-tooltip />
          <el-table-column label="渠道名称" prop="payChannel.channelName" min-width="100" align="center" show-overflow-tooltip />
          <el-table-column label="渠道类型" prop="payChannel.channelType" min-width="100" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ payChannelGroupNames[scope.row.payChannel.channelType].name }}
            </template>
          </el-table-column>
          <el-table-column label="渠道icon" align="center" show-overflow-tooltip min-width="100">
            <template slot-scope="scope">
              <img :src="scope.row.payChannel.channelIcon" alt="icon" width="50">
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { listFactoryAssociatedChannels, updateFactoryAssociatedChannels, addPayFactoryAssociatedChannels, delPayFactoryAssociatedChannels } from '@/api/sys-pay'
import { payChannelGroupNames } from '@/constant/type'
import TransferChannel from './../comp/transfer-channel'
export default {
  components: { TransferChannel },
  props: {
    factoryCode: {
      type: String,
      require: true,
      default: () => ''
    }
  },
  data() {
    return {
      payChannelGroupNames,
      channels: [],
      channelCodes: [],
      channelLoading: false
    }
  },
  watch: {
    factoryCode: {
      handler(newVal) {
        if (!newVal) {
          return
        }
        this.renderData()
      },
      immediate: true
    }
  },
  created() {
    this.renderData()
  },
  methods: {
    handleChange(value, direction, movedKeys) {
      const that = this
      if (direction === 'right') {
        addPayFactoryAssociatedChannels(movedKeys.map(item => {
          return {
            factoryCode: that.factoryCode,
            channelCode: item,
            factoryChannel: ''
          }
        })).then(res => {
          that.$opsMessage.success()
        }).catch(er => {
          console.error(er)
        })
        return
      }
      delPayFactoryAssociatedChannels({
        channelCodes: movedKeys,
        factoryCode: that.factoryCode
      }).then(res => {
        that.$opsMessage.success()
      }).catch(er => {
        console.error(er)
      })
    },
    handleClose() {
      this.$emit('close')
    },
    // @Deprecated
    changeFactoryChannelCode(row) {
      updateFactoryAssociatedChannels({
        id: row.id,
        factoryCode: this.factoryCode,
        channelCode: row.channelCode,
        factoryChannel: row.factoryChannel
      })
    },
    renderData() {
      this.loadListFactoryAssociatedChannels(this.factoryCode)
    },
    loadListFactoryAssociatedChannels(factoryCode) {
      const that = this
      that.channelLoading = true
      listFactoryAssociatedChannels(factoryCode).then(res => {
        that.channelLoading = false
        that.channels = res.body || []
        that.channelCodes = that.channels.map(item => item.channelCode)
      }).catch(er => {
        console.error(er)
        that.channelLoading = false
      })
    },
    hideTransferChannel(isChange) {
      if (isChange) {
        this.renderData()
      }
    }
  }
}
</script>

<style  lang="scss">
.associated-channel {
  .el-transfer-panel {
    width: 300px;
    height: 600px;
 }
 .el-transfer-panel__list {
    height: 600px;
 }
}
</style>
<style scoped lang="scss">
.associated-channel {
  .content {
    max-height: 70vh;
    overflow: auto;
    .operation-but {
      text-align: right;
      padding: 0px 20px;
      position: absolute;
      width: 100%;
      top: 50px;
      left: 0px;
      right: 0px;
      z-index: 999;
    }
  }
}

</style>
