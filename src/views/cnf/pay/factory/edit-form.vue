<template>
  <div class="pay-channel-edit-form">
    <el-dialog
      :title="title"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="550px"
      top="50px"
    >
      <el-form
        ref="form"
        v-loading="submitLoading"
        style="width: 400px; margin-left:50px;"
        :model="form"
        :rules="rules"
        label-width="60px"
      >

        <el-form-item label="Code" prop="factoryCode">
          <el-input v-model.trim="form.factoryCode" :disabled="isEdit" type="text" placeholder="请输入渠道Code" />
        </el-form-item>
        <el-form-item label="名称" prop="factoryName">
          <el-input v-model.trim="form.factoryName" type="text" placeholder="请输入渠道名称" />
        </el-form-item>
        <el-form-item label="图标" prop="factoryIcon">
          <el-upload
            :disabled="iconUploadLoading"
            :file-list="iconFileList"
            :class="{'upload-but-hide': formFactoryIconNotEmpty}"
            action=""
            list-type="picture-card"
            :http-request="iconFileUpload"
            :show-file-list="formFactoryIconNotEmpty"
            :on-remove="handleIconFileRemove"
            accept="image/*"
          >
            <i slot="default" v-loading="iconUploadLoading" class="el-icon-plus" />
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose()">取消</el-button>
        <el-button type="primary" @click="submitForm()">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { deepClone, getElementUiUploadFile } from '@/utils'
import { payChannelGroups } from '@/constant/type'
import { addPayFactory, updatePayFactory } from '@/api/sys-pay'
export default {
  name: 'PayApplFactoryEditForm',
  props: {
    row: {
      type: Object,
      required: true
    }
  },
  data() {
    var commonRequiredParams = [{ required: true, message: '必填参数不可为空', trigger: 'blur' }]
    return {
      payChannelGroups,
      form: {
        id: '',
        factoryCode: '',
        factoryName: '',
        factoryIcon: ''
      },
      rules: {
        factoryCode: commonRequiredParams,
        factoryName: commonRequiredParams,
        factoryIcon: commonRequiredParams
      },
      submitLoading: false,
      iconUploadLoading: false,
      iconFileList: []
    }
  },
  computed: {
    isEdit() {
      return !!(this.form && this.form.id)
    },
    title() {
      return this.form && this.form.id ? '修改厂商' : '添加厂商'
    },
    formFactoryIconNotEmpty() {
      return !!(this.form.factoryIcon && this.form.factoryIcon.length > 0)
    }
  },
  watch: {
    row: {
      handler(newVal) {
        if (newVal && newVal.id) {
          this.form = deepClone(newVal)
          this.iconFileList = getElementUiUploadFile(newVal.factoryIcon)
        }
      },
      immediate: true
    }
  },
  created() {
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('submit error!')
          return
        }
        that.submitLoading = true
        if (that.isEdit) {
          updatePayFactory(that.form).then(res => {
            that.submitLoading = false
            that.handleClose()
            that.$emit('success', { event: 'UPDATE', form: that.form })
          }).catch(er => {
            that.submitLoading = false
            console.error(er)
            that.$emit('fail', { event: 'UPDATE', form: that.form })
          })
          return
        }

        addPayFactory(that.form).then(res => {
          that.submitLoading = false
          that.handleClose()
          that.$emit('success', { event: 'ADD', form: that.form })
        }).catch(er => {
          that.submitLoading = false
          console.error(er)
          that.$emit('fail', { event: 'ADD', form: that.form })
        })
      })
    },
    handleIconFileRemove(file, fileList) {
      this.form.factoryIcon = ''
      this.iconUploadLoading = false
    },
    iconFileUpload(file) {
      const that = this
      that.iconUploadLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.back).then(res => {
        that.iconUploadLoading = false
        that.form.factoryIcon = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.iconUploadLoading = false
      })
    }
  }
}
</script>
