<template>
  <div class="app-container">

    <div class="filter-container">
      <el-select
        v-model="listQuery.channelType"
        placeholder="渠道类型"
        class="filter-item"
        style="width: 200px;"
        clearable
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in payChannelGroups"
          :key="index"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-input
        v-model.trim="listQuery.channelCode"
        placeholder="渠道Code"
        style="width: 200px;"
        class="filter-item"
      />
      <el-input
        v-model.trim="listQuery.channelName"
        placeholder="渠道名称"
        style="width: 200px;"
        class="filter-item"
      />
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="createChannel"
      >
        添加
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="Code" prop="channelCode" align="center" />
      <el-table-column label="名称" prop="channelName" align="center" />
      <el-table-column label="Icon" prop="channelIcon" align="center">
        <template slot-scope="scope">
          <el-image
            style="width: 45px; height: 45px"
            :src="scope.row.channelIcon"
            :preview-src-list="[scope.row.channelIcon]"
          />
        </template>
      </el-table-column>
      <el-table-column label="类型" prop="channelType" align="center">
        <template>
          <template slot-scope="scope">
            {{ payChannelGroupNames[scope.row.channelType].name }}
          </template>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="80">
        <template slot-scope="scope">
          <el-button type="text" @click="clickEdit(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <edit-form
      v-if="formEditVisible"
      :row="selectedRow"
      @close="formEditVisible=false;selectedRow=null"
      @success="formEditSuccess"
    />
  </div>
</template>
<script>
import { pagePayChannel } from '@/api/sys-pay'
import { payChannelGroups, payChannelGroupNames } from '@/constant/type'
import EditForm from './edit-form'
import Pagination from '@/components/Pagination'
export default {
  components: { Pagination, EditForm },
  data() {
    return {
      payChannelGroupNames,
      payChannelGroups,
      formEditVisible: false,
      selectedRow: {},
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 30,
        channelType: '',
        channelCode: '',
        channelName: ''
      },
      listLoading: true,
      uploadLoading: false
    }
  },
  created() {
    this.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      that.listLoading = true
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      pagePayChannel(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    createChannel() {
      const that = this
      that.selectedRow = {}
      that.formEditVisible = true
    },
    clickEdit(row) {
      const that = this
      that.selectedRow = row
      that.formEditVisible = true
    },
    formEditSuccess(res) {
      this.selectedRow = {}
      this.formEditVisible = false
      this.renderData(res.event === 'ADD')
    }
  }
}
</script>
<style scoped lang="scss">
  .bottom {
    color: #666;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 25px;
    .line {
      display: flex;
      >div {
        width: 100%;
      }
    }
  }

</style>
