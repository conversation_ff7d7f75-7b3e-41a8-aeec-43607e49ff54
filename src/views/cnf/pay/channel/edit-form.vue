<template>
  <div class="pay-channel-edit-form">
    <el-dialog
      :title="title"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="550px"
      top="50px"
    >
      <el-form
        ref="form"
        v-loading="submitLoading"
        style="width: 400px; margin-left:50px;"
        :model="form"
        :rules="rules"
        label-width="60px"
      >
        <el-form-item label="类型" prop="channelType">
          <el-select
            v-model="form.channelType"
            placeholder="渠道类型"
            class="filter-item"
            style="width:100%;"
          >
            <el-option
              v-for="(item, index) in payChannelGroups"
              :key="index"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Code" prop="channelCode">
          <el-input v-model.trim="form.channelCode" :disabled="isEdit" type="text" placeholder="请输入渠道Code" />
        </el-form-item>
        <el-form-item label="名称" prop="channelName">
          <el-input v-model.trim="form.channelName" type="text" placeholder="请输入渠道名称" />
        </el-form-item>
        <el-form-item label="图标" prop="channelIcon">
          <el-upload
            :disabled="iconUploadLoading"
            :file-list="iconFileList"
            :class="{'upload-but-hide': formChannelIconNotEmpty}"
            action=""
            list-type="picture-card"
            :http-request="iconFileUpload"
            :show-file-list="formChannelIconNotEmpty"
            :on-remove="handleIconFileRemove"
            accept="image/*"
          >
            <i slot="default" v-loading="iconUploadLoading" class="el-icon-plus" />
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose()">取消</el-button>
        <el-button type="primary" @click="submitForm()">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { deepClone, getElementUiUploadFile } from '@/utils'
import { payChannelGroups } from '@/constant/type'
import { addPayChannel, updatePayChannel } from '@/api/sys-pay'
export default {
  name: 'PayApplChannelEditForm',
  props: {
    row: {
      type: Object,
      required: true
    }
  },
  data() {
    var commonRequiredParams = [{ required: true, message: '必填参数不可为空', trigger: 'blur' }]
    return {
      payChannelGroups,
      form: {
        id: '',
        channelCode: '',
        channelName: '',
        channelIcon: '',
        channelType: ''
      },
      rules: {
        channelCode: commonRequiredParams,
        channelName: commonRequiredParams,
        channelIcon: commonRequiredParams,
        channelType: commonRequiredParams
      },
      submitLoading: false,
      iconUploadLoading: false,
      iconFileList: []
    }
  },
  computed: {
    isEdit() {
      return !!(this.form && this.form.id)
    },
    title() {
      return this.form && this.form.id ? '修改渠道' : '添加渠道'
    },
    formChannelIconNotEmpty() {
      return !!(this.form.channelIcon && this.form.channelIcon.length > 0)
    }
  },
  watch: {
    row: {
      handler(newVal) {
        if (newVal && newVal.id) {
          this.form = deepClone(newVal)
          this.iconFileList = getElementUiUploadFile(newVal.channelIcon)
        }
      },
      immediate: true
    }
  },
  created() {
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('submit error!')
          return
        }
        that.submitLoading = true
        if (that.isEdit) {
          updatePayChannel(that.form).then(res => {
            that.submitLoading = false
            that.handleClose()
            that.$emit('success', { event: 'UPDATE', form: that.form })
          }).catch(er => {
            that.submitLoading = false
            console.error(er)
            that.$emit('fail', { event: 'UPDATE', form: that.form })
          })
          return
        }

        addPayChannel(that.form).then(res => {
          that.submitLoading = false
          that.handleClose()
          that.$emit('success', { event: 'ADD', form: that.form })
        }).catch(er => {
          that.submitLoading = false
          console.error(er)
          that.$emit('fail', { event: 'ADD', form: that.form })
        })
      })
    },
    handleIconFileRemove(file, fileList) {
      this.form.channelIcon = ''
      this.iconUploadLoading = false
    },
    iconFileUpload(file) {
      const that = this
      that.iconUploadLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.back).then(res => {
        that.iconUploadLoading = false
        that.form.channelIcon = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.iconUploadLoading = false
      })
    }
  }
}
</script>
