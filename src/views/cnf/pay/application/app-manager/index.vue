<template>
  <div class="application">
    <div v-loading="listLoading" class="app-card flex-l flex-wrap">
      <div v-for="(item, index) in list" :key="index" class="app-block" @click="clickApplication(item)">
        <div class="app-item flex-c">
          <div class="app-icon">
            <sys-origin-icon :icon="item.appCode" :desc="item.appCode" size="150px" />
          </div>
          <div class="content">
            <div class="head flex-l">
              <div class="appName nowrap-ellipsis">{{ item.appName }}</div>
              <div class="text-center" @click.stop>
                <el-dropdown>
                  <span class="el-dropdown-link">
                    操作<i class="el-icon-arrow-down el-icon--right" />
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item @click.native="copyPayLink(item)"><i class="el-icon-document-copy">支付链接</i></el-dropdown-item>
                    <el-dropdown-item @click.native="clickEdit(item)"><i class="el-icon-edit">编辑</i></el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </div>
            <div class="info">
              <div class="nowrap-ellipsis">ID:{{ item.id }}</div>
              <!-- <div class="nowrap-ellipsis">Code:{{ item.appCode }}</div> -->
              <div class="nowrap-ellipsis">
                <span @click.stop><el-link v-if="item.androidLink" :href="item.androidLink" target="_blank"> <svg-icon icon-class="android" />Android</el-link></span>、
                <span @click.stop> <el-link v-if="item.iosLink" :href="item.iosLink" target="_blank"><svg-icon icon-class="ios" />iOS</el-link></span>
              </div>
            </div>
            <div class="bottom nowrap-ellipsis">
              创建时间: {{ item.createTime | dateFormat }}
            </div>
          </div>
        </div>
      </div>

      <div class="app-block">
        <div class="app-item flex-c app-create" @click="selectedRow={};appEditFormVisible=true;">
          <i class="el-icon-circle-plus" />  创建新的应用
        </div>
      </div>
    </div>

    <edit-form
      v-if="appEditFormVisible"
      :row="selectedRow"
      @close="selectedRow={};appEditFormVisible=false;"
      @success="renderData"
    />
  </div>
</template>
<script>
import { listPayApplication } from '@/api/sys-pay'
import { copyText } from '@/utils'
import EditForm from './edit-form'
import { dateFormat } from '@/filters'
export default {
  name: 'AppManager',
  components: { EditForm },
  data() {
    return {
      appEditFormVisible: false,
      selectedRow: {},
      list: [],
      listQuery: {
        cursor: 1,
        limit: 30,
        countryName: '',
        aliasName: ''
      },
      listLoading: true
    }
  },
  created() {
    this.renderData()
  },
  methods: {
    clickApplication(item) {
      this.$emit('clickItem', item)
    },
    renderData(isClean) {
      const that = this
      that.listLoading = true
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      listPayApplication(that.listQuery).then(res => {
        const { body } = res
        that.list = body || []
        that.listLoading = false
        that.$emit('onLoad', that.list)
      })
    },

    copyPayLink(item) {
      const that = this
      copyText(`https://web.sugartimeapp.com/#/recharge/${item.id}`)
        .then(res => {
          that.$opsMessage.success()
        })
        .catch(er => {
          that.$opsMessage.fail()
        })
    },
    clickEdit(item) {
      const that = this
      that.appEditFormVisible = true
      that.selectedRow = item
    }
  }
}
</script>
<style scoped lang="scss">
  .application {
    .app-card {
      .app-block {
        padding: 10px;
        .app-item {
          line-height: 30px;
          padding: 10px;
          border-radius: 10px;
          box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
          cursor: pointer;
          height: 170px;
          width: 380px;
          .app-icon {
            width: 150px;
            height: 150px;
          }

          .content {
            padding: 0px 10px;
            color: #6e6e6e;
            .head {
              .appName {
                font-size: 24px;
                font-weight: bold;
                width: 120px;
              }
            }
          }
        }
        .app-create {
          font-size: 26px;
          font-weight: bold;
          color: #409EFF;
        }
      }
    }

    .text-center{
      text-align: center;
    }
  }

</style>
