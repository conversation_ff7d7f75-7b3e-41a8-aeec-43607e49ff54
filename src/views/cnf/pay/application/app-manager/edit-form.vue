<template>
  <div class="pay-application-edit-form">
    <el-dialog
      :title="title"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="550px"
      top="50px"
    >
      <el-form
        ref="form"
        v-loading="submitLoading"
        style="width: 400px; margin-left:50px;"
        :model="form"
        :rules="rules"
      >
        <el-form-item v-if="!isEdit" label="类型" prop="appCode">
          <el-select
            v-model="form.appCode"
            placeholder="系统"
            style="width: 100%"
            class="filter-item"
            collapse-tags
            @change="changeSysOrigin"
          >
            <el-option
              v-for="item in permissionsSysOriginPlatforms"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
              <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
              <span style="float: left;margin-left:10px">{{ item.label }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="app名称" prop="appName">
          <el-input v-model.trim="form.appName" type="primary" placeholder="请输入app名称" :disabled="true" />
        </el-form-item> -->
        <el-form-item label="Android链接" prop="androidLink">
          <el-input v-model.trim="form.androidLink" type="primary" placeholder="请输入Android链接" />
        </el-form-item>
        <el-form-item label="iOS链接" prop="iosLink">
          <el-input v-model.trim="form.iosLink" type="primary" placeholder="请输入iOS链接" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose()">取消</el-button>
        <el-button type="primary" @click="submitForm()">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { deepClone, getElementUiUploadFile } from '@/utils'
import { payChannelGroups } from '@/constant/type'
import { addPayApplication, updatePayApplication } from '@/api/sys-pay'
import { mapGetters } from 'vuex'
export default {
  name: 'PayAppEditForm',
  props: {
    row: {
      type: Object,
      required: true
    }
  },
  data() {
    var commonRequiredParams = [{ required: true, message: '必填参数不可为空', trigger: 'blur' }]
    return {
      payChannelGroups,
      form: {
        id: '',
        appName: '',
        appCode: '',
        androidLink: '',
        iosLink: ''
      },
      rules: {
        appName: commonRequiredParams,
        appCode: commonRequiredParams,
        androidLink: commonRequiredParams,
        iosLink: commonRequiredParams
      },
      submitLoading: false,
      iconUploadLoading: false,
      iconFileList: []
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms']),
    isEdit() {
      return !!(this.form && this.form.id)
    },
    title() {
      return this.form && this.form.id ? '修改渠道' : '添加渠道'
    },
    formChannelIconNotEmpty() {
      return !!(this.form.channelIcon && this.form.channelIcon.length > 0)
    }
  },
  watch: {
    row: {
      handler(newVal) {
        if (newVal && newVal.id) {
          this.form = deepClone(newVal)
          this.iconFileList = getElementUiUploadFile(newVal.channelIcon)
        }
      },
      immediate: true
    }
  },
  created() {
  },
  methods: {
    changeSysOrigin(val) {
      const value = this.permissionsSysOriginPlatforms.filter(item => item.value === val)[0]
      this.form.appName = value.label
    },
    handleClose() {
      this.$emit('close')
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('submit error!')
          return
        }
        that.submitLoading = true
        if (that.isEdit) {
          updatePayApplication(that.form).then(res => {
            that.submitLoading = false
            that.handleClose()
            that.$emit('success', { event: 'UPDATE', form: that.form })
          }).catch(er => {
            that.submitLoading = false
            console.error(er)
            that.$emit('fail', { event: 'UPDATE', form: that.form })
          })
          return
        }

        addPayApplication(that.form).then(res => {
          that.submitLoading = false
          that.handleClose()
          that.$emit('success', { event: 'ADD', form: that.form })
        }).catch(er => {
          that.submitLoading = false
          console.error(er)
          that.$emit('fail', { event: 'ADD', form: that.form })
        })
      })
    },
    handleIconFileRemove(file, fileList) {
      this.form.channelIcon = ''
      this.iconUploadLoading = false
    },
    iconFileUpload(file) {
      const that = this
      that.iconUploadLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.svgasource).then(res => {
        that.iconUploadLoading = false
        that.form.channelIcon = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.iconUploadLoading = false
      })
    }
  }
}
</script>
