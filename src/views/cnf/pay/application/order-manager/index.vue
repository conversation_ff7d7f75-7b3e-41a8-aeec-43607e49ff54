<template>
  <div class="app-order">
    开发中近期期待...
    <!-- <div class="filter-container">
      <el-input
        v-model.trim="listQuery.userId"
        v-number
        placeholder="长UID"
        style="width: 200px;"
        class="filter-item"
      />
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleSearch"
      >
        添加
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="渠道Code" align="center" />
      <el-table-column label="渠道名称" align="center" />
      <el-table-column prop="open" label="渠道Icon" align="center" />
      <el-table-column prop="open" label="渠道类型" align="center" />
      <el-table-column prop="open" label="创建时间" align="center" />
      <el-table-column fixed="right" label="操作" align="center" width="150">
        <template slot-scope="scope">
          <el-button type="text" @click="clickEdit(scope.row)">编辑</el-button>
          <el-button type="text" @click="clickEdit(scope.row)">关联渠道</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    /> -->

  </div>
</template>
<script>
import { pageSysCountryCode } from '@/api/sys-dictionary'
import Pagination from '@/components/Pagination'
export default {
  name: 'CountryCodeInfo',
  components: { Pagination },
  data() {
    return {
      formVisible: false,
      textOptTitle: '',
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 30,
        countryName: '',
        aliasName: ''
      },
      listLoading: true,
      uploadLoading: false
    }
  },
  created() {
    this.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      that.listLoading = true
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      pageSysCountryCode(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    handleClose() {
      // 去除校验
      this.$refs.form.clearValidate()
      this.formVisible = false
    },
    clickEdit() {
      const that = this
      that.$prompt('请输入新的美元汇率', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^\d{1,5}(\.\d{0,2})?$/,
        inputErrorMessage: '范围0~99999小数最多两位',
        closeOnClickModal: false
      }).then(({ value }) => {
        this.$message({
          type: 'success',
          message: '你输入的是: ' + value
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消输入'
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">
  .bottom {
    color: #666;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 25px;
    .line {
      display: flex;
      >div {
        width: 100%;
      }
    }
  }

</style>
