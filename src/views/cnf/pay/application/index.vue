<template>
  <div class="app-container">
    <application-manager
      v-show="appManagerVisible"
      @clickItem="clickAppItem"
      @onLoad="onLoadApplicationManager"
    />
    <app-navigation
      v-if="navigationVisible"
      :app-info="appInfo"
      @back="appManagerVisible=true;navigationVisible=false"
    />
  </div>
</template>
<script>
import ApplicationManager from './app-manager'
import AppNavigation from './app-navigation'

import { pageSysCountryCode } from '@/api/sys-dictionary'

export default {
  name: 'CountryCodeInfo',
  components: { ApplicationManager, AppNavigation },
  data() {
    return {
      list: [],
      listQuery: {
        cursor: 1,
        limit: 30
      },
      listLoading: true,
      uploadLoading: false,
      thisClickAppliction: {},
      appManagerVisible: true,
      navigationVisible: false,
      appInfo: {},
      appList: []
    }
  },
  created() {
    this.renderData()
  },
  methods: {
    onLoadApplicationManager(values) {
      this.appList = values
    },
    clickAppItem(val) {
      this.thisClickAppliction = val
      this.appInfo = {
        appList: this.appList,
        appSelect: this.thisClickAppliction
      }
      this.appManagerVisible = false
      this.navigationVisible = true
    },
    renderData(isClean) {
      const that = this
      that.listLoading = true
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      pageSysCountryCode(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    handleClose() {
      // 去除校验
      this.$refs.form.clearValidate()
      this.formVisible = false
    },
    clickEdit() {
      const that = this
      that.$prompt('请输入新的美元汇率', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^\d{1,5}(\.\d{0,2})?$/,
        inputErrorMessage: '范围0~99999小数最多两位',
        closeOnClickModal: false
      }).then(({ value }) => {
        this.$message({
          type: 'success',
          message: '你输入的是: ' + value
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消输入'
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">

</style>
