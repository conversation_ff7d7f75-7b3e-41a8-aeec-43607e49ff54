<template>
  <div class="app-commodity">
    <div class="filter-container">
      <el-select
        v-model="listQuery.type"
        style="width: 200px"
        class="filter-item"
        @change="changeType"
      >
        <el-option v-for="(item,index) in productTypeConfs" :key="index" :label="item.name" :value="item.value" />
      </el-select>
      <el-select
        v-model="listQuery.applicationId"
        placeholder="应用"
        style="width: 200px"
        class="filter-item"
        collapse-tags
        @change="changeApplication"
      >
        <el-option
          v-for="item in appInfo.appList"
          :key="item.id"
          :label="item.appName"
          :value="item.id"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.appCode" :desc="item.appCode" /></span>
          <span style="float: left;margin-left:10px">{{ item.appName }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.payCountryId"
        v-loading="loadingCountry"
        placeholder="国家"
        style="width: 200px"
        class="filter-item"
        collapse-tags
        @change="changeCountry"
      >
        <el-option
          v-for="item in countryList"
          :key="item.id"
          :label="item.country.aliasName || item.country.enName"
          :value="item.id"
        >
          <span style="float: left;"> <img :src="item.country.nationalFlag" width="30"></span>
          <span style="float: left;margin-left:10px">{{ item.country.aliasName || item.country.enName }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.shelf"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index ) in shelfStatus"
          :key="index"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-input
        v-model.trim="listQuery.id"
        v-number
        placeholder="商品ID"
        style="width: 200px;"
        class="filter-item"
      />
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        :disabled="!listQuery.payCountryId"
        @click="createCommodity"
      >
        添加
      </el-button>

    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="ID" prop="id" align="center" />
      <el-table-column label="金币数" prop="content" align="center" />
      <el-table-column label="奖励数" prop="awardContent" align="center" />
      <el-table-column label="价格$" prop="amountUsd" align="center" />
      <el-table-column label="上/下架" prop="shelf" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.shelf"
            :active-value="true"
            :inactive-value="false"
            @change="handleSwitchChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="80">
        <template slot-scope="scope">
          <el-button type="text" @click="clickEditCommodity(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <edit-form
      v-if="appCommodityEditFormVisible"
      :row="selectedEditFrom"
      @close="appCommodityEditFormVisible=false"
      @success="editFromSuccess"
    />
  </div>
</template>
<script>
import { productTypeConfs } from '@/constant/type'
import { pagePayCommodity, listPayOpenCountry, switchShelfCommodity } from '@/api/sys-pay'
import Pagination from '@/components/Pagination'
import EditForm from './edit-form'
export default {
  name: 'AppPaycommodity',
  components: { Pagination, EditForm },
  props: {
    appInfo: {
      type: Object,
      require: true,
      default: () => {}
    }
  },
  data() {
    return {
      productTypeConfs,
      shelfStatus: [
        { value: true, name: '上架' },
        { value: false, name: '下架' }
      ],
      appCommodityEditFormVisible: false,
      selectedEditFrom: {},
      selectedCountry: {},
      selectedApp: {},
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 30,
        id: '',
        applicationId: '',
        payCountryId: '',
        shelf: true,
        type: 'GOLD'
      },
      listLoading: false,
      countryList: [],
      loadingCountry: false
    }
  },
  watch: {
    appInfo: {
      handler(newVal) {
        if (!newVal) {
          return
        }
        this.selectedApp = newVal.appSelect
        this.listQuery.applicationId = this.selectedApp.id
      },
      immediate: true
    }
  },
  created() {
    this.loadOpenCountry()
  },
  methods: {
    handleSwitchChange(row) {
      switchShelfCommodity(row.id, row.shelf)
        .then((res) => {})
        .catch(er => {
          row.shelf = !row.shelf
        })
    },
    changeType() {
      this.renderData()
    },
    changeApplication(val) {
      this.selectApp = this.appInfo.appList.filter(item => item.id === val)[0]
      this.renderData()
    },
    changeCountry(val) {
      this.selectedCountry = this.countryList.filter(item => item.id === val)[0]
      this.renderData()
    },
    loadOpenCountry() {
      const that = this
      that.loadingCountry = true
      listPayOpenCountry().then(res => {
        that.loadingCountry = false
        that.countryList = res.body || []
        this.selectedCountry = that.countryList[0] || {}
        that.listQuery.payCountryId = this.selectedCountry.id
        that.renderData()
      }).catch(er => {
        that.loadingCountry = false
        console.error(er)
      })
    },
    renderData(isClean) {
      const that = this
      that.listLoading = true
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      pagePayCommodity(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    handleClose() {
      // 去除校验
      this.$refs.form.clearValidate()
      this.formVisible = false
    },
    createCommodity() {
      this.selectedEditFrom = {
        'applicationId': this.listQuery.applicationId,
        'payCountryId': this.listQuery.payCountryId,
        'amountUsd': '',
        'selectApp': this.selectedApp,
        'country': this.getEditFromCountry()
      }
      this.appCommodityEditFormVisible = true
    },
    clickEditCommodity(row) {
      const that = this
      that.appCommodityEditFormVisible = true
      that.selectedEditFrom = Object.assign({}, row)
      that.selectedEditFrom.selectApp = this.selectedApp
      that.selectedEditFrom.country = that.getEditFromCountry()
    },
    editFromSuccess() {
      this.appCommodityEditFormVisible = false
      this.renderData()
    },
    getEditFromCountry() {
      return {
        'icon': this.selectedCountry.country.nationalFlag,
        'currency': this.selectedCountry.currency,
        'exchangeRate': this.selectedCountry.usdExchangeRate,
        'countryName': this.selectedCountry.country.aliasName || this.selectedCountry.country.enName
      }
    }
  }
}
</script>
<style scoped lang="scss">
  .bottom {
    color: #666;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 25px;
    .line {
      display: flex;
      >div {
        width: 100%;
      }
    }
  }

</style>
