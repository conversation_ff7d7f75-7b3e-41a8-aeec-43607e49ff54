<template>
  <div class="edit-form application-commodity-edit-from">
    <el-dialog
      :visible="true"
      :before-close="handleClose"
      :close-on-click-modal="false"
      width="80%"
      top="20px"
    >
      <div slot="title">
        <div class="flex-l">
          <sys-origin-icon :icon="row.selectApp.appCode" :desc="row.selectApp.appName" />
          {{ isUpdate ? '修改商品' : '创建商品' }}
        </div>
      </div>
      <div class="form-edit">
        <el-form ref="form" :model="form" :rules="formRules" label-width="100px">
          <el-form-item label="售卖国家">
            <div class="flex-l">
              <img v-if="row.country.icon" :src="row.country.icon" width="40"> {{ row.country.countryName }}
            </div>
          </el-form-item>
          <el-form-item v-if="!isUpdate" label="商品类型" prop="type">
            <el-select v-model="form.type" style="width:100%">
              <el-option v-for="(item,index) in productTypeConfs" :key="index" :label="item.name" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="金币数" prop="content">
            <el-input v-model="form.content" v-number placeholder="请输入金币数量" />
          </el-form-item>
          <el-form-item label="奖励数" prop="awardContent">
            <el-input v-model="form.awardContent" v-number placeholder="请输入奖励金币数量" />
          </el-form-item>
          <el-form-item label="售卖价格" prop="amountUsd">
            <el-input v-model.trim="form.amountUsd" placeholder="请输入商品价格" class="input-with-select" maxlength="10" @change="changeAmount">
              <template slot="append">USD</template>
            </el-input>
          </el-form-item>
          <el-form-item label="支付渠道">
            <div v-loading="associateLoading" />
            <el-collapse v-if="supportAmounts && supportAmounts.length > 0" v-model="activeNames">
              <el-collapse-item v-for="(item,index) in supportAmounts" :key="index" :name="item.channel.channelCode">
                <template slot="title">
                  <div class="flex-l">
                    <img class="channel-icon" :src="item.channel.channelIcon" alt="icon" width="30" height="30">
                    <div style="width: 100%;" class="channel-name nowrap-ellipsis">{{ item.channel.channelName }}</div>
                  </div>
                </template>
                <el-row v-for="(aItem,aIndex) in item.countryChannels" :key="aIndex" class="channel-item">
                  <el-col :md="8" class="col">
                    <div class="flex-l margin-top-10">
                      <img class="channel-icon" :src="aItem.payFactory.factoryIcon" alt="icon" width="30" height="30">
                      <div class="channel-name nowrap-ellipsis">{{ aItem.payFactory.factoryName }}</div>
                    </div>
                  </el-col>
                  <el-col :md="8" class="col" style="text-align: left;">
                    <div>售卖价格: <strong>{{ computedAmount }}</strong> {{ row.country.currency }}</div>
                    <div>售卖价格: <strong>{{ amountUsd }}</strong> USD</div>
                  </el-col>
                  <el-col :md="8" class="col" style="text-align: left;">
                    <div>
                      最小限额: <strong>{{ aItem.countryChannelDetails.computedMinLimit }}</strong> {{ row.country.currency }}、
                      最大限额: <strong>{{ aItem.countryChannelDetails.computedMaxLimit }} </strong>{{ row.country.currency }}、
                      每日限额: <strong>{{ aItem.countryChannelDetails.computedDailyLimit }} </strong>{{ row.country.currency }}
                    </div>
                    <div>
                      最小限额: <strong>{{ aItem.countryChannelDetails.factoryMinLimit }}</strong> USD、
                      最大限额: <strong>{{ aItem.countryChannelDetails.factoryMaxLimit }}</strong> USD、
                      每日限额: <strong>{{ aItem.countryChannelDetails.factoryDailyLimit }}</strong> USD
                    </div>
                  </el-col>
                </el-row>
              </el-collapse-item>
            </el-collapse>
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer">
        <el-button @click="handleClose()">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="submitForm()">保存</el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import { productTypeConfs } from '@/constant/type'
import { deepClone } from '@/utils'
import { validPositiveNumberPointTwo } from '@/utils/validate'
import { addOrUpdatePayCommodity, listCountrytSupportAmountChannels } from '@/api/sys-pay'
export default {
  props: {
    row: {
      type: Object,
      require: false,
      default: () => {}
    }
  },
  data() {
    const commonRules = [
      { required: true, message: '必填字段', trigger: 'blur' }
    ]

    return {
      productTypeConfs,
      form: {
        applicationId: '',
        payCountryId: '',
        content: '',
        awardContent: '',
        amountUsd: '',
        type: ''
      },
      formRules: {
        applicationId: commonRules,
        payCountryId: commonRules,
        content: commonRules,
        awardContent: commonRules,
        type: commonRules,
        amountUsd: {
          required: true,
          trigger: 'blur',
          validator: (rule, value, callback) => {
            if (validPositiveNumberPointTwo(value)) {
              return callback()
            }
            return callback(new Error('请输入正确的正数/小数最多两位小数点'))
          }
        }
      },
      activeNames: '',
      submitLoading: false,
      associateLoading: false,
      supportAmounts: [],
      computedAmount: '',
      amountUsd: ''
    }
  },
  computed: {
    isUpdate() {
      return this.row && this.row.id
    }
  },
  watch: {
    row: {
      handler(newVal) {
        if (!newVal) {
          return
        }
        this.form = deepClone(newVal)
        this.loadSupportAmountChannels()
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    changeAmount(val) {
      if (!val || !validPositiveNumberPointTwo(val)) {
        this.supportAmounts = []
        this.computedAmount = ''
        this.amountUsd = ''
        return
      }
      this.loadSupportAmountChannels()
    },
    loadSupportAmountChannels() {
      const that = this
      that.associateLoading = true
      listCountrytSupportAmountChannels(that.row.payCountryId, that.form.amountUsd)
        .then(res => {
          that.associateLoading = false
          const { channels, computedAmount, amountUsd } = res.body || {}
          that.supportAmounts = channels || []
          that.computedAmount = computedAmount
          that.amountUsd = amountUsd
        }).catch(er => {
          that.associateLoading = false
          console.error(er)
        })
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return
        }
        that.submitLoading = true
        addOrUpdatePayCommodity(that.form).then(res => {
          that.$opsMessage.success()
          that.submitLoading = false
          that.$emit('success')
        }).catch(er => {
          that.submitLoading = false
          that.$emit('fail')
        })
      })
    }

  }
}
</script>
<style scoped lang="scss">
.dark-theme {
  .application-commodity-edit-from  {
    .channel-item {
      background-color: #4b5055 !important;
    }
  }
}

.application-commodity-edit-from {
  .channel-item {
    background-color: #ebf0f5;
  }
}
</style>
<style scoped lang="scss">
.form-edit {
  max-height: 70vh;
  overflow: auto;
  padding: 0px 20px;
  .channel-name {
    padding: 0px 5px;
  }
  .channel-icon {
      border-radius: 50%;
  }
  .col {
      padding: 0px 10px;
      text-align: center;
  }
  .margin-top-10 {
      margin-top: 10px;
  }
  .channel-item {
      padding: 10px 0px;
      border-radius: 5px;
  }
}

</style>
