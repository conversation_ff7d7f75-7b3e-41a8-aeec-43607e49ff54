<template>
  <div class="app-navigation">
    <div class="operation">
      <el-button class="el-icon-back" size="mini" @click="clickBack">
        返回
      </el-button>
    </div>
    <el-tabs v-model="activeName">
      <el-tab-pane
        v-for="item in tables"
        :key="item.component"
        :label="item.title"
        :name="item.component"
      >
        <span slot="label"><i :class="item.icon" /> {{ item.title }}</span>
      </el-tab-pane>
      <component :is="activeName" :app-info="appInfo" />
    </el-tabs>
  </div>
</template>
<script>
import Commodity from './commodity'
import CommodityV2 from './commodity-v2'
import OrderManager from './order-manager'
export default {
  name: 'AppNavigation',
  components: { Commodity, CommodityV2, OrderManager },
  props: {
    appInfo: {
      type: Object,
      require: true,
      default: () => {}
    }
  },
  data() {
    return {
      activeName: 'Commodity',
      tables: [
        {
          title: '商品管理',
          icon: 'el-icon-shopping-bag-1',
          component: 'Commodity'
        },
        {
          title: '商品管理V2',
          icon: 'el-icon-shopping-bag-1',
          component: 'CommodityV2'
        }
        // {
        //   title: '订单管理',
        //   icon: 'el-icon-document',
        //   component: 'OrderManager'
        // }
      ]
    }
  },
  methods: {
    clickBack() {
      this.$emit('back')
    }
  }
}
</script>
