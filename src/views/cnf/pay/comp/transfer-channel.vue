<template>
  <div class="add-channels">
    <el-popover
      placement="bottom"
      title="添加渠道"
      trigger="click"
      @hide="hidePopover"
      @show="isChange=false"
    >
      <div class="content">
        <el-transfer
          v-model="sChannels"
          v-loading="channelLoading"
          style="text-align: left; display: inline-block"
          filterable
          :titles="['可选', '已选']"
          :format="{
            noChecked: '${total}',
            hasChecked: '${checked}/${total}'
          }"
          :button-texts="['删除','开通']"
          :props="{key: 'channelCode', label: 'channelName'}"
          :data="channels"
          @change="handleChange"
        >
          <div slot-scope="{ option }">
            <div class="flex-l">
              <el-image
                style="width: 30px; height: 28px"
                :src="option.channelIcon"
              >
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </el-image>
              <div>{{ option.channelName }}</div>
            </div>
          </div>
        </el-transfer>
      </div>
      <el-button slot="reference" type="text">添加渠道</el-button>
    </el-popover>
  </div>
</template>
<script>
import { listPayChannel, listFactoryAssociatedChannels } from '@/api/sys-pay'
export default {
  props: {
    factoryCode: {
      type: String,
      require: true,
      default: () => ''
    },
    selectedChannels: {
      type: Array,
      require: true,
      default: () => []
    }
  },
  data() {
    return {
      channels: [],
      channelLoading: false,
      isChange: false,
      sChannels: []
    }
  },
  watch: {
    selectedChannels: {
      handler(newVal) {
        if (!newVal) {
          return
        }
        this.sChannels = Object.assign([], newVal)
      },
      immediate: true
    }
  },
  created() {
    this.loadAll()
  },
  methods: {
    handleClose() {
      this.$$emit('close')
    },
    handleChange(value, direction, movedKeys) {
      const that = this
      if (movedKeys.length <= 0) {
        that.$opsMessage.fail('Please select an action option')
        return
      }
      that.isChange = true
      this.$emit('change', value, direction, movedKeys)
    },
    loadAll() {
      const that = this
      that.channelLoading = true
      listPayChannel().then(res => {
        that.channelLoading = false
        that.channels = res.body || []
      }).catch(er => {
        console.error(er)
        that.channelLoading = false
      })
    },
    loadListFactoryAssociatedChannels(factoryCode) {
      const that = this
      that.selectedChannelLoading = true
      listFactoryAssociatedChannels(factoryCode).then(res => {
        that.selectedChannelLoading = false
        that.selectedChannels = res.body || []
      }).catch(er => {
        console.error(er)
        that.selectedChannelLoading = false
      })
    },
    hidePopover() {
      this.$emit('hide', this.isChange)
    }
  }
}
</script>
<style scoped lang="scss">
</style>
