<template>
  <div class="bind-team-drawer">
    <el-drawer
      title="绑定BD"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div class="bind-team">

        <div class="drawer-form">
          <el-form ref="form" :model="form" :rules="formRules" label-width="80px">
            <el-form-item prop="bdUserId" label="BD">
              <account-input v-model="form.bdUserId" :sys-origin="sysOrigin" placeholder="请输入BD账号" />
            </el-form-item>
          </el-form>
        </div>
        <div class="drawer-footer">
          <el-button @click="handleClose()">关闭</el-button>
          <el-button type="primary" :disabled="submitLoading" :loading="submitLoading" @click="submitForm">保存</el-button>
        </div>
      </div>
    </el-drawer>

  </div>
</template>
<script>
import { teamBindBd } from '@/api/team'
import { deepClone } from '@/utils'
import { billCyclePolicyTypes } from '@/constant/team-type'

export default {
  name: 'BindBd',
  props: {
    row: {
      type: Object,
      require: false,
      default: () => {}
    }
  },
  data() {
    const commonRules = [
      { required: true, message: '必填字段不可为空', trigger: 'blur' }
    ]
    return {
      billCyclePolicyTypes,
      submitLoading: false,
      sysOrigin: '',
      form: {
        ownUserId: '',
        bdUserId: ''
      },
      formRules: {
        bdUserId: commonRules
      }
    }
  },
  computed: {
    textOptTitle() {
      return '绑定BD'
    }
  },
  watch: {
    row: {
      handler(newVal) {
        const _newData = deepClone(newVal)
        this.sysOrigin = _newData.sysOrigin
        this.form.ownUserId = _newData.ownUserId
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      if (this.submitLoading) {
        this.$opsMessage.warn('正在提交!')
        return
      }
      this.$emit('close')
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.submitLoading = true
        teamBindBd(that.form).then(res => {
          this.$opsMessage.success()
          that.submitLoading = false
          this.handleClose()
        }).catch(er => {
          that.submitLoading = false
        })
      })
    }
  }
}
</script>

