<template>
  <div class="team-notice-list">
    <el-drawer
      title="团队通知记录"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div v-loading="listLoading">
        <el-row v-for="(item, index) in list" :key="index">
          <el-card style="margin: 20px;">
            <div style="padding: 0px; word-wrap: break-word;">
              <div><span style="font-weight: 600;color:#1890ff">标题: </span>{{ item.title }}</div>
              <div style="margin-top: 10px;"><span style="font-weight: 600;color:#1890ff">内容: </span>{{ item.content }}
                <el-image
                  v-if="item.cover"
                  class="image"
                  :src="item.cover"
                  :preview-src-list="[item.cover]"
                />
              </div>
              <div class="bottom clearfix">
                <time class="time">发送人: {{ item.createUser.nickname }} / {{ item.createTime }}</time>
                <el-button type="text" class="button" @click.native="handleDelete(item)">删除</el-button>
              </div>
            </div>
          </el-card>
        </el-row>
      </div>

    </el-drawer>
  </div>
</template>

<script>
import { listTeamNoticeByTeamId, deleteTeamNoticeById } from '@/api/team'
export default {
  name: 'TeamNoticeList',
  props: {
    teamId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      listLoading: false,
      list: []
    }
  },
  created() {
    this.renderData()
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      listTeamNoticeByTeamId(that.teamId).then(res => {
        const { body } = res
        that.list = body
        that.listLoading = false
      })
    },
    handleDelete(row) {
      const that = this
      that.$confirm('确认删除吗？', '提示', {
        type: 'warning'
      }).then(() => {
        that.listLoading = true
        deleteTeamNoticeById(row.id).then((res) => {
          // 过滤
          that.list = that.list.filter(function(obj) {
            return obj.id !== row.id
          })
          that.$opsMessage.success()
          that.listLoading = false
        }).catch(() => {
          that.listLoading = false
        })
      }).catch(() => {
      })
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>

<style>
  .time {
    font-size: 13px;
    color: #999;
  }

  .bottom {
    margin-top: 13px;
    line-height: 12px;
  }

  .button {
    padding: 0;
    float: right;
  }

  .image {
    width: 30%;
    margin-top: 10px;
    display: block;
  }

  .clearfix:before,
  .clearfix:after {
      display: table;
      content: "";
  }

  .clearfix:after {
      clear: both
  }
</style>
