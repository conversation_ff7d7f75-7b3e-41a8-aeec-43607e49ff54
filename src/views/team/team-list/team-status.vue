<template>
  <!-- 状态变更废弃, 不保留数据; 将团队全部删除清理;-->
  <div class="team-status-drawer">
    <el-drawer
      title="状态变更"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div class="team-status">
        <div class="drawer-form">
          <el-alert
            v-if="form.status === 'CLOSE'"
            title="关闭系统将移除团队所有数据, 操作不可逆!!!! (主播工作/账单/团队信息/团队成员/BD关系等所有)"
            type="error"
            :closable="false"
          />
          <el-form ref="form" :model="form" :rules="formRules" label-width="80px" style="margin-right: 15px;">
            <el-form-item prop="status" label="状态">
              <el-radio-group v-model="form.status">
                <el-radio label="CLOSE">关闭</el-radio>
                <el-radio label="AVAILABLE">启动</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item prop="remarks" label="备注">
              <el-input
                v-model="form.remarks"
                type="textarea"
                placeholder="请输入备注"
                maxlength="100"
                show-word-limit
                resize="none"
                rows="5"
              />
            </el-form-item>
          </el-form>
        </div>
        <div class="drawer-footer">
          <el-button :disabled="submitLoading" @click="handleClose()">关闭</el-button>
          <el-button type="primary" :disabled="submitLoading" :loading="submitLoading" @click="submit">保存</el-button>
        </div>
      </div>
    </el-drawer>

  </div>
</template>
<script>
import { updateTeamStatus } from '@/api/team'

export default {
  name: 'TeamStatus',
  props: {
    teamIds: {
      type: Array,
      require: true,
      default: () => []
    },
    status: {
      type: String,
      require: true,
      default: ''
    }
  },
  data() {
    return {
      submitLoading: false,
      form: {
        teamIds: [],
        status: '',
        remarks: ''
      },
      formRules: {}
    }
  },
  computed: {
  },
  watch: {
    status: {
      handler(newVal) {
        this.form.status = newVal
      },
      immediate: true
    },
    teamIds: {
      handler(newVal) {
        this.form.teamIds = newVal
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      if (this.submitLoading) {
        this.$opsMessage.warn('正在提交!')
        return
      }
      this.$emit('close')
    },
    submit() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.submitLoading = true
        updateTeamStatus(that.form).then(res => {
          that.submitLoading = false
          this.$emit('success', that.form)
        }).catch(er => {
          that.submitLoading = false
        })
      })
    }
  }
}
</script>

