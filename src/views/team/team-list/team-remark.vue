<template>
  <div class="team-remark-drawer">
    <el-drawer
      title="备注"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div class="team-remark">
        <div class="drawer-form">
          <p v-if="!remarks || remarks.length === 0">点击下方按钮添加 ”备注“</p>
          <el-timeline v-else :reverse="true">
            <el-timeline-item
              v-for="(item, index) in remarks"
              :key="index"
              :timestamp="item.createTime | dateFormat"
              placement="top"
            >
              <div class="remark">
                <span v-if="item.createBackUserName" class="nickname">{{ item.createBackUserName }}:</span> &nbsp;{{ item.remark }}
                <i class="el-icon-delete font-danger cursor-pointer" @click="clickDelRemark(item, index)" />
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>

        <div class="drawer-footer">
          <el-button @click="handleClose()">关闭</el-button>
          <el-button type="primary" @click="clickAddRemarks">新增</el-button>
        </div>
      </div>
    </el-drawer>

    <el-dialog
      title="备注"
      :visible.sync="remarkVisible"
      width="30%"
      :before-close="handleCloseRemarkFrom"
    >
      <el-form ref="formRemarks" :model="form" :rules="formRules" label-width="0">
        <el-form-item prop="remarks">
          <el-input
            v-model="form.remarks"
            type="textarea"
            placeholder="请输入备注"
            maxlength="100"
            show-word-limit
            resize="none"
            rows="5"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button :disabled="submitLoading" @click="remarkVisible = false">取 消</el-button>
        <el-button type="primary" :disabled="submitLoading" :loading="submitLoading" @click="submitAddRemarks">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { addTeamRemarks, delTeamRemarks } from '@/api/team'
import { deepClone } from '@/utils'
export default {
  name: 'TeamRemarks',
  props: {
    profile: {
      type: Object,
      require: true,
      default: () => {}
    }
  },
  data() {
    return {
      remarkVisible: false,
      submitLoading: false,
      remarks: [],
      form: {
        teamId: '',
        remarks: ''
      },
      formRules: {
        remarks: [
          { required: true, message: '必填字段不可为空', trigger: 'blur' }
        ]
      },
      delForm: {
        teamId: '',
        remarkId: ''
      }
    }
  },
  computed: {
  },
  watch: {
    profile: {
      handler(newVal) {
        if (newVal.remarks) {
          this.form.teamId = newVal.id
          this.delForm.teamId = newVal.id
          this.remarks = deepClone(newVal.remarks)
        }
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      if (this.submitLoading) {
        this.$opsMessage.warn('正在提交!')
        return
      }
      this.$emit('close')
    },
    submitAddRemarks() {
      const that = this
      that.$refs.formRemarks.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.submitLoading = true
        addTeamRemarks(that.form).then(res => {
          that.submitLoading = false
          that.remarkVisible = false
          that.form.remarks = ''
          if (res.body) {
            that.remarks.push(res.body)
            this.$emit('addRemarks', res.body)
          }
        }).catch(er => {
          that.submitLoading = false
        })
      })
    },
    handleCloseRemarkFrom() {
      if (this.submitLoading) {
        return
      }
      this.remarkVisible = false
    },
    clickDelRemark(row, index) {
      this.remarks.splice(index, 1)
      this.$emit('revemoRemarks', index)
      this.delForm.remarkId = row.remarkId
      delTeamRemarks(this.delForm)
    },
    clickAddRemarks() {
      if (this.remarks && this.remarks.length >= 20) {
        this.$opsMessage.warn('最多添加20条备注, 清删除不必要的备注后重新添加')
        return
      }
      this.remarkVisible = true
    }
  }
}
</script>
<style scoped lang="scss">
.team-remark {
  .remark {
    width: 100%;
    overflow: auto;
    word-wrap: break-word;
    .nickname {
      font-weight: bold;
    }
  }
}
</style>

