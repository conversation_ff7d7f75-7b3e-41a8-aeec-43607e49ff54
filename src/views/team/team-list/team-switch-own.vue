<template>
  <div class="edit-team-drawer">
    <el-drawer
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div slot="title" class="flex-l">
        <sys-origin-icon :icon="profile.sysOrigin" size="18px" />&nbsp;更换团长
      </div>
      <div class="edit-team">
        <el-alert
          title="历史团队如果存在工作数据将会被清理!!! 团队未解散BD关系将继续维持"
          type="info"
          :closable="false"
          style="margin-bottom: 10px;"
        />
        <div class="drawer-form">
          <el-form ref="form" :model="form" :rules="formRules" label-width="80px">
            <el-form-item prop="ownUserId" label="代理">
              <account-input v-model="form.ownUserId" :sys-origin="profile.sysOrigin" placeholder="请输入代理ID" />
            </el-form-item>
          </el-form>
        </div>
        <div class="drawer-footer">
          <el-button :disabled="submitLoading" @click="handleClose()">取消</el-button>
          <el-button type="primary" :disabled="submitLoading" :loading="submitLoading" @click="submitForm()">保存</el-button>
        </div>
      </div>
    </el-drawer>

  </div>
</template>
<script>
import { switchTeamOwn } from '@/api/team'

export default {
  name: 'TeamEdit',
  props: {
    profile: {
      type: Object,
      require: true,
      default: () => {}
    }
  },
  data() {
    const commonRules = [
      { required: true, message: '必填字段不可为空', trigger: 'blur' }
    ]
    return {
      submitLoading: false,
      form: {
        teamId: '',
        ownUserId: ''
      },
      formRules: {
        ownUserId: commonRules
      }
    }
  },
  computed: {
  },
  watch: {
    profile: {
      handler(newVal) {
        if (newVal) {
          this.form.teamId = newVal.id
          this.form.ownUserId = ''
        }
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      if (this.submitLoading) {
        this.$opsMessage.warn('正在提交!')
        return
      }
      this.$emit('close')
    },
    contrySelectChange(id, row) {
      this.form.country.countryId = row.id
      this.form.country.countryCode = row.code
      this.form.country.countryName = row.name
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.submitLoading = true
        switchTeamOwn(that.form).then(res => {
          that.submitLoading = false
          that.$emit('success', { teamId: that.form.teamId, ownUserProfile: res.body })
          that.handleClose()
        }).catch(err => {
          that.submitLoading = false
          that.$emit('fial', err)
        })
      })
    }
  }
}
</script>

