<template>
  <div class="team-application-process-list">
    <el-drawer
      title="申请团队成员列表"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div class="filter-container" style="margin: 10px;">
        <el-select
          v-model="listQuery.status"
          placeholder="审核状态"
          style="width: 120px"
          class="filter-item"
          @change="handleSearch"
        >
          <el-option
            v-for="(item, index) in teamApplicationProcessStatusList"
            :key="index"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
        <el-select
          v-model="listQuery.reason"
          placeholder="原因"
          style="width: 120px"
          class="filter-item"
          @change="handleSearch"
        >
          <el-option
            v-for="(item, index) in teamReasons"
            :key="index"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
        <el-button
          class="filter-item"
          type="primary"
          @click="handleSearch"
        >
          查询
        </el-button>
      </div>
      <div v-loading="listLoading">
        <el-row v-for="(item, index) in list" :key="index">
          <el-card style="margin: 10px;">
            <div>
              <el-tag>申请类型: {{ item.reason == 'JOIN' ? '加入' : '退出' }}</el-tag>
              <el-tag v-if="item.status === 'WAIT'" type="warning">审核状态: 待审核</el-tag>
              <el-tag v-if="item.status === 'AGREE'" type="success">审核状态: 同意</el-tag>
              <el-tag v-if="item.status === 'REJECT'" type="danger">审核状态: 驳回</el-tag>
            </div>
            <div class="bottom clearfix">
              <el-tag v-if="item.createUserOrigin == 1 && item.createSysUser != null" type="info">创建人: <span style="color: black;">{{ item.createSysUser.nickname }}</span>  {{ item.createTime }}</el-tag>
              <el-tag v-if="item.createUserOrigin == 0 && item.createUserProfile != null" type="info" @click.native="queryUserDetails(item.createUserProfile.id)">创建人: <span style="color: black;">{{ item.createUserProfile.userNickname }}</span>  {{ item.createTime }}</el-tag>
              <el-tag v-if="item.updateUserOrigin == 1 && item.updateSysUser != null" type="info">修改人: <span style="color: black;">{{ item.updateSysUser.nickname }}</span>  {{ item.updateTime }}</el-tag>
              <el-tag v-if="item.updateUserOrigin == 0 && item.updateUserProfile != null" type="info" @click.native="queryUserDetails(item.updateUserProfile.id)">修改人: <span style="color: black;">{{ item.updateUserProfile.userNickname }}</span>  {{ item.updateTime }}</el-tag>
            </div>
            <!-- <div v-if="item.status === 'WAIT'" class="clearfix">
              <el-button type="text" @click.native="handleStatus(item.id, 'AGREE')">同意</el-button>
              <el-button type="text" @click.native="handleStatus(item.id, 'REJECT')">拒绝</el-button>
            </div> -->
          </el-card>
        </el-row>
        <div v-if="list && list.length > 0" style="text-align: center; margin-top:20px;">
          <el-button v-if="!notMore " size="mini" :disabled="listLoading" @click="clickLoadMore">加载更多</el-button>
          <span v-else>已加载全部</span>
        </div>
      </div>

      <user-deatils-drawer
        v-if="userDeatilsDrawer"
        :user-id="thatSelectedUserId"
        @close="userDeatilsDrawer=false"
      />
    </el-drawer>

  </div>
</template>

<script>
import { teamProcessTable, teamProcessChangeStatus } from '@/api/team'
import { teamApplicationProcessStatusList, teamReasons } from '@/constant/team-type'
export default {
  name: 'TeamApplicationProcess',
  props: {
    teamId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      teamApplicationProcessStatusList,
      teamReasons,
      submitLoading: false,
      list: [],
      listLoading: false,
      total: 0,
      notMore: false,
      userDeatilsDrawer: false,
      thatSelectedUserId: '',
      listQuery: {
        reason: '',
        associateId: '',
        status: '',
        lastId: ''
      }
    }
  },
  created() {
    this.listQuery.reason = this.teamReasons[0].value
    this.listQuery.status = this.teamApplicationProcessStatusList[0].value
    this.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      that.listLoading = true
      that.listQuery.associateId = that.teamId
      if (isClean === true) {
        that.list = []
        that.listQuery.lastId = ''
      }
      teamProcessTable(that.listQuery).then(res => {
        that.listLoading = false
        const { body } = res
        const list = body || []
        that.notMore = list.length <= 0
        that.list = that.list.concat(list)
        if (that.list && that.list.length > 0) {
          that.listQuery.lastId = that.list[that.list.length - 1].id
        }
      })
    },
    handleStatus(_id, _status) {
      const that = this
      that.listLoading = true
      teamProcessChangeStatus({ 'id': _id, 'status': _status }).then((res) => {
        that.listLoading = false
        that.$opsMessage.success()
        that.renderData(true)
      }).catch(() => {
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    handleClose() {
      this.$emit('close')
    },
    queryUserDetails(id) {
      this.thatSelectedUserId = id
      this.userDeatilsDrawer = true
    },
    clickLoadMore() {
      this.renderData()
    }
  }
}
</script>

<style>
  .time {
    font-size: 13px;
    color: #999;
  }

  .bottom {
    margin-top: 13px;
    line-height: 12px;
  }

  .button {
    padding: 0;
    float: right;
  }

  .image {
    width: 30%;
    margin-top: 10px;
    display: block;
  }

  .clearfix:before,
  .clearfix:after {
      display: table;
      content: "";
  }

  .clearfix:after {
      clear: both
  }
</style>
