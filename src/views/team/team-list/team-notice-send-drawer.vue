<template>
  <div class="team-notice-send">
    <el-drawer
      title="发送团队通知"
      :visible="true"
      width="25%"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <div v-loading="submitLoading">
        <el-form
          ref="dataForm"
          :rules="rules"
          :model="dataForm"
          label-position="left"
          label-width="70px"
          style="width: 450px; margin-left:50px;"
        >
          <el-form-item label="标题" prop="title">
            <el-input
              v-model.trim="dataForm.title"
              placeholder="标题"
              maxlength="60"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="内容" prop="content">
            <el-input
              v-model.trim="dataForm.content"
              placeholder="内容"
              type="textarea"
              :rows="5"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="链接">
            <el-input
              v-model.trim="dataForm.link"
              placeholder="链接"
            />
          </el-form-item>
          <el-form-item label="封面">
            <el-form-item>
              <upload-image
                v-model="dataForm.cover"
                :file-dir="$application.fileBucket.cover"
              />
            </el-form-item>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer" style="text-align: center;">
          <el-button @click="handleClose">
            取消
          </el-button>
          <el-button
            v-loading="submitLoading"
            type="primary"
            @click="handleSubmit()"
          >
            提交
          </el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { sendTeamNotice } from '@/api/team'
function getFormData() {
  return {
    teamId: '',
    title: '',
    content: '',
    cover: '',
    link: ''
  }
}
export default {
  name: 'TeamNoticeSend',
  props: {
    teamId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      submitLoading: false,
      rules: {
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入内容', trigger: 'blur' }
        ]
      },
      dataForm: {
        teamId: '',
        title: '',
        content: '',
        cover: '',
        link: ''
      }
    }
  },
  methods: {
    handleSubmit() {
      const that = this
      that.$refs.dataForm.validate(valid => {
        if (valid) {
          that.dataForm.teamId = that.teamId
          that.$confirm('确认发送通知吗？', '提示', {
            type: 'warning'
          }).then(() => {
            that.submitLoading = true
            sendTeamNotice(that.dataForm).then(res => {
              that.$opsMessage.success()
              that.submitLoading = false
              that.dataForm = getFormData()
              that.handleClose()
            }).catch(er => {
              that.submitLoading = false
              console.error(er)
            })
          }).catch(() => {
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
