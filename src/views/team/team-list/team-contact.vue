<template>
  <div class="team-contact-drawer">
    <el-drawer
      title="联系方式"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div class="team-contact">
        <div class="drawer-form">
          <p v-if="!contacts || contacts.length === 0">点击下方按钮添加 ”联系方式“</p>
          <el-timeline v-else :reverse="true">
            <el-timeline-item
              v-for="(item, index) in contacts"
              :key="index"
              :timestamp="item.createTime"
              placement="top"
            >
              <div class="contact">
                <p> {{ item.createBackUserName }} <i class="el-icon-delete font-danger cursor-pointer" @click="clickDelContact(item, index)" /></p>
                <div>
                  <p>类型: {{ contactTypeMap[item.type].name }}</p>
                  <p>联系: {{ item.contact }}</p>
                  <p v-if="item.remarks">备注: {{ item.remarks }}</p>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
        <div class="drawer-footer">
          <el-button @click="handleClose()">关闭</el-button>
          <el-button type="primary" @click="(contactVisible=true)">新增</el-button>
        </div>
      </div>
    </el-drawer>

    <el-dialog
      title="备注"
      :visible.sync="contactVisible"
      width="30%"
      :before-close="handleCloseContactFrom"
    >
      <el-form ref="formContact" :model="form" :rules="formRules" label-width="80px" style="margin-right: 15px;">
        <el-form-item prop="type" label="类型">
          <el-select v-model="form.type" placeholder="选择类型" style="width: 100%;">
            <el-option v-for="(item, index) in contactTypes" :key="index" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item prop="contact" label="联系">
          <el-input v-model="form.contact" placeholder="请输入联系方式" maxlength="100" show-word-limit />
        </el-form-item>
        <el-form-item prop="remarks" label="备注">
          <el-input
            v-model="form.remarks"
            type="textarea"
            placeholder="请输入备注"
            maxlength="100"
            show-word-limit
            resize="none"
            rows="5"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button :disabled="submitLoading" @click="contactVisible = false">取 消</el-button>
        <el-button type="primary" :disabled="submitLoading" :loading="submitLoading" @click="submitAddContact">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { addTeamContact, delTeamContact } from '@/api/team'
import { deepClone } from '@/utils'
import { contactTypes, contactTypeToMap } from '@/constant/team-type'

export default {
  name: 'CreateTeam',
  props: {
    profile: {
      type: Object,
      require: true,
      default: () => {}
    }
  },
  data() {
    return {
      contactTypes,
      contactTypeMap: contactTypeToMap(),
      contactVisible: false,
      submitLoading: false,
      contacts: [],
      form: {
        teamId: '',
        type: '',
        contact: '',
        remarks: ''
      },
      formRules: {
        type: [
          { required: true, message: '必填字段不可为空', trigger: 'blur' }
        ],
        contact: [
          { required: true, message: '必填字段不可为空', trigger: 'blur' }
        ]
      },
      delForm: {
        teamId: '',
        remarkId: ''
      }
    }
  },
  computed: {
  },
  watch: {
    profile: {
      handler(newVal) {
        if (newVal.contacts) {
          this.form.teamId = newVal.id
          this.delForm.teamId = newVal.id
          this.contacts = deepClone(newVal.contacts)
        }
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      if (this.submitLoading) {
        this.$opsMessage.warn('正在提交!')
        return
      }
      this.$emit('close')
    },
    submitAddContact() {
      const that = this
      that.$refs.formContact.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.submitLoading = true
        addTeamContact(that.form).then(res => {
          that.submitLoading = false
          that.contactVisible = false
          that.form.type = ''
          that.form.contact = ''
          that.form.remarks = ''
          if (res.body) {
            that.contacts.push(res.body)
            this.$emit('addContact', res.body)
          }
        }).catch(er => {
          that.submitLoading = false
        })
      })
    },
    handleCloseContactFrom() {
      if (this.submitLoading) {
        return
      }
      this.contactVisible = false
    },
    clickDelContact(row, index) {
      this.contacts.splice(index, 1)
      this.$emit('revemoContact', index)
      this.delForm.contactId = row.contactId
      delTeamContact(this.delForm)
    }
  }
}
</script>
<style scoped lang="scss">
.team-contact {
  .contact {
    width: 100%;
    overflow: auto;
    word-wrap: break-word;
    .nickname {
      font-weight: bold;
    }
  }
}
</style>

