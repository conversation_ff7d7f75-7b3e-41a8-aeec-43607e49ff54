<template>
  <div class="edit-team-drawer">
    <el-drawer
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div slot="title" class="flex-l">
        <sys-origin-icon :icon="profile.sysOrigin" size="18px" />&nbsp;团队资料
      </div>
      <div class="edit-team">
        <div class="drawer-form">
          <el-alert
            v-if="tmpRegion !== form.region && form.id !== ''"
            title="!!注意: 【变更代理团队区域】将清空该代理名下所有主播目标,请谨慎操作"
            type="warning"
            effect="dark"
            :closable="false"
            style="margin-bottom: 10px;"
          />
          <el-form ref="form" :model="form" :rules="formRules" label-width="80px">
            <el-form-item prop="region" label="区域">
              <select-system-region
                ref="regionSelectPolicy"
                v-model="form.region"
                :sys-origin="profile.sysOrigin"
                clearable
                placeholder="区域"
                @change="changeRegionSelectPolicy"
              />
            </el-form-item>
            <!-- <el-form-item prop="nickname" label="昵称">
              <el-input v-model="form.nickname" placeholder="请输入团队昵称" maxlength="24" show-word-limit />
            </el-form-item>

            <el-form-item label="封面">
              <el-form-item prop="cover">
                <upload-image
                  v-model="form.avatar"
                  :file-dir="$application.fileBucket.avatar"
                />
              </el-form-item>
            </el-form-item> -->

            <el-form-item label="国家" prop="country.countryId">
              <contry-select
                v-model="form.country.countryId"
                @change="contrySelectChange"
              />
            </el-form-item>

            <div style="padding: 10px 0px;">
              <el-divider content-position="left">设置信息</el-divider>
              <el-form-item prop="setting.maxMember" label="成员数量">
                <el-input v-model="form.setting.maxMember" placeholder="最大成员数量" maxlength="100" show-word-limit />
              </el-form-item>
            </div>

          </el-form>
        </div>
        <div class="drawer-footer">
          <el-button :disabled="submitLoading" @click="handleClose()">取消</el-button>
          <el-button type="primary" :disabled="submitLoading" :loading="submitLoading" @click="submitForm()">保存</el-button>
        </div>
      </div>
    </el-drawer>

  </div>
</template>
<script>
import ContrySelect from '@/components/data/ContrySelect'
import { contactTypes, billCyclePolicyTypes } from '@/constant/team-type'
import { updateTeamProfile } from '@/api/team'
import { deepClone } from '@/utils'

export default {
  name: 'TeamEdit',
  components: { ContrySelect },
  props: {
    profile: {
      type: Object,
      require: true,
      default: () => {}
    }
  },
  data() {
    const commonRules = [
      { required: true, message: '必填字段不可为空', trigger: 'blur' }
    ]
    return {
      billCyclePolicyTypes,
      contactTypes,
      submitLoading: false,
      formSettingDefault: false,
      formContactAdd: false,
      tmpRegion: '',
      form: {
        sysOrigin: '',
        region: '',
        nickname: '',
        avatar: '',
        setting: {
          maxMember: '1000'
        },
        country: {
          countryId: '',
          countryCode: '',
          countryName: ''
        }
      },
      formRules: {
        sysOrigin: commonRules,
        region: commonRules,
        nickname: commonRules,
        'country.countryId': commonRules,
        'setting.maxMember': commonRules
      },
      selecteRegion: {}
    }
  },
  computed: {
  },
  watch: {
    profile: {
      handler(newVal) {
        this.tmpRegion = ''
        const form = deepClone(newVal)
        this.tmpRegion = form.region
        if (!form.country) {
          form.country = this.form.country
        }
        if (form.country && !form.country.countryId) {
          form.country = this.form.country
        }
        this.form = form
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      if (this.submitLoading) {
        this.$opsMessage.warn('正在提交!')
        return
      }
      this.$emit('close')
    },
    contrySelectChange(id, row) {
      this.form.country.countryId = row.id
      this.form.country.countryCode = row.code
      this.form.country.countryName = row.name
    },
    changeRegionSelectPolicy(val, item) {
      this.selecteRegion = item
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }

        if (!that.form.id || this.tmpRegion === that.form.region) {
          that.submitLoading = true
          updateTeamProfile(that.form).then(res => {
            that.submitLoading = false
            that.$emit('success', Object.assign({}, that.form), that.selecteRegion)
            that.handleClose()
          }).catch(err => {
            that.submitLoading = false
            that.$emit('fial', err)
          })
          return
        }

        that.$confirm('变更代理团队区域将清空该代理名下所有主播目标,你确定继续吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          that.submitLoading = true
          updateTeamProfile(that.form).then(res => {
            that.submitLoading = false
            that.$emit('success', Object.assign({}, that.form), that.selecteRegion)
            that.handleClose()
          }).catch(err => {
            that.submitLoading = false
            that.$emit('fial', err)
          })
        }).catch(() => {
        })
        // <el-alert
        //         :v-if="tmpRegion !== form.region"
        //         title="变更代理区域将清空所有主播目标,你确定继续吗?"
        //         type="warning"
        //         effect="dark"
        //         :closable="false"
        //       />
      })
    }
  }
}
</script>

