<template>
  <el-dialog
    title="礼物流水"
    :visible="true"
    width="70%"
    :before-close="handleClose"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
      max-height="500"
    >
      <el-table-column label="发送人" align="center" min-width="200">
        <template slot-scope="scope">
          <user-table-exhibit
            :user-profile="scope.row.userProfile"
            :query-details="true"
            :show-sys-origin="true"
          />
        </template>
      </el-table-column>

      <el-table-column
        prop="giftValue.quantity"
        label="礼物数量"
        align="center"
        min-width="80"
      >
        <template slot-scope="scope">
          <div class="flex-c">
            <el-image
              style="width: 30px; height: 30px"
              :src="scope.row.giftCover"
              fit="fill"
              :preview-src-list="[scope.row.giftCover]"
            />
            x {{ scope.row.giftValue.quantity }}
          </div>
        </template>
      </el-table-column>

      <el-table-column
        prop="giftValue.userSize"
        label="接收人数"
        align="center"
        min-width="80"
      />
      <el-table-column
        prop="giftValue.actualAmount"
        label="消费金额"
        align="center"
        min-width="100"
      />
      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
        width="200"
      >
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="60">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="clickQueryDetails(scope.row)"
          >详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div
      v-if="list && list.length > 0"
      style="text-align: center; margin-top:20px;"
    >
      <el-button
        v-if="!notMore"
        size="mini"
        :disabled="listLoading"
        @click="clickLoadMore"
      >加载更多</el-button>
      <span v-else>已加载全部</span>
    </div>

    <gift-history-details-drawer
      v-if="giftHistoryDetailsDrawerVisible"
      :row="thatRow"
      @close="giftHistoryDetailsDrawerVisible = false"
    />
  </el-dialog>
</template>

<script>
import { listGiftGiveAwayRunningWater } from '@/api/gift'
import GiftHistoryDetailsDrawer from '@/components/data/GiftHistoryDetailsDrawer'
export default {
  name: 'GiftRunningWaterFialog',
  components: { GiftHistoryDetailsDrawer },
  props: {
    // SEND , ACCEPT
    type: {
      type: String,
      required: true
    },
    userId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      thatRow: {},
      searchDisabled: false,
      giftHistoryDetailsDrawerVisible: false,
      list: [],
      listQuery: {
        limit: 20,
        userId: '',
        acceptUserId: '',
        lastId: ''
      },
      listLoading: true,
      clickUserId: '',
      notMore: false
    }
  },
  watch: {
    type: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal === 'SEND') {
          this.listQuery.sendUserId = this.userId
        }
        if (newVal === 'ACCEPT') {
          this.listQuery.acceptUserId = this.userId
        }
        this.renderData()
      }
    }
  },
  methods: {
    renderData(isReset) {
      const that = this
      that.listLoading = true
      if (isReset === true) {
        that.list = []
        that.listQuery.lastId = ''
      }
      listGiftGiveAwayRunningWater(that.listQuery)
        .then(res => {
          that.listLoading = false
          const { body } = res
          const list = body || []
          that.notMore = list.length <= 0
          that.list = that.list.concat(list)
          if (that.list && that.list.length > 0) {
            that.listQuery.lastId = that.list[that.list.length - 1].id
          }
        })
        .catch(er => {
          that.listLoading = false
        })
    },
    handleSearch() {
      this.renderData(true)
    },
    loadSearchUser() {
      this.searchDisabled = true
    },
    loadSearchAcceptUser() {
      this.searchDisabled = true
    },
    searchUserSuccess(res) {
      this.searchDisabled = false
      if (!res) {
        return
      }
      this.listQuery.sendUserId = res.id
    },
    searchAcceptUserSuccess(res) {
      this.searchDisabled = false
      if (!res) {
        return
      }
      this.listQuery.acceptUserId = res.id
    },
    searchUserFail() {
      this.listQuery.userId = ''
      this.searchDisabled = false
    },
    searchAcceptUserFail() {
      this.listQuery.acceptUserId = ''
      this.searchDisabled = false
    },
    clickLoadMore() {
      this.renderData()
    },
    clickQueryDetails(row) {
      this.thatRow = row
      this.giftHistoryDetailsDrawerVisible = true
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
