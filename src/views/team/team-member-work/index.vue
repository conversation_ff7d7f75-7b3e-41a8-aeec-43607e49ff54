<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="归属系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in permissionsSysOriginPlatforms"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <el-input v-model="listQuery.billId" :sys-origin="listQuery.billId" placeholder="账单ID" />
      </div>
      <div class="filter-item">
        <account-input v-model="listQuery.ownUserId" :sys-origin="listQuery.sysOrigin" placeholder="代理ID" />
      </div>
      <div class="filter-item">
        <account-input v-model="listQuery.memberUserId" :sys-origin="listQuery.sysOrigin" placeholder="成员ID" />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="listLoading"
        @click="handleSearch"
      >
        搜索
      </el-button>

      <el-button
        v-if="(selectTableRow.length > 0)"
        class="filter-item"
        type="danger"
        icon="el-icon-delete"
        :disabled="delLoading"
        :loading="delLoading"
        @click="clickRemoveMemberBatch"
      >
        删除
      </el-button>
    </div>
    <el-table
      ref="teamTable"
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
      @selection-change="selectTableChange"
    >

      <el-table-column
        type="selection"
        width="50"
        show-overflow-tooltip
        :selectable="tableSelectable"
      />
      <el-table-column type="index" width="50" label="No" />
      <el-table-column label="代理" align="left" show-overflow-tooltip min-width="220">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.ownUserProfile" :query-details="true" />
        </template>
      </el-table-column>

      <el-table-column label="成员" align="center" show-overflow-tooltip min-width="220">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.memberProfile" :query-details="true" :show-sys-origin="true" />
        </template>
      </el-table-column>

      <el-table-column prop="salarySettlementMode" label="工资模式" show-overflow-tooltip min-width="50" />

      <el-table-column label="周期" align="center" show-overflow-tooltip min-width="220">
        <template slot-scope="scope">
          {{ scope.row.billBelong }} <el-tag size="mini" :type="teamBillStatusMap[scope.row.billStatus].tag">{{ teamBillStatusMap[scope.row.billStatus].name }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="时长" align="left" show-overflow-tooltip min-width="100">
        <template slot-scope="scope">
          <div>我的: {{ scope.row.target.ownOnlineTime || 0 }}</div>
          <div>其他: {{ scope.row.target.otherOnlineTime || 0 }}</div>
        </template>
      </el-table-column>
      <el-table-column label="有效天" align="left" show-overflow-tooltip min-width="80">
        <template slot-scope="scope">
          <div>{{ scope.row.target.effectiveDay || 0 }}</div>
        </template>
      </el-table-column>
      <el-table-column label="粉丝" align="left" show-overflow-tooltip min-width="100">
        <template slot-scope="scope">
          <!-- <div>
            <el-link @click="queryRoomGift(scope.row)"><a :title="scope.row.target.roomValue">房间: {{ scope.row.target.roomValue || 0 }} </a></el-link>
          </div>
          <div>粉丝: {{ scope.row.memberFans || 0 }}</div> -->
          <div>{{ scope.row.memberFans || 0 }}</div>
        </template>
      </el-table-column>
      <el-table-column label="礼物" align="left" show-overflow-tooltip min-width="100">
        <template slot-scope="scope">
          <div>
            <el-link @click="queryGiftRunningWater(scope.row,'ACCEPT')">
              <a :title="scope.row.target.acceptGiftValue">
                收<el-tag v-if="scope.row.target.settleRes" type="info" size="mini">Lv.{{ scope.row.target.settleRes.level }}</el-tag>:
                {{ scope.row.target.acceptGiftValue || 0 }}
              </a>
            </el-link>
          </div>
          <div>
            <el-link @click="queryGiftRunningWater(scope.row,'SEND')"><a :title="scope.row.target.giveGiftValue">送: {{ scope.row.target.giveGiftValue || 0 }} </a></el-link>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="工资$" align="left" show-overflow-tooltip min-width="100">
        <template slot-scope="scope">
          <div v-if="scope.row.target.settleRes && scope.row.target.settleRes.policyType != 'SALARY_DIAMOND'">
            <div>成员: {{ scope.row.target.settleRes.memberSalary }}</div>
            <div>代理: {{ scope.row.target.settleRes.ownSalary }}</div>
          </div>
          <div v-else>
            <div>成员: ?</div>
            <div>代理: ?</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="工资钻石" align="left" show-overflow-tooltip min-width="120">
        <template slot-scope="scope">
          <div v-if="scope.row.target.settleRes && scope.row.target.settleRes.policyType === 'SALARY_DIAMOND'">
            <div>成员: {{ scope.row.target.settleRes.memberSalary }}</div>
            <div>代理: {{ scope.row.target.settleRes.ownSalary }}</div>
            <div>成员剩余: {{ scope.row.target.settleRes.surplusDiamonds }}</div>
          </div>
          <div v-else>
            <div>?</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="时间" align="left" show-overflow-tooltip min-width="200">
        <template slot-scope="scope">
          <div>创建: {{ scope.row.createTime | dateFormat }}</div>
          <div>修改: {{ scope.row.updateTime | dateFormat }}</div>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="100">
        <template slot-scope="scope">
          <el-dropdown>
            <span class="el-dropdown-link">
              <i class="el-icon-more" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-if="scope.row.role !== 'OWN'" @click.native="clickQueryDetails(scope.row)">详情</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="listQuery.lastId" class="load-more">
      <span v-if="notData">已加载全部</span>
      <el-button v-else size="mini" :disabled="loadMoreLoading" :loading="loadMoreLoading" @click="clickLoadMore">加载更多</el-button>
    </div>

    <room-gift-dialog
      v-if="roomGiftDialogVisable"
      :user-id="thatSelectedUserId"
      @close="roomGiftDialogVisable=false"
    />

    <gift-running-water-dialog
      v-if="giftRunningWaterDialogVisable"
      :user-id="thatSelectedUserId"
      :type="giftRunningWaterDialogType"
      @close="giftRunningWaterDialogVisable=false;giftRunningWaterDialogType=''"
    />

    <details-drawer
      v-if="detailsDrawerVisible"
      :row="thatRow"
      @close="detailsDrawerVisible=false"
    />

    <team-details-drawer
      v-if="teamDetailsDrawerVisible"
      :team-id="thatRow.teamProfile.id"
      @close="teamDetailsDrawerVisible=false"
    />

  </div>
</template>

<script>
import { listTeamMemberWork } from '@/api/team'
import { pickerOptions } from '@/constant/el-const'
import { sysOriginPlatforms } from '@/constant/origin'
import { teamBillStatusMap } from '@/constant/team-type'
import { teamMemberRoles, teamMemberRoleMap } from '@/constant/team-type'
import { mapGetters } from 'vuex'
import RoomGiftDialog from './room-gift-dialog'
import GiftRunningWaterDialog from './gift-running-water-dialog'
import DetailsDrawer from './details-drawer'
import TeamDetailsDrawer from './../components/TeamDetailsDrawer'

export default {
  components: {
    RoomGiftDialog,
    GiftRunningWaterDialog,
    DetailsDrawer,
    TeamDetailsDrawer
  },
  data() {
    return {
      teamDetailsDrawerVisible: false,
      detailsDrawerVisible: false,
      roomGiftDialogVisable: false,
      giftRunningWaterDialogVisable: false,
      thatSelectedUserId: '',
      giftRunningWaterDialogType: '',

      teamBillStatusMap: teamBillStatusMap(),
      teamMemberRoles,
      memberRoleMap: teamMemberRoleMap(),
      sysOriginPlatforms,
      thatRow: {},
      delLoading: false,
      selectMemberIds: [],
      pickerOptions,
      cardTeamId: '',
      cardSysOrigin: '',
      listQuery: {
        billId: '',
        sysOrigin: '',
        ownUserId: '',
        memberUserId: '',
        lastId: '',
        limit: 20
      },
      listLoading: false,
      loadMoreLoading: false,
      list: [],
      notData: false,
      rangeDate: '',
      selectTableRow: []
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    }
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isReset, loadMore) {
      const that = this
      if (isReset === true) {
        that.list = []
        that.notData = false
        that.listQuery.lastId = ''
      }
      that.loadMoreLoading = loadMore
      that.listLoading = !that.loadMoreLoading
      listTeamMemberWork(that.listQuery).then(res => {
        that.listLoading = false
        that.loadMoreLoading = false
        const { body } = res
        const list = body || []
        that.notData = list.length <= 0
        if (!that.notData) {
          that.list = that.list.concat(list)
          that.listQuery.lastId = that.list[that.list.length - 1].id
        }
      }).catch(er => {
        that.listLoading = false
        that.loadMoreLoading = false
      })
    },
    queryRoomGift(row) {
      this.thatSelectedUserId = row.memberProfile.id
      this.roomGiftDialogVisable = true
    },
    queryGiftRunningWater(row, type) {
      this.thatSelectedUserId = row.memberProfile.id
      this.giftRunningWaterDialogVisable = true
      this.giftRunningWaterDialogType = type
    },
    handleSearch() {
      this.renderData(true)
    },
    clickLoadMore() {
      const that = this
      that.loadMoreLoading = true
      that.renderData(false, true)
    },
    selectTableChange(list) {
      this.selectTableRow = list
    },
    tableSelectable(row) {
      return row.role !== 'OWN'
    },
    clickQueryDetails(row) {
      this.thatRow = row
      this.detailsDrawerVisible = true
    },
    clickQueryTeamDetails(row) {
      this.thatRow = row
      this.teamDetailsDrawerVisible = true
    }
  }
}
</script>
<style scoped lang="scss">
.load-more {
  text-align: center;
  padding: 10px;
}
</style>
