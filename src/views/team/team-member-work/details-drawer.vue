<template>
  <div class="team-member-work-drawer">
    <el-drawer
      title="详情"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div class="team-member-work-content">
        <!-- <div class="blockquote">工作详情</div>
        <div class="content">
          <div class="team-profile flex-l">
            <div class="avatar">
              <avatar :url="row.teamProfile.avatar" :name-avatar="row.teamProfile.nickname" />
              <flag-icon
                v-if="row.teamProfile.country"
                class="flag-icon"
                size="18px"
                :code="row.teamProfile.country.countryCode"
                :tooltip="row.teamProfile.country.countryName"
              />
            </div>
            <div class="info nowrap-ellipsis">
              <div class="nickname">
                <el-link><a :title="row.teamProfile.nickname"> {{ row.teamProfile.nickname }} </a></el-link>
              </div>
              <div class="account">ID: {{ row.teamProfile.account }}</div>
            </div>
          </div>
        </div> -->

        <div class="blockquote">代理</div>
        <div class="content">
          <user-table-exhibit :user-profile="row.ownUserProfile" :query-details="true" />
        </div>

        <div class="blockquote">成员</div>
        <div class="content">
          <user-table-exhibit :user-profile="row.memberProfile" :query-details="true" />
        </div>

        <div class="blockquote">工作数据</div>
        <div class="content">
          <el-row class="text-row">
            <el-col class="text-col" :span="12">周期:  {{ row.billBelong }}</el-col>
            <el-col class="text-col" :span="12">状态:  <el-tag size="mini" :type="teamBillStatusMap[row.billStatus].tag">{{ teamBillStatusMap[row.billStatus].name }}</el-tag></el-col>

            <el-col class="text-col" :span="12">时长(我的):  {{ row.target.ownOnlineTime || 0 }}</el-col>
            <el-col class="text-col" :span="12">时长(其他):  {{ row.target.otherOnlineTime || 0 }}</el-col>

            <el-col class="text-col" :span="12">礼物(送):  {{ row.target.giveGiftValue || 0 }}</el-col>
            <el-col class="text-col" :span="12">礼物(收):  {{ row.target.acceptGiftValue || 0 }}</el-col>

            <!-- <el-col class="text-col" :span="12">房间:  {{ row.target.roomValue || 0 }}</el-col> -->
            <el-col class="text-col" :span="12">粉丝:  {{ row.memberFans || 0 }}</el-col>

            <el-col class="text-col" :span="12">
              <div v-if="row.target.settleRes.policyType === 'MONEY'">成员工资$: {{ row.target.settleRes ? row.target.settleRes.memberSalary : '?' }}</div>
              <div v-else>成员工资钻石: {{ row.target.settleRes ? row.target.settleRes.memberSalary : '?' }}</div>
            </el-col>
            <el-col class="text-col" :span="12">
              <div v-if="row.target.settleRes.policyType === 'MONEY'">代理工资$: {{ row.target.settleRes ? row.target.settleRes.ownSalary : '?' }}</div>
              <div v-else>代理工资钻石: {{ row.target.settleRes ? row.target.settleRes.ownSalary : '?' }}</div>
            </el-col>
            <el-col class="text-col" :span="12">
              <div v-if="row.target.settleRes.policyType === 'MONEY'">合计工资$: {{ row.target.settleRes ? row.target.settleRes.totalSalary : '?' }}</div>
              <div v-else>合计工资钻石: {{ row.target.settleRes ? row.target.settleRes.totalSalary : '?' }}</div>
            </el-col>
            
            <!-- <el-col class="text-col" :span="12">
              <div v-if="row.target.settleRes.policyType === 'SALARY_DIAMOND'">平台扣除钻石: {{ row.target.settleRes ? row.target.settleRes.deductDiamonds : '?' }}</div>
            </el-col> -->
            <el-col class="text-col" :span="12">
              <div v-if="row.target.settleRes.policyType === 'SALARY_DIAMOND'">已兑换钻石: {{ row.target.settleRes ? row.target.settleRes.exchangeDiamonds : '?' }}</div>
            </el-col>
            <el-col class="text-col" :span="12">
              <div v-if="row.target.settleRes.policyType === 'SALARY_DIAMOND'">剩余钻石: {{ row.target.settleRes ? row.target.settleRes.surplusDiamonds : '?' }}</div>
            </el-col>

            <el-col class="text-col" :span="12">道具奖励:  {{ existsPropsRewards ? 'Yes' : 'No' }}</el-col>
            <el-col v-if="existsPropsRewards" class="text-col" :span="12">领取状态:  {{ row.target.settleRes.propsRewardReceive ? '已领取': '待领取' }}</el-col>
            <el-col v-if="existsPropsRewards" class="text-col" :span="24">
              <props-row :list="row.target.settleRes.propsRewards" />
            </el-col>
          </el-row>
        </div>

        <div class="blockquote">每天工作数据</div>
        <div class="content">
          <el-card
            v-for="(item, index) in row.target.dailyTargets"
            :key="index"
          >
            <div slot="header" class="clearfix">
              {{ item.dateNumber }}
            </div>
            <el-row class="text-row">
              <el-col class="text-col" :span="12">时长(我的):  {{ item.ownOnlineTime || 0 }}</el-col>
              <el-col class="text-col" :span="12">时长(其他):  {{ item.otherOnlineTime || 0 }}</el-col>
              <el-col class="text-col" :span="12">礼物(送):  {{ item.giveGiftValue || 0 }}</el-col>
              <el-col class="text-col" :span="12">有效天:  {{ item.effectiveDay || 0 }}</el-col>
              <el-col class="text-col" :span="12">礼物(收):  {{ item.acceptGiftValue || 0 }}</el-col>
              <el-col class="text-col" :span="12">房间:  {{ row.roomValue || 0 }}</el-col>
            </el-row>
          </el-card>
        </div>
      </div>

    </el-drawer>

  </div>
</template>
<script>
import PropsRow from '@/components/data/PropsRow'
import { teamBillStatusMap } from '@/constant/team-type'

export default {
  name: 'TeamBillDetailsDrawer',
  components: { PropsRow },
  props: {
    row: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      teamBillStatusMap: teamBillStatusMap(),
      roomDeatilsDrawerVisible: false
    }
  },
  computed: {
    existsPropsRewards() {
      return this.row.target.settleRes && this.row.target.settleRes.propsRewards && this.row.target.settleRes.propsRewards.length > 0
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    queryRoomDetails() {
      this.roomDeatilsDrawerVisible = true
    }
  }
}
</script>
<style scope.d lang="scss">
.team-member-work-content {
  padding: 0px 10px;
  .content {
    padding-bottom: 10px;
  }
  .team-profile {
      text-align: left;
      .avatar {
        position: relative;
        .flag-icon {
          position: absolute;
          bottom: -5px;
        }
      }
      .info {
        padding: 0px 5px;
        width: 100%;
        line-height: 22px;
      }
    }
  .text-row {
    .text-col {
      padding: 10px 0px;
    }
  }
}
</style>

