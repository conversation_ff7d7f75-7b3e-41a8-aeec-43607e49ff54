<template>
  <el-dialog
    title="房间流水"
    :visible="true"
    width="70%"
    :before-close="handleClose"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="发送人" align="center" min-width="200">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" :show-sys-origin="true" />
          <div class="associate-block">
            <div class="flex-l">
              <div v-if="scope.row.roomProfile" class="block-item room-profile flex-c" @click="queryRoomDetails(scope.row)">
                <el-image
                  style="width: 24px; height: 24px; border-radius: 100%;cursor: pointer;"
                  :src="scope.row.roomProfile.roomCover"
                  fit="fill"
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline" />
                  </div>
                </el-image>
              </div>

              <div v-for="(item, index) in scope.row.acceptUsers" :key="index" class="block-item user-profile flex-c" @click="queryUserDetails(item)">
                <el-image
                  v-if="index<10"
                  style="width: 24px; height: 24px; border-radius: 100%;cursor: pointer;"
                  :src="item.userProfile.userAvatar"
                  fit="fill"
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline" />
                  </div>
                </el-image>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="giftValue.quantity" label="礼物数量" align="center" min-width="80">
        <template slot-scope="scope">
          <div class="flex-c">
            <el-image
              style="width: 30px; height: 30px"
              :src="scope.row.giftCover"
              fit="fill"
              :preview-src-list="[scope.row.giftCover]"
            />
            x {{ scope.row.giftValue.quantity }}
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="giftValue.userSize" label="接收人数" align="center" min-width="80" />
      <el-table-column prop="giftValue.actualAmount" label="消费金额" align="center" min-width="100" />
      <el-table-column prop="createTime" label="创建时间" align="center" width="200">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="60">
        <template slot-scope="scope">
          <el-button type="text" @click="clickQueryDetails(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="list && list.length > 0" style="text-align: center; margin-top:20px;">
      <el-button v-if="!notMore " size="mini" :disabled="listLoading" @click="clickLoadMore">加载更多</el-button>
      <span v-else>已加载全部</span>
    </div>

    <gift-history-details-drawer
      v-if="giftHistoryDetailsDrawerVisible"
      :row="thatRow"
      @close="giftHistoryDetailsDrawerVisible=false"
    />

    <room-deatils-drawer
      v-if="roomDeatilsDrawerVisible"
      :room-id="thatRow.originId"
      @close="roomDeatilsDrawerVisible=false"
    />

    <user-deatils-drawer
      v-if="userDeatilsDrawerVisible"
      :user-id="queryUserId"
      @close="userDeatilsDrawerVisible=false"
    />
  </el-dialog>
</template>

<script>

import { listUserRoomGiftGiveAwayRunningWater } from '@/api/gift'
import { mapGetters } from 'vuex'
import GiftHistoryDetailsDrawer from '@/components/data/GiftHistoryDetailsDrawer'
export default {
  name: 'RoomGiftDialog',
  components: { GiftHistoryDetailsDrawer },
  props: {
    userId: {
      type: String,
      require: true,
      default: ''
    }
  },
  data() {
    return {
      userDeatilsDrawerVisible: false,
      queryUserId: '',
      roomDeatilsDrawerVisible: false,
      thatRow: {},
      searchDisabled: false,
      giftHistoryDetailsDrawerVisible: false,
      list: [],
      listQuery: {
        limit: 20,
        userId: '',
        lastId: ''
      },
      listLoading: true,
      clickUserId: '',
      notMore: false
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    userId: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal) {
          this.listQuery.userId = newVal
          this.renderData(true)
        }
      }
    }
  },
  methods: {
    renderData(isReset) {
      const that = this
      that.listLoading = true
      if (isReset === true) {
        that.list = []
        that.listQuery.lastId = ''
      }
      listUserRoomGiftGiveAwayRunningWater(that.listQuery).then(res => {
        that.listLoading = false
        const { body } = res
        const list = body || []
        that.notMore = list.length <= 0
        that.list = that.list.concat(list)
        if (that.list && that.list.length > 0) {
          that.listQuery.lastId = that.list[that.list.length - 1].id
        }
      }).catch(er => {
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    loadSearchUser() {
      this.searchDisabled = true
    },
    loadSearchAcceptUser() {
      this.searchDisabled = true
    },
    searchUserSuccess(res) {
      this.searchDisabled = false
      if (!res) {
        return
      }
      this.listQuery.sendUserId = res.id
    },
    searchAcceptUserSuccess(res) {
      this.searchDisabled = false
      if (!res) {
        return
      }
      this.listQuery.acceptUserId = res.id
    },
    searchUserFail() {
      this.listQuery.userId = ''
      this.searchDisabled = false
    },
    searchAcceptUserFail() {
      this.listQuery.acceptUserId = ''
      this.searchDisabled = false
    },
    clickLoadMore() {
      this.renderData()
    },
    clickQueryDetails(row) {
      this.thatRow = row
      this.giftHistoryDetailsDrawerVisible = true
    },
    queryRoomDetails(row) {
      this.thatRow = row
      this.roomDeatilsDrawerVisible = true
    },
    queryUserDetails(row) {
      this.queryUserId = row.acceptUserId
      this.userDeatilsDrawerVisible = true
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
<style scoped lang="scss">
.associate-block {
  .block-item {
    padding: 0px 5px;
  }
  .room-profile {
    border-right: 2px solid rgb(115, 133, 157);
  }
}
</style>
