<template>
  <div class="team-member-add-drawer">
    <el-drawer
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div slot="title" class="flex-l">
        <sys-origin-icon :icon="form.sysOrigin" size="18px" />&nbsp;{{ textOptTitle }}
      </div>
      <div class="team-member-add">

        <div class="drawer-form">
          <el-form ref="form" :model="form" :rules="formRules" label-width="80px">
            <el-form-item v-if="!isUpdate" prop="userId" label="Leader">
              <account-input v-model="form.userId" :sys-origin="form.sysOrigin" placeholder="BD Leader 用户ID" />
            </el-form-item>
            <el-form-item prop="regionId" label="区域">
              <select-system-region
                ref="regionSelectPolicy"
                v-model="form.regionId"
                :sys-origin="form.sysOrigin"
                placeholder="请选择区域"
              />
            </el-form-item>
            <el-form-item prop="contact" label="联系方式">
              <el-input v-model="form.contact" :sys-origin="form.sysOrigin" placeholder="联系方式" />
            </el-form-item>
          </el-form>
        </div>
        <div class="drawer-footer">
          <el-button @click="handleClose()">关闭</el-button>
          <el-button type="primary" :disabled="submitLoading" :loading="submitLoading" @click="submitForm">保存</el-button>
        </div>
      </div>
    </el-drawer>

  </div>
</template>
<script>
import { addBdLead, updateBDLead } from '@/api/room-anchor'
import { deepClone } from '@/utils'

export default {
  name: 'LeadAddBd',
  props: {
    row: {
      type: Object,
      require: false,
      default: () => {}
    }
  },
  data() {
    const commonRules = [
      { required: true, message: '必填字段不可为空', trigger: 'blur' }
    ]
    return {
      submitLoading: false,
      form: {
        id: '',
        userId: '',
        sysOrigin: '',
        contact: '',
        regionId: ''
      },
      formRules: {
        userId: commonRules,
        regionId: commonRules
      }
    }
  },
  computed: {
    textOptTitle() {
      return this.row && this.row.id ? '修改BD Leader' : '添加BD Leader'
    },
    isUpdate() {
      return this.row && this.row.id
    }
  },
  watch: {
    row: {
      handler(newVal) {
        this.form = deepClone(newVal)
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      if (this.submitLoading) {
        this.$opsMessage.warn('正在提交!')
        return
      }
      this.$emit('close')
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.submitLoading = true
        if (that.isUpdate) {
          updateBDLead(that.form).then(res => {
            that.$emit('success', that.form)
            that.submitLoading = false
          }).catch(er => {
            that.$emit('fail', that.form)
            that.submitLoading = false
          })
          return
        }
        addBdLead(that.form).then(res => {
          that.submitLoading = false
          this.$emit('success', that.form)
        }).catch(er => {
          this.$emit('fail', that.form)
          that.submitLoading = false
        })
      })
    }
  }
}
</script>

