<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="changeSysOrigin()"
      >
        <el-option
          v-for="(item, index) in permissionsSysOriginPlatforms"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>

      <div class="filter-item">
        <select-system-region
          ref="regionSelectPolicy"
          v-model="listQuery.region"
          :sys-origin="listQuery.sysOrigin"
          clearable
          placeholder="请选择区域"
          @change="selectSystemRegion"
        />
      </div>

      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-dropdown>
        <el-button class="filter-item" type="primary" :disabled="!listQuery.region">
          钻石政策<i class="el-icon-arrow-down el-icon--right" />
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item @click.native="handleCreate(false)">历史钻石政策</el-dropdown-item>
          <el-dropdown-item @click.native="handleCreate(true)">创建钻石政策</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <!-- 浏览框  v-loading="listLoading"-->
    <div v-if="!isEditing">
      <div v-if="data.title" style="margin-bottom: 30px;">
        标题: {{ data.title }}
      </div>
      <div v-if="data.policyType" style="margin-bottom: 30px;color: #d42724;">
        <div v-if="data.policyType === 'MONEY'">政策类型: 美金政策</div>
        <div v-if="data.policyType === 'SALARY_DIAMOND'">政策类型: 工资钻石政策</div>
      </div>
      <el-table
        v-loading="listLoading"
        :data="policys"
        element-loading-text="Loading"
        fit
        highlight-current-row
      >
        <el-table-column label="目标" prop="target" min-width="80" align="center" />
        <el-table-column label="主播钻石工资" prop="memberSalary" min-width="80" align="center" />
        <el-table-column label="代理钻石工资" prop="ownSalary" min-width="80" align="center" />
        <el-table-column label="总钻石工资" min-width="80" align="center">
          <template scope="scope">
            {{ getTotalSalary(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column label="道具" min-width="200" align="center">
          <template scope="scope">
            <div v-if="scope.row.propsRewards && scope.row.propsRewards.length > 0">
              <props-row :list="scope.row.propsRewards" />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="80" align="center">
          <template scope="scope">
            <el-button type="text" :disabled="!(scope.row.propsRewards && scope.row.propsRewards.length > 0)" @click="handleCreatePropsGroup(scope.row, false, index)">道具明细</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 编辑框 -->
    <div v-if="isEditing">

      <div style="margin-bottom: 30px;">
        <el-tag type="danger">标题</el-tag>
        <el-input
          v-model.trim="formData.title"
          style="width: 200px"
          class="filter-item"
          placeholder="请输入标题"
          size="small"
        />
      </div>

      <el-table
        v-loading="listLoading"
        :data="policys"
        element-loading-text="Loading"
        fit
        highlight-current-row
      >
        <el-table-column label="目标" min-width="80" align="center">
          <template scope="scope">
            <el-input v-model.number="scope.row.target" size="small" placeholder="目标" />
          </template>
        </el-table-column>

        <el-table-column label="主播钻石工资" min-width="80" align="center">
          <template scope="scope">
            <el-input v-model="scope.row.memberSalary" type="number" size="small" placeholder="主播钻石工资" />
          </template>
        </el-table-column>

        <el-table-column label="代理钻石工资" min-width="80" align="center">
          <template scope="scope">
            <el-input v-model="scope.row.ownSalary" type="number" size="small" placeholder="代理钻石工资" />
          </template>
        </el-table-column>

        <el-table-column label="总钻石工资" min-width="80" align="center">
          <template scope="scope">
            {{ getTotalSalary(scope.row) }}
          </template>
        </el-table-column>

        <el-table-column label="道具" min-width="80" align="center">
          <template scope="scope">
            <props-row v-if="scope.row.propsRewards && scope.row.propsRewards.length > 0" :list="scope.row.propsRewards" />
            <el-button type="text" @click="handleCreatePropsGroup(scope.row, true, scope.$index)">编辑道具</el-button>
          </template>
        </el-table-column>

        <!-- <el-table-column label="操作" min-width="100" align="center">
          <template scope="scope">
            <el-button type="text" @click="clickUp(scope.$index)">上移</el-button>
            <el-button type="text" @click="clickDown(scope.$index)">下移</el-button>
            <el-button v-if="policys.length > 1" type="text" @click="removeRow(scope.$index)">删除</el-button>
            <el-button type="text" @click="handleShowAddRowBoxAndIndex(scope.$index)">增加行</el-button>
          </template>
        </el-table-column> -->
      </el-table>

      <div v-if="formData.release" style="text-align: center; margin: 0.5rem;">
        <el-button type="success" @click="save(true)">保存并发布</el-button>
        <el-button type="info" @click="handleNewpolicyClose">返回</el-button>
      </div>
      <div v-if="!formData.release" style="text-align: center; margin: 0.5rem;">
        <el-button type="primary" @click="save(false)">保存</el-button>
        <el-button type="success" @click="save(true)">保存并发布</el-button>
        <el-button type="info" @click="handleNewpolicyClose">返回</el-button>
      </div>
    </div>

    <!-- 新增空白行 -->
    <el-dialog
      title="新增空白行"
      :visible.sync="addRowsBoxVisible"
      :before-close="handleAddRowClose"
    >
      <el-input v-model.number="addRowsNumber" maxlength="2" max="2" min="1" minlength="1" placeholder="请输入新增行数" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleAddRowClose()">取 消</el-button>
        <el-button type="primary" @click="addRows()">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 历史策略 -->
    <el-drawer
      title="历史政策"
      :visible.sync="historyReleasesBoxVisible"
      size="45%"
      :before-close="handleHistoryReleasesClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div v-loading="historyReleasesLoading">
        <el-row v-for="(item, index) in historyReleases" :key="index">
          <el-card style="margin: 10px;">
            <div class="ops">
              <el-tag v-if="item.historyRelease" size="mini" :type="item.historyRelease ? 'success': 'info'">发布过: {{ item.historyRelease ? 'Yes' : 'No' }}</el-tag>
              <el-tag v-if="item.release" size="mini" :type="item.release ? 'success' : 'info'">使用: {{ item.release ? 'Yes':'No' }}</el-tag>
            </div>
            <div class="bottom clearfix">
              {{ item.title }}
            </div>
            <div class="bottom clearfix">
              <div class="time">创建人: {{ item.createUserNickname }}</div>
            </div>
            <div class="bottom clearfix">
              <div class="time">创建时间:{{ item.createTime }}</div>
            </div>
            <div class="clearfix">
              <el-button type="text" @click.native="handleUpdate(item)">修改</el-button>
              <el-button v-if="!item.historyRelease" type="text" @click.native="handleDelete(item.id)">删除</el-button>
            </div>
          </el-card>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleHistoryReleasesClose()">取 消</el-button>
      </span>
    </el-drawer>

    <!-- 道具资源 -->
    <props-form-edit
      v-if="propsFormEditVisable"
      :row="thisPropsRow"
      :allow-edit="isAllowEdit"
      :sys-origin="listQuery.sysOrigin"
      @success="handlePropsSuccess"
      @close="propsFormEditVisable = false"
    />
  </div>
</template>

<script>

import { teamPolicyReleases, teamPolicyHistoryReleases, teamPolicyAdd, deleteTeamPolicy } from '@/api/team'
import { mapGetters } from 'vuex'
import PropsFormEdit from './props-form-edit'
import PropsRow from '@/components/data/PropsRow'

function getFormData() {
  return {
    id: '',
    sysOrigin: '',
    region: '',
    release: false,
    title: '',
    policy: [],
    policyType: 'SALARY_DIAMOND'
  }
}
export default {
  name: 'TeamPolicyManager',
  components: { PropsFormEdit, PropsRow },
  data() {
    return {
      isClickNewPolicyButton: false,
      isAllowEdit: false,
      thisPolicysIndex: -1,
      thisPropsRow: {},
      propsFormEditVisable: false,
      addVisible: false,
      historyReleasesBoxVisible: false,
      addRowsBoxVisible: false,
      addRowsNumber: 1,
      currentAddRowIndex: -1,
      searchDisabled: false,
      data: [],
      dtailsDrawerVisible: false,
      thatRow: {},
      policys: [],
      historyReleases: [],
      formData: getFormData(),
      isEditing: false,
      listQuery: {
        sysOrigin: 'MARCIE',
        region: '',
        type: 'WEEK',
        policyType: 'SALARY_DIAMOND'
      },
      sysOrigins: [],
      listLoading: false,
      historyReleasesLoading: false
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
  },
  methods: {
    // 获得已发布政策
    renderData() {
      const that = this
      that.listLoading = true
      teamPolicyReleases(that.listQuery).then(res => {
        that.listLoading = false
        const { body } = res
        that.data = body || {}
        that.policys = []
        if (that.data) {
          that.policys = that.data.policy || []
        }
      }).catch(er => {
        that.listLoading = false
      })
    },
    // 上移
    clickUp(index) {
      const arr = this.policys
      arr.splice(index - 1, 1, ...arr.splice(index, 1, arr[index - 1]))
    },
    // 下移
    clickDown(index) {
      const arr = this.policys
      arr.splice(index, 1, ...arr.splice(index + 1, 1, arr[index]))
    },
    // 创建资源组
    handleCreatePropsGroup(row, _isAllowEdit, _index) {
      const that = this
      that.isAllowEdit = _isAllowEdit
      that.thisPolicysIndex = _index
      if (!row.propsRewards) {
        that.thisPropsRow = {}
        that.propsFormEditVisable = true
        return
      }
      that.thisPropsRow = row
      that.propsFormEditVisable = true
    },
    handleSearch() {
      this.formData = getFormData()
      this.isEditing = false
      this.renderData()
    },
    // 系统修改重新获得值
    changeSysOrigin() {
      this.$refs.regionSelectPolicy.clearValue()
    },
    // 加载历史
    loadHistoryReleases() {
      const that = this
      that.historyReleasesLoading = true
      teamPolicyHistoryReleases(that.listQuery).then(res => {
        that.historyReleasesLoading = false
        const { body } = res
        that.historyReleases = body || []
      }).catch(er => {
        that.historyReleasesLoading = false
      })
    },
    // 下拉菜单点击
    handleCreate(isNew) {
      const that = this
      if (!isNew) {
        that.historyReleases = []
        that.loadHistoryReleases()
        that.historyReleasesBoxVisible = true
        return
      }
      that.formData.id = ''
      that.formData.region = ''
      that.formData.title = ''
      that.formData.release = false
      that.policys = []
      that.addRowsNumber = 1
      that.addRowsBoxVisible = true
      that.isClickNewPolicyButton = true
    },
    // 处理显示添加行弹窗
    handleShowAddRowBoxAndIndex(_index) {
      const that = this
      that.addRowsNumber = 1
      that.addRowsBoxVisible = true
      that.currentAddRowIndex = _index
      that.isClickNewPolicyButton = false
    },
    // 添加行,并追加至指定index下方
    addRows() {
      const that = this
      that.isEditing = true
      for (var i = 0; i < that.addRowsNumber; i++) {
        var policy = {
          level: 0,
          onlineTime: 0,
          target: 0,
          memberSalary: 0,
          ownSalary: 0,
          totalSalary: 0,
          propsRewards: []
        }
        that.policys.splice(that.currentAddRowIndex + 1, 0, policy)
      }
      that.currentAddRowIndex = -1
      that.addRowsBoxVisible = false
    },
    // 移除行
    removeRow(_index) {
      const that = this
      that.policys.splice(_index, 1)
    },
    // 点击修改
    handleUpdate(row) {
      const that = this
      that.policys = row.policy
      that.formData.id = row.id
      that.formData.region = row.region
      that.formData.title = row.title
      that.formData.release = row.release
      that.currentAddRowIndex = -1
      that.historyReleasesBoxVisible = false
      that.isEditing = true
    },
    // 保存政策
    save(_release) {
      const that = this
      that.formData.sysOrigin = that.listQuery.sysOrigin
      that.formData.policy = that.policys
      that.formData.region = that.listQuery.region

      if (!that.formData.region) {
        that.$opsMessage.warn('请选择区域!')
        return
      }

      if (!that.formData.title) {
        that.$opsMessage.warn('请输入政策标题!')
        return
      }

      if (!_release) {
        that.listLoading = true
        that.formData.release = _release
        teamPolicyAdd(that.formData).then(res => {
          that.listLoading = false
          // that.isEditing = false
          // that.renderData()
          that.$opsMessage.success()
        }).catch(er => {
          that.listLoading = false
          that.$opsMessage.fail()
        })
        return
      }

      that.$confirm('确定对外发布当政策吗?', '提示', {
        type: 'warning'
      }).then(() => {
        that.listLoading = true
        that.formData.release = _release
        teamPolicyAdd(that.formData).then(res => {
          that.listLoading = false
          // that.isEditing = false
          // that.renderData()
          that.$opsMessage.success()
        }).catch(er => {
          that.listLoading = false
          that.$opsMessage.fail()
        })
      }).catch(() => {})
    },
    // 删除政策
    handleDelete(id) {
      const that = this
      that.$confirm('确定删除选择的政策吗?', '提示', {
        type: 'warning'
      }).then(() => {
        that.listLoading = true
        deleteTeamPolicy(id).then(res => {
          that.$opsMessage.success()
          // 过滤
          that.historyReleases = that.historyReleases.filter(function(history) {
            return history.id !== id
          })
          that.listLoading = false
        }).catch(er => {
          that.$opsMessage.fail()
          that.listLoading = false
        })
      }).catch(() => {})
    },
    // 关闭弹窗
    handleClose() {
      this.$emit('close')
    },
    // 返回道具数据
    handlePropsSuccess(propsRewards) {
      this.policys[this.thisPolicysIndex].propsRewards = propsRewards
      this.propsFormEditVisable = false
    },
    // 计算合计
    getTotalSalary(item) {
      item.totalSalary = (Number(item.memberSalary) + Number(item.ownSalary)).toFixed(2)
      return item.totalSalary
    },
    // 取消新增或修改政策
    handleNewpolicyClose() {
      this.isEditing = false
      this.renderData()
    },
    // 取消新增行
    handleAddRowClose() {
      this.addRowsBoxVisible = false
      if (!this.isClickNewPolicyButton) {
        return
      }
      this.isEditing = false
    },
    // 关闭历史政策列表
    handleHistoryReleasesClose() {
      this.historyReleasesBoxVisible = false
    },
    selectSystemRegion() {
      this.isEditing = false
      if (!this.listQuery.region) {
        this.policys = []
        this.data = []
        this.formData = getFormData()
        return
      }
      this.renderData()
    }
  }
}
</script>
<style scoped lang="scss">
.app-container {
  .filter-container {
    .el-dropdown {
      vertical-align: top;
    }
    .el-dropdown + .el-dropdown {
      margin-left: 15px;
    }
    .el-icon-arrow-down {
      font-size: 12px;
    }
  }
  .form_ {
    display: flex;
    justify-content: space-around;
    flex-direction: row;
    align-items: center;
    text-align: center;
  }
}

  .time {
    font-size: 13px;
    color: #999;
  }

  .bottom {
    margin-top: 13px;
    line-height: 12px;
  }

  .button {
    padding: 0;
    float: right;
  }

  .image {
    width: 30%;
    margin-top: 10px;
    display: block;
  }

  .clearfix:before,
  .clearfix:after {
      display: table;
      content: "";
  }

  .clearfix:after {
      clear: both
  }
</style>
