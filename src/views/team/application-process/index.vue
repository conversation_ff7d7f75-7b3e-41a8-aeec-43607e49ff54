<template>
  <div class="app-container">
    <div class="filter-container" style="margin: 10px;">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;">
            <sys-origin-icon
              :icon="item.value"
              :desc="item.value"
            /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.type"
        placeholder="类型"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="团队" :value="'TEAM'" />
        <el-option label="BD" :value="'BD'" />
      </el-select>
      <div class="filter-item">
        <account-input
          v-model="listQuery.beProcessUserId"
          :sys-origin="listQuery.sysOrigin"
          placeholder="申请人"
        />
      </div>
      <el-select
        v-if="listQuery.type === 'BD'"
        v-model="listQuery.reason"
        placeholder="原因"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in bdApprovalReasons"
          :key="index"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-if="listQuery.type === 'TEAM'"
        v-model="listQuery.reason"
        placeholder="原因"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in teamApprovalReasons"
          :key="index"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-button class="filter-item" type="primary" @click="handleSearch">
        查询
      </el-button>
    </div>
    <div>
      <el-table
        ref="teamTable"
        v-loading="listLoading"
        :data="list"
        element-loading-text="Loading"
        fit
        highlight-current-row
      >
        <el-table-column type="index" width="50" label="No" />
        <el-table-column label="类型" align="center" width="100px">
          <template slot-scope="scope">
            {{ scope.row.type === "BD" ? "BD" : "团队" }}
          </template>
        </el-table-column>
        <el-table-column label="原因" align="center">
          <template slot-scope="scope">
            {{
              scope.row.type === "BD"
                ? getBdApprovalReasonName(scope.row.reason)
                : getTeamApprovalReasonName(scope.row.reason)
            }}
          </template>
        </el-table-column>
        <el-table-column label="申请人" align="center">
          <template slot-scope="scope">
            <user-table-exhibit
              :user-profile="scope.row.beProcessUser"
              :query-details="true"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作人" align="center">
          <template slot-scope="scope">
            <span
              v-if="
                scope.row.createUserOrigin == 1 &&
                  scope.row.createSysUser != null
              "
            >{{ scope.row.createSysUser.nickname }}</span>
            <span
              v-if="
                scope.row.createUserOrigin == 0 &&
                  scope.row.createUserProfile != null
              "
            >
              <user-table-exhibit
                :user-profile="scope.row.createUserProfile"
                :query-details="true"
              />
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          label="操作时间"
          align="center"
          width="180px"
        >
          <template slot-scope="scope">
            {{ scope.row.createTime | dateFormat }}
          </template>
        </el-table-column>
      </el-table>
      <div
        v-if="list && list.length > 0"
        style="text-align: center; margin-top:20px;"
      >
        <el-button
          v-if="!notMore"
          size="mini"
          :disabled="listLoading"
          @click="clickLoadMore"
        >加载更多</el-button>
        <span v-else>已加载全部</span>
      </div>
    </div>

    <user-deatils-drawer
      v-if="userDeatilsDrawer"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawer = false"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { teamProcessApprovalTable } from '@/api/team'
import { teamApprovalReasons, bdApprovalReasons } from '@/constant/team-type'
export default {
  name: 'TeamApplicationProcessList',
  data() {
    return {
      teamApprovalReasons,
      bdApprovalReasons,
      submitLoading: false,
      list: [],
      listLoading: false,
      total: 0,
      notMore: false,
      userDeatilsDrawer: false,
      thatSelectedUserId: '',
      listQuery: {
        associateId: '',
        beProcessUserId: '',
        reason: '',
        lastId: '',
        type: 'TEAM',
        sysOrigin: 'HALAR'
      }
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    this.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        that.list = []
        that.listQuery.lastId = ''
      }
      that.listLoading = true
      teamProcessApprovalTable(that.listQuery).then(res => {
        that.listLoading = false
        const { body } = res
        const list = body || []
        that.notMore = list.length <= 0
        that.list = that.list.concat(list)
        if (that.list && that.list.length > 0) {
          that.listQuery.lastId = that.list[that.list.length - 1].id
        }
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    getTeamApprovalReasonName(_reason) {
      var _name = ''
      this.teamApprovalReasons.forEach(function(v, i) {
        if (v.value === _reason) {
          _name = v.name
          return
        }
      })
      return _name
    },
    getBdApprovalReasonName(_reason) {
      console.log(_reason)
      var _name = ''
      this.bdApprovalReasons.forEach(function(v, i) {
        if (v.value === _reason) {
          _name = v.name
          return
        }
      })
      return _name
    },
    handleClose() {
      this.$emit('close')
    },
    queryUserDetails(id) {
      this.thatSelectedUserId = id
      this.userDeatilsDrawer = true
    },
    clickLoadMore() {
      this.renderData()
    }
  }
}
</script>

<style>
.time {
  font-size: 13px;
  color: #999;
}

.bottom {
  margin-top: 13px;
  line-height: 12px;
}

.button {
  padding: 0;
  float: right;
}

.image {
  width: 30%;
  margin-top: 10px;
  display: block;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>
