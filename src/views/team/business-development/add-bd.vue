<template>
  <div class="team-member-add-drawer">
    <el-drawer
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div slot="title" class="flex-l">
        <sys-origin-icon :icon="form.sysOrigin" size="18px" />&nbsp;{{ textOptTitle }}
      </div>
      <div class="team-member-add">

        <div class="drawer-form">
          <el-form ref="form" :model="form" :rules="formRules" label-width="80px">
            <el-form-item v-if="!isUpdate" prop="userId" label="BD">
              <account-input v-model="form.userId" :sys-origin="form.sysOrigin" placeholder="BD用户ID" />
            </el-form-item>
            <el-form-item prop="region" label="区域">
              <select-system-region
                ref="regionSelectPolicy"
                v-model="form.region"
                :sys-origin="form.sysOrigin"
                placeholder="请选择区域"
              />
            </el-form-item>
            <el-form-item prop="contact" label="联系方式">
              <el-input v-model="form.contact" :sys-origin="form.sysOrigin" placeholder="联系方式" />
            </el-form-item>
          </el-form>
        </div>
        <div class="drawer-footer">
          <el-button @click="handleClose()">关闭</el-button>
          <el-button type="primary" :disabled="submitLoading" :loading="submitLoading" @click="submitForm">保存</el-button>
        </div>
      </div>
    </el-drawer>

  </div>
</template>
<script>
import { addBD, updateBD } from '@/api/room-anchor'
import { deepClone } from '@/utils'

export default {
  name: 'AddBd',
  props: {
    row: {
      type: Object,
      require: false,
      default: () => {}
    }
  },
  data() {
    const commonRules = [
      { required: true, message: '必填字段不可为空', trigger: 'blur' }
    ]
    return {
      submitLoading: false,
      form: {
        id: '',
        userId: '',
        region: '',
        sysOrigin: '',
        contact: ''
      },
      formRules: {
        userId: commonRules,
        region: commonRules
      }
    }
  },
  computed: {
    textOptTitle() {
      return this.row && this.row.id ? '修改BD' : '添加BD'
    },
    isUpdate() {
      return this.row && this.row.id
    }
  },
  watch: {
    addMolde: {
      handler(newVal) {
        if (newVal === 'TEAM_OWN_USER_ID') {
          this.form.teamAccount = ''
        }
        if (newVal === 'TEAM_ACCOUNT') {
          this.form.ownUserId = ''
        }
      },
      immediate: true
    },
    row: {
      handler(newVal) {
        this.form = deepClone(newVal)
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      if (this.submitLoading) {
        this.$opsMessage.warn('正在提交!')
        return
      }
      this.$emit('close')
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.submitLoading = true
        if (that.isUpdate) {
          updateBD(that.form).then(res => {
            that.$emit('success', that.form)
            that.submitLoading = false
          }).catch(er => {
            that.$emit('fail', that.form)
            that.submitLoading = false
          })
          return
        }
        addBD(that.form).then(res => {
          that.submitLoading = false
          this.$emit('success', that.form)
        }).catch(er => {
          this.$emit('fail', that.form)
          that.submitLoading = false
        })
      })
    }
  }
}
</script>

