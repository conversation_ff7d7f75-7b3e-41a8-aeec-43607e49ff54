<template>

  <el-dialog
    title="BD团队代理成员"
    :visible="true"
    width="70%"
    :before-close="handleClose"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <div class="filter-container">
      <div class="filter-item">
        <account-input v-model="listQuery.agentId" :sys-origin="listQuery.sysOrigin" placeholder="用户ID" />
      </div>
      <el-button
        :loading="searchDisabled"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <div v-loading="listLoading" class="content">

      <el-table
        v-loading="listLoading"
        :data="list"
        fit
        highlight-current-row
        @cell-mouse-enter="handleMouseEnter"
      >
        <el-table-column prop="sysOrigin" label="系统" align="center" min-width="60">
          <template slot-scope="scope">
            <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
          </template>
        </el-table-column>
        <el-table-column label="用户" align="center" min-width="200">
          <template slot-scope="scope">
            <user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" />
          </template>
        </el-table-column>
        <el-table-column prop="anchorQuantity" label="主播数量" align="center" min-width="100" />
        <el-table-column prop="createTime" label="创建时间" align="center" width="160">
          <template slot-scope="scope">
            {{ scope.row.createTime | dateFormat }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="100">
          <template slot-scope="scope">
            <div>
              <el-button type="text" @click.native="handlBDMemberDel(scope.row.id)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
    <user-deatils-drawer
      v-if="userDeatilsDrawer"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawer=false"
    />
  </el-dialog>
</template>

<script>
import { copyText } from '@/utils'
import Pagination from '@/components/Pagination'
import { pageRoomTeamBDInfo, deleteBDMember } from '@/api/room-anchor'
export default {
  name: 'RoomTeamMemberDialog',
  components: {
    Pagination
  },
  props: {
    query: {
      type: Object,
      required: true,
      default: () => {
        return {
          userId: '',
          agentId: ''
        }
      }
    }
  },
  data() {
    return {
      listLoading: false,
      searchDisabled: false,
      thatSelectedUserId: '',
      userDeatilsDrawer: false,
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: '',
        agentId: ''
      }
    }
  },
  watch: {
    query: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal) {
          this.listQuery.userId = newVal.userId
          this.renderData()
        }
      }
    }
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      pageRoomTeamBDInfo(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.searchDisabled = false
        that.listLoading = false
      }).catch(er => {
        that.listLoading = false
      })
    },
    copyTextContent(text) {
      const that = this
      copyText(text).then(() => {
        that.$message({
          message: '复制成功',
          type: 'success'
        })
      }).catch(() => {
        that.$message({
          message: '复制失败',
          type: 'error'
        })
      })
    },
    queryUserDetails(row) {
      this.userDeatilsDrawer = true
    },
    handleSearch() {
      const that = this
      that.searchDisabled = true
      that.renderData(true)
    },
    handleClose() {
      this.$emit('close')
    },
    handlBDMemberDel(id) {
      const that = this
      this.$confirm('您确定要删除吗', '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        deleteBDMember(id).then(res => {
          that.renderData()
          that.$opsMessage.success()
        }).catch(er => {
          that.$opsMessage.success()
        })
      }).catch(() => {})
    },
    handleMouseEnter(row) {
      this.thisRow = row
      this.thatSelectedUserId = row.userProfile.id
    }
  }
}
</script>

<style scoped lang='scss'>
  .content {
    max-height: 400px;
    overflow: auto;
  }
</style>
