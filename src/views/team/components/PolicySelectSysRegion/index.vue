<template>
  <el-select
    v-model="selecteValue"
    :loading="loading"
    :disabled="disabled"
    style="width: 100%;"
    :multiple="multiple"
    :multiple-limit="multipleLimit"
    :filterable="filterable"
    :clearable="clearable"
    :placeholder="placeholder"
    @change="selectChanged"
  >
    <el-input v-model="selecteValue" style="display:none;" />
    <el-option-group
      v-for="group in list"
      :key="group.value"
      :label="group.label"
    >
      <el-option
        v-for="item in group.options"
        :key="item.id"
        :label="item.title"
        :value="item.id"
      >
        {{ item.title }} <el-tag v-if="item.release" size="mini">已发布</el-tag>
      </el-option>
    </el-option-group>
  </el-select>
</template>

<script>
import { listSysRegionPolicy } from '@/api/team'
import { billCyclePolicyTypes } from '@/constant/team-type'

export default {
  name: 'PolicySelectSysRegion',
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    sysOrigin: {
      type: String,
      require: true,
      default: ''
    },
    region: {
      type: String,
      require: true,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    },
    multipleLimit: {
      type: Number,
      default: 0
    },
    filterable: {
      type: Boolean,
      default: true
    },
    clearable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      listCacheData: {},
      list: [],
      notGroupList: [],
      loading: false,
      selecteValue: ''
    }
  },

  watch: {
    sysOrigin: {
      handler(newVal) {
        if (newVal) {
          this.renderData()
        }
      },
      immediate: true
    },
    region: {
      handler(newVal) {
        if (newVal) {
          this.renderData()
        }
      },
      immediate: true
    },
    value: {
      handler(newVal) {
        this.selecteValue = newVal
      },
      immediate: true
    }
  },
  created() {
  },
  methods: {
    clearValue() {
      this.selecteValue = ''
      this.selectChanged('')
    },
    renderData() {
      if (!this.sysOrigin || !this.region) {
        return
      }
      const key = this.getChacheKey()
      const cacheData = this.listCacheData[key]
      if (!cacheData) {
        this.listCacheData[key] = {
          loading: false,
          finish: false,
          list: [],
          notGroupList: ''
        }
      }
      this.list = []
      this.loading = false
      this.loadData()
    },
    getChacheKey() {
      return `${this.sysOrigin}_${this.region}`
    },
    loadData() {
      const that = this
      const cacheData = this.listCacheData[that.getChacheKey()]
      if (cacheData.finish === true) {
        that.list = cacheData.list
        that.notGroupList = cacheData.notGroupList
        that.loading = cacheData.loading
        return
      }

      if (cacheData.loading === true) {
        return
      }
      cacheData.loading = true
      that.loading = true
      listSysRegionPolicy(that.sysOrigin, that.region).then(res => {
        cacheData.loading = false
        that.loading = false
        cacheData.list = that.listGroup(res.body || [])
        cacheData.finish = true
        cacheData.notGroupList = res.body || []
        that.list = cacheData.list
        that.notGroupList = cacheData.notGroupList
        that.$nextTick(() => {
          that.selecteValue = that.value
          that.emitInput(that.selecteValue)
        })
      }).catch(er => {
        that.loading = false
        cacheData.loading = false
      })
    },
    listGroup(list) {
      if (!list || list.length === 0) {
        return []
      }

      const result = {}

      billCyclePolicyTypes.forEach(item => {
        result[item.value] = {
          value: item.value,
          label: item.name,
          options: []
        }
      })

      list.forEach(element => {
        result[element.type].options.push(element)
      })

      const resultList = []
      for (const key in result) {
        const row = result[key]
        if (row && row.options && row.options.length > 0) {
          resultList.push(result[key])
        }
      }
      return resultList
    },
    selectChanged(val) {
      this.emitInput(val)
      const item = this.notGroupList.filter((item) => item.id === val)
      if (item) {
        this.emitChange(val, item[0])
      }
    },
    emitInput(val) {
      this.$emit('input', val)
    },
    emitChange(val, item) {
      this.$emit('change', val, item)
    }
  }
}
</script>
