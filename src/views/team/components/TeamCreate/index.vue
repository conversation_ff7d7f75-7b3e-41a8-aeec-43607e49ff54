<template>
  <div class="create-team-drawer">
    <el-drawer
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div slot="title" class="flex-l">
        <sys-origin-icon :icon="sysOrigin" size="18px" />&nbsp;创建代理
      </div>
      <div class="create-team ">
        <div class="drawer-form">
          <el-form ref="form" :model="form" :rules="formRules" label-width="80px">
            <el-form-item v-if="bindBd==='no'" prop="region" label="区域">
              <select-system-region
                ref="regionSelectPolicy"
                v-model="form.region"
                :sys-origin="sysOrigin"
                clearable
                placeholder="区域"
              />
            </el-form-item>
            <el-form-item prop="ownUserId" label="代理">
              <account-input v-model="form.ownUserId" :sys-origin="sysOrigin" placeholder="请输入代理ID" />
            </el-form-item>

            <el-form-item label="绑定BD">
              <el-radio-group v-model="bindBd" :disabled="bdOpsTrue">
                <el-radio label="yes">是</el-radio>
                <el-radio label="no">否</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item v-if="bindBd==='yes'" prop="bdUserId" label="BD">
              <account-input v-model="form.bdUserId" :sys-origin="sysOrigin" :default-select-type="bdAccountDefaultSelectType" placeholder="请输入BD ID" />
            </el-form-item>

            <el-form-item prop="remarks" label="备注">
              <el-input
                v-model="form.remarks"
                type="textarea"
                placeholder="请输入备注"
                maxlength="100"
                show-word-limit
                resize="none"
                rows="5"
              />
            </el-form-item>

            <el-form-item label="">
              <el-checkbox v-model="formSettingDefault">设置 </el-checkbox>
              <el-checkbox v-model="formContactAdd">联系方式 </el-checkbox>
            </el-form-item>

            <div v-if="formSettingDefault" style="padding: 10px 0px;">
              <el-divider content-position="left">设置信息</el-divider>
              <el-form-item prop="setting.maxMember" label="成员数量">
                <el-input v-model="form.setting.maxMember" placeholder="最大成员数量" maxlength="100" show-word-limit />
              </el-form-item>
            </div>

            <div v-if="formContactAdd" style="padding: 10px 0px;">
              <el-divider content-position="left">联系方式</el-divider>
              <el-form-item prop="contact.type" label="类型">
                <el-select v-model="form.contact.type" placeholder="选择类型" style="width: 100%;">
                  <el-option v-for="(item, index) in contactTypes" :key="index" :label="item.name" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item prop="contact.contact" label="联系">
                <el-input v-model="form.contact.contact" placeholder="请输入联系方式" maxlength="100" show-word-limit />
              </el-form-item>
              <el-form-item prop="contact.remarks" label="备注">
                <el-input
                  v-model="form.contact.remarks"
                  type="textarea"
                  placeholder="请输入备注"
                  maxlength="100"
                  show-word-limit
                  resize="none"
                  rows="5"
                />
              </el-form-item>
            </div>
          </el-form>
        </div>
        <div v-if="sysOrigin" class="drawer-footer">
          <el-button :disabled="submitLoading" @click="handleClose()">取消</el-button>
          <el-button type="primary" :disabled="submitLoading" :loading="submitLoading" @click="submitForm()">保存</el-button>
        </div>
      </div>
    </el-drawer>

  </div>
</template>
<script>
import { contactTypes, billCyclePolicyTypes } from '@/constant/team-type'
import { createTeam } from '@/api/team'
import { deepClone } from '@/utils'

export default {
  name: 'TeamCreate',
  props: {
    sysOrigin: {
      type: String,
      require: true,
      default: ''
    },
    bdOps: {
      type: Object,
      require: false,
      default: () => {}
    }
  },
  data() {
    const commonRules = [
      { required: true, message: '必填字段不可为空', trigger: 'blur' }
    ]
    return {
      bdAccountDefaultSelectType: '',
      bindBd: 'no',
      billCyclePolicyTypes,
      contactTypes,
      submitLoading: false,
      formSettingDefault: false,
      formContactAdd: false,
      form: {
        sysOrigin: '',
        region: '',
        account: '',
        ownUserId: '',
        remarks: '',
        setting: {
          maxMember: '1000'
        },
        contact: {
          type: '',
          contact: '',
          remarks: ''
        },
        bdUserId: ''
      },
      formRules: {
        sysOrigin: commonRules,
        region: commonRules,
        ownUserId: commonRules,
        bdUserId: commonRules,
        'contact.type': commonRules,
        'contact.contact': commonRules,
        'setting.maxMember': commonRules
      },
      bdOpsTrue: false
    }
  },
  watch: {
    bdOps: {
      handler(newVal) {
        if (newVal) {
          this.bindBd = 'yes'
          this.bdOpsTrue = true
          this.bdAccountDefaultSelectType = 'LONG'
          this.form.bdUserId = newVal.userId
          this.form.region = newVal.region
        }
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      if (this.submitLoading) {
        this.$opsMessage.warn('正在提交!')
        return
      }
      this.$emit('close')
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.submitLoading = true

        const submitForm = deepClone(that.form)

        if (!that.formSettingDefault) {
          submitForm.setting = null
        }
        if (!that.formContactAdd) {
          submitForm.contact = null
        }
        submitForm.sysOrigin = that.sysOrigin

        if (that.bindBd === 'no') {
          submitForm.bdUserId = ''
        }

        createTeam(submitForm).then(res => {
          that.submitLoading = false
          that.$emit('success', Object.assign({}, submitForm))
          that.handleClose()
        }).catch(err => {
          that.submitLoading = false
          that.$emit('fial', err)
        })
      })
    }
  }
}
</script>

