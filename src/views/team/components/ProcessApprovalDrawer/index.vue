<template>
  <div class="team-application-process-approval-list">
    <el-drawer
      title="申请团队日志列表"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div class="filter-container" style="margin: 10px;">

        <div class="filter-item">
          <account-input v-model="listQuery.beProcessUserId" :sys-origin="sysOrigin" placeholder="被处理人ID" />
        </div>
        <el-select
          v-model="listQuery.reason"
          placeholder="原因"
          style="width: 120px"
          class="filter-item"
          @change="handleSearch"
        >
          <el-option
            v-for="(item, index) in teamApprovalReasons"
            :key="index"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
        <el-button
          class="filter-item"
          type="primary"
          @click="handleSearch"
        >
          查询
        </el-button>
      </div>
      <div v-loading="listLoading">
        <el-row v-for="(item, index) in list" :key="index">
          <el-card style="margin: 10px;">
            <div>
              <el-tag type="warning">原因: {{ getTeamApprovalReasonName(item.reason) }}</el-tag>
              <el-tag type="warning" @click.native="queryUserDetails(item.beProcessUser.id)">被处理人: {{ item.beProcessUser.userNickname }}</el-tag>
              <el-tag v-if="item.createUserOrigin == 1 && item.createSysUser != null" type="info">操作人: {{ item.createSysUser.nickname }}</el-tag>
              <el-tag v-if="item.createUserOrigin == 0 && item.createUserProfile != null" type="info" @click.native="queryUserDetails(item.createUserProfile.id)">创建人: {{ item.createUserProfile.userNickname }}</el-tag>
            </div>
            <div class="bottom clearfix">
              <time class="time">创建时间:{{ item.createTime }}</time>
            </div>
          </el-card>
        </el-row>
        <div v-if="list && list.length > 0" style="text-align: center; margin-top:20px;">
          <el-button v-if="!notMore " size="mini" :disabled="listLoading" @click="clickLoadMore">加载更多</el-button>
          <span v-else>已加载全部</span>
        </div>
      </div>

      <user-deatils-drawer
        v-if="userDeatilsDrawer"
        :user-id="thatSelectedUserId"
        @close="userDeatilsDrawer=false"
      />
    </el-drawer>

  </div>
</template>

<script>
import { teamProcessApprovalTable } from '@/api/team'
import { teamApprovalReasons } from '@/constant/team-type'
export default {
  name: 'TeamApplicationProcessApproval',
  props: {
    teamId: {
      type: String,
      required: true
    },
    sysOrigin: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      teamApprovalReasons,
      submitLoading: false,
      list: [],
      listLoading: false,
      total: 0,
      notMore: false,
      userDeatilsDrawer: false,
      thatSelectedUserId: '',
      listQuery: {
        associateId: '',
        beProcessUserId: '',
        reason: '',
        lastId: ''
      }
    }
  },
  created() {
    this.listQuery.reason = this.teamApprovalReasons[0].value
    this.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        that.list = []
        that.listQuery.lastId = ''
      }
      that.listLoading = true
      that.listQuery.associateId = that.teamId
      teamProcessApprovalTable(that.listQuery).then(res => {
        that.listLoading = false
        const { body } = res
        const list = body || []
        that.notMore = list.length <= 0
        that.list = that.list.concat(list)
        if (that.list && that.list.length > 0) {
          that.listQuery.lastId = that.list[that.list.length - 1].id
        }
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    getTeamApprovalReasonName(_reason) {
      var _name = ''
      this.teamApprovalReasons.forEach(function(v, i) {
        if (v.value === _reason) {
          _name = v.name
          return
        }
      })
      return _name
    },
    handleClose() {
      this.$emit('close')
    },
    queryUserDetails(id) {
      this.thatSelectedUserId = id
      this.userDeatilsDrawer = true
    },
    clickLoadMore() {
      this.renderData()
    }
  }
}
</script>

<style>
  .time {
    font-size: 13px;
    color: #999;
  }

  .bottom {
    margin-top: 13px;
    line-height: 12px;
  }

  .button {
    padding: 0;
    float: right;
  }

  .image {
    width: 30%;
    margin-top: 10px;
    display: block;
  }

  .clearfix:before,
  .clearfix:after {
      display: table;
      content: "";
  }

  .clearfix:after {
      clear: both
  }
</style>
