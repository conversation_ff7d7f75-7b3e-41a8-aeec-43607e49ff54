<template>
  <div class="team-details-drawer">
    <el-drawer
      title="详情"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div v-loading="loading" class="team-details-drawer-content">

        <div v-if="profile.ownUserProfile">
          <div class="blockquote">代理</div>
          <div class="content">
            <user-table-exhibit :user-profile="profile.ownUserProfile" :query-details="true" />
          </div>
        </div>

        <div v-if="profile.teamProfile">
          <div class="blockquote">设置资料</div>
          <div class="content">
            <div class="flex-b team-row">
              <div class="team-col nowrap-ellipsis">
                ID: {{ profile.teamProfile.id }}
              </div>
              <div class="team-col nowrap-ellipsis">
                状态:
                <el-tag v-if="profile.teamProfile.status === 'AVAILABLE'" size="mini" type="success">正常</el-tag>
                <el-tag v-if="profile.teamProfile.status === 'CLOSE'" size="mini" type="danger">关闭</el-tag>
              </div>
            </div>

            <div class="flex-b team-row">
              <div class="team-col nowrap-ellipsis">
                区域ID: {{ profile.teamProfile.region }}
              </div>
              <div class="team-col nowrap-ellipsis">
                区域: {{ profile.regionName }}
              </div>
            </div>

            <div class="flex-b team-row">
              <div class="team-col nowrap-ellipsis">
                成员: {{ profile.teamProfile.counter.memberQuantity }}/{{ profile.teamProfile.setting.maxMember }}
              </div>
              <div class="team-col nowrap-ellipsis">
                国家(团队): {{ profile.teamProfile.country.countryName }}
              </div>
            </div>

            <div class="flex-b team-row">
              <div class="team-col nowrap-ellipsis">
                创建时间: {{ profile.teamProfile.createTime }}
              </div>
              <div class="team-col nowrap-ellipsis">
                修改时间: {{ profile.teamProfile.updateTime }}
              </div>
            </div>

          </div>

        </div>

        <div v-if="profile.createUserProfile || profile.createUserNickname">
          <div class="blockquote">创建用户</div>
          <div class="content">
            <user-table-exhibit v-if="profile.createUserProfile" :user-profile="profile.createUserProfile" :query-details="true" :tag-name="profile.createUserBdRoles ? 'BD': ''" />
            <span v-else>{{ profile.createUserNickname }}</span>
          </div>
        </div>

        <div v-if="profile.updateUserProfile || profile.updateUserNickname">
          <div class="blockquote">最近操作用户</div>
          <div class="content">
            <user-table-exhibit v-if="profile.updateUserProfile" :user-profile="profile.updateUserProfile" :query-details="true" :tag-name="profile.updateUserBdRoles ? 'BD': ''" />
            <span v-else>{{ profile.updateUserNickname }}</span>
          </div>
        </div>

        <div v-if="profile.releasePolicy">
          <div class="blockquote">发布政策</div>
          <div class="content">
            <div class="policy">
              <div v-for="(item, index) in profile.releasePolicy.policy" :key="index" class="policy-row">
                <div class="title blockquote">Lv.{{ item.level }}</div>
                <div class="policy-content flex-l flex-wrap">
                  <div class="policy-block">
                    <div class="label">Time(Hours)</div>
                    <div class="value">{{ item.onlineTime }}</div>
                  </div>

                  <div class="policy-block">
                    <div class="label">Gift Value</div>
                    <div class="value">{{ item.target }}</div>
                  </div>

                  <div class="policy-block">
                    <div class="label">Host Salary</div>
                    <div class="value">{{ item.memberSalary }}</div>
                  </div>

                  <div class="policy-block">
                    <div class="label">Agent Salary</div>
                    <div class="value">{{ item.ownSalary }}</div>
                  </div>

                  <div class="policy-block">
                    <div class="label">Total Salary</div>
                    <div class="value">{{ item.totalSalary }}</div>
                  </div>
                </div>
                <div v-if="item.propsRewards" class="props-reward">
                  <props-row :list="item.propsRewards" />
                </div>

              </div>
            </div>
          </div>
        </div>

      </div>
    </el-drawer>
  </div>
</template>
<script>
import { getTeamDetails } from '@/api/team'
import PropsRow from '@/components/data/PropsRow'
export default {
  name: 'TeamProfileDetails',
  components: { PropsRow },
  props: {
    teamId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      profile: []
    }
  },
  watch: {
    teamId: {
      handler(newVal) {
        if (newVal) {
          this.loadTeamDetails()
        }
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    queryRoomDetails() {
      this.roomDeatilsDrawerVisible = true
    },
    loadTeamDetails() {
      const that = this
      that.loading = true
      getTeamDetails(that.teamId).then(res => {
        that.loading = false
        that.profile = res.body || {}
      }).catch(er => {
        that.loading = false
      })
    }
  }
}
</script>
<style scoped lang="scss">
.team-details-drawer-content {
    padding: 0px 10px;
    .content {
      padding-bottom: 10px;
    }
    .team-profile {
        text-align: left;
        .avatar {
          position: relative;
          .flag-icon {
            position: absolute;
            bottom: -5px;
          }
        }
        .info {
          padding: 0px 5px;
          width: 100%;
          line-height: 22px;
        }
      }
      .team-row {
        padding: 10px 0px;
        >.team-col {
          width: 50%;
        }
      }
      .policy {
        .policy-row {
          color: #333333;
          .title {
            padding: 5px 15px !important;
            background-color: transparent !important;
          }
          .policy-content {
            text-align: center;
            .policy-block {
              border: 1px solid #FFFFFF;
              width: 33.33%;
              padding: 10px;
              background-color: #F1F2F3;
              .label {
                padding-bottom: 5px;
              }
            }
          }

        }
      }

}
</style>

