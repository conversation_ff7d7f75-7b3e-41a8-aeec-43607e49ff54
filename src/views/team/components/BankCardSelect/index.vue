<template>
  <el-select
    v-model="selecteValue"
    :loading="loading"
    :disabled="disabled"
    style="width: 100%;"
    :multiple="multiple"
    :multiple-limit="multipleLimit"
    :filterable="filterable"
    :clearable="clearable"
    :placeholder="placeholder"
    @change="selectChanged"
  >
    <el-input v-model="selecteValue" style="display:none;" />
    <el-option-group
      v-for="group in list"
      :key="group.value"
      :label="group.label"
    >
      <el-option
        v-for="item in group.options"
        :key="item.id"
        :label="item.cardNo +'('+item.payee+')'"
        :value="item.id"
      >
        {{ item.cardNo }}({{ item.payee }}) <el-tag v-if="item.use" size="mini">使用</el-tag>
      </el-option>
    </el-option-group>
  </el-select>
</template>

<script>
import { listUserPassBankCards } from '@/api/user'
import { bankCardType } from '@/constant/team-type'

export default {
  name: 'BankCardSelect',
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    userId: {
      type: [String, Number],
      require: true,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    },
    multipleLimit: {
      type: Number,
      default: 0
    },
    filterable: {
      type: Boolean,
      default: true
    },
    clearable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      list: [],
      notGroupList: [],
      loading: false,
      selecteValue: ''
    }
  },

  watch: {
    userId: {
      handler(newVal) {
        if (newVal) {
          this.list = []
          this.loading = false
          this.loadData()
        }
      },
      immediate: true
    }
  },
  created() {
    this.selecteValue = this.value
  },
  methods: {
    clearValue() {
      this.selecteValue = ''
      this.selectChanged('')
    },
    loadData() {
      const that = this
      that.loading = true
      listUserPassBankCards(that.userId).then(res => {
        that.loading = false
        that.notGroupList = res.body || []
        that.list = that.listGroup(res.body || [])
        that.selecteValue = ''
        that.emitInput(that.selecteValue)
      }).catch(er => {
        that.loading = false
      })
    },
    listGroup(list) {
      if (!list || list.length === 0) {
        return []
      }

      const result = {}
      bankCardType.forEach(item => {
        result[item.value] = {
          value: item.value,
          label: item.name,
          options: []
        }
      })

      list.forEach(element => {
        result[element.cardType].options.push(element)
      })

      console.log('result', result)
      const resultList = []
      for (const key in result) {
        const row = result[key]
        if (row && row.options && row.options.length > 0) {
          resultList.push(result[key])
        }
      }
      return resultList
    },
    selectChanged(val) {
      this.emitInput(val)
      const item = this.notGroupList.filter((item) => item.id === val)
      if (item) {
        this.emitChange(val, item[0])
      }
    },
    emitInput(val) {
      this.$emit('input', val)
    },
    emitChange(val, item) {
      this.$emit('change', val, item)
    }
  }
}
</script>
