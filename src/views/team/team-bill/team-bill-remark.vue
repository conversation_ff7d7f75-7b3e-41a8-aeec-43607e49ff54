<template>
  <div class="team-bill-remarks-drawer">
    <el-drawer
      title="编辑备注"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div class="team-bill-remarks">
        <div class="drawer-form">
          <el-form ref="form" :model="form" :rules="formRules" label-width="80px" style="margin-right: 15px;">
            <el-form-item prop="remarks" label="备注">
              <el-input
                v-model="form.remarks"
                type="textarea"
                placeholder="请输入备注"
                maxlength="100"
                show-word-limit
                resize="none"
                rows="5"
              />
            </el-form-item>
          </el-form>
        </div>
        <div class="drawer-footer">
          <el-button :disabled="submitLoading" @click="handleClose()">关闭</el-button>
          <el-button type="primary" :disabled="submitLoading" :loading="submitLoading" @click="submit">保存</el-button>
        </div>
      </div>
    </el-drawer>

  </div>
</template>
<script>
import { updateBillRemarks } from '@/api/team'

export default {
  name: 'TeamBillRemarks',
  props: {
    row: {
      type: Object,
      require: true,
      default: () => {}
    }
  },
  data() {
    return {
      submitLoading: false,
      form: {
        billId: '',
        remarks: ''
      },
      formRules: {}
    }
  },
  computed: {
  },
  watch: {
    row: {
      handler(newVal) {
        if (newVal) {
          this.form.billId = newVal.id
          this.form.remarks = newVal.remarks
        }
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      if (this.submitLoading) {
        this.$opsMessage.warn('正在提交!')
        return
      }
      this.$emit('close')
    },
    submit() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.submitLoading = true
        updateBillRemarks(that.form).then(res => {
          that.submitLoading = false
          this.$emit('success', that.form)
        }).catch(er => {
          that.submitLoading = false
        })
      })
    }
  }
}
</script>

