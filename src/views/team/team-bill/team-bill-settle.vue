<template>
  <div class="team-bill-settle-drawer">
    <el-drawer
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div slot="title" class="flex-l">
        <sys-origin-icon :icon="row.sysOrigin" size="18px" />&nbsp;结算账单 <el-button :loading="calculatorLoading" type="text" @click="reloadCalculator"><i v-if="!calculatorLoading" class="el-icon-refresh cursor-pointer" /></el-button>
      </div>
      <div class="team-bill-settle">
        <div class="drawer-form">
          <!-- <div v-if="row.details"> -->
          <div class="blockquote">政策计算
            <el-tooltip class="item" effect="dark">
              <div slot="content">
                <p>* 根据选择 “区域+政策” 计算团队成员工资情况</p>
                <p>* 哪些用会被计算到工作人数中? <br> &nbsp;&nbsp;&nbsp;&nbsp; 在线/房间流水/发送礼物/收礼物 都将会记录到工作账单中</p>
                <p>* 政策根据什么计算的? <br> &nbsp;&nbsp;&nbsp;&nbsp; 根据选择的 “区域+政策” 中 ”政策“ 数据中的接受礼物作为 ”目标“ 进行计算</p>
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </div>
          <div class="content">
            <div class="flex-b bill-row">
              <div class="left bill-col nowrap-ellipsis">
                工作人数: {{ calculatorRes.workMembers }}
              </div>
              <div class="right bill-col nowrap-ellipsis">
                完成人数:  {{ calculatorRes.finishMembers }}
              </div>
            </div>

            <div class="flex-b bill-row">
              <div class="left bill-col nowrap-ellipsis">
                成员工资$: {{ calculatorRes.memberSalary }}
              </div>
              <div class="right bill-col nowrap-ellipsis">
                团长工资$: {{ calculatorRes.ownSalary }}
              </div>
            </div>

            <div class="flex-b bill-row">
              <div class="right bill-col nowrap-ellipsis">
                合计工资$: {{ calculatorRes.totalSalary }}
              </div>
            </div>

          <!-- </div> -->
          </div>

          <div class="blockquote">结算
            <el-tooltip class="item" effect="dark">
              <div slot="content">
                <p>* 区域: 选择本次团队结算区域, 切换区域将会对应影响到 “本次结算” 政策</p>
                <p>* 政策: 团队结算对应的政策</p>
                <p>* 收款: 结算到用户的银行卡信息, 结算人员注意跟转账凭证截图中的对应起来</p>
                <p>* 类型: 本次账单结算类型
                  <br>&nbsp;&nbsp;&nbsp;&nbsp; 美元: 发送美元
                  <br>&nbsp;&nbsp;&nbsp;&nbsp; 金币: 结算人员将对应的工资折算金币, 需要输入金币数量(结算系统不会自动发送金币)
                  <br>&nbsp;&nbsp;&nbsp;&nbsp; 美元+金币: 结算人员将部分工资折算成金币 + 部分美元 (线下协调: 主播看到的还是他的实际工资, 代理看到的是更详细的: 金币 + 美元)
                </p>
                <p>* 转账凭证: 结算人员发送金币/美元 后的记录凭证截图</p>
                <p>* 备注: 本次账单备注, 对外显示</p>
              </div>
              <i class="el-icon-info" />
            </el-tooltip>
          </div>
          <el-form ref="form" :model="form" :rules="formRules" label-width="80px" style="margin-right: 15px;">

            <el-form-item prop="region" label="区域">
              <select-system-region
                ref="regionSelectPolicy"
                v-model="form.region"
                :sys-origin="row.sysOrigin"
                placeholder="请选择区域"
                :disabled="calculatorLoading"
                @change="changeRegion"
              />
            </el-form-item>

            <el-form-item prop="policyId" label="政策">
              <policy-select-sys-region
                ref="PolicySelectedSysRegion"
                v-model="form.policyId"
                :sys-origin="nativeRow.sysOrigin"
                :region="region"
                placeholder="请选择政策"
                :disabled="calculatorLoading"
                @change="cahngePolicy"
              />
            </el-form-item>

            <!-- <el-form-item prop="bankCardId" label="收款">
              <bank-card-select
                ref="BankCardSelect"
                v-model="form.bankCardId"
                :user-id="row.userId"
                placeholder="请选择收款"
                @change="changeBankCard"
              />
            </el-form-item> -->

            <el-form-item prop="settleType" label="类型">
              <el-select
                v-model="form.settleType"
                placeholder="请选择结算类型"
                style="width: 100%;"
              >
                <el-option
                  v-for="(item, index) in teamBillSettleTypes"
                  :key="index"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>

            </el-form-item>

            <el-form-item v-if="form.settleType === 'GOLD' || form.settleType === 'MONEY_GOLD'" prop="goldQuantity" label="金币">
              <el-input v-model="form.goldQuantity" v-number placeholder="请输入金币数量,系统不会自动发送" />
            </el-form-item>

            <el-form-item v-if="form.settleType === 'MONEY_GOLD'" prop="salaryQuantity" label="工资$">
              <el-input v-model="form.salaryQuantity" v-double placeholder="请输入工资$" />
            </el-form-item>

            <el-form-item prop="tmpExistsCertificate" label="凭证上传">
              <el-row>
                <el-col :span="8">
                  <upload-image
                    v-model="certificateOne"
                    :file-dir="$application.fileBucket.back"
                  />
                </el-col>
                <el-col :span="8">
                  <upload-image
                    v-model="certificateTwo"
                    :file-dir="$application.fileBucket.back"
                  />
                </el-col>
                <el-col :span="8">
                  <upload-image
                    v-model="certificateThree"
                    :file-dir="$application.fileBucket.back"
                  />
                </el-col>
              </el-row>
            </el-form-item>

            <el-form-item label="备注">
              <el-input
                v-model="form.remarks"
                type="textarea"
                placeholder="请输入备注"
                maxlength="100"
                show-word-limit
                resize="none"
                rows="5"
              />
            </el-form-item>
          </el-form>
        </div>

        <div v-if="!calculatorLoading" class="drawer-footer">
          <el-button :disabled="submitLoading" @click="handleClose()">关闭</el-button>
          <el-button type="primary" :disabled="submitLoading" :loading="submitLoading" @click="submit">保存</el-button>
        </div>
      </div>
    </el-drawer>

  </div>
</template>
<script>
import { teamBillSettle, getTeamBillCalculator } from '@/api/team'
import { teamBillSettleTypes } from '@/constant/team-type'
import { deepClone } from '@/utils'
import PolicySelectSysRegion from './../components/PolicySelectSysRegion'
import BankCardSelect from './../components/BankCardSelect'

export default {
  name: 'TeamBillSettle',
  components: { PolicySelectSysRegion, BankCardSelect },
  props: {
    row: {
      type: Object,
      require: true,
      default: () => {}
    }
  },
  data() {
    const commonRules = [
      { required: true, message: '必填字段不可为空', trigger: 'blur' }
    ]
    return {
      calculatorLoading: false,
      calculatorRes: {},
      correctWagesVisible: false,
      rewardCheckBoxVisible: false,
      deductCheckBoxVisible: false,
      teamBillSettleTypes,
      submitLoading: false,
      certificateOne: '',
      certificateTwo: '',
      certificateThree: '',
      form: {
        id: '',
        teamId: '',
        region: '',
        policyId: '',
        bankCardId: '',
        settleType: '',
        goldQuantity: '',
        salaryQuantity: '',
        remarks: '',
        certificateUrls: [],
        tmpExistsCertificate: '',
        bankCardArchives: {
          id: '',
          teamId: '',
          cardType: '',
          payee: '',
          cardNo: ''
        }
      },
      region: '',
      formRules: {
        id: commonRules,
        teamId: commonRules,
        policyId: commonRules,
        region: commonRules,
        bankCardId: commonRules,
        settleType: commonRules,
        goldQuantity: commonRules,
        salaryQuantity: commonRules,
        tmpExistsCertificate: [
          { required: true, message: '最少上传1张凭证', trigger: 'blur' }
        ]
      },
      nativeRow: {}
    }
  },
  computed: {
  },
  watch: {
    row: {
      handler(newVal) {
        if (newVal) {
          this.nativeRow = deepClone(newVal)
          this.form.id = this.nativeRow.id
          this.form.teamId = this.nativeRow.teamId
          this.form.region = this.nativeRow.region
          this.region = this.nativeRow.region
          // this.form.bankCardId = this.nativeRow.bankCardId
          this.form.settleType = this.nativeRow.settleType
          this.form.rewardSalary = this.nativeRow.rewardSalary
          this.form.remarks = this.nativeRow.remarks
          this.form.certificateUrls = []
          this.form.tmpExistsCertificate = ''
          this.loadTeamBillCalculator()
        }
      },
      immediate: true
    }
  },
  methods: {
    changeRegion(id, val) {
      this.region = id
      this.$refs.PolicySelectedSysRegion.clearValue()
      this.calculatorRes = {}
      this.form.actualSalary = 0
    },
    handleClose() {
      if (this.submitLoading) {
        this.$opsMessage.warn('正在提交!')
        return
      }
      this.$emit('close')
    },
    reloadCalculator() {
      this.loadTeamBillCalculator()
    },
    cahngePolicy() {
      this.form.actualSalary = 0
      this.loadTeamBillCalculator()
    },
    submit() {
      const that = this
      if (that.calculatorLoading) {
        this.$opsMessage.warn('正在计算政策工资, 请稍等!')
        return
      }

      that.form.certificateUrls = []
      if (that.certificateOne) {
        that.form.certificateUrls.push(that.certificateOne)
      }
      if (that.certificateTwo) {
        that.form.certificateUrls.push(that.certificateTwo)
      }
      if (that.certificateThree) {
        that.form.certificateUrls.push(that.certificateThree)
      }
      that.form.tmpExistsCertificate = that.form.certificateUrls.length > 0 ? 'true' : ''

      that.form.workMembers = that.calculatorRes.workMembers
      that.form.finishMembers = that.calculatorRes.finishMembers
      that.form.memberSalary = that.calculatorRes.memberSalary
      that.form.ownSalary = that.calculatorRes.ownSalary
      that.form.totalSalary = that.calculatorRes.totalSalary
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.submitLoading = true
        teamBillSettle(that.form).then(res => {
          that.submitLoading = false
          this.$emit('success', that.form)
        }).catch(er => {
          that.submitLoading = false
        })
      })
    },
    loadTeamBillCalculator() {
      const that = this
      that.calculatorLoading = true
      getTeamBillCalculator({
        billId: that.row.id,
        policyId: that.form.policyId
      }).then(res => {
        that.calculatorLoading = false
        that.calculatorRes = res.body || {}
        that.form.policyId = that.calculatorRes.policyId
        that.form.actualSalary = that.calculatorRes.totalSalary
      }).catch(er => {
        that.calculatorLoading = false
        console.error(er)
      })
    },
    changeBankCard(id, card) {
      this.form.bankCardArchives = card
    },
    changeCorrectWages(val) {
      if (val === false) {
        this.form.actualSalary = this.calculatorRes.totalSalary || 0
      }
    }
  }
}
</script>
<style scoped lang="scss">
.team-bill-settle {
  .content {
    margin-bottom: 10px;
    .bill-row {
      padding: 10px 0px;
      >.bill-col {
        width: 50%;
      }
    }
  }
}
</style>

