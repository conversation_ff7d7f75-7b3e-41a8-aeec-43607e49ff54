<template>
  <div class="member-team-change-drawer">
    <el-drawer
      title="更换团队"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div slot="title" class="flex-l">
        <sys-origin-icon :icon="row.sysOrigin" size="18px" />&nbsp;添加成员
      </div>
      <div class="member-team-change">

        <div class="drawer-form">
          <el-form ref="form" :model="form" :rules="formRules" label-width="80px" style="margin-right: 15px;">
            <!-- <el-form-item prop="status" label="模式">
              <el-radio-group v-model="addMolde">
                <el-radio label="TEAM_OWN_USER_ID">团长ID</el-radio>
                <el-radio label="TEAM_ACCOUNT">团队账号</el-radio>
              </el-radio-group>
            </el-form-item> -->

            <el-form-item v-if="addMolde === 'TEAM_OWN_USER_ID'" prop="ownUserId" label="代理ID">
              <account-input v-model="form.ownUserId" :sys-origin="sysOrigin" placeholder="代理ID" />
            </el-form-item>

            <el-form-item v-if="addMolde === 'TEAM_ACCOUNT'" prop="teamAccount" label="团队账号">
              <el-input v-model="form.teamAccount" placeholder="请输入团队账号" maxlength="10" show-word-limit />
            </el-form-item>
            <el-form-item prop="remarks" label="备注">
              <el-input
                v-model="form.remarks"
                type="textarea"
                placeholder="请输入备注"
                maxlength="100"
                show-word-limit
                resize="none"
                rows="5"
              />
            </el-form-item>
          </el-form>
        </div>

        <div class="drawer-footer">
          <el-button :disabled="submitLoading" @click="handleClose()">关闭</el-button>
          <el-button type="primary" :disabled="submitLoading" :loading="submitLoading" @click="submit">保存</el-button>
        </div>
      </div>
    </el-drawer>

  </div>
</template>
<script>
import { updateMemberTeam } from '@/api/team'

export default {
  name: 'TeamMemberChangeTeam',
  props: {
    row: {
      type: Object,
      require: true,
      default: () => {}
    }
  },
  data() {
    const commonRules = [
      { required: true, message: '必填字段不可为空', trigger: 'blur' }
    ]
    return {
      addMolde: 'TEAM_OWN_USER_ID',
      submitLoading: false,
      form: {
        ownUserId: '',
        sysOrigin: '',
        teamAccount: '',
        memberUserId: '',
        remarks: ''
      },
      formRules: {
        ownUserId: commonRules,
        sysOrigin: commonRules,
        teamAccount: commonRules,
        memberUserId: commonRules
      }
    }
  },
  computed: {
  },
  watch: {
    row: {
      handler(newVal) {
        if (newVal) {
          this.form.sysOrigin = newVal.sysOrigin
          this.form.memberUserId = newVal.userProfile.id
        }
      },
      immediate: true
    },
    addMolde: {
      handler(newVal) {
        if (newVal === 'TEAM_OWN_USER_ID') {
          this.form.teamAccount = ''
        }
        if (newVal === 'TEAM_ACCOUNT') {
          this.form.ownUserId = ''
        }
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      if (this.submitLoading) {
        this.$opsMessage.warn('正在提交!')
        return
      }
      this.$emit('close')
    },
    submit() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.submitLoading = true
        updateMemberTeam(that.form).then(res => {
          that.submitLoading = false
          this.$emit('success', res.body)
        }).catch(er => {
          that.submitLoading = false
        })
      })
    }
  }
}
</script>
