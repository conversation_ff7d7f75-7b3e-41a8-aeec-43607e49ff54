<template>
  <div class="team-bill-drawer">
    <el-drawer
      title="详情"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div class="team-bill-content">

        <div class="blockquote">代理</div>
        <div class="content">
          <user-table-exhibit :user-profile="row.ownUserProfile" :query-details="true" />
        </div>

        <div class="blockquote">账单信息</div>
        <div class="content">
          <div class="flex-b bill-row">
            <div class="left bill-col nowrap-ellipsis">
              ID: {{ row.id }}
            </div>
            <div class="right bill-col nowrap-ellipsis">
              区域: {{ row.regionName }}
            </div>
          </div>
          <div class="flex-b bill-row">
            <div class="left bill-col nowrap-ellipsis">
              账单归属: {{ row.billBelong }}
            </div>
            <div class="left bill-col nowrap-ellipsis">
              账单标题:  {{ row.billTitle }}
            </div>
          </div>

          <div class="flex-b bill-row">
            <div class="left bill-col nowrap-ellipsis">
              状态: {{ teamBillStatusMap[row.status].name }}
            </div>
          </div>

          <div v-if="row.settleResult">
            <div class="flex-b bill-row">
              <div class="left bill-col nowrap-ellipsis">
                工作人数: {{ row.settleResult.workMembers }}
              </div>
              <div class="right bill-col nowrap-ellipsis">
                完成人数: {{ row.settleResult.finishMembers }}
              </div>
            </div>

            <div class="flex-b bill-row">
              <div class="left bill-col nowrap-ellipsis">
                成员工资: {{ row.settleResult.memberSalary || 0 }}
              </div>
              <div class="right bill-col nowrap-ellipsis">
                团长工资: {{ row.settleResult.ownSalary || 0 }}
              </div>
            </div>

            <div class="flex-b bill-row">
              <div class="left bill-col nowrap-ellipsis">
                扣除工资: {{ row.settleResult.deductSalary || 0 }}
              </div>
              <div class="right bill-col nowrap-ellipsis">
                合计工资: {{ row.settleResult.totalSalary || 0 }}
              </div>
            </div>

            <div v-if="row.settleResult.policy.policyType === 'SALARY_DIAMOND'" class="flex-b bill-row">
              <div class="left bill-col nowrap-ellipsis">
                团队已兑换钻石: {{ row.settleResult.redeemedDiamondsSalary || 0 }}
              </div>
              <div class="right bill-col nowrap-ellipsis">
                团队剩余钻石: {{ row.settleResult.remainingDiamondsSalary || 0 }}
              </div>
            </div>

          </div>

          <div v-if="row.remarks" class="bill-row">
            对外备注: {{ row.remarks }}
          </div>
        </div>

        <div v-if="row.internalRemarks && row.internalRemarks.length > 0">
          <div class="blockquote">内部备注</div>
          <div class="content">
            <el-timeline :reverse="true">
              <el-timeline-item
                v-for="(item, index) in row.internalRemarks"
                :key="index"
                :timestamp="item.createTime"
              >
                <div class="remark">
                  <span class="nickname">{{ item.createBackUserName }}:</span> &nbsp;{{ item.remark }}
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>

        <div v-if="row.settleResult && row.settleResult.bankCardArchives">
          <div class="blockquote">银行卡</div>
          <div class="content">
            <div class="bank-card-list">
              <div class="bank-card-item back-blue">
                <div class="card-info flex-b">
                  <div class="card-icon">
                    <img src="https://img.sugartimeapp.com/avatar/********-7e89-4ba5-9640-4697a5a61048.png" alt="">
                  </div>
                  <div class="profile">
                    <div class="card-no ellipsis-nowrap">
                      {{ row.settleResult.bankCardArchives.cardNo }}
                    </div>
                    <div class="payee ellipsis-nowrap flex-b">
                      payee: {{ row.settleResult.bankCardArchives.payee }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-if="row.settleResult">
          <div class="blockquote">政策</div>
          <div class="content">
            <div class="policy">
              <div v-for="(item, index) in row.settleResult.policy.policy" :key="index" class="policy-row">
                <div class="title blockquote">Lv.{{ item.level }}</div>
                <div class="policy-content flex-l flex-wrap">
                  <div class="policy-block">
                    <div class="label">Time(Hours)</div>
                    <div class="value">{{ item.onlineTime }}</div>
                  </div>

                  <div class="policy-block">
                    <div class="label">GiftValue</div>
                    <div class="value">{{ item.target }}</div>
                  </div>

                  <div class="policy-block">
                    <div class="label">MemberSalary</div>
                    <div class="value">{{ item.memberSalary }}</div>
                  </div>

                  <div class="policy-block">
                    <div class="label">OwnSalary</div>
                    <div class="value">{{ item.ownSalary }}</div>
                  </div>

                  <div class="policy-block">
                    <div class="label">TotalSalary</div>
                    <div class="value">{{ item.totalSalary }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </el-drawer>
  </div>
</template>
<script>
import { teamBillStatusMap } from '@/constant/team-type'
export default {
  name: 'TeamBillDetailsDrawer',
  props: {
    row: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      teamBillStatusMap: teamBillStatusMap()
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
  <style scope.d lang="scss">
  .team-bill-content {
      padding: 0px 10px;
      .content {
        margin-bottom: 10px;
      }

      .remark {
        width: 100%;
        overflow: auto;
        word-wrap: break-word;
        .nickname {
          font-weight: bold;
        }
      }
      .bill-row {
        padding: 10px 0px;
        >.bill-col {
          width: 50%;
        }
      }
      .policy {
        .policy-row {
          color: #333333;
          .title {
            padding: 5px 15px !important;
            background-color: transparent !important;
          }
          .policy-content {
            text-align: center;
            .policy-block {
              border: 1px solid #FFFFFF;
              width: 33.33%;
              padding: 10px;
              background-color: #F1F2F3;
              .label {
                padding-bottom: 5px;
              }
            }
          }

        }
      }
      .bank-card-list {
        .back-green {
          background-image: linear-gradient(to left, #018AA8 0%, #00B39F 100%);
        }
        .back-blue {
          background-image: linear-gradient(to left, #2D61CF 0%, #3893E6 100%);
        }
        .bank-card-item {
          color: #FFFFFF;
          margin-bottom: .2rem;
          border-radius: .2rem;
          padding: 10px;
          .card-info {
            .card-icon {
              height: 50px;
              width: 50px;
              flex-shrink: 0;
              img {
                width: 100%;
                height: 100%;
                border-radius: 100%;
              }
            }
            .profile {
              width: 100%;
              padding: 0px 10px;
              .payee {
                padding: 5px 0px;
              }
            }
          }
        }
      }
  }
  </style>

