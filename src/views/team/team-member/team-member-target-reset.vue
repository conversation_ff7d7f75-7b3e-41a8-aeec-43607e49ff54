<template>
  <div class="member-team-change-drawer">
    <el-drawer
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div slot="title" class="flex-l">
        <sys-origin-icon :icon="row.sysOrigin" size="18px" />&nbsp;重置目标
      </div>
      <div class="member-team-change">
        <div class="drawer-form">
          <el-alert
            type="info"
            :closable="false"
            style="margin:0px 0px 20px 0px;"
            description="注意: 当前重置目标将计算到最新未结算账单(更细的操作将在后期完善)"
          />
          <el-form ref="form" :model="form" :rules="formRules" label-width="80px" style="margin-right: 15px;">

            <el-form-item prop="startTime" label="时间范围">
              <el-date-picker
                v-model="rangeDate"
                style="width: 100%;"
                value-format="timestamp"
                type="datetimerange"
                :picker-options="pickerOptions"
                range-separator="至"
                start-placeholder="开始日期开始"
                end-placeholder="结束日期结束"
              />
            </el-form-item>
            <el-form-item label="">
              <el-checkbox v-model="form.checkAnchor">验证用户收礼物时段必须是主播身份</el-checkbox>
            </el-form-item>
            <!-- <el-form-item label="">
              <el-checkbox v-model="luckyGift">处理幸运礼物目标问题(彭石刚专用)</el-checkbox>
            </el-form-item> -->
          </el-form>
        </div>

        <div class="drawer-footer">
          <el-button :disabled="checkLoading" @click="handleClose()">关闭</el-button>
          <el-button v-if="!luckyGift" type="primary" :disabled="checkLoading" :loading="checkLoading" @click="clickCheckTarget">检测</el-button>
          <el-input v-if="luckyGift" v-model="money" placeholder="请输入内容比例" />
          <el-button v-if="luckyGift" type="primary" :disabled="checkLoading" :loading="checkLoading" @click="sumbitResetTargetTmp()">(彭石刚)检测幸运礼物目标问题</el-button>
        </div>
      </div>
    </el-drawer>

  </div>
</template>
<script>
import { checkTarget, checkTargetLuckyGift, resetUnpaidBillMemberTarget, resetUnpaidBillMemberTargetTmp } from '@/api/team'
import { pickerOptions } from '@/constant/el-const'
export default {
  name: 'TeamMemberChangeTeam',
  props: {
    row: {
      type: Object,
      require: true,
      default: () => {}
    }
  },
  data() {
    const commonRules = [
      { required: true, message: '必填字段不可为空', trigger: 'blur' }
    ]
    return {
      pickerOptions,
      rangeDate: '',
      checkLoading: false,
      luckyGift: false,
      bili: 0.00,
      money: 0,
      form: {
        acceptUserId: '',
        startTime: '',
        endTime: '',
        checkAnchor: true
      },
      formRules: {
        startTime: commonRules
      },
      targetVal: 0
    }
  },
  computed: {
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.form.startTime = newVal[0]
          this.form.endTime = newVal[1]
          return
        }
        this.form.startTime = ''
        this.form.endTime = ''
      }
    },
    row: {
      handler(newVal) {
        if (newVal) {
          this.form.acceptUserId = newVal.userProfile.id
        }
      },
      immediate: true
    }
  },
  created() {
  },
  methods: {
    handleClose() {
      if (this.checkLoading) {
        this.$opsMessage.warn('正在处理中!')
        return
      }
      this.$emit('close')
    },
    submitResetTarget() {
      const that = this
      that.handleClose()
      resetUnpaidBillMemberTarget({
        resetUserId: that.row.userProfile.id,
        target: that.targetVal
      }).then(res => {
        this.$opsMessage.success()
      }).catch(er => {
        this.$opsMessage.fail()
      })
    },
    sumbitResetTargetTmp() {
      const that = this
      resetUnpaidBillMemberTargetTmp({
        resetUserId: that.row.userProfile.id,
        money: that.money
      }).then(res => {
        this.$opsMessage.success()
      }).catch(er => {
        this.$opsMessage.fail()
      })
    },
    clickCheckTarget() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.checkLoading = true
        checkTarget(that.form).then(res => {
          that.targetVal = res.body || 0
          that.checkLoading = false

          // if (that.targetVal <= 0) {
          //   that.$opsMessage.warn('没有检测到目标记录!')
          //   return
          // }

          that.$confirm(`是否确认恢复用户目标: ${that.targetVal}?`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            that.submitResetTarget()
          }).catch(() => {
          })
        }).catch(er => {
          that.checkLoading = false
        })
      })
    },
    clickCheckTargetLuckyGift() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.checkLoading = true
        checkTargetLuckyGift(that.form).then(res => {
          that.targetVal = res.body || 0
          that.checkLoading = false

          // if (that.targetVal <= 0) {
          //   that.$opsMessage.warn('没有检测到目标记录!')
          //   return
          // }

          that.$confirm(`是否确认恢复用户目标: ${that.targetVal}?`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            that.submitResetTarget()
          }).catch(() => {
          })
        }).catch(er => {
          that.checkLoading = false
        })
      })
    }
  }
}
</script>
