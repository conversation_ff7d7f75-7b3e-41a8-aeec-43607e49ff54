<template>
  <div class="lang-text-editor">
    <div class="lang-content">
      <el-row v-for="(item, index) in langList" :key="index" style="margin-bottom: 10px;">
        <el-col :span="20">
          <div v-if="type ==='textarea'">
            {{ item.name }}
          </div>
          <el-input v-model="item.value" :type="type ? type : 'text'" clearable rows="8" resize="none" @input="inputEvent">
            <template slot="prepend">{{ item.name }}</template>
          </el-input>
        </el-col>
        <el-col :span="4" style="text-align: center;">
          <i class="el-icon-delete-solid cursor-pointer font-danger" style="font-size: 18px;" @click="clickDelLang(item,index)" />
        </el-col>
      </el-row>
    </div>
    <div v-if="langList.length !== langs.length" class="add-but">
      <el-dropdown size="mini">
        <span class="el-dropdown-link">
          添加语言<i class="el-icon-arrow-down el-icon--right" />
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-for="(item, index) in langs" :key="index" :disabled="existsLang(item.value)" @click.native="clickAddLang(item)">{{ item.name }}</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { deepClone } from '@/utils'
import { langs } from '@/constant/activity-template'
export default {
  name: 'LangTextEditor',
  props: {
    type: {
      type: String,
      require: false,
      default: 'text'
    },
    langArray: {
      type: Array,
      require: true,
      default: () => []
    }
  },
  data() {
    return {
      langList: [],
      langs
    }
  },
  watch: {
    langArray: {
      handler(newVal) {
        if (newVal) {
          this.langList = deepClone(newVal)
        }
      },
      immediate: true
    }
  },
  created() {

  },
  methods: {
    createLangObj(lang, name) {
      return { lang, name, value: '' }
    },
    clickAddLang(item) {
      this.langList.push(this.createLangObj(item.value, item.name))
    },
    clickDelLang(item, index) {
      this.langList.splice(index, 1)
      this.textChangeEmit()
    },
    existsLang(val) {
      return this.langList.some(item => item.lang === val)
    },
    inputEvent() {
      this.textChangeEmit()
    },
    textChangeEmit() {
      this.$emit('text-change', this.langList || [])
    }
  }
}
</script>

<style scoped lang="scss">

</style>
