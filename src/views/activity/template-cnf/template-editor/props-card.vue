<template>
  <div class="props-card" :class="className">
    <div class="reward-card flex-c flex-wrap-wrap">
      <div v-for="(item, index) in list" :key="index" class="reward-item" :style="cardStyle.cardStyle">
        <div class="reward-props-name nowrap-ellipsis" :style="cardStyle.topStyle">
          {{ getPropsName(item) }}
        </div>
        <div class="reward-cover flex-c" :style="cardStyle.centerStyle">
          <el-image v-if="item.type === 'GOLD'" src="@/assets/gold_icon.png" height="1.3rem" width="1.3rem" alt="img">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline" />
            </div>
          </el-image>

          <el-image v-else-if="item.type === 'DIAMOND'" src="@/assets/diamond_icon.png" height="1.3rem" width="1.3rem" alt="img">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline" />
            </div>
          </el-image>

          <el-image v-else-if="item.type === 'SPECIAL_ID'" src="@/assets/special_id.png" height="1.3rem" width="1.3rem" alt="img">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline" />
            </div>
          </el-image>

          <el-image v-else-if="item.type === 'GAME_COUPON'" src="@/assets/game_coupon.png" height="1.3rem" width="1.3rem" alt="img">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline" />
            </div>
          </el-image>
          <el-image v-else :src="item.cover" height="1.3rem" width="1.3rem" alt="img">
            <template v-slot:loading>
              <van-loading type="spinner" size="20" />
            </template>
          </el-image>
        </div>
        <div class="reward-props-quantity nowrap-ellipsis" :style="cardStyle.bottomStyle">
          {{ formatPropsContent(item) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PropsCard',
  props: {
    className: {
      type: String,
      default: 'default-card'
    },
    cardStyle: {
      type: Object,
      default: () => {
        return {
          cardStyle: 'background-image: linear-gradient(180deg, #5FE3E9 0%, #55A1D8 100%);',
          topStyle: 'font-size: .35rem; color: #333; ',
          centerStyle: 'background-color: #2b8fb4;',
          bottomStyle: 'font-size: .35rem; color: #e1f8ee; '
        }
      }
    },
    // {
    //    type: '', detailType:'', amount: ''
    // }
    list: {
      type: Array,
      require: true,
      default: () => []
    },
    lang: {
      type: String,
      require: false,
      default: 'zh'
    }
  },
  data() {
    return {
      propsText: {
        'zh': {
          'GIFT': '礼物',
          'DIAMOND': '钻石',
          'GOLD': '金币',
          'ROOM_BADGE': '房间徽章',
          'BADGE': '个人徽章',
          'SPECIAL_ID': '靓号',
          'NOBLE_VIP': '贵族VIP',
          'AVATAR_FRAME': '头像框',
          'RIDE': '座驾',
          'THEME': '房间背景',
          'EMOJI': '表情包',
          'CHAT_BUBBLE': '聊天气泡',
          'GAME_COUPON': '游戏券',
          'FRAGMENTS': '碎片',
          'FLOAT_PICTURE': '个人飘窗',
          'LAYOUT': '房间装扮',
          'CUSTOMIZE': '自定义'
        },
        en: {
          'GIFT': 'Gift',
          'DIAMOND': 'Diamond',
          'GOLD': 'Gold',
          'ROOM_BADGE': 'Room Badge',
          'BADGE': 'Badge',
          'SPECIAL_ID': 'Special ID',
          'NOBLE_VIP': 'VIP',
          'AVATAR_FRAME': 'Frame',
          'RIDE': 'Car',
          'THEME': 'Background',
          'EMOJI': 'Emoji',
          'CHAT_BUBBLE': 'Bubble',
          'GAME_COUPON': 'Game Coupon',
          'FRAGMENTS': 'Fragment',
          'FLOAT_PICTURE': 'Effects',
          'LAYOUT': 'Theme',
          'CUSTOMIZE': 'Customize'
        },
        ar: {
          'GIFT': 'هدية',
          'DIAMOND': 'الماس',
          'GOLD': 'عملة ذهبية',
          'ROOM_BADGE': 'لقب الغرفة',
          'BADGE': 'لقب شخصي',
          'SPECIAL_ID': 'أيدي مميز',
          'NOBLE_VIP': 'أرستقراطية',
          'AVATAR_FRAME': 'إطار',
          'RIDE': 'دخولية',
          'THEME': 'خلفية غرفة',
          'EMOJI': 'إيموجي',
          'CHAT_BUBBLE': 'فقاعة',
          'GAME_COUPON': 'قسيمة لعبة',
          'FRAGMENTS': 'شظية',
          'FLOAT_PICTURE': 'تأثيرات',
          'LAYOUT': 'شكل الغرفة',
          'CUSTOMIZE': 'يعدل أو يكيف'
        },
        tr: {
          'GIFT': 'Hediye',
          'DIAMOND': 'elmas',
          'GOLD': 'altın',
          'ROOM_BADGE': 'oda rozeti',
          'BADGE': 'kişisel rozet',
          'SPECIAL_ID': 'Liang',
          'NOBLE_VIP': 'asil VIP',
          'AVATAR_FRAME': 'avatar çerçevesi',
          'RIDE': 'araba',
          'THEME': 'oda arka planı',
          'EMOJI': 'emoji',
          'CHAT_BUBBLE': 'sohbet balonu',
          'GAME_COUPON': 'oyun kuponu',
          'FRAGMENTS': 'parça',
          'FLOAT_PICTURE': 'kişisel cumba',
          'LAYOUT': 'oda giyinmek',
          'CUSTOMIZE': 'özelleştirme'
        },
        id: {
          'GIFT': 'Hadiah',
          'DIAMOND': 'berlian',
          'GOLD': 'emas',
          'ROOM_BADGE': 'lencana kamar',
          'BADGE': 'lencana pribadi',
          'SPECIAL_ID': 'Liang',
          'NOBLE_VIP': 'VIP Mulia',
          'AVATAR_FRAME': 'bingkai avatar',
          'RIDE': 'mobil',
          'THEME': 'latar belakang ruangan',
          'EMOJI': 'Emoji',
          'CHAT_BUBBLE': 'gelembung obrolan',
          'GAME_COUPON': 'kupon permainan',
          'FRAGMENTS': 'Pecahan',
          'FLOAT_PICTURE': 'jendela teluk pribadi',
          'LAYOUT': 'kamar berdandan',
          'CUSTOMIZE': 'menyesuaikan'
        }
      },
      propsTypeFormat: {
        'GIFT': {
          prefix: 'x',
          field: 'quantity',
          suffix: ''
        },
        'DIAMOND': {
          prefix: '',
          field: 'content',
          suffix: ''
        },
        'GOLD': {
          prefix: '',
          field: 'content',
          suffix: ''
        },
        'ROOM_BADGE': {
          prefix: '',
          field: 'quantity',
          suffix: 'D',
          forever: true
        },
        'BADGE': {
          prefix: '',
          field: 'quantity',
          suffix: 'D',
          forever: true
        },
        'SPECIAL_ID': {
          prefix: '',
          field: 'content',
          suffix: ''
        },
        'NOBLE_VIP': {
          prefix: '',
          field: 'quantity',
          suffix: 'D'
        },
        'AVATAR_FRAME': {
          prefix: '',
          field: 'quantity',
          suffix: 'D'
        },
        'RIDE': {
          prefix: '',
          field: 'quantity',
          suffix: 'D'
        },
        'THEME': {
          prefix: '',
          field: 'quantity',
          suffix: 'D'
        },
        'EMOJI': {
          prefix: 'x',
          field: 'quantity',
          suffix: ''
        },
        'CHAT_BUBBLE': {
          prefix: '',
          field: 'quantity',
          suffix: 'D'
        },
        'GAME_COUPON': {
          prefix: '',
          field: 'amount',
          suffix: ''
        },
        'FRAGMENTS': {
          prefix: 'x',
          field: 'quantity',
          suffix: ''
        },
        'FLOAT_PICTURE': {
          prefix: '',
          field: 'quantity',
          suffix: 'D'
        },
        'LAYOUT': {
          prefix: 'x',
          field: 'quantity',
          suffix: ''
        }
      }
    }
  },
  computed: {
  },
  created() {

  },
  methods: {
    getStyle(style) {
      return `color: ${style.fontColor}; font-size: ${style.fontSize}; background-color: ${style.backgroundColor}`
    },
    getPropsName(item) {
      if (item.detailType === 'CUSTOMIZE' && item.remark) {
        return item.remark.indexOf('#') >= 0 ? item.remark.trim().split('#')[0] : item.remark
      }
      const text = this.propsText[this.lang]
      const name = text[item.detailType]
      if (!name) {
        const nameRoot = text[item.type]
        return nameRoot || '?'
      }
      return name
    },
    formatPropsContent(item) {
      if (item.detailType === 'CUSTOMIZE' && item.remark) {
        return item.remark.indexOf('#') >= 0 ? `${item.remark.trim().split('#')[1]}D` : '?'
      }

      const format = this.propsTypeFormat[item.detailType] || this.propsTypeFormat[item.type]

      if (!format) {
        return '?'
      }

      const val = item[format.field]
      if (format.forever === true && val <= 0) {
        return '永久'
      }

      return `${format.prefix}${val}${format.suffix}`
    }

  }
}
</script>

<style scoped lang="scss">
.default-card {
  // background-image: linear-gradient(to right, #e4f6f9 0%, #fff7ee 100%);
}
.props-card {
  position: relative;
  .reward-card {
    height: 100%;
    // padding: .26rem;
    .reward-item {
      width: 2.6rem;
      margin: .1rem;
      text-align: center;
      color: #333333;
      background-image: linear-gradient(180deg, #5FE3E9 0%, #55A1D8 100%);
      border-radius: .1rem;
      overflow: hidden;
      .reward-props-name {
        padding: .1rem;
        font-size: .35rem;
        color: #333333;
      }
      .reward-cover {
        position: relative;
        background: #2B8FB4;
        margin: .1rem .2rem;
        border-radius: .15rem;
        cursor: pointer;
        height: 1.5rem;
        padding: .1rem;
      }
      .reward-props-quantity {
        font-size: .35rem;
        padding: .1rem;
        color: #E1F8EE;
        .icon-gold {
          background-image: url('~@/assets/gold_icon.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
          height: 0.5rem;
          width: 0.5rem;
          margin: 0rem .05rem;
        }
        .icon-diamond {
          background-image: url('~@/assets/diamond_icon.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
          height: 0.5rem;
          width: 0.5rem;
          margin: 0rem .05rem;
        }
      }
    }
  }
}
</style>
