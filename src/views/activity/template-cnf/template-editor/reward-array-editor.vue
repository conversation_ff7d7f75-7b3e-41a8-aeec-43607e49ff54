<template>
  <div class="reward-array-editor">
    <div class="lang-content">
      <el-row v-for="(item, index) in list" :key="index" style="margin-bottom: 10px;">
        <el-col :span="20">
          <el-input v-model="item.value" @input="inputEvent" />
        </el-col>
        <el-col :span="4" style="text-align: center;">
          <i class="el-icon-delete-solid cursor-pointer font-danger" style="font-size: 18px;" @click="clickDelLang(item,index)" />
        </el-col>
      </el-row>
    </div>
    <div v-if="list.length < 10" class="add-but">
      <el-button type="text" size="mini" @click.native="clickAdd(item)">
        添加<i class="el-icon-circle-plus" />
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RewardArrayEditor',
  props: {
    rewards: {
      type: Array,
      require: true,
      default: () => []
    }
  },
  data() {
    return {
      list: []
    }
  },
  watch: {
    rewards: {
      handler(newVal) {
        if (newVal) {
          this.list = newVal.map(item => {
            return { value: item }
          })
        }
      },
      immediate: true
    }
  },
  created() {

  },
  methods: {
    clickAdd() {
      this.list.push({ value: '' })
    },
    clickDelLang(item, index) {
      this.list.splice(index, 1)
      this.rewardChangeEmit()
    },
    inputEvent() {
      this.rewardChangeEmit()
    },
    rewardChangeEmit() {
      this.$emit('reward-change', this.list.map(item => item.value))
    }
  }
}
</script>

<style scoped lang="scss">

</style>
