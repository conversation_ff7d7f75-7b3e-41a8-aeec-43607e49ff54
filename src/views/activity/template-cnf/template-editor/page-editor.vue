<template>
  <div ref="INDEX" class="template-layout" :style="renderStyle('INDEX')" :dir="dirVal">
    <div class="head">
      <div ref="TOP" class="top" :style="renderStyle('TOP')">
        <div ref="RULE" class="rule" :style="renderStyle('RULE')" @click="showRule = true">
          {{ getLangText('RULE') }}
        </div>
        <div class="layout-count-down">
          <div ref="COUNT_DOWN" :style="renderStyle('COUNT_DOWN')" class="count-down" dir="ltr">
            <div class="flex-c">
              <div v-for="(item, index) in countDowns" :key="index" class="count-down-text">
                <div :ref="item.block" :style="renderStyle(item.block)" class="block flex-c">0</div>
                <div :ref="item.label" :style="renderStyle(item.label)" class="label">{{ getLangText(item.label) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div ref="ACTIVIY_GIFT" class="activity-gift" :style="renderStyle('ACTIVIY_GIFT')">
        <div ref="ACTIVIY_GIFT_TITLE" :style="renderStyle('ACTIVIY_GIFT_TITLE')" class="title">{{ getLangText('ACTIVIY_GIFT_TITLE') }}</div>
        <div class="flex-c gift-content">
          <div class="gift">
            <div class="gift-info">
              <div ref="ACTIVIY_GIFT_BODY_GIFT_1" :style="renderStyle('ACTIVIY_GIFT_BODY_GIFT_1')" class="gift-cover flex-c">
                <img src="@/assets/default_img.png" alt="">
              </div>
              <div class="amount flex-c">
                <img v-for="(item, index) in getAttrImgUrls('ACTIVIY_GIFT_BODY_GIFT_1_ICON')" ref="ACTIVIY_GIFT_BODY_GIFT_1_ICON" :key="index" :src="item.url" width="20" style="margin: 0px 5px;" alt="">
                <span ref="ACTIVIY_GIFT_BODY_GIFT_1_TEXT" :style="renderStyle('ACTIVIY_GIFT_BODY_GIFT_1_TEXT')">0</span>
              </div>
            </div>
          </div>
          <div class="gift">
            <div class="gift-info">
              <div ref="ACTIVIY_GIFT_BODY_GIFT_2" :style="renderStyle('ACTIVIY_GIFT_BODY_GIFT_2')" class="gift-cover">
                <img src="@/assets/default_img.png" alt="">
              </div>
              <div class="amount flex-c">
                <img v-for="(item, index) in getAttrImgUrls('ACTIVIY_GIFT_BODY_GIFT_2_ICON')" ref="ACTIVIY_GIFT_BODY_GIFT_2_ICON" :key="index" :src="item.url" width="20" style="margin: 0px 5px;" alt="">
                <span ref="ACTIVIY_GIFT_BODY_GIFT_2_TEXT" :style="renderStyle('ACTIVIY_GIFT_BODY_GIFT_2_TEXT')">0</span>
              </div>
            </div>
          </div>
          <div class="gift">
            <div class="gift-info">
              <div ref="ACTIVIY_GIFT_BODY_GIFT_3" :style="renderStyle('ACTIVIY_GIFT_BODY_GIFT_3')" class="gift-cover">
                <img src="@/assets/default_img.png" alt="">
              </div>
              <div class="amount flex-c">
                <img v-for="(item, index) in getAttrImgUrls('ACTIVIY_GIFT_BODY_GIFT_3_ICON')" ref="ACTIVIY_GIFT_BODY_GIFT_3_ICON" :key="index" :src="item.url" width="20" style="margin: 0px 5px;" alt="">
                <span ref="ACTIVIY_GIFT_BODY_GIFT_3_TEXT" :style="renderStyle('ACTIVIY_GIFT_BODY_GIFT_3_TEXT')">0</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div ref="ACTIVITY_BUT" class="operation-but flex-c">
        <div ref="ACTIVITY_BUT_1" :style="renderStyle(selectedBut === 'WEALTH' ? 'ACTIVITY_BUT' :'ACTIVITY_BUT_1')" class="but" @click="clickBut('WEALTH')">{{ getLangText('ACTIVITY_BUT_1') }}</div>
        <div ref="ACTIVITY_BUT_2" :style="renderStyle(selectedBut === 'CHARM' ? 'ACTIVITY_BUT' :'ACTIVITY_BUT_2')" class="but" @click="clickBut('CHARM')">{{ getLangText('ACTIVITY_BUT_2') }}</div>
      </div>
    </div>
    <div ref="ACTIVITY_BODY" class="content" :style="renderStyle('ACTIVITY_BODY')">
      <div ref="ACTIVITY_BODY_BUT" class="operation-but-child flex-c">
        <div ref="ACTIVITY_BODY_BUT_1" class="but" :style="renderStyle(selectedButChild === 'RANKING' ? 'ACTIVITY_BODY_BUT' :'ACTIVITY_BODY_BUT_1')" @click="clickButChild('RANKING')">{{ getLangText('ACTIVITY_BODY_BUT_1') }}</div>
        <div ref="ACTIVITY_BODY_BUT_2" class="but" :style="renderStyle(selectedButChild === 'REWARD' ? 'ACTIVITY_BODY_BUT' :'ACTIVITY_BODY_BUT_2')" @click="clickButChild('REWARD')">{{ getLangText('ACTIVITY_BODY_BUT_2') }}</div>
      </div>
      <div v-show="selectedButChild === 'RANKING'">
        <div class="ranking">
          <div v-for="index in 4" :key="index" ref="ACTIVITY_BODY_RING" class="ranking-user" :style="renderStyle('ACTIVITY_BODY_RING')">
            <div ref="ACTIVITY_BODY_RING_BAG" class="gold_medal_icon">
              <img v-if="index<4" class="gold_medal" :src="getAttrImgUrls('ACTIVITY_BODY_RING_BAG')[index-1] ? getAttrImgUrls('ACTIVITY_BODY_RING_BAG')[index-1].url : ''">
              <span v-else>{{ index }}</span>
            </div>
            <img ref="ACTIVITY_BODY_RING_AVATAR" class="avatar" src="@/assets/default_avatar.png">
            <div ref="ACTIVITY_BODY_RING_NICKNAME" :style="renderStyle('ACTIVITY_BODY_RING_NICKNAME')" class="nickanme nowrap-ellipsis">用户昵称-1</div>
            <div ref="ACTIVITY_BODY_RING_AMOUNT" :style="renderStyle('ACTIVITY_BODY_RING_AMOUNT')" class="golds nowrap-ellipsis">100.1K</div>
            <img v-for="(imgItem, imgIndex) in getAttrImgUrls('ACTIVITY_BODY_RING_ICON')" ref="ACTIVITY_BODY_RING_ICON" :key="imgIndex" class="gift_pack" :src="imgItem.url">
          </div>
        </div>
      </div>
      <div v-show="selectedButChild==='REWARD'">
        <div ref="ACTIVITY_BODY_REWARD" class="reward-card" :style="renderStyle('ACTIVITY_BODY_REWARD')">
          <div class="title"><div ref="ACTIVITY_BODY_REWARD_TITLE" :style="renderStyle('ACTIVITY_BODY_REWARD_TITLE')" class="border nowrap-ellipsis">TOP 1</div></div>
          <div class="reward">
            <div class="reward-users">
              <img v-for="userItem in 5" :key="userItem" class="avatar" src="@/assets/default_avatar.png">
            </div>
            <div ref="ACTIVITY_BODY_REWARD_CARD">
              <props-card :list="propsCards" :lang="lang" :card-style="cardStyle" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div ref="COPYRIGHT" class="copyright" :style="renderStyle('COPYRIGHT')">
      {{ getLangText('COPYRIGHT') }}
    </div>
    <div ref="ACTIVITY_MY" :style="renderStyle('ACTIVITY_MY')" class="flex-user flex-l">
      <img ref="ACTIVITY_MY_AVATAR" class="avatar" src="@/assets/default_avatar.png">
      <div ref="ACTIVITY_MY_NICKNAME" :style="renderStyle('ACTIVITY_MY_NICKNAME')" class="nickname nowrap-ellipsis">用户昵称</div>
      <div ref="ACTIVITY_MY_TEXT" :style="renderStyle('ACTIVITY_MY_TEXT')" class="golds">
        <div class="nowrap-ellipsis">{{ getLangText('ACTIVITY_BUT_1') }}: 100K</div>
        <div class="nowrap-ellipsis">{{ getLangText('ACTIVITY_BUT_2') }}: 100k</div>
      </div>
      <img v-for="(imgItem, imgIndex) in getAttrImgUrls('ACTIVITY_MY_ICON')" ref="ACTIVITY_MY_ICON" :key="imgIndex" class="gift_pack" :src="imgItem.url">
    </div>

    <el-dialog
      :visible.sync="showRule"
      width="320px"
      :show-close="false"
      :center="true"
      :close-on-click-modal="false"
    >
      <div ref="RULE_CONTENT" class="rule-desc" :style="renderStyle('RULE_CONTENT')">
        <div class="rule-title">
          {{ getLangText('RULE') }}
        </div>
        <div class="rule-content" v-html="getLangText('RULE_CONTENT') " />
      </div>
      <span slot="footer" class="dialog-footer">
        <div ref="RULE_BUTTON" :style="renderStyle('RULE_BUTTON')" class="cursor-pointer" @click="showRule = false">OK</div>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { isArray } from '@/utils'
import PropsCard from './props-card'

export default {
  name: 'PageEditor',
  components: { PropsCard },
  props: {
    lang: {
      type: String,
      require: false,
      default: 'zh'
    },
    selectElId: {
      type: String,
      require: false,
      default: ''
    },
    // { 'elId': {} }
    indexData: {
      type: Object,
      require: true,
      default: () => {}
    }
  },
  data() {
    return {
      selectedBut: 'CHARM',
      selectedButChild: 'RANKING',
      showRule: false,
      countDowns: [
        {
          block: 'COUNT_DOWN_NUMBER',
          label: 'COUNT_DOWN_TEXT_DAY'
        },
        {
          block: 'COUNT_DOWN_NUMBER',
          label: 'COUNT_DOWN_TEXT_HOURS'
        },
        {
          block: 'COUNT_DOWN_NUMBER',
          label: 'COUNT_DOWN_TEXT_MINUTES'
        },
        {
          block: 'COUNT_DOWN_NUMBER',
          label: 'COUNT_DOWN_TEXT_SECONDS'
        }
      ],
      imgUrlBase: 'http://img.sugartimeapp.com',
      propsCards: [
        {
          'id': '1493107873717178369',
          'groupId': '1493103725420498945',
          'type': 'PROPS',
          'detailType': 'NOBLE_VIP',
          'content': '1403650373974679654',
          'sort': 1,
          'cover': 'http://img.sugartimeapp.com/svga_cover/manager-3501b436-785c-414c-86bf-ffa64d833805.png',
          'quantity': 7,
          'amount': '231000'
        },
        {
          'id': '1493107873717178370',
          'groupId': '1493103725420498945',
          'type': 'BADGE',
          'detailType': 'BADGE',
          'content': '1476808638933434369',
          'sort': 2,
          'cover': 'http://img.sugartimeapp.com/other/manager-421e05f8-0431-4fd1-b7b0-198c18f93bb6.png',
          'quantity': 7,
          'amount': '0'
        },
        {
          'id': '1493107873717178371',
          'groupId': '1493103725420498945',
          'type': 'PROPS',
          'detailType': 'AVATAR_FRAME',
          'content': '1493101108250304513',
          'sort': 3,
          'cover': 'http://img.sugartimeapp.com/svga_cover/manager-f8fa5f45-36f1-4708-bf8f-815372334a26.png',
          'quantity': 7,
          'amount': '69993'
        },
        {
          'id': '1493107873717178372',
          'groupId': '1493103725420498945',
          'type': 'PROPS',
          'detailType': 'RIDE',
          'content': '1466716137258098690',
          'sort': 4,
          'cover': 'http://img.sugartimeapp.com/svga_cover/manager-65434a8e-a8ca-4000-a7c7-57d052e3f165.png',
          'quantity': 7,
          'amount': '699993'
        }
      ],
      cardStyle: {
        cardStyle: '',
        topStyle: '',
        centerStyle: '',
        bottomStyle: ''
      }
    }
  },
  computed: {
    dirVal() {
      return this.lang === 'ar' ? 'rtl' : 'ltr'
    }
  },
  watch: {
    selectElId: {
      handler(newVal) {
        this.switchSeletBorder(newVal)
        this.showRule = newVal === 'RULE_CONTENT' || newVal === 'RULE_BUTTON'
        this.selectedButChild = newVal && newVal.startsWith('ACTIVITY_BODY_REWARD') ? 'REWARD' : 'RANKING'
      },
      immediate: true
    },
    indexData: {
      handler(newVal) {
        this.cardStyle = {
          cardStyle: this.renderStyle('ACTIVITY_BODY_REWARD_CARD'),
          topStyle: this.renderStyle('ACTIVITY_BODY_REWARD_CARD_TOP'),
          centerStyle: this.renderStyle('ACTIVITY_BODY_REWARD_CARD_CENTER'),
          bottomStyle: this.renderStyle('ACTIVITY_BODY_REWARD_CARD_BOTTOM')
        }
      },
      immediate: true
    }
  },
  created() {
  },
  methods: {
    getLangText(elId) {
      const that = this
      const elData = that.indexData[elId]
      if (!elData) {
        return ''
      }

      const attr = elData.attr
      if (!attr) {
        return ''
      }

      const copywriting = elData.attr.copywriting
      if (!copywriting) {
        return ''
      }
      const i18n = copywriting.i18n
      if (!i18n) {
        return
      }
      for (let index = 0; index < i18n.length; index++) {
        const i18nItem = i18n[index]
        if (i18nItem.lang === that.lang) {
          return i18nItem.value
        }
      }

      for (let index = 0; index < i18n.length; index++) {
        const i18nItem = i18n[index]
        if (i18nItem.lang === 'en') {
          return i18nItem.value
        }
      }
      return ''
    },
    getAttrImgUrls(elId) {
      const that = this
      const elData = that.indexData[elId]
      if (!elData) {
        return []
      }

      const attr = elData.attr
      if (!attr) {
        return []
      }
      return attr.imgUrls || []
    },
    renderStyle(elId) {
      const that = this
      const elData = that.indexData[elId]
      if (!elData) {
        return ''
      }

      const style = elData.style
      if (!style) {
        return ''
      }

      const { background, font, border } = style
      let styleValue = ''
      if (background) {
        const { img, color } = background
        if (img) {
          const { url, repeat, size } = img
          if (url) {
            styleValue += `background-image: url(${url});`
            if (repeat) {
              styleValue += `background-repeat: ${repeat};`
            }
            if (size) {
              styleValue += `background-size: ${size};`
            }
          }
        }

        if (color) {
          const { start, direction, end } = color
          if ((!img || !img.url) && (start && direction !== 'NONE' && end)) {
            styleValue += `background-image: linear-gradient(${direction}, ${start} 0%, ${end} 100%);`
          } else if ((!direction || direction === 'NONE') && (start || end)) {
            styleValue += `background-color: ${start || end};`
          }
        }
      }

      if (font) {
        const { color, size, weight } = font
        if (color) {
          styleValue += `color: ${color};`
        }
        if (size) {
          styleValue += `font-size: ${size};`
        }
        if (weight) {
          styleValue += `font-weight: ${weight};`
        }
      }

      if (border) {
        const { width, color, style, radius } = border
        if (width) {
          styleValue += `border-width: ${width};`
        }
        if (color) {
          styleValue += `border-color: ${color};`
        }
        if (style) {
          styleValue += `border-style: ${style};`
        }
        if (radius) {
          styleValue += `border-radius: ${radius};`
        }
      }
      return styleValue
    },
    clickBut(type) {
      this.selectedBut = type
    },
    clickButChild(type) {
      this.selectedButChild = type
    },
    goAnchor(selector) {
      document.querySelector(selector).scrollIntoView({
        behavior: 'smooth'
      })
    },
    switchSeletBorder(elId) {
      const that = this

      that.$nextTick(() => {
        const refs = that.$refs
        if (refs) {
          for (const key in refs) {
            const ref = refs[key]
            if (isArray(ref)) {
              ref.forEach(el => {
                if (that.existSelectedBorderClassName(el.className)) {
                  el.className = el.className.replaceAll('selected-border', '').trim()
                }
              })
              continue
            }

            if (that.existSelectedBorderClassName(ref.className)) {
              ref.className = ref.className.replaceAll('selected-border', '').trim()
            }
          }
        }

        if (!elId) {
          return
        }
        that.switchSeletBorderClassName(elId)
      })
    },
    existSelectedBorderClassName(className) {
      return className && String(className).indexOf('selected-border') >= 0
    },
    switchSeletBorderClassName(elId) {
      const refs = this.$refs
      if (elId.startsWith('ACTIVITY_BODY_REWARD_CARD')) {
        elId = 'ACTIVITY_BODY_REWARD_CARD'
      }
      const ref = refs[elId]
      if (!ref) {
        return
      }
      if (isArray(ref)) {
        ref.forEach(el => {
          el.className = el.className ? el.className + ' selected-border' : 'selected-border'
        })
        return
      }
      ref.className = ref.className ? ref.className + ' selected-border' : 'selected-border'
    },
    getRewardUserSize(item) {
      if (!item) {
        return 0
      }
      const rex = /\d+/
      if (item.indexOf('~') >= 0) {
        const nums = item.split('~')
        const first = rex.test(nums[0]) ? nums[0] : 0
        const last = rex.test(nums[1]) ? nums[1] : 0
        const num = Number(last) - Number(first)
        return num >= 0 ? num : 0
      }
      if (!rex.test(item)) {
        return 0
      }
      return Number(item)
    }
  }
}
</script>
<style lang="scss">
.template-layout {
  .el-dialog__body {
    padding: 0rem !important;
  }
  .el-dialog__header {
    padding: 0rem !important;
  }
  .el-dialog__footer {
    padding: 0rem !important;
    line-height: .8rem;
  }
}
</style>
<style scoped lang="scss">
  .template-layout {
    position: relative;
    min-height: 100%;
    font-family: PingFangSC-Medium;
    padding-bottom: 1rem;
    // background: #392110;
    cursor: pointer;
    .head {
      position: relative;
      .top {
        position: relative;
        height: 10rem;
        // background-image: url("#{$img-base-url}/web/aswat_king_queen_bg.png");
        // background-size: cover;
        // background-repeat: no-repeat;
        overflow: hidden;
        .rule {
          // color: #FFFFFF;
          // background-image: linear-gradient(180deg, #E22123 0%, #F98F48 100%);
          line-height: .8rem;
          min-width: 1.8rem;
          text-align: center;
          // border-radius: 1rem;
          position: absolute;
          right: -.35rem;
          top: 5.8rem;
          cursor: pointer;
        }
        .layout-count-down {
            position: absolute;
            left: 0rem;
            right: 0rem;
            bottom: 1.1rem;
            text-align: center;
            .count-down {
                display: inline-block;
                vertical-align: middle;
                text-align: center;
                // color: #FFFFFF;
                padding: .3rem 0rem;
                // background: #8D0502;
                min-width: 50%;
                max-width: 100%;
                // border-radius: .1rem;
                // border: .02rem solid #FFFFFF;
                .count-down-text {
                    padding: 0px .3rem;
                    .label {
                        // color: #fff8db;
                        margin-top: .15rem;
                    }
                    .block {
                        width: 1.11rem;
                        height: 1.11rem;
                        padding: .08rem .2rem;

                        // color: #FFFFFF;
                        text-align: center;
                        // background-image: linear-gradient(#FDCC4C, #F86711);
                        // border-radius: .1rem;
                    }
                }
            }
        }
      }
      .activity-gift {
        // border: .02rem solid #d3be87;
        // border-radius: .1rem;
        width: 95%;
        margin: -.5rem auto .1rem auto;
        color: #d3be87;
        padding: .2rem;
        position: relative;
        z-index: 1;
        .title {
          // border: .02rem solid #d3be87;
          // background-color: #392110;
          //border-radius: .1rem;
          padding: .2rem;
          width: 50%;
          text-align: center;
          margin: -.6rem auto;
        }
        .gift-content {
          padding: .8rem .05rem .05rem .05rem;
          .gift {
            .gift-info {
              margin: 15px;
              .gift-cover {
                // border: .02rem solid #d3be87;
                // border-radius: .1rem;
                padding: .15rem;
                img {
                  width: 1.8rem;
                  height: 1.8rem;
                }
              }
             .amount {
              text-align: center;
              padding: .1rem;
              span {
                padding: 0rem .05rem;
              }
             }
            }
          }
        }

      }
      .operation-but {
        padding: .5rem 0rem;
        .but {
          // border: .05rem solid #FFFFFF;
          // border-radius: .4rem;
          padding: .2rem;
          // color: #FFFFFF;
          margin: 0rem .2rem;
          cursor: pointer;
           min-width: 4rem;
          text-align: center;
        }
      }
    }

    .content {
      position: relative;
      width: 95%;
      padding: .4rem;
      margin: .2rem auto auto auto;
      // background: #1F1209;
      // border-top-left-radius: .5rem;
      // border-top-right-radius: .5rem;
      .operation-but-child {
        // border: .01rem solid #FFFFFF;
        width: 60%;
        margin: auto auto .5rem auto;
        border-radius: .1rem;
        overflow: hidden;
        .but {
          padding: .2rem;
          // color: #FFFFFF;
          cursor: pointer;
          width: 100%;
          text-align: center;
        }
      }
      .reward-card {
       // background-color: #2b221b;
       // border-radius: .1rem;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        position: relative;
        margin: 1rem 0rem;
        .title {
          position: absolute;
          top: -.4rem;
          width: 100%;
          // color: #FFFFFF;
          text-align: center;
          .border {
            // border: .02rem solid #D3BE87;
            //border-radius: .1rem;
            background: #392110;
            display: inline-block;
            vertical-align: middle;
            text-align: center;
            padding: .2rem;
            min-width: 50%;
          }
        }
        .reward {
          height: 100%;
          padding: .66rem .36rem .3rem .36rem;
          .reward-users {
            img {
              width: .6rem;
              height: .6rem;
              margin: .1rem;
              border-radius: 50%;
            }
          }
        }
      }
      .ranking {
        border-radius: .1rem;
        .ranking-user {
            padding: .2rem;
            display: flex;
            align-items: center;
            cursor: pointer;
            margin-bottom: .23rem;
            // background: #392110;
            // border: .02rem solid #D3BE87;
            // border-radius: .2rem;
            .avatar {
                width: 1rem;
                height: 1rem;
                margin: 0rem .2rem;
                flex-shrink: 0;
                border-radius: 50%;
            }
            .nickanme {
                width: 45%;
                // color: #FFFFFF;
            }
            .golds {
                // color: #DCC79C;
                padding: 0px .2rem;
                width: 25%;
            }
            .gold_medal_icon {
                width: 30px;
                text-align: center;
                font-weight: bold;
                color: #DCC79C;
            }
            .gold_medal {
                width: .6rem;
                flex-shrink: 0;
            }
            .gift_pack {
                flex-shrink: 0;
                width: .6rem;
                height: .6rem;
            }
        }
     }
    }
  .rule-desc {
    .rule-title {
      font-weight: bold;
      text-align: center;
    }
    // color: #FFFFFF;
    padding: .2rem;
    // background: #1f1309;
    .rule-content {
      line-height: .6rem;
    }
  }

  .flex-user {
    position: absolute;
    // background: linear-gradient(138deg, #967245 0%, #1f1309 100%);
    bottom: 0px;
    width: 100%;
    // color: #FFFFFF;
    border-top-left-radius: .3rem;
    border-top-right-radius: .3rem;
    padding: .2rem .1rem;
    .avatar {
      margin: 0rem .2rem;
      flex-shrink: 0;
      width: 1rem;
      height: 1rem;
      border-radius: 50%;
    }
    .nickname {
      // color: #FFFFFF;
      width: 70%
    }
    .golds {
      width: 30%;
      // color: #FFFFFF;
      padding: 0rem .2rem;
      text-align: left;
    }
    .gift_pack {
      flex-shrink: 0;
      width: 1rem;
      height: 1rem;
    }
  }
  .copyright {
    padding: 1rem .2rem;
    text-align: center;
    // color: #DCC79C;
    // font-weight: bold;
  }
  .but-select {
    background: #FFFFFF;
    color: #8D0502 !important;
  }
}
.selected-border {
  border: 2px dashed #409EFF !important;
}
</style>
