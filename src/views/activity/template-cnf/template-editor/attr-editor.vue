<template>
  <div class="attr-editor">
    <el-tabs v-model="activeName" size="mini">
      <el-tab-pane v-if="attrForm" label="属性" name="attr">
        <el-form ref="form" class="form" :model="attrForm" label-width="90px" size="mini">
          <el-form-item v-if="attrForm.id" label="ID">
            {{ attrForm.id }}
          </el-form-item>
          <el-form-item v-if="attrForm.label" label="组件名称">
            {{ attrForm.label }}
          </el-form-item>
          <el-form-item v-if="hasOwnProperty(attrForm,'copywriting')" :label="attrForm.copywriting.label">
            <lang-text-editor :lang-array="attrForm.copywriting.i18n" :type="attrForm.copywriting.type" @text-change="langChange" />
          </el-form-item>
          <el-form-item v-if="hasOwnProperty(attrForm,'imgUrls')" label-width="0">
            <el-form-item v-for="(item, index) in attrForm.imgUrls" :key="index" :label="item.label ? item.label : '图片'+index">
              <el-input v-model="item.url" clearable @input="changeNotice" />
            </el-form-item>
          </el-form-item>
          <!-- <el-form-item v-if="hasOwnProperty(attrForm,'rewards')" label-width="0">
            <el-form-item label="奖励阶梯">
              <reward-array-editor :rewards="attrForm.rewards" @reward-change="rewardChange" />
            </el-form-item>
          </el-form-item> -->
        </el-form>
      </el-tab-pane>
      <el-tab-pane v-if="styleForm && !styleForm.hide" label="样式" name="style">
        <el-alert
          v-if="styleForm.tips"
          :title="styleForm.tips"
          type="info"
          size="mini"
          effect="dark"
          :closable="false"
          style="margin-bottom: 20px"
          show-icon
        />
        <el-form ref="form" class="form" :model="styleForm" label-width="90px" size="mini">
          <div class="blockquote blockquote-mini">背景</div>
          <el-form-item v-if="hasOwnProperty(styleForm,'background.img.url')" label="背景图">
            <el-input v-model="styleForm.background.img.url" clearable @input="changeNotice" />
          </el-form-item>
          <el-form-item v-if="hasOwnProperty(styleForm,'background.color')" label="背景颜色">
            <el-col :span="4" class="flex-c">
              <el-color-picker v-model="styleForm.background.color.start" @change="changeNotice" />
            </el-col>
            <el-col :span="styleForm.background.color.direction !== 'NONE' ? 16 : 20">
              <el-select
                v-model="styleForm.background.color.direction"
                style="width: 100%;"
                @change="changeNotice"
              >
                <el-option v-for="item in backgroundColorDirections" :key="item.value" :label="item.name" :value="item.value" />
              </el-select>
            </el-col>
            <el-col v-if="styleForm.background.color.direction !== 'NONE'" :span="4" class="flex-c">
              <el-color-picker v-model="styleForm.background.color.end" @change="changeNotice" />
            </el-col>
          </el-form-item>
          <el-form-item v-if="hasOwnProperty(styleForm,'background.img.repeat')" label="背景图重复">
            <el-select
              v-model="styleForm.background.img.repeat"
              style="width: 100%;"
              clearable
              @change="changeNotice"
            >
              <el-option v-for="item in backgroundImgRepeats" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>

          </el-form-item>
          <el-form-item v-if="hasOwnProperty(styleForm,'background.img.size')" label="背景图大小">
            <el-input v-model="styleForm.background.img.size" clearable @input="changeNotice" />
          </el-form-item>

          <div class="blockquote blockquote-mini">字体</div>
          <el-form-item v-if="hasOwnProperty(styleForm,'font.color')" label="颜色">
            <el-color-picker v-model="styleForm.font.color" @change="changeNotice" />
          </el-form-item>
          <el-form-item v-if="hasOwnProperty(styleForm,'font.size')" label="大小">
            <el-input v-model="styleForm.font.size" clearable @input="changeNotice" />
          </el-form-item>
          <el-form-item v-if="hasOwnProperty(styleForm,'font.weight')" label="粗细">
            <el-input v-model="styleForm.font.weight" clearable @input="changeNotice" />
          </el-form-item>

          <div class="blockquote blockquote-mini">边框</div>
          <el-form-item v-if="hasOwnProperty(styleForm,'border.width')" label="边框宽">
            <el-input v-model="styleForm.border.width" clearable @input="changeNotice" />
          </el-form-item>
          <el-form-item v-if="hasOwnProperty(styleForm,'border.style')" label="边框样式">
            <el-input v-model="styleForm.border.style" clearable @input="changeNotice" />
          </el-form-item>
          <el-form-item v-if="hasOwnProperty(styleForm,'border.color')" label="边框颜色">
            <el-color-picker v-model="styleForm.border.color" @change="changeNotice" />
          </el-form-item>
          <el-form-item v-if="hasOwnProperty(styleForm,'border.radius')" label="边框圆角">
            <el-input v-model="styleForm.border.radius" clearable @input="changeNotice" />
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

  </div>
</template>

<script>
import LangTextEditor from './lang-text-editor'
import RewardArrayEditor from './reward-array-editor'

export default {
  name: 'AttrEditor',
  components: { LangTextEditor, RewardArrayEditor },
  props: {
    form: {
      type: Object,
      require: true,
      default: () => {}
    }
  },
  data() {
    return {
      activeName: 'attr',
      backgroundColorDirections: [
        { name: '无', value: 'NONE' },
        { name: '从左到右渐变', value: 'to right' },
        { name: '从右到左渐变', value: 'to left' },
        { name: '从下到上渐变', value: 'to top' },
        { name: '从上到下渐变', value: 'to bottom' },
        { name: '从左对角渐变', value: 'to bottom right' },
        { name: '从右对角渐变', value: 'to bottom left' }
      ],
      backgroundImgRepeats: [
        { name: 'repeat', value: 'repeat' },
        { name: 'repeat-x', value: 'repeat-x' },
        { name: 'repeat-y', value: 'repeat-y' },
        { name: 'no-repeat', value: 'no-repeat' },
        { name: 'inherit', value: 'inherit' }
      ],
      borderStyles: [
        { name: 'none', value: 'none' },
        { name: 'hidden', value: 'hidden' },
        { name: 'dotted', value: 'dotted' },
        { name: 'dashed', value: 'dashed' },
        { name: 'solid', value: 'solid' },
        { name: 'double', value: 'double' },
        { name: 'groove', value: 'groove' },
        { name: 'ridge', value: 'ridge' },
        { name: 'inset', value: 'inset' },
        { name: 'outset', value: 'outset' },
        { name: 'inherit', value: 'inherit' }
      ],
      attrForm: {},
      styleForm: {}
    }
  },
  watch: {
    form: {
      handler(newVal) {
        if (newVal) {
          this.attrForm = newVal.attr || {}
          this.styleForm = newVal.style
        }
      },
      immediate: true
    }
  },
  created() {

  },
  methods: {
    filterAutocomplete(restaurants, queryString, callback) {
      var results = queryString ? restaurants.filter(item => (item.value.toLowerCase().indexOf(queryString.toLowerCase()) >= 0)) : restaurants
      callback(results)
    },
    hasOwnProperty(obj, property) {
      if (!obj || !property) {
        return false
      }
      let tmpObj = obj
      if (property.indexOf('.') >= 0) {
        const propertys = property.split('.').filter(key => !!key)
        for (let index = 0; index < propertys.length; index++) {
          const key = propertys[index]
          if (key in tmpObj) {
            tmpObj = tmpObj[key]
            if (typeof obj === Object || tmpObj instanceof Object) {
              return true
            }
            continue
          }
          return false
        }
      }
      return property in obj
    },
    langTextChange(list) {
      console.log('langTextChange', list)
    },
    langChange(list) {
      this.attrForm.copywriting.i18n = list
      this.emitAttrChange()
    },
    rewardChange(list) {
      this.attrForm.rewards = list
      this.emitAttrChange()
    },
    changeNotice() {
      this.emitAttrChange()
    },
    emitAttrChange() {
      this.$emit('attr-change', {
        attr: this.attrForm,
        style: this.styleForm
      })
    }
  }
}
</script>

<style scoped lang="scss">
.attr-editor {
    position: relative;
    height: 100%;
    padding: 0px 20px;
    .bottom-line {
        border-bottom: 1px solid rgb(240, 235, 235);
    }
    .card {
        padding: 20px;
        .title {
        font-weight: bold;
        }
        .content {
        padding: 20px 0px;
        }
    }
}
</style>
