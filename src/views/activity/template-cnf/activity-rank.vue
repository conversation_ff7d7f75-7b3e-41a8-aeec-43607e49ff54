<template>
  <div class="activity-rank">
    <el-drawer
      title="活动排名"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div v-loading="rankLoading" class="activity-rank-content">
        <el-alert
          type="info"
          show-icon
          style="margin-bottom: 10px;"
          :closable="false"
        >
          <p>活动排行榜数据只会保留30天, 活动结束后排行榜不可查看</p>
          <p>结束后: 通过系统活动模块发送奖品,系统会保留获奖名单数据, 其他数据将在30天后逐渐清理</p>
        </el-alert>
        <div class="blockquote blockquote-mini">按钮1 TOP20</div>
        <div v-for="(item, index) in rank.butOneRanks" :key="'but1_'+index" class="reward">
          <el-row>
            <el-col :span="12">
              <user-table-exhibit :user-profile="item.userProfile" :query-details="true" />
            </el-col>
            <el-col :span="12">
              <el-tag style="margin-top: 20px;">{{ item.quantity }}/ {{ item.quantityFormat }}</el-tag>
            </el-col>
          </el-row>
        </div>
        <div class="blockquote blockquote-mini">按钮2 TOP20</div>
        <div v-for="(item, index) in rank.butTwoRanks" :key="'but1_'+index" class="reward">
          <el-row>
            <el-col :span="12">
              <user-table-exhibit :user-profile="item.userProfile" :query-details="true" />
            </el-col>
            <el-col :span="12">
              <el-tag style="margin-top: 20px;">{{ item.quantity }}/ {{ item.quantityFormat }}</el-tag>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-drawer>

  </div>
</template>
<script>
import { listActivityRank } from '@/api/sys'
export default {
  props: {
    row: {
      type: Object,
      require: true,
      default: () => {}
    }
  },
  data() {
    return {
      rank: {},
      rankLoading: false
    }
  },
  computed: {
  },
  watch: {
    row: {
      handler(val) {
        if (!val) {
          return
        }
        this.loadActivityRank()
      },
      immediate: true
    }
  },
  created() {
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    loadActivityRank() {
      const that = this
      that.rankLoading = true
      listActivityRank(that.row.config.id).then(res => {
        that.rankLoading = false
        that.rank = res.body || {}
      }).catch(er => {
        that.rankLoading = false
      })
    }
  }
}
</script>
<style scoped lang="scss">
.activity-rank-content {
  padding: 0px 15px 100px 15px;
  .reward {
    padding: 0px 15px;
  }
}
</style>
