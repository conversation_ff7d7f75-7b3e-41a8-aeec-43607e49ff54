<template>
  <div class="ranking-reward">
    <el-alert
      type="info"
      show-icon
      style="margin-bottom: 10px;"
      :closable="false"
    >
      <p>1.排行榜只会展示前20名, 后台处理奖品也只支持道TOP20</p>
      <p>2.排名展示在输入的时候自行排好顺序, 系统不会整理排序</p>
      <p>3.直接输入指定数字 1、2、3... 或 使用~将两个数字连接: 1~10、11~20...</p>
    </el-alert>
    <el-row v-for="(rootItem,rootIndex) in formRewards" :key="rootIndex">
      <el-col :span="18">
        <el-input v-model.trim="rootItem.rankRange" placeholder="排名范围" show-word-limit maxlength="100" @input="rankRangeInput" />
      </el-col>
      <el-col :span="4" class="flex-c">
        <el-button type="text" @click="clickAddAssociationReward(rootItem, rootIndex)">
          <i class="el-icon-circle-plus" />奖品
        </el-button>
      </el-col>
      <el-col :span="2" class="flex-c">
        <i class="font-danger el-icon-delete-solid cursor-pointer" style="font-size: 20px;margin-top:8px" @click="clickRmoveAssociationReward(rootItem, rootIndex)" />
      </el-col>
      <el-col :span="24">
        <props-row :list="rootItem.rewards" />
      </el-col>
    </el-row>
    <div class="add" @click="clickAdd"><i class="el-icon-circle-plus" />添加</div>

    <associated-resources
      v-if="associatedResourcesVisable"
      :sys-origin="sysOrigin"
      @select="selectSource"
      @close="associatedResourcesVisable=false;"
    />
  </div>
</template>
<script>
import AssociatedResources from '@/components/data/AssociatedResources'
import { deepClone } from '@/utils'
import PropsRow from '@/components/data/PropsRow'
export default {
  components: { AssociatedResources, PropsRow },
  props: {
    sysOrigin: {
      type: String,
      default: ''
    },
    rewardsGroup: {
      type: Array,
      require: true,
      default: () => []
    }
  },
  data() {
    return {
      formRewards: [],
      associatedResourcesVisable: false,
      selectedRankingRewards: null
    }
  },
  computed: {
  },
  watch: {
    rewardsGroup: {
      handler(val) {
        if (!val) {
          return
        }
        this.formRewards = deepClone(val)
      },
      immediate: true
    }
  },
  created() {
  },
  methods: {
    selectSource(source) {
      const that = this
      that.associatedResourcesVisable = false
      that.selectedRankingRewards.row.resourceGroupId = source.id
      that.selectedRankingRewards.row.rewards = source.rewardConfigList || []
      this.emitChange()
    },
    handleClose() {
      this.$emit('close')
    },
    clickAddAssociationReward(row, index) {
      const that = this
      that.associatedResourcesVisable = true
      that.selectedRankingRewards = { row, index }
    },
    clickRmoveAssociationReward(row, index) {
      this.formRewards.splice(index, 1)
      this.emitChange()
    },
    clickAdd() {
      this.formRewards.push({
        rankRange: '',
        resourceGroupId: '',
        rewards: []
      })
      this.emitChange()
    },
    rankRangeInput() {
      this.emitChange()
    },
    emitChange() {
      this.$emit('change', this.formRewards)
    }
  }
}
</script>
<style scoped lang="scss">
.add {
  border: 1px dashed #999999;
  text-align: center;
  padding: 10px;
  cursor: pointer;
  i {
    padding: 0px 5px;
  }
}
</style>
