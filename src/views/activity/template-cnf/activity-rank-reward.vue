<template>
  <div class="activity-rank">
    <el-drawer
      :title="'活动排名奖品-'+ (rewardRank.awardStatus === true? '快照': '实时')"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div v-loading="rewardRankLoading" class="activity-rank-content">
        <div class="blockquote blockquote-mini blockquote-info">按钮1</div>
        <div v-for="(item, index) in rewardRank.butOneRanks" :key="'but1_'+index" class="reward">
          <div class="blockquote blockquote-mini">TOP{{ item.reward.rankRange }}
            <el-tag v-if="item.rankUsers && item.rankUsers.length > 0" size="mini" :type="item.status === true? 'success' : 'danger'">{{ item.status === true ? '已发送' : '未发送' }}</el-tag>
          </div>
          <div v-for="(userItem,userIndex) in item.rankUsers" :key="userIndex">
            <el-row>
              <el-col :span="12">
                <user-table-exhibit :user-profile="userItem.userProfile" :query-details="true" />
              </el-col>
              <el-col :span="12">
                <el-tag style="margin-top: 20px;">{{ userItem.quantity }}/ {{ userItem.quantityFormat }}</el-tag>
              </el-col>
            </el-row>
          </div>
          <props-row :list="item.reward.rewards" />
        </div>

        <div class="blockquote blockquote-mini blockquote-info">按钮2</div>
        <div v-for="(item, index) in rewardRank.butTwoRanks" :key="'but2_'+index" class="reward">
          <div class="blockquote blockquote-mini">TOP{{ item.reward.rankRange }}
            <el-tag v-if="item.rankUsers && item.rankUsers.length > 0" size="mini" :type="item.status === true? 'success' : 'danger'">{{ item.status === true ? '已发送' : '未发送' }}</el-tag>
          </div>
          <div v-for="(userItem,userIndex) in item.rankUsers" :key="userIndex">
            <el-row>
              <el-col :span="12">
                <user-table-exhibit :user-profile="userItem.userProfile" :query-details="true" />
              </el-col>
              <el-col :span="12">
                <el-tag style="margin-top: 20px;">{{ userItem.quantity }}/ {{ userItem.quantityFormat }}</el-tag>
              </el-col>
            </el-row>
          </div>
          <props-row :list="item.reward.rewards" />
        </div>
        <div v-if="!rewardRank.awardStatus" class="drawer-footer">
          <el-button :disabled="sendLoading" :loading="sendLoading" type="primary" @click="clickSend">发送</el-button>
        </div>
      </div>
    </el-drawer>

  </div>
</template>
<script>
import { listActivityRewardRank, sendActivityReward } from '@/api/sys'
import PropsRow from '@/components/data/PropsRow'
export default {
  components: { PropsRow },
  props: {
    row: {
      type: Object,
      require: true,
      default: () => {}
    }
  },
  data() {
    return {
      rewardRank: {},
      rewardRankLoading: false,
      sendLoading: false
    }
  },
  computed: {
  },
  watch: {
    row: {
      handler(val) {
        if (!val) {
          return
        }
        this.loadActivityRank()
      },
      immediate: true
    }
  },
  created() {
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    loadActivityRank() {
      const that = this
      that.rewardRankLoading = true
      listActivityRewardRank(that.row.config.id).then(res => {
        that.rewardRankLoading = false
        that.rewardRank = res.body || {}
      }).catch(er => {
        that.rewardRankLoading = false
      })
    },
    clickSend() {
      const that = this
      that.sendLoading = true
      sendActivityReward(that.row.config.id).then(res => {
        that.sendLoading = false
        that.rewardRank.awardStatus = true
        that.$opsMessage.success()
      }).catch(er => {
        that.sendLoading = false
      })
    }
  }
}
</script>
<style scoped lang="scss">
.activity-rank-content {
  padding: 0px 15px 100px 15px;
  .reward {
    padding: 0px 15px;
  }
}
</style>
