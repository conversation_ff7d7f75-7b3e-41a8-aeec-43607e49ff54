<template>
  <div class="app-container">
    <div class="table">
      <div class="filter-container">
        <el-select
          v-model="listQuery.sysOrigin"
          placeholder="系统"
          style="width: 120px"
          class="filter-item"
          @change="handleSearch"
        >
          <el-option
            v-for="item in permissionsSysOriginPlatforms"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
            <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
            <span style="float: left;margin-left:10px">{{ item.label }}</span>
          </el-option>
        </el-select>
        <el-select
          v-model="listQuery.showcase"
          placeholder="状态"
          style="width: 120px"
          class="filter-item"
          clearable
          @change="handleSearch"
        >

          <el-option-group
            label="运行状态"
          >
            <el-option label="已开始" :value="1" />
            <el-option label="已结束" :value="2" />
          </el-option-group>
          <el-option-group
            label="管理状态"
          >
            <el-option label="上架" :value="3" />
            <el-option label="下架" :value="4" />
          </el-option-group>
        </el-select>
        <el-input
          v-model.trim="listQuery.id"
          v-number
          placeholder="活动ID"
          style="width: 200px;"
          class="filter-item"
        />
        <el-input
          v-model.trim="listQuery.templateId"
          v-number
          placeholder="模版ID"
          style="width: 200px;"
          class="filter-item"
        />
        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleSearch"
        >
          搜索
        </el-button>
        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-edit"
          @click="clickCreate"
        >
          新增
        </el-button>
        <el-popover
          title="操作手册"
          trigger="hover"
        >
          <div class="content">
            <div class="help">
              <strong>怎么创建一个活动?</strong>
              <p>1.在 “活动模版” 创建一个模版页面, 如果已创建可以直接使用</p>
              <p>2.在当前 “活动管理” 创建一个活动并关联 “活动模版”</p>
              <p>3.使用 “复制活动链接” 获得活动地址</p>
              <p>4.在 “Banner” 配置活动地址</p>
              <strong>活动排行榜计算规则?</strong>
              <p>1.活动处于上架状态</p>
              <p>2.活动进行中: 系统当前时间 >= 开始时间 并且 系统当前时间  &lt;= 结束时间</p>
              <p>3.活动中包含礼物</p>
              <p>满足上面条件的情况下, 用户发送礼物触发</p>
              <strong>注意事项</strong>
              <p>1.活动结束后不可用在次编辑, 如果需要重写激活请使用 “复制活动” 重写创建一个新的活动</p>
              <p>2.排行榜只能在活动进行中可查看, 活动结束后通过系统 “发送奖品” 系统将缓存TOP20 或 通过 “奖品发送记录” 查看</p>
              <p>3.一个 “活动模版” 如果被多个活动关联, 修改模版所有关联活动将全部会改变</p>
            </div>
          </div>
          <span slot="reference"><i class="el-icon-info" />帮助</span>
        </el-popover>
      </div>
      <el-table
        v-loading="listLoading"
        :data="list"
        element-loading-text="Loading"
        fit
        highlight-current-row
      >

        <el-table-column label="关联模版" prop="templateName" align="center" min-width="200" />
        <el-table-column label="备注" prop="config.remark" align="center" min-width="200" />
        <el-table-column label="状态" prop="config.startTime" align="center" min-width="200">
          <template slot-scope="scope">
            <el-tag :type="scope.row.config.showcase === true ? 'primary': 'danger'">
              {{ scope.row.config.showcase === true ? '上架':'下架' }}
            </el-tag>
            <el-tag v-if="scope.row.status === 0" type="info">未开始</el-tag>
            <el-tag v-if="scope.row.status === 1" type="success">已开始</el-tag>
            <el-tag v-if="scope.row.status === 2" type="warning">已结束</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="活动时间" prop="config.startTime" align="center" min-width="200">
          <template slot-scope="scope">
            <div>开始: {{ scope.row.config.startTime | dateFormat }}</div>
            <div>结束: {{ scope.row.config.endTime | dateFormat }}</div>
          </template>
        </el-table-column>
        <el-table-column label="修改/创建人" prop="config.createTime" align="center" min-width="200">
          <template slot-scope="scope">
            <div>修改: {{ scope.row.updateUserName }}</div>
            <div>创建: {{ scope.row.createUserName }}</div>
          </template>
        </el-table-column>
        <el-table-column label="修改/创建时间" prop="config.createTime" align="center" min-width="200">
          <template slot-scope="scope">
            <div>修改: {{ scope.row.config.updateTime | dateFormat }}</div>
            <div>创建: {{ scope.row.config.createTime | dateFormat }}</div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" align="center" width="80">
          <template slot-scope="scope">
            <el-dropdown>
              <span class="el-dropdown-link">
                <i class="el-icon-more" />
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="clickEdit(scope.row)"> {{ scope.row.status !== 2 ?'编辑': '查看' }}</el-dropdown-item>
                <el-dropdown-item v-if="scope.row.status === 1" @click.native="clickRank(scope.row)">排行榜</el-dropdown-item>
                <el-dropdown-item v-if="scope.row.status !== 2" @click.native="clickEditTemplate(scope.row)">编辑模版</el-dropdown-item>
                <el-dropdown-item @click.native="clickCopyActivity(scope.row)">复制活动</el-dropdown-item>
                <el-dropdown-item v-if="scope.row.status === 2" @click.native="clickSendReward(scope.row)">{{ scope.row.config.awardStatus === true ? '奖品发送记录' : '发送奖品' }}</el-dropdown-item>
                <el-dropdown-item @click.native="copyContent(scope.row.config.id)">复制活动ID</el-dropdown-item>
                <el-dropdown-item @click.native="copyContent(scope.row.config.templateId)">复制模版ID</el-dropdown-item>
                <el-dropdown-item @click.native="clickActivityLink(scope.row)">复制活动链接</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <div v-if="listQuery.lastId" class="load-more">
        <span v-if="notData">已加载全部</span>
        <el-button v-else size="mini" :disabled="loadMoreLoading" :loading="loadMoreLoading" @click="clickLoadMore">加载更多</el-button>
      </div>

      <form-edit
        v-if="formEditVisible"
        :row="thatRow"
        :sys-origin="listQuery.sysOrigin"
        @close="formEditVisible = false"
        @success="formEditSuccess"
      />

      <activity-rank-reward
        v-if="activityRankRewardVisible"
        :row="thatRow"
        @close="activityRankRewardVisible = false"
      />

      <activity-rank
        v-if="activityRankVisible"
        :row="thatRow"
        @close="activityRankVisible = false"
      />
    </div>
  </div>
</template>

<script>
import { listActivityConf } from '@/api/sys'
import FormEdit from './form-edit'
import ActivityRankReward from './activity-rank-reward'
import ActivityRank from './activity-rank'
import { mapGetters } from 'vuex'
import { copyText, deepClone } from '@/utils'
export default {
  components: { FormEdit, ActivityRankReward, ActivityRank },
  data() {
    return {
      activityRankVisible: false,
      activityRankRewardVisible: false,
      list: [],
      listLoading: true,
      templatId: '',
      formEditVisible: false,
      thatRow: {},
      notData: false,
      loadMoreLoading: false,
      txtVal: 0,
      activeGiftId: '',
      pushTextHistoryLoading: false,
      pushTextHistoryVisible: false,
      pushTextHistory: [],
      delarr: [],
      listQuery: {
        limit: 20,
        lastId: '',
        id: '',
        sysOrigin: '',
        showcase: 1,
        templateId: ''
      },
      textOptTitle: ''
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData(true)
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        this.listQuery.lastId = ''
        this.list = []
      }
      that.listLoading = true
      listActivityConf(that.listQuery).then(res => {
        that.listLoading = false
        that.loadMoreLoading = false
        // 网络差的情况, 可能会绕过第一个clean,出现多次concat
        if (isClean === true) {
          this.listQuery.lastId = ''
          this.list = []
        }
        const { body } = res
        const list = body || []
        that.notData = list.length < that.listQuery.limit
        if (list && list.length > 0) {
          that.list = that.list.concat(list)
          that.listQuery.lastId = that.list[that.list.length - 1].config.id
        }
      }).catch(er => {
        console.error(er)
        that.listLoading = false
        that.loadMoreLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    clickCreate() {
      this.thatRow = null
      this.formEditVisible = true
    },
    copyContent(text) {
      copyText(text).then(() => {
        this.$opsMessage.success()
      }).catch(er => {
        this.$opsMessage.fail()
      })
    },
    clickActivityLink(row) {
      this.copyContent(`${process.env.VUE_APP_H5_BASE_URL}/#/activity/${row.config.id}`)
    },
    clickCopyActivity(row) {
      this.thatRow = deepClone(row)
      this.thatRow.config.id = null
      this.thatRow.status = 0
      this.thatRow.config.awardStatus = false
      if (this.thatRow.config.butOneRewards) {
        this.thatRow.config.butOneRewards.forEach(element => {
          element.rankUsers = []
          element.status = false
        })
      }
      if (this.thatRow.config.butTwoRewards) {
        this.thatRow.config.butTwoRewards.forEach(element => {
          element.rankUsers = []
          element.status = false
        })
      }
      this.formEditVisible = true
    },
    clickSendReward(row) {
      this.thatRow = row
      this.activityRankRewardVisible = true
    },
    clickRank(row) {
      this.thatRow = row
      this.activityRankVisible = true
    },
    clickEditTemplate(row) {
      const that = this
      that.$confirm('注意: 模版发生改变后相关引用都会同步发生变化, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.$router.push('/activity/template?id=' + row.config.templateId)
      }).catch(() => {
      })
    },
    clickEdit(row) {
      this.thatRow = row
      this.formEditVisible = true
    },
    formEditSuccess() {
      this.formEditVisible = false
      this.renderData(true)
    },
    clickLoadMore() {
      const that = this
      that.loadMoreLoading = true
      that.renderData()
    }
  }
}
</script>
<style scoped lang="scss">
.popover-content {
  max-width: 300px;
  line-height: 20px;
}
.load-more {
  padding: 20px;
  text-align: center;
}
</style>
