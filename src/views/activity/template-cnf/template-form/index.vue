<template>
  <div class="app-container">
    <div v-show="!templateEditorVisible" class="table">
      <div class="filter-container">
        <el-input
          v-show="!templateEditorVisible"
          v-model.trim="listQuery.id"
          v-number
          placeholder="模版ID"
          style="width: 200px;"
          class="filter-item"
        />
        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleSearch"
        >
          搜索
        </el-button>
        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-edit"
          @click="clickCreateTemplate"
        >
          新增
        </el-button>
      </div>
      <el-table
        v-loading="listLoading"
        :data="list"
        element-loading-text="Loading"
        fit
        highlight-current-row
      >
        <el-table-column label="ID" prop="template.id" align="center" min-width="200" />
        <el-table-column label="名称" prop="template.name" align="center" min-width="200" />
        <el-table-column label="创建时间" prop="template.createTime" align="center" min-width="200">
          <template slot-scope="scope">
            {{ scope.row.template.createTime | dateFormat }}
          </template>
        </el-table-column>
        <el-table-column label="修改时间" prop="template.updateTime" align="center" min-width="200">
          <template slot-scope="scope">
            {{ scope.row.template.updateTime | dateFormat }}
          </template>
        </el-table-column>
        <el-table-column label="创建人" prop="createUserName" align="center" min-width="150" />
        <el-table-column label="修改人" prop="updateUserName" align="center" min-width="150" />
        <el-table-column fixed="right" label="操作" align="center" width="80">
          <template slot-scope="scope">
            <el-dropdown>
              <span class="el-dropdown-link">
                <i class="el-icon-more" />
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="clickEditName(scope.row)">编辑名称</el-dropdown-item>
                <el-dropdown-item @click.native="clickEditTemplate(scope.row)">编辑模版</el-dropdown-item>
                <el-dropdown-item @click.native="clickCopyTemplate(scope.row)">复制模版</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <div v-if="list.length >= 20" style="text-align: center; margin: 20px;">没有做分页,模版数量>20个后请联系管理员添加分页功能!!!</div>
    </div>

    <template-editor
      v-if="templateEditorVisible"
      :template-id="templatId"
      @close="templateEditorVisible=false"
    />

  </div>
</template>

<script>
import { listTemplates, addTemplate, updateTemplateName } from '@/api/sys'
import { specifyLanguage } from '@/constant/type'
import TemplateEditor from './../template-editor'
export default {
  components: { TemplateEditor },
  data() {
    return {
      templateEditorVisible: false,
      list: [],
      listLoading: true,
      templatId: '',
      //
      thatRow: {},
      txtVal: 0,
      specifyLanguage,
      activeGiftId: '',
      pushTextHistoryLoading: false,
      pushTextHistoryVisible: false,
      pushTextHistory: [],
      delarr: [],
      listQuery: {
        limit: 20,
        lastId: '',
        id: ''
      },
      textOptTitle: ''
    }
  },
  computed: {
  },
  created() {
    const that = this
    that.listQuery.id = that.$route.query.id
    that.renderData(true)
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        this.listQuery.lastId = ''
        this.list = []
      }
      that.listLoading = true
      listTemplates(that.listQuery).then(res => {
        that.listLoading = false
        const { body } = res
        that.list = body || []
      }).catch(er => {
        console.error(er)
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    clickCreateTemplate() {
      this.$prompt('请输入模版名称', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        inputValidator: (val) => {
          if (!val) {
            return true
          }
          return val.length <= 200
        },
        inputErrorMessage: '模版名称必须<=200个字符串'
      }).then(({ value }) => {
        const that = this
        addTemplate({
          name: value,
          indexTree: []
        }).then(res => {
          that.renderData()
        }).catch(er => {
          console.error(er)
        })
      }).catch(() => {
      })
    },
    clickEditName(row) {
      this.$prompt('请输入新的模版名称', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        inputValidator: (val) => {
          if (!val) {
            return true
          }
          return val.length <= 200
        },
        inputErrorMessage: '模版名称必须<=200个字符串'
      }).then(({ value }) => {
        const that = this
        updateTemplateName(row.template.id, value).then(res => {
          that.renderData()
        }).catch(er => {
          console.error(er)
        })
      }).catch(() => {

      })
    },
    clickEditTemplate(row) {
      this.templatId = row.template.id
      this.templateEditorVisible = true
    },
    clickCopyTemplate(row) {
      this.$prompt('请输入模版名称', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        inputValidator: (val) => {
          if (!val) {
            return true
          }
          return val.length <= 200
        },
        inputErrorMessage: '模版名称必须<=200个字符串'
      }).then(({ value }) => {
        const that = this
        addTemplate({
          name: value,
          indexTree: row.template.indexTree || []
        }).then(res => {
          that.renderData()
        }).catch(er => {
          console.error(er)
        })
      }).catch(() => {
      })
    }
  }
}
</script>
<style scoped lang="scss">
.popover-content {
  max-width: 300px;
  line-height: 20px;
}
</style>
