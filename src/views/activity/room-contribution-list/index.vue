<template>
  <div class="app-container">

    <div class="filter-container">
      <div class="filter-item">
        <account-input v-model="listQuery.roomId" placeholder="房间账号" type="ROOM" />
      </div>
      <el-button
        :loading="searchLoading"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="房间" align="center">
        <template slot-scope="scope">
          <div class="room-profile flex-l">
            <div class="avatar">
              <el-image
                style="width:50px;height: 50px;border-radius: 50p%;"
                :src="scope.row.roomProfile.roomCover"
                :preview-src-list="[scope.row.roomProfile.roomCover]"
              >
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </el-image>
            </div>
            <div class="info nowrap-ellipsis">
              <div class="nickname">
                <el-link v-if="scope.row.roomProfile.roomName" @click="queryRoomDetails(scope.row.roomProfile.id)">
                  {{ scope.row.roomProfile.roomName }}
                </el-link>
              </div>
              <div class="desc">
                {{ scope.row.roomProfile.roomAccount }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="用户" align="center">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column prop="contributionValue" label="积分" align="center" />
      <el-table-column prop="ratio" label="比例" align="center" />
      <el-table-column prop="balance" label="领取金币" align="center" />
      <el-table-column prop="dateNumber" label="时间(周一至周日)" align="center" />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <room-deatils-drawer
      v-if="roomDeatilsDrawerVisible"
      :room-id="roomId"
      @close="roomDeatilsDrawerVisible=false"
    />

  </div>
</template>

<script>
import { pageRoomContribution } from '@/api/activity'
import Pagination from '@/components/Pagination'
import RoomDeatilsDrawer from '@/components/data/RoomDeatilsDrawer'

export default {
  name: 'RoomContributionList',
  components: {
    Pagination, RoomDeatilsDrawer
  },
  data() {
    return {
      list: [],
      total: 0,
      roomId: '',
      roomDeatilsDrawerVisible: false,
      thatSelectedUserId: '',
      searchDisabled: false,
      listQuery: {
        cursor: 1,
        limit: 20,
        roomId: ''
      },
      listLoading: true,
      searchLoading: false
    }
  },
  created() {
    this.renderData(true)
  },
  methods: {
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      pageRoomContribution(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.searchLoading = that.listLoading = false
      }).catch(er => {
        that.searchLoading = that.listLoading = false
      })
    },
    handleSearch() {
      this.searchLoading = true
      this.renderData(true)
    },
    queryRoomDetails(roomId) {
      this.roomDeatilsDrawerVisible = true
      this.roomId = roomId
    }

  }
}
</script>
<style scoped lang="scss">
.this-level {
  padding-bottom: 30px;
  display: flex;
  > div {
    width: 150px;
  }
}
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
