<template>
  <el-dialog
    title="描述列表"
    :visible="true"
    :before-close="handleClose"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    width="80%"
  >
    <div class="app-container">
      <div class="filter-container">
        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-edit"
          @click="handleCreate"
        >
          新增描述
        </el-button>
      </div>
      <el-table
        v-loading="listLoading"
        :data="list"
        :before-close="handleClose"
        element-loading-text="Loading"
        fit
        highlight-current-row
        max-height="350px"
      >
        <el-table-column label="ID" prop="id" align="center" />
        <el-table-column label="语言" prop="language" align="center">
          <template slot-scope="scope">
            <span v-for="item in language" :key="item.value">
              <span v-if="item.value === scope.row.language">{{
                item.name
              }}</span>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="描述" prop="description" align="center" />
        <el-table-column prop="createTime" label="创建时间" align="center">
          <template slot-scope="scope">
            {{ scope.row.createTime | dateFormat }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" align="center" width="120">
          <template slot-scope="scope">
            <el-button
              type="text"
              @click.native="handleUpdate(scope.row)"
            >修改</el-button>
            <el-button
              type="text"
              @click.native="removeDescription(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="listQuery.cursor"
        :limit.sync="listQuery.limit"
        @pagination="renderData"
      />

      <el-dialog
        :title="textOptTitle"
        :visible.sync="formVisible"
        :before-close="handleDescriptionClose"
        :close-on-click-modal="false"
        :modal="false"
        append-to-bod="true"
        width="450px"
      >
        <div v-loading="submitLoading">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="100px"
            style="margin-left: 15px;"
          >
            <el-form-item label="语言" prop="language">
              <el-select
                v-model="form.language"
                placeholder="语言"
                clearable
                style="width: 100%"
                class="filter-item"
                :disabled="form.id !== ''"
              >
                <el-option
                  v-for="item in language"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="活动名称" prop="updateDescribe">
              <el-input
                v-model.trim="form.description"
                placeholder="请输入活动名称"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="submitForm()">保存</el-button>
              <el-button @click="handleDescriptionClose()">取消</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-dialog>
    </div>
  </el-dialog>
</template>

<script>
import {
  globalizationDelete,
  globalizationTable,
  addOrUpdateGlobalization
} from '@/api/activity'
import Pagination from '@/components/Pagination'
function getFormData() {
  return {
    id: '',
    relationId: '',
    description: '',
    language: ''
  }
}
export default {
  name: 'HallFameLanguage',
  components: { Pagination },
  props: {
    hallFameId: {
      type: String,
      required: true
    }
  },
  data() {
    const validateUpdateDescribe = (rule, value, callback) => {
      if (!value || value.trim().length === 0) {
        callback(new Error('必填项不可为空'))
      } else if (value && value.length > 200) {
        callback(new Error('最大 200 字符'))
      } else {
        callback()
      }
    }
    return {
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        relationId: ''
      },
      language: [
        { value: 'en', name: '英语' },
        { value: 'ar', name: '阿拉伯语' },
        { value: 'id', name: '印尼语' },
        { value: 'tr', name: '土耳其语' },
        { value: 'zh', name: '中文' }
      ],
      submitLoading: false,
      listLoading: false,
      searchDisabled: false,
      textOptTitle: '',
      formVisible: false,
      form: getFormData(),
      rules: {
        language: [
          { required: true, message: '必填项不可为空', trigger: 'blur' }
        ],
        description: [
          { required: true, trigger: 'blur', validator: validateUpdateDescribe }
        ]
      }
    }
  },
  watch: {
    hallFameId: {
      handler(newVal) {
        this.listQuery.relationId = newVal
        this.renderData()
      },
      immediate: true
    }
  },
  methods: {
    renderData() {
      const that = this
      if (!that.listQuery.relationId) {
        return
      }
      that.listLoading = true
      globalizationTable(that.listQuery.relationId).then(res => {
        const { body } = res
        that.total = 1
        that.list = body || []
        that.listLoading = false
      })
    },
    handleCreate() {
      this.textOptTitle = '新增描述'
      this.formVisible = true
      this.form = getFormData()
    },
    handleUpdate(row) {
      this.textOptTitle = '修改描述'
      this.formVisible = true
      this.form = row
    },
    handleDescriptionClose() {
      this.formVisible = false
      this.resetForm()
    },
    resetForm() {
      this.form = getFormData()
    },
    handleClose() {
      this.$emit('close')
    },
    handleSearch() {
      this.renderData(true)
    },
    loadSearchUser() {
      this.searchDisabled = true
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (valid) {
          that.submitLoading = true
          that.form.relationId = that.listQuery.relationId
          addOrUpdateGlobalization(this.form)
            .then(res => {
              if (res.errorCode === 0) {
                that.$opsMessage.success()
                that.submitLoading = false
                that.formVisible = false
                that.resetForm()
                that.renderData(true)
                return
              }
              that.$opsMessage.fail(res.errorMsg)
              that.submitLoading = false
            })
            .catch(er => {
              that.submitLoading = false
              console.error(er)
              this.$emit('fail')
            })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    },
    removeDescription(item) {
      const that = this
      that
        .$confirm('确定删除描述吗？', '提示', {
          type: 'warning'
        })
        .then(() => {
          that.listLoading = true
          globalizationDelete(item.id)
            .then(res => {
              that.listLoading = false
              that.$message({
                message: '删除成功',
                type: 'success'
              })
              that.renderData()
            })
            .catch(er => {
              console.error(er)
              that.listLoading = false
            })
        })
        .catch(er => {
          console.error(er)
          that.listLoading = false
        })
    }
  }
}
</script>
<style scoped>
.v-modal {
  z-index: 0;
}
</style>
