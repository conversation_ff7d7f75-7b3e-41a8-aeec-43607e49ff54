<template>
  <div class="edit-role">
    <el-dialog
      :title="title"
      :visible="true"
      width="550px"
      top="50px"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <div>
        <el-form
          ref="dataForm"
          :rules="rules"
          :model="formData"
          style="width: 400px; margin-left:50px;"
        >
          <el-form-item label="中文备注" prop="notes">
            <el-input v-model.trim="formData.notes" placeholder="如：斋月活动,方便自己人看" />
          </el-form-item>
          <el-form-item label="用户长ID" prop="accounts">
            <el-input v-model.trim="formData.accounts" placeholder="最多填写两个userId(用户长id),用英语逗号分割" />
          </el-form-item>
          <el-form-item label="顺序" prop="sort">
            <el-input v-model="formData.sort" v-number placeholder="顺序(越大越靠前)" />
          </el-form-item>
          <el-form-item label="上架" prop="del">
            <el-select
              v-model="formData.del"
              clearable
              style="width: 100%"
              class="filter-item"
            >
              <el-option label="否" :value="true" />
              <el-option label="是" :value="false" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">
          取消
        </el-button>
        <el-button
          :loading="submitLoading"
          type="primary"
          @click="handleSubmit"
        >
          提交
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import { addOrUpdateHallFame } from '@/api/activity'
export default {
  name: 'AppHallFameEdit',
  props: {
    updateData: {
      type: Object,
      default: null
    },
    sysOrigin: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      formData: {
        id: '',
        sysOrigin: '',
        accounts: '',
        sort: '',
        notes: '',
        del: ''
      },
      rules: {
        accounts: [{ required: true, message: '必填项不可为空', trigger: 'blur' }],
        notes: [{ required: true, message: '必填项不可为空', trigger: 'blur' }],
        del: [{ required: true, message: '必选项不可不选', trigger: 'blur' }]
      },
      submitLoading: false
    }
  },
  computed: {
    isAdd() {
      return this.updateData === null
    },
    title() {
      return this.isAdd ? `添加(${this.sysOrigin})` : `修改(${this.updateData.sysOrigin})`
    }
  },
  watch: {
    updateData: {
      immediate: true,
      deep: true,
      handler(newVal) {
        Object.assign(this.formData, newVal)
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    handleSubmit() {
      const that = this
      that.$refs.dataForm.validate(valid => {
        if (valid) {
          that.submitLoading = true
          that.formData.sysOrigin = that.sysOrigin
          addOrUpdateHallFame(that.formData).then(res => {
            that.submitLoading = false
            this.$emit('success', 'create')
          }).catch(er => {
            that.submitLoading = false
            console.error(er)
            this.$emit('fail')
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    }
  }
}
</script>
