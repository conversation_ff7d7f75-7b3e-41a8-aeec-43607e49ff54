<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatformAlls"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;">
            <sys-origin-icon
              :icon="item.value"
              :desc="item.value"
            /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.del"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option label="上架" value="false" />
        <el-option label="下架" value="true" />
      </el-select>
      <el-button
        :loading="searchLoading"
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-edit"
        @click="handleAdd"
      >
        添加
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="sysOrigin" label="平台" align="center" />
      <el-table-column prop="notes" label="备注" />
      <el-table-column prop="accounts" label="用户账号" />
      <el-table-column prop="sort" label="顺序" />
      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
        width="200"
      >
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="180">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click.native="hanldeEidt(scope.row)"
          >编辑</el-button>
          <el-button
            type="text"
            @click.native="handleUpdateDescribeList(scope.row)"
          >更新描述</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
    <hall-fame-edit
      v-if="hallFameEditVisible"
      :update-data="updateData"
      :sys-origin="listQuery.sysOrigin"
      @close="hallFameEditVisible = false"
      @success="hallFameEditSuccess"
    />

    <hall-fame-language
      v-if="updateDescribeVisible"
      :hall-fame-id="thatSelectedHallFameId"
      @close="updateDescribeVisible = false"
    />
  </div>
</template>

<script>
import { hallFameTable } from '@/api/activity'
import Pagination from '@/components/Pagination'
import HallFameEdit from '@/views/activity/hall-fame/hall-fame-edit'
import { mapGetters } from 'vuex'
import HallFameLanguage from '@/views/activity/hall-fame/hall-fame-language'

export default {
  name: 'AppUserTable',
  components: {
    Pagination,
    HallFameEdit,
    HallFameLanguage
  },
  data() {
    return {
      hallFameEditVisible: false,
      updateDescribeVisible: false,
      thatSelectedHallFameId: '',
      updateData: null,
      thisRow: {},
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        sysOrigin: 'MARCIE',
        del: 'false'
      },
      listLoading: true,
      searchLoading: false,
      clickUserId: ''
    }
  },
  computed: {
    ...mapGetters([
      'permissionsSysOriginPlatformAlls',
      'permissionsFirstSysOrigin'
    ])
  },
  created() {
    if (!this.permissionsFirstSysOrigin) {
      return
    }
    this.listQuery.sysOrigin = this.permissionsFirstSysOrigin.value
    this.renderData(true)
  },
  methods: {
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      hallFameTable(that.listQuery)
        .then(res => {
          const { body } = res
          that.total = body.total || 0
          that.list = body.records
          that.searchLoading = that.listLoading = false
        })
        .catch(er => {
          that.searchLoading = that.listLoading = false
        })
    },
    handleSearch() {
      this.searchLoading = true
      this.renderData(true)
    },
    handleUpdateDescribeList(row) {
      this.thatSelectedHallFameId = String(row.id)
      this.updateDescribeVisible = true
    },
    handleAdd() {
      this.hallFameEditVisible = true
      this.updateData = null
    },
    hanldeEidt(row) {
      this.hallFameEditVisible = true
      this.updateData = row
    },
    hallFameEditSuccess(event) {
      this.updateData = null
      this.hallFameEditVisible = false
      this.renderData(event === 'create')
    }
  }
}
</script>
<style scoped lang="scss">
.this-level {
  padding-bottom: 30px;
  display: flex;
  > div {
    width: 150px;
  }
}
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
