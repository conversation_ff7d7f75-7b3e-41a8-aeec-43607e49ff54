<template>
  <div class="app-container">

    <div class="filter-container">
      <div class="filter-item">
        <el-select
          v-model="listQuery.type"
          placeholder="类型"
          style="width: 120px"
          class="filter-item"
          clearable
          @change="handleSearch"
        >
          <el-option :label="'周'" :value="'WEEK'" />
          <el-option :label="'月'" :value="'MONTH'" />
        </el-select>
        <el-select
          v-model="listQuery.regionId"
          v-loading="loading"
          placeholder="区域"
          style="width:120px;"
          class="filter-item"
          clearable
          @change="handleSearch"
        >
          <el-option
            v-for="(item, index) in regions"
            :key="index"
            :label="item.regionName"
            :value="item.id"
          />
        </el-select>

        <el-button
          :loading="searchLoading"
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleSearch"
        >
          搜索
        </el-button>
      </div>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="用户" align="center">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column prop="regionName" label="区域" align="center" />
      <el-table-column prop="target" label="目标值" align="center" />
      <el-table-column prop="groupName" label="类型" align="center" />
      <el-table-column prop="dateNumber" label="时间" align="center" />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

  </div>
</template>

<script>
import { regionConfigTable } from '@/api/sys'
import { pageAgentActivity } from '@/api/activity'
import Pagination from '@/components/Pagination'

export default {
  name: 'RoomContributionList',
  components: {
    Pagination
  },
  data() {
    return {
      regions: [],
      list: [],
      total: 0,
      searchDisabled: false,
      listQuery: {
        cursor: 1,
        limit: 20,
        type: 'WEEK',
        regionId: ''
      },
      loading: false,
      listLoading: true,
      searchLoading: false
    }
  },
  created() {
    this.listRegion()
    this.renderData(true)
  },
  methods: {
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      pageAgentActivity(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.searchLoading = that.listLoading = false
      }).catch(er => {
        that.searchLoading = that.listLoading = false
      })
    },
    handleSearch() {
      this.searchLoading = true
      this.renderData(true)
    },
    listRegion() {
      const that = this
      that.loading = true
      regionConfigTable({ 'sysOrigin': 'ASWAT' }).then(res => {
        that.regions = res.body || []
        that.loading = false
      }).catch(er => {
        that.loading = false
      })
    }
  }
}
</script>
<style scoped lang="scss">
.this-level {
  padding-bottom: 30px;
  display: flex;
  > div {
    width: 150px;
  }
}
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
