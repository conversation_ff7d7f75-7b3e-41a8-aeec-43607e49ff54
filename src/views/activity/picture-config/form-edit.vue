<template>
  <div class="banner-form-edite">
    <el-drawer
      :title="textOptTitle"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div v-loading="submitLoading">

        <div class="drawer-form">
          <el-form ref="form" :model="form" :rules="rules" label-width="100px">
            <el-form-item label="系统" prop="sysOrigin">
              <el-select
                v-model="form.sysOrigin"
                placeholder="选择系统"
                style="width:100%;"
                class="filter-item"
              >
                <el-option
                  v-for="item in permissionsSysOriginPlatforms"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
                  <span style="float: left;margin-left:10px">{{ item.label }}</span>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="活动图" prop="picture">
              <el-upload
                :disabled="pictureUploadLoading"
                :file-list="pictureFileList"
                :class="{'upload-but-hide': !isShowPictureUpload}"
                action=""
                list-type="picture-card"
                :http-request="uploadPicture"
                :show-file-list="!isShowPictureUpload"
                :on-remove="handlePictureFileRemove"
                accept="image/*"
              >
                <i slot="default" v-loading="pictureUploadLoading" class="el-icon-plus" />
              </el-upload>
            </el-form-item>
            <el-form-item label="描述" prop="depict">
              <el-input v-model.trim="form.name" placeholder="描述" />
            </el-form-item>
          </el-form>
        </div>
        <div class="drawer-footer">
          <el-button @click="handleClose()">取消</el-button>
          <el-button type="primary" @click="submitForm()">保存</el-button>
        </div>
      </div>

    </el-drawer>
  </div>
</template>
<script>

import { addActivityPicture, updateActivityPicture } from '@/api/activity'
import { getElementUiUploadFile } from '@/utils'
import { mapGetters } from 'vuex'
export default {
  props: {
    row: {
      type: Object,
      default: null
    },
    sysOrigin: {
      type: String,
      require: false,
      default: ''
    }
  },
  data() {
    const commonRules = [{ required: true, message: '必填字段', trigger: 'blur' }]
    return {
      pictureUploadLoading: false,
      pictureFileList: [],
      form: {
        id: '',
        picture: '',
        name: ''
      },
      submitLoading: false,
      uploadLoading: false,
      rules: {
        picture: commonRules,
        name: commonRules,
        sysOrigin: commonRules
      }
    }
  },
  computed: {
    textOptTitle() {
      return `${(this.row && this.row.id ? '修改' : '新增')}(${this.sysOrigin})`
    },
    isShowPictureUpload() {
      return !this.form.picture
    },
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    row: {
      handler(val) {
        if (!val) {
          this.listRegion()
          return
        }
        const form = Object.assign({}, val)
        this.pictureFileList = getElementUiUploadFile(form.picture)
        this.form = Object.assign(this.form, form)
        this.listRegion()
      },
      immediate: true
    }
  },
  methods: {
    uploadPicture(file) {
      const that = this
      that.pictureUploadLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.other).then(res => {
        that.pictureUploadLoading = false
        that.form.picture = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.pictureUploadLoading = false
      })
    },
    handlePictureFileRemove(file, fileList) {
      this.form.picture = ''
      this.pictureUploadLoading = false
    },
    handleClose() {
      this.$emit('close')
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return
        }
        const submitForm = Object.assign({}, that.form)
        that.submitLoading = true

        if (!submitForm.sysOrigin) {
          submitForm.sysOrigin = that.sysOrigin
        }

        if (!submitForm.sysOrigin) {
          that.$message.error('系统错误, 没有平台信息!')
          return
        }
        if (submitForm.id) {
          updateActivityPicture(submitForm).then(res => {
            that.submitLoading = false
            that.$emit('success', res)
          }).catch(er => {
            that.submitLoading = false
            that.$emit('fial', er)
          })
          return
        }
        addActivityPicture(submitForm).then(res => {
          that.submitLoading = false
          that.$emit('success', res)
        }).catch(er => {
          that.submitLoading = false
          console.error(er)
          that.$emit('fial', er)
        })
      })
    }
  }
}
</script>
