<template>
  <div class="app-container">

    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width:120px;"
        class="filter-item"
        @change="changeSysOrigin"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        新增
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
      @cell-mouse-enter="handleMouseEnter"
    >

      <el-table-column label="归属系统" align="center">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column label="活动图片" align="center" width="150">
        <template slot-scope="scope">
          <el-image
            :lazy="true"
            style="width: 150px; height: 111px"
            :src="scope.row.picture"
            :preview-src-list="[scope.row.picture]"
          >
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline" />
            </div>
          </el-image>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="描述" align="center" />
      <el-table-column fixed="right" label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button type="text" @click.native="copyTextContent(scope.row.id)">复制活动内容</el-button>
          <el-button type="text" @click.native="handleUpdate()">修改</el-button>
          <el-button type="text" @click.native="handlDel(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <form-edit
      v-if="formEditVisible"
      :row="thatRow"
      :sys-origin="listQuery.sysOrigin"
      @close="formEditVisible = false"
      @success="formEditSuccess"
    />
  </div>
</template>

<script>
import { activityPicture, deleteActivityPicture } from '@/api/activity'
import Pagination from '@/components/Pagination'
import { sysOriginPlatforms } from '@/constant/origin'
import FormEdit from './form-edit.vue'
import { copyText } from '@/utils'
import { mapGetters } from 'vuex'

export default {
  components: { Pagination, FormEdit },
  data() {
    return {
      thatRow: {},
      textContent: '',
      sysOriginPlatforms,
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        sysOrigin: 'HALAR'
      },
      formEditVisible: false,
      listLoading: true
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        this.listQuery.cursor = 1
        this.listQuery.list = []
      }
      that.listLoading = true
      activityPicture(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    renderDataSuccess() {
      this.$message({
        message: '操作成功',
        type: 'success'
      })
      this.renderData()
    },
    changeSysOrigin() {
      this.handleSearch()
    },
    queryUserDetails(row) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = row.id
    },
    // 删除
    handlDel(row) {
      this.$confirm('确认删除吗？', '提示', {
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        deleteActivityPicture(row.id).then((res) => {
          this.listLoading = false
          this.$message({
            message: '删除成功',
            type: 'success'
          })
          this.renderData()
        })
      }).catch(() => {

      })
    },
    handleCreate() {
      this.thatRow = null
      this.formEditVisible = true
    },
    handleUpdate() {
      this.formEditVisible = true
    },
    handleMouseEnter(row) {
      this.thatRow = row
    },
    formEditSuccess() {
      this.formEditVisible = false
      this.renderData()
    },
    copyTextContent(text) {
      const that = this
      that.textContent = 'https://web.aswat1304.com/#/activity_picture_config/' + text
      copyText(that.textContent).then(() => {
        that.$message({
          message: '复制成功',
          type: 'success'
        })
      }).catch(() => {
        that.$message({
          message: '复制失败',
          type: 'error'
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">
.popover-content {
  max-width: 300px;
  line-height: 20px;
}
</style>
