<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="sysOrigins"
        placeholder="系统"
        style="width: 200px"
        class="filter-item"
        multiple
        collapse-tags
      >
        <el-option
          v-for="item in permissionsSysOriginPlatformAlls"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;">
            <sys-origin-icon
              :icon="item.value"
              :desc="item.value"
            /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.platform"
        placeholder="来源平台"
        clearable
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in originPlatforms"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <div class="filter-item">
        <account-input
          v-model="listQuery.userId"
          :sys-origin="
            sysOrigins && sysOrigins.length === 1 ? sysOrigins[0] : ''
          "
          placeholder="用户ID"
        />
      </div>

      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column type="index" width="50" label="No" />
      <el-table-column prop="orderPurchase.evn" label="环境" align="center" width="80">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.orderPurchase.evn === 'PROD'" type="success">{{
            scope.row.orderPurchase.evn
          }}</el-tag>
          <el-tag v-else-if="scope.row.orderPurchase.evn === 'TEST'" type="warning">{{
            scope.row.orderPurchase.evn
          }}</el-tag>
          <el-tag v-else type="danger">{{ scope.row.orderPurchase.evn }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="orderPurchase.sysOrigin" label="系统" align="center" width="150">
        <template slot-scope="scope">
          <sys-origin-icon
            :icon="scope.row.orderPurchase.sysOrigin"
            :desc="scope.row.orderPurchase.sysOrigin"
          />
        </template>
      </el-table-column>
      <el-table-column
        prop="expandTypeName"
        label="事件"
        align="center"
        width="100"
      />
      <el-table-column label="昵称" align="center">
        <template slot-scope="scope">
          <el-link
            v-if="scope.row.userBaseInfo && scope.row.userBaseInfo.userNickname"
            @click="queryUserDetails(scope.row.userBaseInfo.id)"
          >
            {{ scope.row.userBaseInfo.userNickname }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column
        prop="orderPurchase.productDescription"
        label="描述"
        align="center"
        width="100"
      />
      <el-table-column prop="orderPurchase.platform" label="平台" width="50" align="center">
        <template slot-scope="scope">
          <platform-svg-icon :icon="scope.row.orderPurchase.platform" />
        </template>
      </el-table-column>
      <el-table-column prop="orderPurchase.payPlatform" label="支付方式" align="center" />
      <el-table-column label="单价" align="center" width="120">
        <template slot-scope="scope">
          <div>
            {{ scope.row.orderPurchase.unitPrice
            }}<span v-if="scope.row.orderPurchase.unit">（{{ scope.row.orderPurchase.unit }}）</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="statusName"
        label="状态"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <el-tag type="danger">{{ scope.row.statusName }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="orderPurchase.trialPeriod"
        label="免费试用"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <div>{{ scope.row.orderPurchase.trialPeriod === true ? "是" : "否" }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="orderPurchase.expiresDateMs" label="过期时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.orderPurchase.expiresDateMs | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column prop="orderPurchase.purchaseDateMs" label="购买时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.orderPurchase.purchaseDateMs | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column prop="orderPurchase.updateTime" label="修改时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.orderPurchase.updateTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="100">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click.native="queryOrderDetails(scope.row)"
          >查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
    <user-deatils-drawer
      v-if="userDeatilsDrawerVisible"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawerVisible = false"
    />

  </div>
</template>

<script>
import { getPurchaseTable } from '@/api/purchase'
import { originPlatforms } from '@/constant/origin'
import Pagination from '@/components/Pagination'
import PlatformSvgIcon from '@/components/PlatformSvgIcon'
import { pickerOptions } from '@/constant/el-const'
import { mapGetters } from 'vuex'

export default {
  name: 'SubscriptionPurchasing',
  components: {
    Pagination,
    PlatformSvgIcon
  },
  data() {
    return {
      searchDisabled: false,
      pickerOptions,
      purchaseId: '',
      userDeatilsDrawerVisible: false,
      thatSelectedUserId: '',
      originPlatforms,
      list: [],
      total: 0,
      rangeDate: [],
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: '',
        platform: '',
        startTime: '',
        endTime: '',
        status: 'REIMBURSE',
        sysOrigin: ''
      },
      sysOrigins: [],
      listLoading: true,
      clickUserId: ''
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatformAlls'])
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    }
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatformAlls[0]
    if (!querySystem) {
      return
    }
    that.sysOrigins.push(querySystem.value)
    that.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      that.listQuery.sysOrigin =
        that.sysOrigins.length > 0
          ? that.sysOrigins.join(',')
          : that.permissionsSysOriginPlatformAlls
            .map(item => item.value)
            .join(',')
      getPurchaseTable(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    queryUserDetails(id) {
      this.userDeatilsDrawerVisible = true
      this.thatSelectedUserId = id
    },
    queryOrderDetails(row) {
      this.purchaseId = row.id
    }
  }
}
</script>
