<template>
  <div class="inside-operation-gold-log app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="item in tables" :key="item.component" :label="item.title" :name="item.component" />
      <component :is="activeName" :query-back-operation="true" />
    </el-tabs>
  </div>
</template>

<script>
import UserRunningWater from '@/views/order/candy/running-water'
import FreightRunningWater from '@/views/user/freight-info-list'
export default {
  components: { UserRunningWater, FreightRunningWater },
  data() {
    return {
      activeName: 'UserRunningWater',
      tables: [
        {
          title: '用户',
          component: 'UserRunningWater'
        },
        {
          title: '货运代理',
          component: 'FreightRunningWater'
        }
      ]

    }
  },
  created() {
  },
  methods: {
  }
}
</script>
