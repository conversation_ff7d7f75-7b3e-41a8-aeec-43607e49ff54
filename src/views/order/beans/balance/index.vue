<template>
  <div class="app-container">
    <div class="filter-container">
      <div class="filter-item">
        <account-input v-model="listQuery.userId" :sys-origin="listQuery.sysOrigin" placeholder="用户ID" />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="用户名称" align="center" show-overflow-tooltip min-width="220">
        <template slot-scope="scope">
          <div v-if="scope.row.userBaseInfo" class="box-header flex-l">
            <div class="avatar">
              <avatar :url="scope.row.userBaseInfo.userAvatar" :gender="scope.row.userBaseInfo.userSex" />
            </div>
            <div class="info nowrap-ellipsis">
              <div class="nickname">
                <el-link @click="queryUserDetails(scope.row.userBaseInfo.id)"><a :title="scope.row.userBaseInfo.userNickname"> {{ scope.row.userBaseInfo.userNickname }} </a></el-link>
              </div>
              <div class="sex-account">
                <gender :gender="scope.row.userBaseInfo.userSex" :gender-name="scope.row.userBaseInfo.userSexName" :desc="scope.row.userBaseInfo.account" />
                ({{ scope.row.userBaseInfo.accountStatusName }})
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="获得金币" align="center" prop="earnPoints" />
      <el-table-column label="消耗金币" align="center" prop="consumptionPoints" />
      <el-table-column label="金币余额" align="center" prop="balance" />
      <el-table-column prop="createTime" label="创建时间" width="200" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" label="修改时间" width="200" align="center">
        <template slot-scope="scope">
          {{ scope.row.updateTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button type="text" @click="clickAddOrSubtractBeans(scope.row)">赠送/扣除</el-button>
          <el-button type="text" @click="clickBeanRunningWater(scope.row)">流水</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <user-deatils-drawer
      v-if="userDeatilsDrawerVisible"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawerVisible=false"
    />

    <edit-user-bean-balance
      v-if="userBeanBalanceVisible"
      :user-id="thatSelectedUserId"
      @close="userBeanBalanceVisible=false"
      @success="handleSearch"
    />

    <el-dialog
      v-if="beanRunningWaterVisible"
      title="流水"
      :visible="true"
      width="60%"
      :before-close="() => beanRunningWaterVisible=false"
    >
      <div style="max-height:550px;overflow:auto;">
        <bean-running-water
          :user-id="thatSelectedUserId"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { pageUserBeanBalance } from '@/api/bean'
import { originPlatforms } from '@/constant/origin'
import { candyPurchasingTypes, currencyOrigins } from '@/constant/type'
import Pagination from '@/components/Pagination'
import { pickerOptions } from '@/constant/el-const'
import { mapGetters } from 'vuex'
import EditUserBeanBalance from '@/components/data/EditUserBeanBalance'
import BeanRunningWater from './../running-water'

export default {
  name: 'CandyPurchasing',
  components: { Pagination, EditUserBeanBalance, BeanRunningWater },
  data() {
    return {
      beanRunningWaterVisible: false,
      userBeanBalanceVisible: false,
      searchDisabled: false,
      pickerOptions,
      userDeatilsDrawerVisible: false,
      thatSelectedUserId: '',
      originPlatforms,
      candyPurchasingTypes,
      currencyOrigins,
      list: [],
      total: 0,
      rangeDate: [],
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: ''
      },
      listLoading: true,
      clickUserId: '',
      restaurants: []
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  mounted() {
    this.restaurants = this.currencyOrigins
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      pageUserBeanBalance(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    queryUserDetails(id) {
      this.userDeatilsDrawerVisible = true
      this.thatSelectedUserId = id
    },
    querySearch(queryString, cb) {
      var restaurants = currencyOrigins
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
      cb(results)
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0) || (restaurant.name.toLowerCase().indexOf(queryString.toLowerCase()) === 0)
      }
    },
    handleSelect(item) {
      this.renderData(true)
    },
    clickAddOrSubtractBeans(row) {
      this.thatSelectedUserId = row.userId
      this.userBeanBalanceVisible = true
    },
    clickBeanRunningWater(row) {
      this.thatSelectedUserId = row.userId
      this.beanRunningWaterVisible = true
    }
  }
}
</script>
