<template>
  <div :class="{'app-container': !userId}">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.type"
        placeholder="类型"
        clearable
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in candyPurchasingTypes"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <div class="filter-item">
        <el-autocomplete
          v-model="listQuery.origin"
          popper-class="my-autocomplete"
          :fetch-suggestions="querySearch"
          placeholder="请输入或选择内容"
          clearable
          @select="handleSelect"
          @clear="handleSelect"
        >
          <i
            slot="suffix"
            class="el-icon-edit el-input__icon"
          />
          <template slot-scope="{ item }">
            <span :label="item.name">{{ item.name }}</span>
            <div :key="item.value" :value="item.value" style="font-size:12px; color:#d8d0d0">{{ item.value }}</div>
          </template>
        </el-autocomplete>
      </div>
      <div v-if="!userId" class="filter-item">
        <account-input v-model="listQuery.userId" :sys-origin="listQuery.sysOrigin" placeholder="用户ID" />
      </div>
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="sysOrigin" label="系统" align="center" width="150">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column label="用户名称" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <div v-if="scope.row.userBaseInfo" class="box-header flex-l">
            <div class="avatar">
              <avatar :url="scope.row.userBaseInfo.userAvatar" :gender="scope.row.userBaseInfo.userSex" />
            </div>
            <div class="info nowrap-ellipsis">
              <div class="nickname">
                <el-link @click="queryUserDetails(scope.row.userBaseInfo.id)"><a :title="scope.row.userBaseInfo.userNickname"> {{ scope.row.userBaseInfo.userNickname }} </a></el-link>
              </div>
              <div class="sex-account">
                <gender :gender="scope.row.userBaseInfo.userSex" :gender-name="scope.row.userBaseInfo.userSexName" :desc="scope.row.userBaseInfo.account" />
                ({{ scope.row.userBaseInfo.accountStatusName }})
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="类型" align="center">
        <template slot-scope="scope">
          <div>
            <span v-if="scope.row.type === 0">收入</span>
            <span v-if="scope.row.type === 1">支出</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="数量" align="center">
        <template slot-scope="scope">
          <div>
            <span v-if="scope.row.type === 0" class="font-danger">+{{ scope.row.quantity }}</span>
            <span v-if="scope.row.type === 1">-{{ scope.row.quantity }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="remarks" label="备注" align="center" />
      <el-table-column prop="balance" label="余额" align="center" />
      <el-table-column prop="originName" label="来源" align="center" />
      <el-table-column prop="createTime" label="创建时间" width="200" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <user-deatils-drawer
      v-if="userDeatilsDrawerVisible"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawerVisible=false"
    />
  </div>
</template>

<script>

import { pageUserBeanRunningWater } from '@/api/bean'
import { originPlatforms } from '@/constant/origin'
import { candyPurchasingTypes, beanOrigins } from '@/constant/type'
import Pagination from '@/components/Pagination'
import { pickerOptions } from '@/constant/el-const'
import { mapGetters } from 'vuex'

export default {
  name: 'BeanRunningWater',
  components: { Pagination },
  props: {
    userId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isComp: false,
      searchDisabled: false,
      pickerOptions,
      userDeatilsDrawerVisible: false,
      thatSelectedUserId: '',
      originPlatforms,
      candyPurchasingTypes,
      beanOrigins,
      list: [],
      total: 0,
      rangeDate: [],
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: '',
        type: '',
        origin: '',
        startTime: '',
        endTime: '',
        sysOrigin: ''
      },
      listLoading: true,
      clickUserId: '',
      restaurants: []
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    userId: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal) {
          this.isComp = true
          this.listQuery.userId = newVal
        }
      }
    },
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    }
  },
  created() {
    const that = this
    if (!that.isComp) {
      const querySystem = this.permissionsSysOriginPlatforms[0]
      if (!querySystem) {
        return
      }
      that.listQuery.sysOrigin = querySystem.value
    }
    that.renderData()
  },
  mounted() {
    this.restaurants = this.beanOrigins
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      pageUserBeanRunningWater(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    queryUserDetails(id) {
      this.userDeatilsDrawerVisible = true
      this.thatSelectedUserId = id
    },
    querySearch(queryString, cb) {
      var restaurants = beanOrigins
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
      cb(results)
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0) || (restaurant.name.toLowerCase().indexOf(queryString.toLowerCase()) === 0)
      }
    },
    handleSelect(item) {
      this.renderData(true)
    }
  }
}
</script>
