<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;">
            <sys-origin-icon
              :icon="item.value"
              :desc="item.value"
            /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.status"
        placeholder="状态"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in statusValues"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="listQuery.platform"
        placeholder="平台"
        clearable
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in originPlatforms"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>

      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="sysOrigin" label="系统" align="center" width="150">
        <template slot-scope="scope">
          <sys-origin-icon
            :icon="scope.row.sysOrigin"
            :desc="scope.row.sysOrigin"
          />
        </template>
      </el-table-column>
      <el-table-column prop="platform" label="平台" width="50" align="center">
        <template slot-scope="scope">
          <platform-svg-icon :icon="scope.row.platform" />
        </template>
      </el-table-column>
      <el-table-column label="订单编号" align="center">
        <template slot-scope="scope">
          <div>{{ scope.row.originalOrderId }} / {{ scope.row.orderId }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="productCode" label="产品编号" align="center" />
      <el-table-column label="单据" align="center">
        <template slot-scope="scope">
          <el-link @click="copyCertificate(scope.row)">
            点击复制
          </el-link>
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        width="200"
        align="center"
      >
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="isShowOperation"
        fixed="right"
        label="操作"
        align="center"
        width="100"
      >
        <template slot-scope="scope">
          <el-dropdown>
            <span class="el-dropdown-link">
              变更状态
            </span>
            <el-dropdown-menu slot="dropdown">
              <div v-for="(item, index) in statusValues" :key="index">
                <el-dropdown-item
                  v-if="item.value !== scope.row.status"
                  @click.native="changeStatus(scope.row, item.value)"
                >{{ item.name }}</el-dropdown-item>
              </div>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
  </div>
</template>

<script>
import {
  getOrderAbnormalPage,
  getOrderAbnormalCertificate,
  updateOrderAbnormalStatus
} from '@/api/purchase'
import { originPlatforms } from '@/constant/origin'
import Pagination from '@/components/Pagination'
import { pickerOptions } from '@/constant/el-const'
import PlatformSvgIcon from '@/components/PlatformSvgIcon'
import { copyText } from '@/utils'
import { mapGetters } from 'vuex'

export default {
  name: 'CandyPurchasing',
  components: { Pagination, PlatformSvgIcon },
  data() {
    return {
      isShowOperation: true,
      statusValues: [
        { value: 'PENDING', name: '待处理' },
        { value: 'PROCESSED', name: '已处理' },
        { value: 'IGNORE', name: '忽略' }
      ],
      pickerOptions,
      originPlatforms,
      list: [],
      total: 0,
      rangeDate: [],
      listQuery: {
        cursor: 1,
        limit: 20,
        platform: '',
        status: 'PENDING',
        startTime: '',
        endTime: '',
        sysOrigin: ''
      },
      listLoading: true,
      clickUserId: ''
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    }
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      getOrderAbnormalPage(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
      this.isShowOperation = this.listQuery.status === 'PENDING'
    },
    copyCertificate(row) {
      const that = this
      getOrderAbnormalCertificate(row.id).then(res => {
        copyText(res.body)
          .then(() => {
            that.$opsMessage.success()
          })
          .catch(() => {
            that.$opsMessage.fail()
          })
      })
    },
    changeStatus(row, status) {
      const that = this
      updateOrderAbnormalStatus({ id: row.id, status })
        .then(res => {
          that.renderData()
        })
        .catch(er => {
          console.error(er)
        })
    }
  }
}
</script>
