<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="item in tables" :key="item.name" :label="item.title" :name="item.component" />
      <component :is="activeName" />
    </el-tabs>
  </div>
</template>
<script>
import RunningWaterTimelyPreview from './timely'
export default {
  name: 'GameCouponRunningWater',
  components: { RunningWaterTimelyPreview },
  data() {
    return {
      activeName: 'RunningWaterTimelyPreview',
      tables: [
        {
          title: '游戏券收支',
          component: 'RunningWaterTimelyPreview'
        }
      ]
    }
  }
}
</script>
