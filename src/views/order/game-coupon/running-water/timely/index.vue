<template>
  <div>
    <div class="filter-container">
      <el-select
        v-model="sysOrigins"
        placeholder="系统"
        style="width: 200px"
        class="filter-item"
        multiple
        collapse-tags
      >
        <el-option
          v-for="item in permissionsSysOriginPlatformAlls"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;">
            <sys-origin-icon
              :icon="item.value"
              :desc="item.value"
            /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>

      <el-select
        v-model="listQuery.platform"
        placeholder="平台"
        clearable
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in originPlatforms"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="listQuery.type"
        placeholder="类型"
        clearable
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in candyPurchasingTypes"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <div class="filter-item">
        <el-autocomplete
          v-model="listQuery.origin"
          popper-class="my-autocomplete"
          :fetch-suggestions="querySearch"
          placeholder="请输入或选择内容"
          clearable
          @select="handleSelect"
          @clear="handleSelect"
        >
          <i slot="suffix" class="el-icon-edit el-input__icon" />
          <template slot-scope="{ item }">
            <span :label="item.name">{{ item.name }}</span>
            <div
              :key="item.value"
              :value="item.value"
              style="font-size:12px; color:#d8d0d0"
            >
              {{ item.value }}
            </div>
          </template>
        </el-autocomplete>
      </div>
      <div class="filter-item">
        <account-input
          v-model="listQuery.userId"
          :sys-origin="
            sysOrigins && sysOrigins.length === 1 ? sysOrigins[0] : ''
          "
          placeholder="用户ID"
        />
      </div>
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="sysOrigin" label="系统" align="center" width="150">
        <template slot-scope="scope">
          <sys-origin-icon
            :icon="scope.row.sysOrigin"
            :desc="scope.row.sysOrigin"
          />
        </template>
      </el-table-column>
      <el-table-column label="类型" align="center">
        <template slot-scope="scope">
          <div>
            <span v-if="scope.row.type === 0">收入</span>
            <span v-if="scope.row.type === 1">支出</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="游戏券数量" align="center">
        <template slot-scope="scope">
          <div>
            <span
              v-if="scope.row.type === 0"
              class="font-danger"
            >+{{ scope.row.couponNumber }}</span>
            <span
              v-if="scope.row.type === 1"
            >-{{ scope.row.couponNumber }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="昵称" align="center">
        <template slot-scope="scope">
          <el-link
            v-if="scope.row.userBaseInfo && scope.row.userBaseInfo.userNickname"
            @click="queryUserDetails(scope.row.userBaseInfo.id)"
          >
            {{ scope.row.userBaseInfo.userNickname }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="remarks" label="备注" align="center" />
      <el-table-column prop="balance" label="余额" align="center" />
      <el-table-column prop="originName" label="来源" align="center" />
      <!-- <el-table-column prop="platform" label="平台" align="center" /> -->
      <el-table-column
        prop="createTime"
        label="创建时间"
        width="200"
        align="center"
      >
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <user-deatils-drawer
      v-if="userDeatilsDrawerVisible"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawerVisible = false"
    />
  </div>
</template>

<script>
import {
  getGameCouponRunningWaterDetailsTable,
  originList
} from '@/api/game-coupon'
import { originPlatforms } from '@/constant/origin'
import { candyPurchasingTypes } from '@/constant/type'
import Pagination from '@/components/Pagination'
import { pickerOptions } from '@/constant/el-const'
import { mapGetters } from 'vuex'

export default {
  name: 'CandyPurchasing',
  components: { Pagination },
  data() {
    return {
      searchDisabled: false,
      pickerOptions,
      userDeatilsDrawerVisible: false,
      thatSelectedUserId: '',
      originPlatforms,
      candyPurchasingTypes,
      origins: [],
      list: [],
      total: 0,
      rangeDate: [],
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: '',
        platform: '',
        type: '',
        origin: '',
        startTime: '',
        endTime: '',
        sysOrigin: ''
      },
      sysOrigins: [],
      listLoading: true,
      clickUserId: '',
      restaurants: []
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatformAlls'])
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    }
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatformAlls[0]
    if (!querySystem) {
      return
    }
    that.sysOrigins.push(querySystem.value)
    that.renderData()
  },
  mounted() {
    originList().then(res => {
      const { body } = res
      this.origins = body
    })
    this.restaurants = this.origins
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      that.listQuery.sysOrigin =
        that.sysOrigins.length > 0
          ? that.sysOrigins.join(',')
          : that.permissionsSysOriginPlatformAlls
            .map(item => item.value)
            .join(',')
      getGameCouponRunningWaterDetailsTable(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      if (this.listQuery.origin !== '') {
        const origins = this.origins.filter(
          this.createFilter(this.listQuery.origin)
        )
        this.listQuery.origin =
          origins.length > 0 ? origins[0].value : this.listQuery.origin
      }
      this.renderData(true)
    },
    queryUserDetails(id) {
      this.userDeatilsDrawerVisible = true
      this.thatSelectedUserId = id
    },
    querySearch(queryString, cb) {
      var restaurants = this.origins
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants
      cb(results)
    },
    createFilter(queryString) {
      return restaurant => {
        return (
          restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) ===
            0 ||
          restaurant.name.toLowerCase().indexOf(queryString.toLowerCase()) === 0
        )
      }
    },
    handleSelect(item) {
      this.renderData(true)
    }
  }
}
</script>
