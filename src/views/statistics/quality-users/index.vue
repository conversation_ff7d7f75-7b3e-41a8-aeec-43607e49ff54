<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="item in tables" :key="item.name" :label="item.title" :name="item.component" />
      <component :is="activeName" />
    </el-tabs>
  </div>
</template>
<script>
import TotalPreview from './total-preview'
import MonthPreview from './month-preview'
export default {
  name: 'StatisticsQuailtyUsersIndex',
  components: { TotalPreview, MonthPreview },
  data() {
    return {
      activeName: 'TotalPreview',
      tables: [
        {
          title: '总预览',
          component: 'TotalPreview'
        },
        {
          title: '每月预览',
          component: 'MonthPreview'
        }
      ]
    }
  }
}
</script>
