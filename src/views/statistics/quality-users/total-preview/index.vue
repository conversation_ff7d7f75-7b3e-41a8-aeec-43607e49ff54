<template>
  <div class="app-container-quality">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <account-input v-model="listQuery.userId" :sys-origin="listQuery.sysOrigin" placeholder="用户ID" />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="用户" align="center" width="200">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column label="总额" prop="total" align="center" width="150" />
      <el-table-column label="充值明细" prop="onlineQuantity" align="center">
        <template slot-scope="scope">
          <div class="recharge-details">
            <el-tag v-for="(item, index) in scope.row.totalRecharges" :key="index" class="item" type="info">{{ getRechargeTypeDes(item.type) }}:{{ item.amount }}</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="备注" prop="remark" align="center" />
      <el-table-column fixed="right" label="操作" align="center" width="100">
        <template slot-scope="scope">
          <el-dropdown>
            <span class="el-dropdown-link">
              <i class="el-icon-more" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="handleRemark(scope.row)">备注</el-dropdown-item>
              <el-dropdown-item @click.native="handleDetails(scope.row)">每月明细</el-dropdown-item>
              <el-dropdown-item @click.native="clickQueryThisMonthRecharge(scope.row.userProfile.id)">本月明细</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <user-deatils-drawer
      v-if="userDeatilsDrawerVisible"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawerVisible=false"
    />

    <monthly-users-details
      v-if="userDialogVisible"
      :user-id="thatSelectedDetailsUserId"
      @close="userDialogVisible=false"
    />

    <el-dialog
      title="备注信息"
      :visible.sync="formVisible"
      :before-close="handleClose"
      :close-on-click-modal="false"
      width="400px"
    >
      <el-form ref="form" v-loading="submitLoading" :model="form" label-width="0px">
        <el-form-item prop="remark">
          <el-input
            v-model.trim="form.remark"
            type="textarea"
            resize="none"
            :rows="5"
            maxlength="200"
            show-word-limit
            placeholder="备注信息，最多200个字"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm()">保存</el-button>
        <el-button @click="handleClose()">取消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      :title="'本月充值明细'+ (thisMonthRecharge.rechargeDate ? '('+thisMonthRecharge.rechargeDate+')' : '' )"
      :visible.sync="thisMonthRechargeVisible"
      width="400px"
    >
      <div v-loading="thisMonthRechargeLoading" style="padding-bottom:20px">
        <div v-if="!thisMonthRechargeLoading && !thisMonthRecharge.rechargeDate" class="recharge-tips">没有充值记录, 快去提醒一下该充值了~</div>
        <div v-else>
          <div class="this-recharge-details flex-b flex-wrap">
            <div v-for="(item, index) in thisMonthRecharge.recharges" :key="index" class="item" :span="24">{{ getRechargeTypeDes(item.type) }}：{{ item.amount }} </div>
          </div>
          <div class="recharge-total"> 总额: {{ thisMonthRecharge.totalAmount || 0 }}</div>
        </div>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import { pageQualityUsers, qualityUserRemarkSave, getThisMonthRechargeByUserId } from '@/api/statistics'
import Pagination from '@/components/Pagination'
import { mapGetters } from 'vuex'
import { rechargeTypeDesMap } from '@/constant/type'
import MonthlyUsersDetails from './monthly-users-details.vue'

function getFormData() {
  return {
    id: '',
    remark: ''
  }
}

export default {
  name: 'TotalPreviewQualityUsers',
  components: { Pagination, MonthlyUsersDetails },
  data() {
    return {
      searchDisabled: false,
      form: getFormData(),
      submitLoading: false,
      formVisible: false,
      thisMonthRechargeVisible: false,
      userDeatilsDrawerVisible: false,
      thatSelectedUserId: '',
      userDialogVisible: false,
      thatSelectedDetailsUserId: '',
      list: [],
      total: 0,
      rangeDate: [],
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: '',
        sysOrigin: ''
      },
      listLoading: true,
      clickUserId: '',
      thisMonthRecharge: {},
      thisMonthRechargeLoading: false
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      pageQualityUsers(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleClose() {
      this.formVisible = false
      this.resetForm()
    },
    getRechargeTypeDes(type) {
      return rechargeTypeDesMap[type] || type
    },
    resetForm() {
      this.form = getFormData()
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (valid) {
          that.submitLoading = true
          qualityUserRemarkSave(that.form).then(res => {
            that.$opsMessage.success()
            that.submitLoading = false
            that.formVisible = false
            that.resetForm()
            that.renderData(true)
          }).catch(er => {
            that.submitLoading = false
            console.error(er)
            this.$emit('fail')
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    queryUserDetails(id) {
      this.userDeatilsDrawerVisible = true
      this.thatSelectedUserId = id
    },
    handleDetails(row) {
      this.thatSelectedDetailsUserId = String(row.userProfile.id)
      this.userDialogVisible = true
    },
    handleRemark(row) {
      this.form.id = row.userProfile.id
      this.form.remark = row.remark
      this.formVisible = true
    },
    clickQueryThisMonthRecharge(userId) {
      this.thisMonthRechargeVisible = true
      this.thisMonthRechargeLoading = true
      getThisMonthRechargeByUserId(userId).then(res => {
        this.thisMonthRechargeLoading = false
        this.thisMonthRecharge = res.body || {}
      }).catch(er => {
        this.thisMonthRechargeLoading = false
        console.error(er)
      })
    }
  }
}
</script>
<style lang="scss">
.app-container-quality {
  .el-dialog__body {
    padding: 20px 20px 0px 20px;
  }
}
</style>
<style scoped lang="scss">

 .recharge-tips {
   text-align: center;
   padding-bottom: 20px;
 }
 .this-recharge-details {
    width: 100%;
    overflow: auto;
   .item {
     width: 50%;
     line-height: 30px;
   }
 }

.recharge-details {
  width: 100%;
  overflow: auto;
  white-space: nowrap;
  text-align: left;
  .item {
    margin: 0px 5px;
  }
}
.recharge-total {
  margin-top: 20px;
  border-radius: 5px;
  padding: 5px 0px;
  font-weight: bold;
}

</style>
