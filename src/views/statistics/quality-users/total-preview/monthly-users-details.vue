<template>
  <el-dialog
    title="每月明细"
    :visible="true"
    :before-close="handleClose"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    width="80%"
  >
    <div class="app-container">
      <el-table
        v-loading="listLoading"
        :data="list"
        :before-close="handleClose"
        element-loading-text="Loading"
        fit
        highlight-current-row
        max-height="350px"
      >
        <el-table-column label="日期" prop="rechargeDate" align="center" width="100" />
        <el-table-column label="总额" prop="total" align="center" width="100" />
        <el-table-column label="充值明细" prop="onlineQuantity" align="center">
          <template slot-scope="scope">
            <div class="recharge-details">
              <el-tag v-for="(item, index) in scope.row.recharges" :key="index" class="item" type="info">{{ getRechargeTypeDes(item.type) }}:{{ item.amount }}</el-tag>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="listQuery.cursor"
        :limit.sync="listQuery.limit"
        @pagination="renderData"
      />

    </div>
  </el-dialog>
</template>

<script>
import { pageQualityUsersDetails } from '@/api/statistics'
import Pagination from '@/components/Pagination'
import { rechargeTypeDesMap } from '@/constant/type'

export default {
  name: 'QualityUsersDetails',
  components: { Pagination },
  props: {
    userId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: ''
      },
      listLoading: false,
      searchDisabled: false,
      thatSelectedUserId: ''
    }
  },
  watch: {
    userId: {
      handler(newVal) {
        this.listQuery.userId = newVal
        this.renderData()
      },
      immediate: true
    }
  },
  methods: {
    renderData() {
      const that = this
      if (!that.listQuery.userId) {
        return
      }
      that.listLoading = true
      pageQualityUsersDetails(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleClose() {
      this.$emit('close')
    },
    getRechargeTypeDes(type) {
      return rechargeTypeDesMap[type] || type
    },
    handleSearch() {
      this.renderData(true)
    },
    getTime(time) {
      return String(time).substring(0, 4) + '年 ' + String(time).substring(4) + '月'
    },
    loadSearchUser() {
      this.searchDisabled = true
    },
    searchUserSuccess(res) {
      this.searchDisabled = false
      this.userInfo = res
      if (!res) {
        return
      }
      this.listQuery.userId = res.id
    },
    searchUserFail() {
      this.listQuery.userId = ''
      this.searchDisabled = false
    }
  }
}
</script>
<style scoped lang="scss">
 .recharge-details {
    width: 100%;
    overflow: auto;
    white-space: nowrap;
    text-align: left;
   .item {
     margin: 0px 5px;
   }
 }
</style>
