<template>
  <div class="app-container-month">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <account-input v-model="listQuery.userId" :sys-origin="listQuery.sysOrigin" placeholder="用户ID" />
      </div>
      <div class="filter-item">
        <el-date-picker
          v-model="listQuery.rechargeDate"
          type="month"
          value-format="yyyyMM"
          :clearable="false"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="用户" align="center" width="200">

        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column label="总额" prop="totalAmount" align="center" width="150" />
      <el-table-column label="充值明细" align="center">
        <template slot-scope="scope">
          <div class="recharge-details">
            <el-tag v-for="(item, index) in scope.row.recharges" :key="index" class="item" type="info">{{ getRechargeTypeDes(item.type) }}:{{ item.amount }}</el-tag>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

  </div>
</template>

<script>

import { pageMonthlyQualityUsers } from '@/api/statistics'
import Pagination from '@/components/Pagination'
import { mapGetters } from 'vuex'
import { rechargeTypeDesMap } from '@/constant/type'
import { formatDate } from '@/utils'

export default {
  name: 'MonthlyPreviewQualityUsers',
  components: { Pagination },
  data() {
    return {
      searchDisabled: false,
      submitLoading: false,
      formVisible: false,
      thisMonthRechargeVisible: false,
      thatSelectedUserId: '',
      userDialogVisible: false,
      thatSelectedDetailsUserId: '',
      list: [],
      total: 0,
      rangeDate: [],
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: '',
        sysOrigin: '',
        rechargeDate: formatDate(new Date(), 'yyyyMM')
      },
      listLoading: true,
      clickUserId: '',
      thisMonthRecharge: {},
      thisMonthRechargeLoading: false
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      pageMonthlyQualityUsers(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleClose() {
      this.formVisible = false
      this.resetForm()
    },
    getRechargeTypeDes(type) {
      return rechargeTypeDesMap[type] || type
    },
    handleSearch() {
      this.renderData(true)
    },
    searchUserFail() {
      this.listQuery.userId = ''
      this.searchDisabled = false
    }

  }
}
</script>
<style scoped lang="scss">
 .store-table-expand {
    font-size: 0;
    label {
    width: 90px;
    color: #99a9bf;
    .el-form-item {
      margin-right: 0;
      margin-bottom: 0;
      width: 50%;
    }
   }
 }

 .recharge-tips {
   text-align: center;
   padding-bottom: 20px;
 }
 .this-recharge-details {
    width: 100%;
    overflow: auto;
   .item {
     width: 50%;
     line-height: 30px;
   }
 }

.recharge-details {
  width: 100%;
  overflow: auto;
  white-space: nowrap;
  text-align: left;
  .item {
    margin: 0px 5px;
  }
}
.recharge-total {
  margin-top: 20px;
  border-radius: 5px;
  padding: 5px 0px;
  font-weight: bold;
}

</style>
