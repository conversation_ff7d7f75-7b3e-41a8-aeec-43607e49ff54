<template>
  <div class="online-hourly-user-charts">
    <div id="videoUserCharts" ref="videoUserCharts" style="width: 100%;height: 4.7rem;" />
  </div>
</template>

<script>

import { formatDate } from '@/utils'
import { getVideoUsageRateTimeCharts } from '@/api/statistics'

export default {
  name: 'OnlineHourlyUserCharts',
  props: {
    timeOutSecond: {
      type: Number,
      required: false,
      default: 60 * 5
    }
  },
  data() {
    const now = Date.now()
    return {
      listLoading: true,
      listQuery: {
        year: formatDate(now, 'yyyy'),
        month: formatDate(now, 'MM'),
        day: formatDate(now, 'dd')
      },
      rangeDate: formatDate(now, 'yyyy-MM-dd'),
      list: [],
      videoRealTimeUserCharts: null,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > now
        }
      }
    }
  },
  created() {
    const that = this
    that.renderData()
    var intervalIndex = setInterval(() => {
      that.renderData()
    }, that.timeOutSecond * 1000)
    that.$store.dispatch('app/pushRunTask', intervalIndex)
  },
  mounted() {
    const that = this
    that.videoRealTimeUserCharts = that.$echarts.init(that.$refs.videoUserCharts)
    window.onresize = function() {
      that.videoRealTimeUserCharts.resize()
    }
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      getVideoUsageRateTimeCharts(that.listQuery).then(res => {
        const { body } = res
        that.list = body
        that.renderCharts()
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData()
    },
    renderCharts() {
      const that = this
      // const lastIndex = that.list.length > 8 ? that.list.length - 8 : 0
      const matchSizeSeries = []
      // const matchUserSeries = []
      that.list.forEach((item, index) => {
        // if (index < lastIndex) {
        //   return
        // }
        const date = formatDate(item.minTime, 'yyyy-MM-dd HH:mm')
        const matchSize = item.matchSize || 0
        matchSizeSeries.push([date, matchSize])

        // const matchUserSize = item.matchUserSize || 0
        // matchUserSeries.push([date, matchUserSize])
      })

      that.renderVideoRealTimeUserCharts({ matchSizeSeries })
    },
    renderVideoRealTimeUserCharts({ matchSizeSeries }) {
      const that = this
      that.videoRealTimeUserCharts.setOption({
        color: ['#6450F9'],
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
          borderColor: '#eee'
        },
        xAxis: [
          {
            type: 'category',
            axisLabel: {
              rotate: 38,
              formatter: function(value) {
                return formatDate(value, 'HH')
              }
            },
            axisLine: {
              lineStyle: {
                color: '#fff'
              }
            },
            splitLine: { lineStyle: { color: ['#eee'] }}
          }
        ],
        yAxis: [{
          type: 'value',
          axisTick: { show: true, length: 0 },
          splitNumber: 5,
          splitLine: { lineStyle: { color: ['#eee'] }},
          axisLine: {
            lineStyle: {
              color: '#fff'
            }
          }
        }],
        legend: {
          show: false,
          textStyle: { color: '#8e929b' }
        },
        series: [
          {
            name: '用户量',
            type: 'bar',
            barWidth: 8,
            data: matchSizeSeries
          }]
      }, true)
    }
  }
}
</script>
