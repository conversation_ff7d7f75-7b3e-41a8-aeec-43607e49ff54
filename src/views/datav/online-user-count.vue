<template>
  <div class="online-user-counts">
    <el-row>
      <el-col class="count" :span="6">
        <div class="title">总人数</div>
        <div><countTo :start-val="0" :end-val="userTotal" :duration="3000" /></div>
      </el-col>
      <el-col class="count" :span="6">
        <div class="title">女生</div>
        <div><countTo :start-val="0" :end-val="femaleUserCount" :duration="3000" /></div>
      </el-col>
      <el-col class="count" :span="6">
        <div class="title">男生</div>
        <div><countTo :start-val="0" :end-val="maleUserCount" :duration="3000" /></div>
      </el-col>
      <el-col class="count" :span="6">
        <div class="title">主播</div>
        <div><countTo :start-val="0" :end-val="anchorCount" :duration="3000" /></div>
      </el-col>
      <!--      <el-col class="count" :span="4">-->
      <!--        <div class="title">会员</div>-->
      <!--        <div><countTo :start-val="0" :end-val="vipCount" :duration="3000" /></div>-->
      <!--      </el-col>-->
      <!--      <el-col class="count" :span="4">-->
      <!--        <div class="title">PLUS</div>-->
      <!--        <div><countTo :start-val="0" :end-val="plusCount" :duration="3000" /></div>-->
      <!--      </el-col>-->
    </el-row>
  </div>
</template>

<script>
import { onlineUserCount } from '@/api/datav'
export default {
  name: 'OnlineUserCount',
  props: {
    timeOutSecond: {
      type: Number,
      required: false,
      default: 30
    }
  },
  data() {
    return {
      userTotal: 0,
      plusCount: 0,
      vipCount: 0,
      femaleUserCount: 0,
      anchorCount: 0,
      maleUserCount: 0
    }
  },
  created() {
    const that = this
    that.loadData()
    var intervalIndex = setInterval(() => {
      that.loadData()
    }, that.timeOutSecond * 1000)
    that.$store.dispatch('app/pushRunTask', intervalIndex)
  },
  methods: {
    loadData() {
      const that = this
      onlineUserCount().then(res => {
        that.userTotal = res.body || 0
      }).catch(er => {})

      onlineUserCount({ gender: 0 }).then(res => {
        that.femaleUserCount = res.body || 0
      }).catch(er => {})

      onlineUserCount({ gender: 0, anchor: true }).then(res => {
        that.anchorCount = res.body || 0
      }).catch(er => {})

      onlineUserCount({ gender: 1 }).then(res => {
        that.maleUserCount = res.body || 0
      }).catch(er => {})

      onlineUserCount({ gender: 1, candy: true }).then(res => {
        that.plusCount = res.body || 0
      }).catch(er => {})

      onlineUserCount({ gender: 1, vip: true }).then(res => {
        that.vipCount = res.body || 0
      }).catch(er => {})
    }
  }
}
</script>
<style scoped lang="scss">
.online-user-counts {
    .count {
      text-align: center;
      line-height: .6rem;
      font-size: 0.4rem;
      padding-bottom: .2rem;
    }
}
</style>
