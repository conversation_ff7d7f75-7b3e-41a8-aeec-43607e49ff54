<template>
  <div class="latest-purchase">
    <vue-seamless-scroll :data="list" :class="{'seamless-warp-mobile': mobile , 'seamless-warp': !mobile}">
      <div class="pay-user-info">
        <div v-for="(item,index) in list" :key="index" class="user-info">
          <div class="left">
            <avatar :gender="item.userBaseInfo.userSex" :url="item.userBaseInfo.userAvatar" customize-style="width: 1rem; height: 1rem;border-radius: 50%" />
          </div>
          <div class="right">
            <div class="right-head">
              <div class="flag-icon"><flag-icon :code="item.userBaseInfo.countryCode" size="0.5rem" /></div>
              <span class="nickname">{{ item.userBaseInfo.userNickname }}</span>
            </div>
            <div class="right-content">
              <div class="right-content-item">{{ item.orderPurchase.createTime | formatTime }}</div>
              <div class="right-content-item product-name">{{ item.orderPurchase.productDescription }}</div>
              <div class="right-content-item">{{ item.orderPurchase.unitPrice }}</div>
            </div>
          </div>
        </div>
      </div>
    </vue-seamless-scroll>

  </div>
</template>

<script>
import { mobile } from '@/utils/device'
import Avatar from '@/components/data/Avatar'
import { getPurchaseTable } from '@/api/purchase'
import { mapGetters } from 'vuex'
export default {
  name: 'DatavLatestPurchase',
  components: { Avatar },
  props: {
    timeOutSecond: {
      type: Number,
      required: false,
      default: 60
    }
  },
  data() {
    return {
      mobile: mobile(),
      list: []
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms']),
    permissionsSysOriginCode() {
      if (!this.permissionsSysOriginPlatforms) {
        return []
      }
      return this.permissionsSysOriginPlatforms.map(item => item.value)
    }
  },
  created() {
    const that = this
    that.renderData()
    var intervalIndex = setInterval(() => {
      that.renderData()
    }, that.timeOutSecond * 1000)
    that.$store.dispatch('app/pushRunTask', intervalIndex)
  },
  methods: {
    renderData() {
      const that = this
      getPurchaseTable({ cursor: 1, limit: 60, sysOrigin: this.permissionsSysOriginCode.join(',') }).then(res => {
        that.list = res.body.records || []
      }).catch(er => {})
    }
  }
}
</script>
<style scoped lang="scss">
.seamless-warp {
  height: 13rem;
  overflow: hidden;
}
.seamless-warp-mobile {
  height: 6rem;
  overflow: hidden;
}
.pay-user-info {
  .user-info {
      display: flex;
      overflow: hidden;
      text-overflow:ellipsis;
      white-space: nowrap;
      padding: 0.1rem;
      > .left {
          width: 0.6rem;
          text-align: center;
      }
      > .right {
          width: 100%;
          padding-left: .6rem;
          .right-head {
              .flag-icon {
                  display: inline-block;
                  vertical-align: middle;
              }
          }
          .right-content {
              display: flex;
              color: #909399;
              > .right-content-item {
                  width: 100%;
                  padding-right: 1rem;
                  font-size: .3rem;
              }
              > .product-name{
                  color: #03c2ec;
              }
          }
      }
  }
}
</style>
