<template>
  <div class="online-user-map">
    <div id="onlineUserMapCharts" ref="onlineUserMapCharts" style="width: 100%;height: 5.5rem;" />
  </div>
</template>

<script>
import datavGeomap from '@/assets/data/datav_geomap.json'
import { latestActiveUserCountryCode } from '@/api/datav'
export default {
  name: 'OnlineUserMap',
  props: {
    timeOutSecond: {
      type: Number,
      required: false,
      default: 60
    }},
  data() {
    return {
      onlineUserMapCharts: null
    }
  },
  mounted() {
    const that = this
    that.onlineUserMapCharts = that.$echarts.init(that.$refs.onlineUserMapCharts)
    that.loadMapCharts()
    var intervalIndex = setInterval(() => {
      that.loadMapCharts()
    }, that.timeOutSecond * 1000)
    that.$store.dispatch('app/pushRunTask', intervalIndex)
  },
  methods: {
    loadMapCharts() {
      const that = this
      latestActiveUserCountryCode().then(res => {
        that.$echarts.registerMap('GLOBAL', datavGeomap)
        that.onlineUserMapCharts.setOption({
          tooltip: {
            show: false
          },
          visualMap: {
            min: 0,
            max: 1000,
            text: ['High', 'Low'],
            realtime: false,
            calculable: true,
            inRange: {
              color: ['lightskyblue', 'yellow', 'orangered']
            }
          },
          series: [
            {
              name: '在线人员分布国家',
              type: 'map',
              mapType: 'GLOBAL',
              nameProperty: 'id',
              label: {
                show: false
              },
              data: res.body || []
            }
          ]
        })
      }).catch(er => {})
    }
  }
}
</script>
<style scoped lang="scss">
</style>
