<template>
  <div class="app-container-datav">
    <div ref="datavFullscreen" class="office-efficiency-index" @dblclick="screen">
      <div v-if="!mobile" class="office-header">
        <div class="title-info">数据大屏</div>
        <div class="analysis-info">实时数据预览</div>
      </div>
      <el-row v-if="buttonPermissions.includes('datav:query')" class="office-content">
        <el-col :xs="24" :sm="6">
          <el-row>
            <el-col>
              <div id="today-pay" class="col-info">
                <div class="title">购买用户</div>
                <div class="content">
                  <latest-purchase />
                </div>
              </div>
            </el-col>
            <!-- <el-col>
              <div id="today-pay-tim-chat" class="col-info">
                <div class="content">
                  <latest-purchase-tim-chat />
                </div>
              </div>
            </el-col> -->
          </el-row>
        </el-col>
        <el-col :xs="24" :sm="18">
          <el-row>
            <el-col v-if="buttonPermissions.includes('datav:paid:preview')" :span="24">
              <div id="online-info" class="col-info">
                <div class="title">今日付费预览</div>
                <div class="content">
                  <purchase-count />
                </div>
              </div>
            </el-col>

            <el-col  :span="24">
              <div id="online-info-tim-chat" class="col-info">
                <div class="title">MARCIE在线</div>
                <div class="content">
                  <online-user-count-origin origin="MARCIE" />
                </div>
              </div>
            </el-col>
            <!-- <el-col  :span="24">
              <div id="online-info-tim-chat" class="col-info">
                <div class="title">Yahlla在线</div>
                <div class="content">
                  <online-user-count-origin origin="YAHLLA" />
                </div>
              </div>
            </el-col> -->

          </el-row>

        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
// import OnlineUserMap from './online-user-map'
import LatestPurchase from './latest-purchase'
// import LatestPurchaseTimChat from './latest-purchase-timchat'
import OnlineUserCountOrigin from './online-user-count-origin'
import PurchaseCount from './purchase-count'
import { mapGetters } from 'vuex'
import { mobile } from '@/utils/device'
export default {
  name: 'DatavContent',
  components: { LatestPurchase, OnlineUserCountOrigin, PurchaseCount },
  data() {
    return {
      mobile: mobile()
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms', 'buttonPermissions']),
    permissionsSysOriginCode() {
      if (!this.permissionsSysOriginPlatforms) {
        return []
      }
      return this.permissionsSysOriginPlatforms.map(item => item.value)
    }
  },
  mounted() {
  },
  methods: {
    screen() {
      const element = this.$refs.datavFullscreen
      if (this.fullscreen) {
        if (document.exitFullscreen) {
          document.exitFullscreen()
        } else if (document.webkitCancelFullScreen) {
          document.webkitCancelFullScreen()
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen()
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen()
        }
      } else {
        if (element.requestFullscreen) {
          element.requestFullscreen()
        } else if (element.webkitRequestFullScreen) {
          element.webkitRequestFullScreen()
        } else if (element.mozRequestFullScreen) {
          element.mozRequestFullScreen()
        } else if (element.msRequestFullscreen) {
          // IE11
          element.msRequestFullscreen()
        }
      }
      this.fullscreen = !this.fullscreen
    }
  }
}
</script>
<style scoped lang="scss">
.app-container-datav {
  overflow: hidden;
  min-height: calc(100vh - 50px);
  background-color: #22284a;
  .office-efficiency-index {
      font-family:PingFangSC-Semibold,PingFang SC;background-color:#22284A;background-size:cover;
      /*头部样式*/
      .office-header{
          background: url("./../../assets/datav/office_efficiency_header_bg.png")no-repeat center center;background-size: 100% 100%;
          .title-info{color:#03C2EC;text-align: center;font-size: 0.26rem;font-family:'cuhei';font-weight: 600;line-height: 0.6rem;vertical-align: middle;}
      }
      .analysis-info{line-height:0.6rem;vertical-align:middle;padding: 0px 0.2rem; font-size: 0.24rem;font-weight: 600;background-image: linear-gradient(#D8AE22, #DC9546);-webkit-background-clip: text;-webkit-text-fill-color: transparent;-webkit-animation: hue 60s infinite linear;}
      /*数据模块样式*/
      .office-content {
          margin-top: 20px;
          .col-info{
              margin:0px 10px 20px 10px;
              border-radius:.1rem;
              background-color:#181C41;
              .title{
                line-height:0.5rem;
                vertical-align:middle;
                padding: .2rem .3rem;
                font-size: 0.4rem;
                font-weight: 600;
                color:#03C2EC;
                text-align: left;
              }
              .content{vertical-align:middle;padding:0px 10px;font-size:0.14rem;color:#fff;}
          }
          #today-pay {
              height: 100%;
              overflow: hidden;
          }
          #real-time-charts {
              height: 5.6rem;
          }
          #online-map {
              height: 7.3rem;
              .content {
                  padding-top: 0.6rem;
              }
          }
      }
  }
}
</style>
