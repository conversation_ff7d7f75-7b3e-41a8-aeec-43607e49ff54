<template>
  <div class="online-user-counts">
    <el-row>
      <el-col  class="count" :span="6">
        <div class="title">总额</div>
        <div><countTo :start-val="0" :end-val="total" :duration="3000" /></div>
      </el-col>
      <el-col  class="count" :span="6">
        <div class="title">MARCIE</div>
        <div><countTo :start-val="0" :end-val="timchatTotal" :duration="3000" /></div>
      </el-col>
      <!-- <el-col  class="count" :span="6">
        <div class="title">Aswat</div>
        <div><countTo :start-val="0" :end-val="aswatTotal" :duration="3000" /></div>
      </el-col> -->
      <!-- <el-col  class="count" :span="6">
        <div class="title">Yahlla</div>
        <div><countTo :start-val="0" :end-val="yahllaTotal" :duration="3000" /></div>
      </el-col>   -->
    </el-row>
  </div>
</template>

<script>
import { purchaseTodayTotal, purchaseTodayTotalBySysOrigin } from '@/api/purchase'
import { mapGetters } from 'vuex'
export default {
  name: 'PurchaseCount',
  props: {
    timeOutSecond: {
      type: Number,
      required: false,
      default: 60
    }
  },
  data() {
    return {
      total: 0,
      timchatTotal: 0,
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms']),
    permissionsSysOriginCode() {
      if (!this.permissionsSysOriginPlatforms) {
        return []
      }
      return this.permissionsSysOriginPlatforms.map(item => item.value)
    }
  },
  created() {
    const that = this
    that.loadData()
    var intervalIndex = setInterval(() => {
      that.loadData()
    }, that.timeOutSecond * 1000)
    that.$store.dispatch('app/pushRunTask', intervalIndex)
  },
  methods: {
    loadData() {
      const that = this
      purchaseTodayTotal().then(res => {
        that.total = res.body || 0
      }).catch(er => {})

      purchaseTodayTotalBySysOrigin('MARCIE').then(res => {
        that.timchatTotal = res.body || 0
      }).catch(er => {})

      // purchaseTodayTotalBySysOrigin('ASWAT').then(res => {
      //   that.aswatTotal = res.body || 0
      // }).catch(er => {})

      // purchaseTodayTotalBySysOrigin('YAHLLA').then(res => {
      //   that.yahllaTotal = res.body || 0
      // }).catch(er => {})
    }
  }
}
</script>
<style scoped lang="scss">
.online-user-counts {
    .count {
      text-align: center;
      line-height: .6rem;
      font-size: 0.4rem;
      padding-bottom: .2rem;
    }
}
</style>
