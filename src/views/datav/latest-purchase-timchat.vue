<template>
  <div class="latest-purchase">
    <vue-seamless-scroll :data="list" class="seamless-warp">
      <div class="pay-user-info">
        <div v-for="(item,index) in list" :key="index" class="user-info">
          <div class="left">
            <avatar :gender="item.userBaseInfo.userSex" :url="item.userBaseInfo.userAvatar" customize-style="width: 1rem; height: 1rem;border-radius: 50%" />
          </div>
          <div class="right">
            <div class="right-head">
              <div class="flag-icon"><flag-icon :code="item.userBaseInfo.countryCode" size="0.25rem" /></div>
              <span class="nickname">{{ item.userBaseInfo.userNickname }}</span>
            </div>
            <div class="right-content">
              <div class="right-content-item">{{ item.orderPurchase.createTime | formatTime }}</div>
              <div class="right-content-item product-name">{{ item.orderPurchase.productDescription }}</div>
              <div class="right-content-item">{{ item.orderPurchase.unitPrice }}</div>
            </div>
          </div>
        </div>
      </div>
    </vue-seamless-scroll>

  </div>
</template>

<script>
import Avatar from '@/components/data/Avatar'
import { getPurchaseTable } from '@/api/purchase'
export default {
  name: 'DatavLatestPurchase',
  components: { Avatar },
  props: {
    timeOutSecond: {
      type: Number,
      required: false,
      default: 60
    }
  },
  data() {
    return {
      list: [],
      listQuery: {
        cursor: 1,
        limit: 20,
        sysOrigin: 'TIM_CHAT'
      }
    }
  },
  created() {
    const that = this
    that.renderData()
    var intervalIndex = setInterval(() => {
      that.renderData()
    }, that.timeOutSecond * 1000)
    that.$store.dispatch('app/pushRunTask', intervalIndex)
  },
  methods: {
    renderData() {
      const that = this
      getPurchaseTable(that.listQuery).then(res => {
        that.list = res.body.records || []
      }).catch(er => {})
    }
  }
}
</script>
<style scoped lang="scss">
.seamless-warp {
  height: 8rem;
  overflow: hidden;
}
.pay-user-info {
  .user-info {
      display: flex;
      overflow: hidden;
      text-overflow:ellipsis;
      white-space: nowrap;
      padding: 0.05rem;
      > .left {
          width: 0.6rem;
          text-align: center;
      }
      > .right {
          width: 100%;
          padding-left: 0.1rem;
          .right-head {
              .flag-icon {
                  display: inline-block;
                  vertical-align: middle;
              }
          }
          .right-content {
              display: flex;
              color: #909399;
              > .right-content-item {
                  width: 100%;
                  padding-right: 0.01rem;
              }
              > .product-name{
                  color: #03c2ec;
              }
          }
      }
  }
}
</style>
