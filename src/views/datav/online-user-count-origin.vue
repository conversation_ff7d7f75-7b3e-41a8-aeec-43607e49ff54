<template>
  <div class="online-user-counts">
    <el-row>
      <el-col class="count" :span="6">
        <div class="title">总人数</div>
        <div><countTo :start-val="0" :end-val="userTotal" :duration="3000" /></div>
      </el-col>
      <el-col class="count" :span="6">
        <div class="title">女生</div>
        <div><countTo :start-val="0" :end-val="femaleUserCount" :duration="3000" /></div>
      </el-col>
      <el-col class="count" :span="6">
        <div class="title">男生</div>
        <div><countTo :start-val="0" :end-val="maleUserCount" :duration="3000" /></div>
      </el-col>
      <el-col class="count" :span="6">
        <div class="title">房间</div>
        <div><countTo :start-val="0" :end-val="roomCount" :duration="3000" /></div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { onlineUserCount, onlineRoomCount } from '@/api/datav'
export default {
  name: 'OnlineUserCountorigin',
  props: {
    timeOutSecond: {
      type: Number,
      required: false,
      default: 60
    },
    origin: {
      type: String,
      require: true,
      default: ''
    }
  },
  data() {
    return {
      userTotal: 0,
      femaleUserCount: 0,
      roomCount: 0,
      maleUserCount: 0
    }
  },
  created() {
    const that = this
    that.loadData()
    var intervalIndex = setInterval(() => {
      that.loadData()
    }, that.timeOutSecond * 1000)
    that.$store.dispatch('app/pushRunTask', intervalIndex)
  },
  methods: {
    loadData() {
      const that = this
      onlineUserCount({ sysOrigin: that.origin }).then(res => {
        that.userTotal = res.body || 0
      }).catch(er => {})

      onlineUserCount({ gender: 0, sysOrigin: that.origin }).then(res => {
        that.femaleUserCount = res.body || 0
      }).catch(er => {})

      onlineUserCount({ gender: 1, sysOrigin: that.origin }).then(res => {
        that.maleUserCount = res.body || 0
      }).catch(er => {})

      onlineRoomCount({ sysOrigin: that.origin }).then(res => {
        that.roomCount = res.body || 0
      }).catch(er => {})
    }
  }
}
</script>
<style scoped lang="scss">
.online-user-counts {
    .count {
      text-align: center;
      line-height: .6rem;
      font-size: 0.4rem;
      padding-bottom: .2rem;
    }
}
</style>
