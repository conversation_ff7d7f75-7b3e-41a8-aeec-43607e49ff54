<!--违规记录表格-->
<template>
  <div class="app-container not_pass_history">
    <div class="filter-container">
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          type="datetimerange"
          value-format="timestamp"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          align="right"
        />
      </div>
      <div class="filter-item">
        <account-input v-model="listQuery.userId" placeholder="用户ID" />
      </div>
      <el-select
        v-model="listQuery.violationType"
        placeholder="审批类型"
        style="width: 240px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option
          v-for="item in approvalTypes"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <!-- <span class="text-item" style="width: 120px;color:#F00;">数量：{{ total }}</span> -->
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <div class="block">
      <el-timeline>
        <el-timeline-item v-for="(item, index) in list" :key="index" :timestamp="item.createTime" placement="top">
          <el-card>
            <h4>
              <span v-if="!item.originType" class="label">{{ item.approvalNickname }}</span>
              <el-link v-if="item.originType" @click="queryUserDetails(item.approvalUserBaseInfo.id)"> <span class="label">{{ item.approvalUserBaseInfo.userNickname }}</span></el-link>  审批
              <el-link @click="queryUserDetails(item.userBaseInfo.id)"> <span class="label">{{ item.userBaseInfo.userNickname }}</span></el-link>
              <el-tag type="info" size="mini">{{ item.violationTypeName }} {{ item.approvalResultName }}</el-tag>
            </h4>
            <div class="contnet">
              <span v-if="item.violationType === 'AVATAR' || item.violationType === 'PHOTO_WALL' || item.violationType === 'ROOM_AVATAR'">
                <el-image
                  style="width: 100px; height: 100px"
                  :src="item.content"
                  :preview-src-list="[item.content]"
                />
              </span>
              <span v-else-if="item.violationType === 'LIVE' || item.violationType === 'SHORT_VIDEO'">
                <video
                  style="width:100%;height:100px;"
                  :src="item.content"
                  muted
                  @mouseout="removeActive($event)"
                  @mouseover="changeActive($event)"
                >
                  您的浏览器不支持 video 标签。
                </video>
              </span>
              <span v-else>
                {{ item.content }}
              </span>
            </div>
            <div v-if="item.labelNames && item.labelNames != '-'">机器标签：{{ item.labelNames }}</div>
            <div v-if="item.description">审核描述：{{ item.description }}</div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <user-deatils-drawer
      v-if="userDeatilsDrawer"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawer=false"
    />
  </div>
</template>

<script>

import { pageViolationHistory } from '@/api/approval'
import Pagination from '@/components/Pagination'
import { approvalTypes } from '@/constant/type'
import { pickerOptions } from '@/constant/el-const'

export default {
  name: 'NotPassHistory',
  components: { Pagination },
  props: {
    userId: {
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      searchDisabled: false,
      disabledUserId: false,
      userDeatilsDrawer: false,
      thatSelectedUserId: '',
      pickerOptions,
      approvalTypes,
      list: [],
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: '',
        violationType: '',
        startDateTime: '',
        endDateTime: ''
      },
      total: 0,
      listLoading: false,
      rangeDate: ''
    }
  },
  watch: {
    userId: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal) {
          this.listQuery.userId = newVal
          this.disabledUserId = true
          this.renderData(true)
        }
      }
    },
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startDateTime = newVal[0]
          this.listQuery.endDateTime = newVal[1]
          return
        }
        this.listQuery.startDateTime = ''
        this.listQuery.endDateTime = ''
      }
    }
  },
  created() {
    this.renderData(true)
  },
  methods: {
    renderData(isClean) {
      const that = this
      that.listLoading = true
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      pageViolationHistory(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      }).catch(er => {
        that.listLoading = false
      })
    },
    changeActive($event) {
      $event.currentTarget.play()
    },
    removeActive($event) {
      $event.currentTarget.pause()
    },
    handleSearch() {
      this.renderData(true)
    },
    queryUserDetails(userId) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = userId
    }
  }
}
</script>

<style scoped lang='scss'>
.contnet {
    line-height: 30px;
}
.label {
    color: #909399;
}
</style>
