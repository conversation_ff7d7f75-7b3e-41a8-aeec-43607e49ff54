<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;">
            <sys-origin-icon
              :icon="item.value"
              :desc="item.value"
            /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.approvalStatus"
        placeholder="状态"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in reportStatus"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="listQuery.reportType"
        placeholder="举报类型"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option
          v-for="item in reportTypes"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-input
        v-model.trim="listQuery.reportUserId"
        placeholder="举报人ID"
        style="width: 200px;"
        class="filter-item"
        @blur="handlerReportUserIdBlur"
      />
      <el-input
        v-model.trim="listQuery.reportedUserId"
        placeholder="被举报人ID"
        style="width: 200px;"
        class="filter-item"
        @blur="handlerReportedUserIdBlur"
      />
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          :picker-options="pickerOptions"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
      @selection-change="selsUserChange"
    >
      <el-table-column v-if="isAllowApproval" type="selection" width="55" />
      <el-table-column prop="sysOrigin" label="系统" align="center">
        <template slot-scope="scope">
          <sys-origin-icon
            :icon="scope.row.sysOrigin"
            :desc="scope.row.sysOrigin"
          />
        </template>
      </el-table-column>
      <el-table-column label="举报人" align="center">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.reportUser"
            type="text"
            @click="queryUserDetails(scope.row.reportUser.id)"
          >{{ scope.row.reportUser.userNickname }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="被举报人" align="center">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.reportedUser"
            type="text"
            @click="queryUserDetails(scope.row.reportedUser.id)"
          >{{ scope.row.reportedUser.userNickname }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="举报类型" align="center">
        <template slot-scope="scope">
          <div v-for="item in reportTypes" :key="item.value">
            <span v-if="scope.row.reportType === item.value">
              {{ item.name }}
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="动态-图片" align="center">
        <template slot-scope="scope">
          <div class="cursor-pointer">
            <div v-if="handleImageUrls(scope.row).length > 0">
              <el-image
                v-for="(item, index) in handleImageUrls(scope.row)"
                :key="index"
                :src="item"
                :preview-src-list="handleImageUrls(scope.row)"
                style="width: 50px; height: 50px; margin: 0px 10px 10px 0px"
              />
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="dynamicContent" label="动态-内容" align="center" />
      <el-table-column label="时间" align="center">
        <template slot-scope="scope">
          <div>创建时间: {{ scope.row.createTime | dateFormat }}</div>
          <div v-if="listQuery.approvalStatus !== 0">
            审批时间: {{ scope.row.updateTime | dateFormat }}
          </div>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="accountHanle(scope.row)"
          >被举报账号处理</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div
      v-if="listQuery.approvalStatus === 0"
      style="text-align:center;padding: 20px;"
    >
      <el-collapse>
        <el-collapse-item>
          <template slot="title">
            操作描述&nbsp;<i class="header-icon el-icon-info" />
          </template>
          <div style="text-align:left;">
            <p>举报审批不会对用户信息及资源做 “任何处理”</p>
            <p>* 违规：表示举报有效</p>
            <p>* 正常：表示举报无效</p>
          </div>
        </el-collapse-item>
      </el-collapse>
      <div style="padding:5px;font-size:14px;color:#909399">
        已勾选了:<span style="color:#F00">{{ checkList.length }}</span>
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-folder-checked"
        @click="pass"
      >
        正常
      </el-button>

      <el-button
        class="filter-item"
        type="danger"
        icon="el-icon-folder-delete"
        @click="notpass"
      >
        违规
      </el-button>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <user-deatils-drawer
      v-if="userDeatilsDrawer"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawer = false"
    />
    <account-hanle
      v-if="accountHandleVisible"
      :user-id="thatSelectedUserId"
      @success="renderDataSuccess"
      @close="accountHandleVisible = false"
    />
  </div>
</template>

<script>
import { reportTable, report } from '@/api/dynamic'
import Pagination from '@/components/Pagination'
import { validPositiveNumber } from '@/utils/validate'
import { pickerOptions } from '@/constant/el-const'
import { copyText } from '@/utils'
import { sysOriginPlatforms } from '@/constant/origin'
import AccountHanle from '@/components/data/AccountHanle'
import { mapGetters } from 'vuex'

export default {
  name: 'DynamicReport',
  components: { Pagination, AccountHanle },
  data() {
    return {
      sysOriginPlatforms,
      pickerOptions,
      reportStatus: [
        { value: 0, name: '待审核' },
        { value: 1, name: '违规' },
        { value: 2, name: '正常' }
      ],
      thatSelectedUserId: '',
      userDeatilsDrawer: false,
      accountHandleVisible: false,
      reportTypes: [
        { value: 0, name: '非法信息' },
        { value: 1, name: '人身攻击' },
        { value: 2, name: '不适当的内容' },
        { value: 3, name: '发送垃圾邮件' },
        { value: 4, name: '诈骗' },
        { value: 5, name: '涉及色情' }
      ],
      list: [],
      checkList: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        sysOrigin: '',
        reportType: '',
        reportUserId: '',
        reportedUserId: '',
        approvalStatus: 0,
        startTime: '',
        endTime: ''
      },
      rangeDate: '',
      listLoading: true
    }
  },
  computed: {
    isAllowApproval() {
      return this.listQuery.approvalStatus === 0
    },
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    }
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      that.checkList = []
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      reportTable(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    accountHanle(row) {
      this.accountHandleVisible = true
      this.thatSelectedUserId = row.reportedUser.id
    },
    renderDataSuccess() {
      this.$message({
        message: 'Successful',
        type: 'success'
      })
      this.renderData()
    },
    selsUserChange(checkList) {
      this.checkList = checkList
    },
    queryUserDetails(userId) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = userId
    },
    pass() {
      const that = this
      if (that.checkList.length === 0) {
        that.$opsMessage.warn('请勾选审批项')
        return
      }
      that
        .$confirm('确认审核选中记录吗？', '提示', {
          type: 'warning'
        })
        .then(() => {
          that.listLoading = true
          const paramsData = {
            contentIds: that.getApprovalParams(),
            approvalStatus: 2
          }
          report(paramsData)
            .then(res => {
              that.listLoading = false
              that.$opsMessage.success()
              that.renderData(true)
            })
            .catch(() => {
              that.listLoading = false
            })
        })
        .catch(() => {})
    },
    notpass() {
      const that = this
      if (that.checkList.length === 0) {
        that.$opsMessage.warn('请勾选审批项')
        return
      }
      that
        .$confirm('确认审核选中记录吗？', '提示', {
          type: 'warning'
        })
        .then(() => {
          that.listLoading = true
          const paramsData = {
            contentIds: that.getApprovalParams(),
            approvalStatus: 1
          }
          report(paramsData)
            .then(res => {
              that.listLoading = false
              that.$opsMessage.success()
              that.renderData(true)
            })
            .catch(() => {
              that.listLoading = false
            })
        })
        .catch(() => {})
    },
    getApprovalParams() {
      const that = this
      const approvalParams = []
      that.checkList.forEach(item => {
        approvalParams.push(item.dynamicContentId)
      })
      return approvalParams
    },
    handleImageUrls(row) {
      var pictures = []
      if (!row.dynamicPictures) {
        return []
      }
      row.dynamicPictures.forEach((item, index) => {
        pictures.push(item.resourceUrl)
      })
      return pictures
    },
    handlerReportUserIdBlur(event) {
      if (!validPositiveNumber(this.listQuery.reportUserId)) {
        this.listQuery.reportUserId = ''
      }
    },
    handlerReportedUserIdBlur(event) {
      if (!validPositiveNumber(this.listQuery.reportedUserId)) {
        this.listQuery.reportedUserId = ''
      }
    },
    handleVideoClick(imageUrls) {
      if (!imageUrls) {
        return
      }
      window.open(imageUrls)
    },
    handleVideoOpenClick(url) {
      if (!url) {
        return
      }
      window.open(url)
    },
    copyTextContent(text) {
      const that = this
      copyText(text)
        .then(() => {
          that.$message({
            message: '复制成功',
            type: 'success'
          })
        })
        .catch(() => {
          that.$message({
            message: '复制失败',
            type: 'error'
          })
        })
    }
  }
}
</script>
