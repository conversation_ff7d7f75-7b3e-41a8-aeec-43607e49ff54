<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.status"
        placeholder="状态"
        style="width: 240px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in approvalStatus"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          type="datetimerange"
          value-format="timestamp"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          align="right"
        />
      </div>
      <div class="filter-item">
        <account-input v-model="listQuery.userId" placeholder="用户ID" />
      </div>
      <el-input
        v-model.trim="listQuery.userNickname"
        placeholder="昵称"
        style="width: 200px;"
        class="filter-item"
      />
      <el-select
        v-model="listQuery.userSex"
        placeholder="性别"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option
          v-for="item in genders"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-checkbox
      v-model="checkAll"
      :indeterminate="isIndeterminate"
      @change="handleCheckAllChange"
    >全选</el-checkbox>
    <div style="margin: 15px 0;" />
    <el-row v-loading="listLoading" :gutter="10">
      <el-col
        v-for="(item, index) in list"
        :key="index"
        :span="4"
        style="margin-bottom: 10px;"
      >
        <el-card :body-style="{ padding: '0px' }">
          <div style="position: relative;">
            <el-image
              style="width:100%;height:300px;"
              :src="item.resourceUrl"
              :preview-src-list="[item.resourceUrl]"
            />
          </div>
          <div style="padding: 14px">
            <div class="bottom">
              <el-checkbox-group v-model="checkList">
                <el-checkbox
                  v-if="listQuery.status === 'SUSPECTED'"
                  :key="item.id"
                  :label="index"
                >选中</el-checkbox>
              </el-checkbox-group>
              <!--<el-table-column type="radio" width="55" />-->
              <div class="info" @click="handleClickCard(index)">
                <div>
                  昵称：
                  <a
                    class="underline"
                    @click.stop="queryUserDetails(item.userId)"
                  >{{ item.userNickname }}</a>
                </div>
                <div class="line">
                  <div>性别：{{ item.userSexName }}</div>
                  <div>年龄：{{ item.age }}</div>
                </div>
                <div>
                  标签：
                  <el-tooltip placement="top-start">
                    <div slot="content">
                      <span v-html="cutout(item.labelNames)" />
                    </div>
                    <span>{{ item.labelNames }}</span>
                  </el-tooltip>
                </div>
              </div>
              <el-button
                type="text"
                class="button"
                @click="editUserForm(item.userId)"
              >编辑用户</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <div v-if="listQuery.status === 'SUSPECTED'" style="text-align:center;">
      <div style="padding:5px;font-size:14px;color:#909399">
        已勾选了:<span style="color:#F00">{{ checkList.length }}</span>
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-folder-checked"
        @click="pass"
      >
        鉴定通过
      </el-button>

      <el-button
        class="filter-item"
        type="danger"
        icon="el-icon-folder-delete"
        @click="notpass"
      >
        鉴定违规
      </el-button>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <user-deatils-drawer
      v-if="userDeatilsDrawer"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawer = false"
    />

    <edit-user-form
      v-if="editUserFormVisible"
      :user-id="thatSelectedUserId"
      @close="editUserFormVisible = false"
      @success="renderDataSuccess"
    />
  </div>
</template>
<script>
import { getPotoWallTableSeach } from '@/api/app-user'
import {
  approvalPhotoWallPass,
  approvalPhotoWallNotPass
} from '@/api/approval'
import Pagination from '@/components/Pagination'
import { approvalStatus, genders } from '@/constant/user'
import EditUserForm from '@/components/data/EditUserForm'
import { pickerOptions } from '@/constant/el-const'
export default {
  name: 'UserPhotoWallApproval',
  components: { Pagination, EditUserForm },
  data() {
    return {
      searchDisabled: false,
      pickerOptions,
      approvalStatus,
      genders,
      userDeatilsDrawer: false,
      thatSelectedUserId: '',
      list: [],
      checkList: [],
      checkAll: false,
      isIndeterminate: false,
      editUserFormVisible: false,
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 30,
        userId: '',
        userNickname: '',
        userSex: '',
        status: 'SUSPECTED',
        startTime: '',
        endTime: ''
      },
      rangeDate: '',
      listLoading: true
    }
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    }
  },
  created() {
    this.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      that.listLoading = true
      that.checkList = []
      that.isIndeterminate = true
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      getPotoWallTableSeach(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    // 截取数据
    cutout(cellValue) {
      if (cellValue != null) {
        return cellValue.replace(/\,/g, '</br>')
      }
    },
    handleCheckAllChange(val) {
      const that = this
      that.checkList = []
      if (val) {
        that.list.forEach((item, index) => {
          that.checkList.push(index)
        })
      }
      this.isIndeterminate = false
    },
    pass() {
      const that = this
      if (that.checkList.length === 0) {
        that.$opsMessage.warn('请勾选审批项')
        return
      }
      that
        .$confirm('确认审核选中记录吗？', '提示', {
          type: 'warning'
        })
        .then(() => {
          that.listLoading = true
          approvalPhotoWallPass(that.getApprovalParams())
            .then(res => {
              that.listLoading = false
              that.$opsMessage.success()
              that.renderData()
            })
            .catch(() => {
              that.listLoading = false
            })
        })
        .catch(() => {})
    },
    notpass() {
      const that = this
      if (that.checkList.length === 0) {
        that.$opsMessage.warn('请勾选审批项')
        return
      }
      that
        .$confirm('确认审核选中记录吗？', '提示', {
          type: 'warning'
        })
        .then(() => {
          that.listLoading = true
          approvalPhotoWallNotPass(that.getApprovalParams())
            .then(res => {
              that.listLoading = false
              that.$opsMessage.success()
              that.renderData()
            })
            .catch(() => {
              that.listLoading = false
            })
        })
        .catch(() => {})
    },
    editUserForm(id) {
      this.thatSelectedUserId = id
      this.editUserFormVisible = true
    },
    renderDataSuccess() {
      this.$message({
        message: 'Successful',
        type: 'success'
      })
      this.renderData()
    },
    queryUserDetails(id) {
      this.thatSelectedUserId = id
      this.userDeatilsDrawer = true
    },
    handleClickCard(id) {
      const index = this.checkList.findIndex(nid => nid === id)
      if (index > -1) {
        this.checkList.splice(index, 1)
        return
      }
      this.checkList.push(id)
    },
    getApprovalParams() {
      const that = this
      const approvalParams = []
      that.checkList.forEach(item => {
        const checkData = that.list[item]
        approvalParams.push({
          userId: checkData.userId,
          contentId: checkData.id,
          content: checkData.resourceUrl,
          tags: checkData.labelNames
        })
      })
      return approvalParams
    }
  }
}
</script>
<style scoped lang="scss">
.bottom {
  color: #666;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 25px;
  .line {
    display: flex;
    > div {
      width: 100%;
    }
  }
  .info {
    cursor: pointer;
  }
}
</style>
