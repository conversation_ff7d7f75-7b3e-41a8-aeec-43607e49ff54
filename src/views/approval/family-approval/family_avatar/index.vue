<!--房间封面审批-->
<template>
  <div class="app-container-family">

    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="归属系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in permissionsSysOriginPlatforms"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.approveStatus"
        placeholder="审批状态"
        style="width: 240px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in commonApprovalStatus"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          type="datetimerange"
          value-format="timestamp"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          align="right"
        />
      </div>
      <div class="filter-item">
        <account-input v-model="listQuery.userId" :sys-origin="listQuery.sysOrigin" placeholder="用户ID" />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">全选</el-checkbox>
    <div style="margin: 15px 0;" />
    <el-row v-loading="listLoading" :gutter="15">
      <el-col v-for="(item, index) in list" :key="index" :span="6" style="margin-bottom: 15px;">
        <el-card :body-style="{ padding: '0px' }" class="family-card">
          <!-- 工会长身份证大图 -->
          <div class="id-card-section">
            <div class="id-card-header">
              <i class="el-icon-postcard"></i>
              <span>工会长身份证</span>
            </div>
            <div class="id-card-image">
              <el-image
                v-if="item.leaderIdCardPhoto"
                style="width:100%;height:200px;"
                :src="item.leaderIdCardPhoto"
                :preview-src-list="[item.leaderIdCardPhoto]"
                fit="cover"
              >
                <div slot="error" class="image-error">
                  <i class="el-icon-picture-outline"></i>
                  <p>身份证图片加载失败</p>
                </div>
              </el-image>
              <div v-else class="no-id-card">
                <i class="el-icon-warning-outline"></i>
                <p>暂无身份证图片</p>
              </div>
            </div>
            <div class="label">
              <el-tooltip placement="top-start">
                <div slot="content">
                  <span v-html="cutout(item.machineLabel)" />
                </div>
                <span>{{ item.machineLabel }}</span>
              </el-tooltip>
            </div>
          </div>

          <!-- 工会信息区域 -->
          <div class="family-info-section">
            <div class="family-header">
              <!-- 工会头像小图 -->
              <div class="family-avatar">
                <el-image
                  style="width: 50px; height: 50px; border-radius: 8px;"
                  :src="item.familyAvatar"
                  :preview-src-list="[item.familyAvatar]"
                  fit="cover"
                >
                  <div slot="error" class="avatar-error">
                    <i class="el-icon-user"></i>
                  </div>
                </el-image>
              </div>

              <!-- 基本信息 -->
              <div class="basic-info">
                <div class="family-name">{{ item.familyName || '未设置工会名' }}</div>
                <div class="family-account">账号: {{ item.familyAccount }}</div>
              </div>
            </div>

            <!-- 详细信息 -->
            <div class="detail-info">
              <div class="info-item">
                <span class="label">用户昵称:</span>
                <a class="underline" @click.stop="queryUserDetails(item.userId)">
                  {{ item.userBaseInfo.userNickname }}
                </a>
              </div>
              <div class="info-item">
                <span class="label">工会ID:</span>
                <span>{{ item.familyId }}</span>
              </div>
              <div class="info-item" v-if="item.familyWhatapp">
                <span class="label">WhatsApp:</span>
                <el-link
                  :href="'https://wa.me/' + item.familyWhatapp.replace(/[^0-9]/g, '')"
                  target="_blank"
                  type="success"
                  :underline="false"
                  class="whatsapp-link"
                >
                  <i class="el-icon-message"></i>
                  {{ item.familyWhatapp }}
                </el-link>
              </div>
              <div class="info-item" v-else>
                <span class="label">WhatsApp:</span>
                <span class="no-data">暂无</span>
              </div>
            </div>

            <!-- 操作区域 -->
            <div class="action-section">
              <el-checkbox-group v-model="checkList">
                <el-checkbox :key="item.userId" :label="index" class="select-checkbox">选中审核</el-checkbox>
              </el-checkbox-group>
              <div class="action-buttons">
                <el-button type="text" size="small" @click="editUserForm(item.userId)">编辑用户</el-button>
                <el-button type="text" size="small" @click="accountHanle(item.userId)">账号处理</el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <div style="text-align:center;">
      <div style="padding:5px;font-size:14px;color:#909399">已勾选了:<span style="color:#F00">{{ checkList.length }}</span></div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-folder-checked"
        @click="pass"
      >
        鉴定通过
      </el-button>

      <el-button
        class="filter-item"
        type="danger"
        icon="el-icon-folder-delete"
        @click="notpass"
      >
        鉴定违规
      </el-button>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <user-deatils-drawer
      v-if="userDeatilsDrawer"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawer=false"
    />

    <edit-user-form
      v-if="editUserFormVisible"
      :user-id="thatSelectedUserId"
      @close="editUserFormVisible=false"
      @success="renderDataSuccess"
    />

    <account-hanle
      v-if="accountHandleVisible"
      :user-id="thatSelectedUserId"
      @success="renderDataSuccess"
      @close="accountHandleVisible=false"
    />
  </div>
</template>
<script>
import { sysOriginPlatforms } from '@/constant/origin'
import { approvalData, getFamilyApprovalPage, notPassFamilyApproval } from '@/api/approval'
import Pagination from '@/components/Pagination'
import { commonApprovalStatus, genders } from '@/constant/user'
import EditUserForm from '@/components/data/EditUserForm'
import { pickerOptions } from '@/constant/el-const'
import AccountHanle from '@/components/data/AccountHanle'
import { mapGetters } from 'vuex'

export default {
  name: 'FamilyAvatar',
  components: { Pagination, EditUserForm, AccountHanle },
  data() {
    return {
      searchDisabled: false,
      sysOriginPlatforms,
      pickerOptions,
      commonApprovalStatus,
      genders,
      userDeatilsDrawer: false,
      thatSelectedUserId: '',
      list: [],
      checkList: [],
      checkAll: false,
      isIndeterminate: false,
      editUserFormVisible: false,
      accountHandleVisible: false,
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 30,
        userId: '',
        approveType: 'FAMILY_AVATAR',
        approveStatus: 'PENDING',
        startDateTime: '',
        endDateTime: '',
        sysOrigin: ''
      },
      rangeDate: '',
      listLoading: true
    }
  },
  computed: {
    isAllowApproval() {
      return this.listQuery.approveStatus === 'PENDING'
    },
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startDateTime = newVal[0]
          this.listQuery.endDateTime = newVal[1]
          return
        }
        this.listQuery.startDateTime = ''
        this.listQuery.endDateTime = ''
      }
    }
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      that.listLoading = true
      that.checkList = []
      that.isIndeterminate = true
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      getFamilyApprovalPage(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    // 截取数据
    cutout(cellValue) {
      if (cellValue != null) {
        return cellValue.replace(/\,/g, '</br>')
      }
    },
    accountHanle(userId) {
      this.accountHandleVisible = true
      this.thatSelectedUserId = userId
    },
    handleCheckAllChange(val) {
      const that = this
      that.checkList = []
      if (val) {
        that.list.forEach((item, index) => {
          that.checkList.push(index)
        })
      }
      this.isIndeterminate = false
    },
    pass() {
      const that = this
      if (that.checkList.length === 0) {
        that.$opsMessage.warn('请勾选审批项')
        return
      }
      that.$confirm('确认审核选中记录吗？', '提示', {
        type: 'warning'
      }).then(() => {
        that.listLoading = true
        approvalData({
          approvalType: that.listQuery.approveType,
          approvalStatus: 'PASS',
          waitApprovalUser: that.getApprovalParams()
        }).then((res) => {
          that.listLoading = false
          that.$opsMessage.success()
          that.renderData()
        }).catch(() => { that.listLoading = false })
      }).catch(() => {})
    },
    notpass() {
      const that = this
      if (that.checkList.length === 0) {
        that.$opsMessage.warn('请勾选审批项')
        return
      }
      that.$confirm('确认审核选中记录吗？', '提示', {
        type: 'warning'
      }).then(() => {
        that.listLoading = true
        const approvalParams = that.getApprovalParams()
        const familyIds = approvalParams.map(approval => approval.familyId)
        approvalData({
          approvalType: that.listQuery.approveType,
          approvalStatus: 'NOT_PASS',
          waitApprovalUser: approvalParams
        }).then((res) => {
          notPassFamilyApproval({
            type: 'FAMILY_AVATAR',
            familyIds: familyIds
          }).then((res) => {}).catch(() => { that.listLoading = false })

          that.listLoading = false
          that.$opsMessage.success()
          that.renderData()
        }).catch(() => { that.listLoading = false })
      }).catch(() => {})
    },
    editUserForm(id) {
      this.thatSelectedUserId = id
      this.editUserFormVisible = true
    },
    renderDataSuccess() {
      this.$message({
        message: 'Successful',
        type: 'success'
      })
      this.renderData()
    },
    queryUserDetails(id) {
      this.thatSelectedUserId = id
      this.userDeatilsDrawer = true
    },
    handleClickCard(id) {
      const index = this.checkList.findIndex(nid => nid === id)
      if (index > -1) {
        this.checkList.splice(index, 1)
        return
      }
      this.checkList.push(id)
    },
    getApprovalParams() {
      const that = this
      const approvalParams = []
      that.checkList.forEach(item => {
        const checkData = that.list[item]
        approvalParams.push({
          userId: checkData.userId,
          contentId: checkData.userId,
          content: checkData.familyAvatar,
          tags: checkData.machineLabel,
          familyId: checkData.familyId
        })
      })
      return approvalParams
    }
  }
}
</script>
<style scoped lang="scss">
.family-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
}

// 身份证区域
.id-card-section {
  position: relative;

  .id-card-header {
    position: absolute;
    top: 8px;
    left: 8px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 2;

    i {
      margin-right: 4px;
    }
  }

  .id-card-image {
    position: relative;
  }

  .image-error, .no-id-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    background: #f5f7fa;
    color: #909399;

    i {
      font-size: 40px;
      margin-bottom: 8px;
    }

    p {
      margin: 0;
      font-size: 12px;
    }
  }

  .no-id-card {
    background: #fff2f0;
    color: #ff4d4f;
  }

  .label {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: #fff;
    padding: 20px 8px 8px;
    font-size: 11px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

// 工会信息区域
.family-info-section {
  padding: 16px;

  .family-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .family-avatar {
      margin-right: 12px;

      .avatar-error {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        background: #f5f7fa;
        border-radius: 8px;
        color: #909399;
        font-size: 20px;
      }
    }

    .basic-info {
      flex: 1;

      .family-name {
        font-weight: 600;
        font-size: 14px;
        color: #303133;
        margin-bottom: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .family-account {
        font-size: 12px;
        color: #909399;
      }
    }
  }

  .detail-info {
    margin-bottom: 16px;

    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      font-size: 12px;

      .label {
        color: #606266;
        margin-right: 8px;
        min-width: 60px;
        font-weight: 500;
      }

      .underline {
        color: #409eff;
        cursor: pointer;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }

      .whatsapp-link {
        font-size: 12px;

        i {
          margin-right: 4px;
        }
      }

      .no-data {
        color: #c0c4cc;
        font-style: italic;
      }
    }
  }

  .action-section {
    border-top: 1px solid #f0f2f5;
    padding-top: 12px;

    .select-checkbox {
      margin-bottom: 8px;

      ::v-deep .el-checkbox__label {
        font-size: 12px;
        color: #606266;
      }
    }

    .action-buttons {
      display: flex;
      gap: 8px;

      .el-button--text {
        padding: 4px 8px;
        font-size: 12px;
        color: #409eff;

        &:hover {
          background: rgba(64, 158, 255, 0.1);
        }
      }
    }
  }
}

// 响应式调整
@media (max-width: 1200px) {
  .family-card {
    margin-bottom: 20px;
  }
}
</style>
