<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="item in tables" :key="item.name" :label="item.title" :name="item.component" />
      <component :is="activeName" />
    </el-tabs>
  </div>
</template>
<script>
import FamilyAvatar from './family_avatar'
import FamilyNickname from './family_nickname'
import FamilyNotice from './family_notice'
export default {
  name: 'FamilyApproval',
  components: { FamilyAvatar, FamilyNickname, FamilyNotice },
  data() {
    return {
      activeName: 'FamilyAvatar',
      tables: [
        {
          title: '家族头像审批',
          component: 'FamilyAvatar'
        }, {
          title: '家族名称审批',
          component: 'FamilyNickname'
        }, {
          title: '家族公告审批',
          component: 'FamilyNotice'
        }
      ]
    }
  }
}
</script>
