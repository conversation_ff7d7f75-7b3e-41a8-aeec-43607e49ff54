<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;">
            <sys-origin-icon
              :icon="item.value"
              :desc="item.value"
            /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.approveStatus"
        placeholder="审核"
        style="width: 240px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in commonApprovalStatus"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          type="datetimerange"
          value-format="timestamp"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          align="right"
        />
      </div>
      <div class="filter-item">
        <account-input
          v-model="listQuery.userId"
          :sys-origin="listQuery.sysOrigin"
          placeholder="用户ID"
        />
      </div>
<!--      <el-select-->
<!--        v-model="listQuery.dynamicType"-->
<!--        placeholder="类型"-->
<!--        style="width: 240px"-->
<!--        class="filter-item"-->
<!--        @change="handleSearch"-->
<!--      >-->
<!--        <el-option-->
<!--          v-for="item in commonDynamicType"-->
<!--          :key="item.value"-->
<!--          :label="item.name"-->
<!--          :value="item.value"-->
<!--        />-->
<!--      </el-select>-->
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        v-if="buttonPermissions.includes('dynamic:list:add')"
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        新增
      </el-button>
    </div>
    <el-checkbox
      v-model="checkAll"
      :indeterminate="isIndeterminate"
      @change="handleCheckAllChange"
    >全选</el-checkbox>
    <div style="margin: 15px 0;" />
    <el-row v-loading="listLoading" :gutter="10">
      <div
        v-for="(item, index) in list"
        :key="index"
        style="margin-bottom: 10px;"
      >
        <div>
          <el-card :body-style="{ padding: '15px' }">
            <div style="padding-bottom: 15px;">
              <div
                style="overflow: hidden;text-overflow: ellipsis; white-space: normal;word-break: break-all;line-height: 20px;"
              >
                {{ item.dynamicContent }}
              </div>
            </div>
            <div style="display: flex;overflow-x: auto;">
              <div
                v-for="(item2, index2) in item.pictures"
                :key="index2 + item2.id"
                style="margin-right: 5px;"
              >
                <div class="img">
                  <el-image
                    style="width:250px;height:300px;"
                    :src="item2.resourceUrl"
                    :preview-src-list="[item2.resourceUrl]"
                  />
                  <div class="label">
                    <el-tooltip placement="top-start">
                      <div slot="content">
                        <span v-html="cutout(item2.labelNames)" />
                      </div>
                      <span>{{ item2.labelNames }}</span>
                    </el-tooltip>
                  </div>
                </div>
              </div>
            </div>
            <div style="padding-top: 10px">
              <div class="bottom">
                <el-checkbox-group v-model="checkList">
                  <el-checkbox
                    v-if="
                      listQuery.approveStatus === 'PENDING' ||
                        listQuery.approveStatus === 'PASS'
                    "
                    :key="item.id"
                    :label="index"
                  >选中</el-checkbox>
                </el-checkbox-group>
                <div class="info" @click="handleClickCard(index)">
                  <div>
                    昵称：
                    <a
                      class="underline"
                      @click.stop="queryUserDetails(item.userId)"
                    >{{ item.userBaseInfo.userNickname }}</a>
                  </div>
                  <div class="line">
                    <div>
                      性别：{{ item.userSexName }} / 年龄：{{
                        item.userBaseInfo.age
                      }}
                    </div>
                  </div>
                  <div>创建时间: {{ item.createTime | dateFormat}}</div>
                  <div v-if="listQuery.approveStatus !== 'PENDING'">
                    审批时间: {{ item.updateTime | dateFormat}}
                  </div>
                </div>
                <el-button
                  type="text"
                  class="button"
                  @click="accountHanle(item.userId)"
                >账号处理</el-button>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </el-row>
    <div
      v-if="
        listQuery.approveStatus === 'PENDING' ||
          listQuery.approveStatus === 'PASS'
      "
      style="text-align:center;"
    >
      <div style="padding:5px;font-size:14px;color:#909399">
        已勾选了:<span style="color:#F00">{{ checkList.length }}</span>
      </div>
      <el-button
        v-if="listQuery.approveStatus === 'PENDING'"
        class="filter-item"
        type="primary"
        icon="el-icon-folder-checked"
        @click="pass"
      >
        鉴定通过
      </el-button>

      <el-button
        class="filter-item"
        type="danger"
        icon="el-icon-folder-delete"
        @click="notpass"
      >
        鉴定违规
      </el-button>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <add-bd
      v-if="addDynamicVisible"
      :row="thisRow"
      @success="addDynamicSuccess"
      @close="addDynamicVisible=false;"
    />

    <user-deatils-drawer
      v-if="userDeatilsDrawer"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawer = false"
    />

    <edit-user-form
      v-if="editUserFormVisible"
      :user-id="thatSelectedUserId"
      @close="editUserFormVisible = false"
      @success="renderDataSuccess"
    />

    <account-hanle
      v-if="accountHandleVisible"
      :user-id="thatSelectedUserId"
      @success="renderDataSuccess"
      @close="accountHandleVisible = false"
    />
  </div>
</template>
<script>
import {
  getContentTable,
  approvalContentPass,
  approvalContentNotPass
} from '@/api/dynamic'

// import AddDynamic from './add-dynamic'
import Pagination from '@/components/Pagination'
import { commonApprovalStatus, genders, commonDynamicType } from '@/constant/user'
import EditUserForm from '@/components/data/EditUserForm'
import { pickerOptions } from '@/constant/el-const'
import { mapGetters } from 'vuex'
import AccountHanle from '@/components/data/AccountHanle'
import { dateFormat } from '@/filters'
export default {
  name: 'DynamicContentApproval',
  components: {
    // AddDynamic,
    Pagination,
    EditUserForm,
    AccountHanle
  },
  data() {
    return {
      searchDisabled: false,
      pickerOptions,
      commonApprovalStatus,
      genders,
      userDeatilsDrawer: false,
      thatSelectedUserId: '',
      list: [],
      checkList: [],
      checkAll: false,
      accountHandleVisible: false,
      isIndeterminate: false,
      editUserFormVisible: false,
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 10,
        sysOrigin: 'HALAR',
        userId: '',
        approveStatus: 'PENDING',
        startTime: '',
        endTime: '',
        dynamicType: ''
      },
      commonDynamicType,
      rangeDate: '',
      listLoading: true,
      addDynamicVisible: false
    }
  },
  computed: {
    ...mapGetters(['buttonPermissions', 'permissionsSysOriginPlatforms']),
    isQueryPermissions() {
      return this.buttonPermissions.includes('bd:list:query:all') || this.buttonPermissions.includes('bd:list:query:self')
    }
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    }
  },
  created() {
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    this.listQuery.sysOrigin = querySystem.value
    this.renderData()
  },
  methods: {
    dateFormat,
    renderData(isClean) {
      const that = this
      that.listLoading = true
      that.checkList = []
      that.isIndeterminate = true
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      getContentTable(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    // 截取数据
    cutout(cellValue) {
      if (cellValue != null) {
        return cellValue.replace(/\,/g, '</br>')
      }
    },
    accountHanle(userId) {
      this.accountHandleVisible = true
      this.thatSelectedUserId = userId
    },
    handleCheckAllChange(val) {
      const that = this
      that.checkList = []
      if (val) {
        that.list.forEach((item, index) => {
          that.checkList.push(index)
        })
      }
      this.isIndeterminate = false
    },
    pass() {
      const that = this
      if (that.checkList.length === 0) {
        that.$opsMessage.warn('请勾选审批项')
        return
      }
      that
        .$confirm('确认审核选中记录吗？', '提示', {
          type: 'warning'
        })
        .then(() => {
          that.listLoading = true
          approvalContentPass(that.getApprovalParams())
            .then(res => {
              that.listLoading = false
              that.$opsMessage.success()
              that.renderData()
            })
            .catch(() => {
              that.listLoading = false
            })
        })
        .catch(() => {})
    },
    notpass() {
      const that = this
      if (that.checkList.length === 0) {
        that.$opsMessage.warn('请勾选审批项')
        return
      }
      that
        .$confirm('确认审核选中记录吗？', '提示', {
          type: 'warning'
        })
        .then(() => {
          that.listLoading = true
          approvalContentNotPass(that.getApprovalParams())
            .then(res => {
              that.listLoading = false
              that.$opsMessage.success()
              that.renderData()
            })
            .catch(() => {
              that.listLoading = false
            })
        })
        .catch(() => {})
    },
    editUserForm(id) {
      this.thatSelectedUserId = id
      this.editUserFormVisible = true
    },

    renderDataSuccess() {
      this.$message({
        message: 'Successful',
        type: 'success'
      })
      this.renderData()
    },
    queryUserDetails(id) {
      this.thatSelectedUserId = id
      this.userDeatilsDrawer = true
    },
    handleClickCard(id) {
      const index = this.checkList.findIndex(nid => nid === id)
      if (index > -1) {
        this.checkList.splice(index, 1)
        return
      }
      this.checkList.push(id)
    },
    getApprovalParams() {
      const that = this
      const approvalParams = []
      that.checkList.forEach(item => {
        const checkData = that.list[item]
        approvalParams.push(checkData.dynamicId)
        // approvalParams.push({
        //   userId: checkData.userId,
        //   contentId: checkData.id,
        //   content: checkData.resourceUrl,
        //   tags: checkData.labelNames
        // })
      })
      return approvalParams
    },
    handleCreate() {
      this.textOptTitle = '新增视频'
      this.thisRow = {}
      this.thisRow.sysOrigin = this.listQuery.sysOrigin
      this.addDynamicVisible = true
    },
    addDynamicSuccess(form) {
      this.$opsMessage.success()
      this.addDynamicVisible = false
      this.renderData(!!(form.id))
    },
    handleClose() {
      this.addDynamicVisible = false
    }
  }
}
</script>
<style scoped lang="scss">
.bottom {
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 25px;
  .line {
    display: flex;
    > div {
      width: 100%;
    }
  }
  .info {
    cursor: pointer;
  }
}
.img {
  position: relative;
  .label {
    position: absolute;
    bottom: 0px;
    border: 1px solid;
    width: 100%;
    background: black;
    opacity: 0.5;
    color: #fff;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 25px;
  }
}
</style>
