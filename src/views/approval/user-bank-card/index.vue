<template>
  <div class="app-container">

    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="归属系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in permissionsSysOriginPlatforms"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <account-input v-model="listQuery.ownUserId" :sys-origin="listQuery.sysOrigin" placeholder="归属人ID" />
      </div>
      <el-select
        v-model="listQuery.status"
        placeholder="审批状态"
        style="width: 240px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in commonApprovalStatus"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="listQuery.cardType"
        placeholder="银行类型"
        style="width: 240px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in bankCardType"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <!-- <span class="text-item" style="width: 120px;color:#F00;">数量：{{ total }}</span> -->
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
      @selection-change="selsUserChange"
    >
      <el-table-column v-if="isAllowApproval" type="selection" width="55" />
      <el-table-column prop="cardNo" label="卡号" align="center" />
      <el-table-column prop="payee" label="收款人" align="center" />
      <el-table-column prop="cardName" label="银行" align="center" />
      <el-table-column prop="cardType" label="卡片类型" align="center" />
      <el-table-column label="归属人" align="center">
        <template slot-scope="scope">
          <el-link v-if="scope.row.userProfile.id" @click="queryUserDetails(scope.row.userProfile)">
            {{ scope.row.userProfile.userNickname }} / {{ scope.row.userProfile.actualAccount }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 'PENDING'" type="info">待审核</el-tag>
          <el-tag v-if="scope.row.status === 'NOT_PASS'" type="danger">驳回</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center" width="200">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="100">
        <template slot-scope="scope">
          <div><el-button type="text" @click="accountHanle(scope.row.userProfile.id)">账号处理</el-button></div>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="isAllowApproval" style="text-align:center;padding: 20px;">
      <div style="padding:5px;font-size:14px;color:#909399">已勾选了:<span style="color:#F00">{{ checkList.length }}</span></div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-folder-checked"
        @click="pass"
      >
        鉴定通过
      </el-button>

      <el-button
        class="filter-item"
        type="danger"
        icon="el-icon-folder-delete"
        @click="notpass"
      >
        鉴定违规
      </el-button>

    </div>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
    <user-deatils-drawer
      v-if="userDeatilsDrawer"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawer=false"
    />
    <account-hanle
      v-if="accountHandleVisible"
      :user-id="thatSelectedUserId"
      @success="renderDataSuccess"
      @close="accountHandleVisible=false"
    />
  </div>
</template>
<script>
import { pageUserBankCard, userBankCardPass, userBankCardNotPass } from '@/api/approval'
import Pagination from '@/components/Pagination'
import { commonApprovalStatus } from '@/constant/user'
import { bankCardType } from '@/constant/team-type'
import { pickerOptions } from '@/constant/el-const'
import AccountHanle from '@/components/data/AccountHanle'
import { sysOriginPlatforms } from '@/constant/origin'
import { mapGetters } from 'vuex'

export default {
  name: 'UserBankCardApproval',
  components: { Pagination, AccountHanle },
  data() {
    return {
      searchDisabled: false,
      sysOriginPlatforms,
      accountHandleVisible: false,
      pickerOptions,
      commonApprovalStatus,
      bankCardType,
      userDeatilsDrawer: false,
      thatSelectedUserId: '',
      list: [],
      checkList: [],
      total: 0,
      listQuery: {
        sysOrigin: 'MARCIE',
        cursor: 1,
        limit: 30,
        ownUserId: '',
        status: 'PENDING',
        cardType: ''
      },
      listLoading: true
    }
  },
  computed: {
    isAllowApproval() {
      return this.listQuery.status === 'PENDING'
    },
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      that.checkList = []
      that.listLoading = true
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      pageUserBankCard(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    accountHanle(userId) {
      this.accountHandleVisible = true
      this.thatSelectedUserId = userId
    },
    selsUserChange(checkList) {
      this.checkList = checkList
    },
    queryUserDetails(user) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = user.id
    },
    pass() {
      const that = this
      if (that.checkList.length === 0) {
        that.$opsMessage.warn('请勾选审批项')
        return
      }
      that.$confirm('确认审核选中记录吗？', '提示', {
        type: 'warning'
      }).then(() => {
        that.listLoading = true
        userBankCardPass(that.getApprovalParams()).then((res) => {
          that.listLoading = false
          that.$opsMessage.success()
          that.renderData(true)
        }).catch(() => { that.listLoading = false })
      }).catch(() => {})
    },
    notpass() {
      const that = this
      if (that.checkList.length === 0) {
        that.$opsMessage.warn('请勾选审批项')
        return
      }
      that.$confirm('确认审核选中记录吗？', '提示', {
        type: 'warning'
      }).then(() => {
        that.listLoading = true
        userBankCardNotPass(that.getApprovalParams()).then((res) => {
          that.listLoading = false
          that.$opsMessage.success()
          that.renderData(true)
        })
      }).catch(() => {})
    },
    getApprovalParams() {
      const that = this
      const approvalParams = []
      that.checkList.forEach(item => {
        approvalParams.push(item.id)
      })
      return approvalParams
    },
    renderDataSuccess() {
      this.$message({
        message: 'Successful',
        type: 'success'
      })
      this.renderData()
    }
  }
}
</script>
<style scoped lang="scss">
  .bottom {
    color: #666;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 25px;
    .line {
      display: flex;
      >div {
        width: 100%;
      }
    }
  }

</style>
