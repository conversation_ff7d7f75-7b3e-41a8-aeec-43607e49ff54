<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;">
            <sys-origin-icon
              :icon="item.value"
              :desc="item.value"
            /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.approvalStatus"
        placeholder="状态"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in reportStatus"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="listQuery.reportType"
        placeholder="举报类型"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option
          v-for="item in reportTypes"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <div class="filter-item">
        <account-input
          v-model="listQuery.reportUserId"
          :sys-origin="listQuery.sysOrigin"
          placeholder="举报人ID"
        />
      </div>
      <div class="filter-item">
        <account-input
          v-model="listQuery.reportedUserId"
          :sys-origin="listQuery.sysOrigin"
          placeholder="被举报人ID"
        />
      </div>
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          :picker-options="pickerOptions"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </div>
      <el-select
        v-model="listQuery.updateUserId"
        placeholder="后台成员"
        style="width: 120px"
        class="filter-item"
        clearable
        filterable
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in members"
          :key="index"
          :label="item.nickname"
          :value="item.id"
        />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
      default-expand-all
      @selection-change="selsUserChange"
    >
      <el-table-column type="expand">
        <template slot-scope="scope">
          <el-row class="row-expand">
            <el-col :span="24" class="expand-col">
              <div class="flex-l">
                <div>
                  <user-table-exhibit :user-profile="scope.row.reportUser" />
                </div>
                <div style="margin: 0px 20px;"><el-tag>举报</el-tag></div>
                <div>
                  <user-table-exhibit :user-profile="scope.row.reportedUser" />
                </div>
                <div style="margin: 0px 20px;"><el-tag>涉嫌</el-tag></div>
                <div>{{ scope.row.reportTypeName }}</div>
              </div>
            </el-col>
            <el-col :span="24" class="expand-col">
              <div class="cursor-pointer">
                <div
                  v-if="
                    scope.row.origin !== 'SHORT_VIDEO' &&
                      handleImageUrls(scope.row).length > 0
                  "
                >
                  <div class="flex-l">
                    <div
                      v-for="(item, index) in handleImageUrls(scope.row)"
                      :key="index"
                      class="feedback-img"
                      style="margin-right: 10px;"
                    >
                      <el-image
                        :src="item"
                        :preview-src-list="handleImageUrls(scope.row)"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </el-col>

            <el-col :span="24" class="expand-col">
              <div class="cursor-pointer">
                <div
                  v-if="handleVideoUrls(scope.row).length > 0"
                  style="width: 120px;margin-right: 10px;"
                  class="flex-l"
                >
                  <div
                    v-for="(item, index) in handleVideoUrls(scope.row)"
                    :key="index"
                    class="video-info"
                  >
                    <video
                      style="width:50px;height:50px;"
                      :src="item"
                      muted
                      @click="handleVideoOpenClick(item)"
                    />
                    <div class="video-icon" @click="handleVideoOpenClick(item)">
                      <i class="el-icon-video-play" />
                    </div>
                  </div>
                </div>
              </div>
            </el-col>

            <el-col :span="24" class="expand-col">
              举报内容: {{ scope.row.reportedContent }}
            </el-col>
            <el-col v-if="scope.row.updateNickname" :span="24">
              最近处理人: {{ scope.row.updateNickname }}
            </el-col>
          </el-row>
        </template>
      </el-table-column>
      <el-table-column
        v-if="listQuery.approvalStatus === 0"
        type="selection"
        width="55"
      />

      <el-table-column
        prop="reportTypeName"
        label="举报类型"
        align="center"
        min-width="100"
      />
      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
        width="200"
      >
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column
        prop="updateTime"
        label="修改时间"
        align="center"
        width="200"
      >
        <template slot-scope="scope">
          {{ scope.row.updateTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="100">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="accountHanle(scope.row)"
          >账号处理</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div
      v-if="listQuery.approvalStatus === 0"
      style="text-align:center;padding: 20px;"
    >
      <el-collapse>
        <el-collapse-item>
          <template slot="title">
            操作描述&nbsp;<i class="header-icon el-icon-info" />
          </template>
          <div style="text-align:left;">
            <p>
              举报审批不会对用户信息及资源做
              “任何处理”，处理需要到对应的资源模块进行审批
            </p>
            <p>
              * 有效：表示举报有效，系统将发放 “参数配置”
              中指定糖果数量奖励给举报提交用户
            </p>
            <p>* 无效： 表示举报无效</p>
          </div>
        </el-collapse-item>
      </el-collapse>
      <div style="padding:5px;font-size:14px;color:#909399">
        已勾选了:<span style="color:#F00">{{ checkList.length }}</span>
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-folder-checked"
        @click="pass"
      >
        有效
      </el-button>

      <el-button
        class="filter-item"
        type="danger"
        icon="el-icon-folder-delete"
        @click="notpass"
      >
        无效
      </el-button>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <user-deatils-drawer
      v-if="userDeatilsDrawer"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawer = false"
    />
    <account-hanle
      v-if="accountHandleVisible"
      :user-id="thatSelectedUserId"
      @success="renderDataSuccess"
      @close="accountHandleVisible = false"
    />
  </div>
</template>

<script>
import { getReportedTable } from '@/api/table'
import Pagination from '@/components/Pagination'
import { approvalReportedPass, approvalReportedNotPass } from '@/api/approval'
import { pickerOptions } from '@/constant/el-const'
import { copyText } from '@/utils'
import { sysOriginPlatforms } from '@/constant/origin'
import AccountHanle from '@/components/data/AccountHanle'
import { mapGetters } from 'vuex'
import { listMembers } from '@/api/team'

export default {
  name: 'UserReportApproval',
  components: { Pagination, AccountHanle },
  data() {
    return {
      sysOriginPlatforms,
      pickerOptions,
      reportStatus: [
        { value: 0, name: '待审核' },
        { value: 1, name: '有效举报' },
        { value: 2, name: '无效举报' }
      ],
      thatSelectedUserId: '',
      userDeatilsDrawer: false,
      accountHandleVisible: false,
      reportOrigins: [
        { value: 'VIDEO', name: '视频' },
        { value: 'CARD', name: '卡片' },
        { value: 'IM', name: 'IM聊天' },
        { value: 'SHORT_VIDEO', name: '短视频' },
        { value: 'PROFILE', name: '个人资料' }
      ],
      reportTypes: [
        { value: 0, name: '非法信息' },
        { value: 1, name: '人身攻击' },
        { value: 2, name: '不适当的内容' },
        { value: 3, name: '发送垃圾邮件' },
        { value: 4, name: '诈骗' },
        { value: 5, name: '涉及色情' }
      ],
      list: [],
      checkList: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        sysOrigin: '',
        reportType: '',
        reportUserId: '',
        reportedUserId: '',
        approvalStatus: 0,
        startTime: '',
        endTime: '',
        updateUserId: ''
      },
      rangeDate: '',
      listLoading: true,
      members: [],
      numbersLoading: false
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    }
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
    that.loadMembers()
  },
  methods: {
    loadMembers() {
      const that = this
      that.numbersLoading = true
      listMembers()
        .then(res => {
          const { body } = res
          that.members = body
          that.numbersLoading = false
        })
        .catch(er => {
          that.numbersLoading = false
        })
    },
    renderData(isClean) {
      const that = this
      that.checkList = []
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      getReportedTable(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    accountHanle(row) {
      this.accountHandleVisible = true
      this.thatSelectedUserId = row.reportedUser.id
    },
    renderDataSuccess() {
      this.$message({
        message: 'Successful',
        type: 'success'
      })
      this.renderData()
    },
    selsUserChange(checkList) {
      this.checkList = checkList
    },
    queryUserDetails(userId) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = userId
    },
    pass() {
      const that = this
      if (that.checkList.length === 0) {
        that.$opsMessage.warn('请勾选审批项')
        return
      }
      that
        .$confirm('确认审核选中记录吗？', '提示', {
          type: 'warning'
        })
        .then(() => {
          that.listLoading = true
          approvalReportedPass(that.getApprovalParams())
            .then(res => {
              that.listLoading = false
              that.$opsMessage.success()
              that.renderData(true)
            })
            .catch(() => {
              that.listLoading = false
            })
        })
        .catch(() => {})
    },
    notpass() {
      const that = this
      if (that.checkList.length === 0) {
        that.$opsMessage.warn('请勾选审批项')
        return
      }
      that
        .$confirm('确认审核选中记录吗？', '提示', {
          type: 'warning'
        })
        .then(() => {
          that.listLoading = true
          approvalReportedNotPass(that.getApprovalParams())
            .then(res => {
              that.listLoading = false
              that.$opsMessage.success()
              that.renderData(true)
            })
            .catch(() => {
              that.listLoading = false
            })
        })
        .catch(() => {})
    },
    getApprovalParams() {
      const that = this
      const approvalParams = []
      that.checkList.forEach(item => {
        if (!item.reportUserId || !item.reportedUserId) {
          return
        }
        approvalParams.push({
          userId: item.reportUserId,
          contentId: item.id,
          ext: item.reportedUserId
        })
      })
      return approvalParams
    },
    handleImageUrls(row) {
      if (row.imageUrls === 'null') {
        return []
      }
      return row.imageUrls ? row.imageUrls.split(',') : []
    },
    handleVideoUrls(row) {
      if (row.videoUrls === 'null') {
        return []
      }
      return row.videoUrls ? row.videoUrls.split(',') : []
    },
    handleVideoClick(imageUrls) {
      if (!imageUrls) {
        return
      }
      window.open(imageUrls)
    },
    handleVideoOpenClick(url) {
      if (!url) {
        return
      }
      window.open(url)
    },
    copyTextContent(text) {
      const that = this
      copyText(text)
        .then(() => {
          that.$message({
            message: '复制成功',
            type: 'success'
          })
        })
        .catch(() => {
          that.$message({
            message: '复制失败',
            type: 'error'
          })
        })
    }
  }
}
</script>
<style scoped lang="scss">
.feedback-img {
  position: relative;
  width: 50px;
  height: 50px;
  overflow: hidden;
  img {
    width: 100%;
    height: 100%;
  }
}
.row-expand {
  padding: 20px;
  .expand-col {
    margin-bottom: 10px;
  }
  .video-info {
    position: relative;
    .video-icon {
      font-size: 30px;
      position: absolute;
      top: 10px;
      left: 10px;
    }
  }
}
</style>
