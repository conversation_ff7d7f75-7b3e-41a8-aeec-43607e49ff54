<template>
  <div class="app-container">

    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="归属系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in permissionsSysOriginPlatforms"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          type="datetimerange"
          value-format="timestamp"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          align="right"
        />
      </div>
      <div class="filter-item">
        <account-input v-model="listQuery.userId" :sys-origin="listQuery.sysOrigin" placeholder="用户ID" />
      </div>
      <el-select
        v-model="listQuery.approveStatus"
        placeholder="审批状态"
        style="width: 240px"
        class="filter-item"
        :disabled="searchDisabled"
        @change="handleSearch"
      >
        <el-option
          v-for="item in commonApprovalStatus"
          :key="item.value"
          :label="item.name"
          :value="item.value"
          :disabled="item.value == 'NOT_PASS'"
        />
      </el-select>
      <!-- <span class="text-item" style="width: 120px;color:#F00;">数量：{{ total }}</span> -->
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
      @selection-change="selsUserChange"
    >
      <el-table-column v-if="isAllowApproval" type="selection" width="55" />
      <el-table-column prop="userId" label="用户id" align="center" width="200" />
      <el-table-column prop="userNickname" label="昵称" align="center" />
      <el-table-column prop="updateTime" label="修改时间" align="center" width="200">
        <template slot-scope="scope">
          {{ scope.row.updateTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="100">
        <template slot-scope="scope">
          <div>
            <el-button type="text" @click="queryUserDetails(scope.row)">用户详情</el-button>
          </div>
          <div>
            <el-button type="text" @click="accountHanle(scope.row)">账号处理</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="isAllowApproval" style="text-align:center;padding: 20px;">
      <div style="padding:5px;font-size:14px;color:#909399">已勾选了:<span style="color:#F00">{{ checkList.length }}</span></div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-folder-checked"
        @click="pass"
      >
        鉴定通过
      </el-button>

      <el-button
        class="filter-item"
        type="danger"
        icon="el-icon-folder-delete"
        @click="notpass"
      >
        鉴定违规
      </el-button>

    </div>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
    <user-deatils-drawer
      v-if="userDeatilsDrawer"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawer=false"
    />
    <account-hanle
      v-if="accountHandleVisible"
      :user-id="thatSelectedUserId"
      @success="renderDataSuccess"
      @close="accountHandleVisible=false"
    />
  </div>
</template>
<script>
import { approvalData, pageUserProfileApproval } from '@/api/approval'
import Pagination from '@/components/Pagination'
import { commonApprovalStatus } from '@/constant/user'
import { pickerOptions } from '@/constant/el-const'
import { sysOriginPlatforms } from '@/constant/origin'
import AccountHanle from '@/components/data/AccountHanle'
import { mapGetters } from 'vuex'

export default {
  name: 'UserAvatarNickname',
  components: { Pagination, AccountHanle },
  data() {
    return {
      searchDisabled: false,
      sysOriginPlatforms,
      pickerOptions,
      commonApprovalStatus,
      userDeatilsDrawer: false,
      thatSelectedUserId: '',
      accountHandleVisible: false,
      AccountHanle,
      list: [],
      checkList: [],
      total: 0,
      listQuery: {
        sysOrigin: '',
        cursor: 1,
        limit: 30,
        userId: '',
        approveType: 'NICKNAME',
        approveStatus: 'PENDING',
        startDateTime: '',
        endDateTime: ''
      },
      rangeDate: '',
      listLoading: true
    }
  },
  computed: {
    isAllowApproval() {
      return this.listQuery.approveStatus === 'PENDING'
    },
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startDateTime = newVal[0]
          this.listQuery.endDateTime = newVal[1]
          return
        }
        this.listQuery.startDateTime = ''
        this.listQuery.endDateTime = ''
      }
    }
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      that.checkList = []
      that.listLoading = true
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      pageUserProfileApproval(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    accountHanle(row) {
      this.accountHandleVisible = true
      this.thatSelectedUserId = row.userId
    },
    renderDataSuccess() {
      this.$message({
        message: 'Successful',
        type: 'success'
      })
      this.renderData()
    },
    handleSearch() {
      this.renderData(true)
    },
    selsUserChange(checkList) {
      this.checkList = checkList
    },
    queryUserDetails(row) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = row.userId
    },
    pass() {
      const that = this
      if (that.checkList.length === 0) {
        that.$opsMessage.warn('请勾选审批项')
        return
      }
      that.$confirm('确认审核选中记录吗？', '提示', {
        type: 'warning'
      }).then(() => {
        that.listLoading = true
        approvalData({
          approvalType: that.listQuery.approveType,
          approvalStatus: 'PASS',
          waitApprovalUser: that.getApprovalParams()
        }).then((res) => {
          that.listLoading = false
          that.$opsMessage.success()
          that.renderData(true)
        }).catch(() => { that.listLoading = false })
      }).catch(() => {})
    },
    notpass() {
      const that = this
      if (that.checkList.length === 0) {
        that.$opsMessage.warn('请勾选审批项')
        return
      }
      that.$confirm('确认审核选中记录吗？', '提示', {
        type: 'warning'
      }).then(() => {
        that.listLoading = true
        approvalData({
          approvalType: that.listQuery.approveType,
          approvalStatus: 'NOT_PASS',
          waitApprovalUser: that.getApprovalParams()
        }).then((res) => {
          that.listLoading = false
          that.$opsMessage.success()
          that.renderData(true)
        })
      }).catch(() => {})
    },
    getApprovalParams() {
      const that = this
      const approvalParams = []
      that.checkList.forEach(item => {
        approvalParams.push({
          userId: item.userId,
          contentId: item.userId,
          content: item.userNickname
        })
      })
      return approvalParams
    }
  }
}
</script>
<style scoped lang="scss">
  .bottom {
    color: #666;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 25px;
    .line {
      display: flex;
      >div {
        width: 100%;
      }
    }
  }

</style>
