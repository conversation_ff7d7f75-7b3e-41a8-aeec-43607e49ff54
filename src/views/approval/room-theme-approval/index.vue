<!--房间主题审批-->
<template>
  <div class="app-container">

    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="归属系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in permissionsSysOriginPlatforms"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          type="datetimerange"
          value-format="timestamp"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          align="right"
        />
      </div>
      <div class="filter-item">
        <account-input v-model="listQuery.userId" :sys-origin="listQuery.sysOrigin" placeholder="用户ID" />
      </div>
      <el-select
        v-model="listQuery.themeStatus"
        placeholder="审批状态"
        style="width: 240px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in commonApprovalStatus"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-checkbox v-if="isAllowApproval" v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">全选</el-checkbox>
    <div style="margin: 15px 0;" />
    <el-row v-loading="listLoading" :gutter="10">
      <el-col v-for="(item, index) in list" :key="index" :md="4" style="margin-bottom: 10px;">
        <el-card :body-style="{ padding: '0px' }">
          <div class="img">
            <el-image
              style="width:100%;height:300px;"
              :src="item.customize.themeBack"
              :preview-src-list="[item.customize.themeBack]"
            />
          </div>
          <div style="padding: 14px">
            <div class="bottom">
              <el-checkbox-group v-model="checkList">
                <el-checkbox v-if="isAllowApproval" :key="item.id" :label="index">选中</el-checkbox>
              </el-checkbox-group>
              <div class="info" @click="handleClickCard(index)">
                <div>昵称： <a class="underline" @click.stop="queryUserDetails(item.userId)">{{ item.userProfile.userNickname }}</a></div>
                <div class="line">
                  <div>性别：{{ item.userProfile.userSexName }} </div>
                  <div>年龄：{{ item.userProfile.age }} </div>
                </div>
                <div>创建时间：<a :title="item.createTime">{{ item.createTime }}</a> </div>
                <div>修改时间：<a :title="item.createTime">{{ item.updateTime }}</a> </div>
                <div v-if="listQuery.themeStatus !== 'PENDING'">审核人: <a :title="item.optUserNickname">{{ item.optUserNickname || '-' }}</a></div>
              </div>
              <el-button type="text" class="button" @click="editUserForm(item.userProfile.id)">编辑用户</el-button>
              <el-button type="text" class="button" @click="accountHanle(item.userProfile.id)">账号处理</el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <div v-if="isAllowApproval" style="text-align:center;">
      <div style="padding:5px;font-size:14px;color:#909399">已勾选了:<span style="color:#F00">{{ checkList.length }}</span></div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-folder-checked"
        @click="pass"
      >
        鉴定通过
      </el-button>

      <el-button
        class="filter-item"
        type="danger"
        icon="el-icon-folder-delete"
        @click="notpass"
      >
        鉴定违规
      </el-button>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <user-deatils-drawer
      v-if="userDeatilsDrawer"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawer=false"
    />

    <edit-user-form
      v-if="editUserFormVisible"
      :user-id="thatSelectedUserId"
      @close="editUserFormVisible=false"
      @success="renderDataSuccess"
    />

    <account-hanle
      v-if="accountHandleVisible"
      :user-id="thatSelectedUserId"
      @success="renderDataSuccess"
      @close="accountHandleVisible=false"
    />
  </div>
</template>
<script>
import { themeApprove, pageRoomThemeApproval } from '@/api/approval'
import Pagination from '@/components/Pagination'
import { commonApprovalStatus, genders } from '@/constant/user'
import EditUserForm from '@/components/data/EditUserForm'
import { pickerOptions } from '@/constant/el-const'
import AccountHanle from '@/components/data/AccountHanle'
import { sysOriginPlatforms } from '@/constant/origin'
import { mapGetters } from 'vuex'

export default {
  name: 'RoomThemeApproval',
  components: { Pagination, EditUserForm, AccountHanle },
  data() {
    return {
      searchDisabled: false,
      sysOriginPlatforms,
      accountHandleVisible: false,
      pickerOptions,
      commonApprovalStatus,
      genders,
      userDeatilsDrawer: false,
      thatSelectedUserId: '',
      list: [],
      checkList: [],
      checkAll: false,
      isIndeterminate: false,
      editUserFormVisible: false,
      total: 0,
      listQuery: {
        sysOrigin: '',
        cursor: 1,
        limit: 30,
        userId: '',
        themeStatus: 'PENDING',
        startDateTime: '',
        endDateTime: ''
      },
      rangeDate: '',
      listLoading: true
    }
  },
  computed: {
    isAllowApproval() {
      return this.listQuery.themeStatus === 'PENDING'
    },
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startDateTime = newVal[0]
          this.listQuery.endDateTime = newVal[1]
          return
        }
        this.listQuery.startDateTime = ''
        this.listQuery.endDateTime = ''
      }
    }
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      that.listLoading = true
      that.checkList = []
      that.isIndeterminate = true
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      pageRoomThemeApproval(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      }).catch(er => {
        that.listLoading = false
        console.error(er)
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    accountHanle(userId) {
      this.accountHandleVisible = true
      this.thatSelectedUserId = userId
    },
    handleCheckAllChange(val) {
      const that = this
      that.checkList = []
      if (val) {
        that.list.forEach((item, index) => {
          that.checkList.push(index)
        })
      }
      this.isIndeterminate = false
    },
    pass() {
      const that = this
      if (that.checkList.length === 0) {
        that.$opsMessage.warn('请勾选审批项')
        return
      }
      that.$confirm('确认审核选中记录吗？', '提示', {
        type: 'warning'
      }).then(() => {
        that.listLoading = true
        themeApprove({
          approvalStatus: 'PASS',
          waitApprovalUser: that.getApprovalParams()
        }).then((res) => {
          that.listLoading = false
          that.$opsMessage.success()
          that.renderData()
        }).catch(() => { that.listLoading = false })
      }).catch(() => {})
    },
    notpass() {
      const that = this
      if (that.checkList.length === 0) {
        that.$opsMessage.warn('请勾选审批项')
        return
      }
      that.$confirm('确认审核选中记录吗？', '提示', {
        type: 'warning'
      }).then(() => {
        that.listLoading = true
        themeApprove({
          approvalStatus: 'NOT_PASS',
          waitApprovalUser: that.getApprovalParams()
        }).then((res) => {
          that.listLoading = false
          that.$opsMessage.success()
          that.renderData()
        }).catch(() => { that.listLoading = false })
      }).catch(() => {})
    },
    editUserForm(id) {
      this.thatSelectedUserId = id
      this.editUserFormVisible = true
    },
    renderDataSuccess() {
      this.$message({
        message: 'Successful',
        type: 'success'
      })
      this.renderData()
    },
    queryUserDetails(id) {
      this.thatSelectedUserId = id
      this.userDeatilsDrawer = true
    },
    handleClickCard(id) {
      const index = this.checkList.findIndex(nid => nid === id)
      if (index > -1) {
        this.checkList.splice(index, 1)
        return
      }
      this.checkList.push(id)
    },
    getApprovalParams() {
      const that = this
      const approvalParams = []
      that.checkList.forEach(item => {
        const checkData = that.list[item]
        approvalParams.push({
          userId: checkData.userProfile.id,
          contentId: checkData.customize.id,
          content: checkData.customize.themeMoney
        })
      })
      return approvalParams
    }
  }
}
</script>
<style scoped lang="scss">
.img {
  position: relative;
  .details {
    position: absolute;
    top: 5px;
    left: 5px;
    font-size: 20px;
    z-index: 100;
    cursor: pointer;
  }
  .label {
    position: absolute;
    bottom: 0px;
    border: 1px solid;
    width: 100%;
    background: black;
    opacity: 0.5;
    color: #fff;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 25px;
  }
}
.bottom {
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 25px;
  .line {
    display: flex;
    >div {
      width: 100%;
    }
  }
  .info {
    cursor: pointer;
  }
}

</style>
