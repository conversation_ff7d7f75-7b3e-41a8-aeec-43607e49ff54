<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;">
            <sys-origin-icon
              :icon="item.value"
              :desc="item.value"
            /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <div>
          <account-input
            v-model="listQuery.userId"
            placeholder="用户id"
            type="USER"
            :sys-origin="listQuery.sysOrigin"
          />
        </div>
      </div>
      <el-select
        v-model="listQuery.mikeType"
        placeholder="麦位类型"
        style="width:120px;"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="特殊麦位" :value="'SPECIAL_MIKE'" />
        <el-option label="聚会麦位" :value="'PARTY_MIKE'" />
        <el-option label="尊贵麦位" :value="'HONORABLE_MIKE'" />
      </el-select>
      <el-select
        v-model="listQuery.expireType"
        placeholder="过期类型"
        style="width:120px;"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="临时" :value="'TEMPORARY'" />
        <el-option label="永久" :value="'PERMANENT'" />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="购买人" align="center" min-width="200">
        <template slot-scope="scope">
          <div
            style="width:100%;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;"
          >
            <el-link @click="queryUserDetails(scope.row.userId)">
              <a :title="scope.row.buyerNickname">
                {{ scope.row.buyerNickname }}
              </a>
            </el-link>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="封面" align="center" min-width="80">
        <template slot-scope="scope">
          <el-image
            :src="handleImageUrl(scope.row)"
            style="width: 50px; height: 50px; margin: 0px 10px 10px 0px"
          />
        </template>
      </el-table-column>
      <el-table-column label="归属系统" align="center">
        <template slot-scope="scope">
          <sys-origin-icon
            :icon="scope.row.sysOrigin"
            :desc="scope.row.sysOrigin"
          />
        </template>
      </el-table-column>
      <el-table-column label="麦位类型" align="center">
        <template slot-scope="scope">
          <div>
            <el-tag
              v-if="scope.row.mikeType === 'SPECIAL_MIKE'"
            >特殊麦位</el-tag>
            <el-tag v-if="scope.row.mikeType === 'PARTY_MIKE'">聚会麦位</el-tag>
            <el-tag
              v-else-if="scope.row.mikeType === 'HONORABLE_MIKE'"
            >尊贵麦位</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="过期类型" align="center">
        <template slot-scope="scope">
          <div>
            <el-tag v-if="scope.row.expireType === 'TEMPORARY'">临时</el-tag>
            <el-tag
              v-else-if="scope.row.expireType === 'PERMANENT'"
            >永久</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="propsCandy"
        label="金额"
        align="center"
        min-width="100"
      />
      <el-table-column
        prop="expireTime"
        label="过期时间"
        align="center"
        width="200"
      >
        <template slot-scope="scope">
          {{ scope.row.expireTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
        width="200"
      >
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <user-deatils-drawer
      v-if="userDeatilsDrawer"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawer = false"
    />
  </div>
</template>

<script>
import { userPropsMikeType } from '@/api/user'
import { mapGetters } from 'vuex'
import Pagination from '@/components/Pagination'
import { pickerOptions } from '@/constant/el-const'
export default {
  name: 'UserPropsMikeType',
  components: { Pagination },
  data() {
    return {
      thatRow: {},
      userDeatilsDrawer: false,
      thatSelectedUserId: '',
      pickerOptions,
      list: [],
      checkList: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: '',
        sysOrigin: 'MARCIE',
        mikeType: '',
        expireType: ''
      },
      listLoading: true
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData(true)
  },
  methods: {
    handleImageUrl(row) {
      return row.mikeCover ? row.mikeCover : ''
    },
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      userPropsMikeType(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    queryUserDetails(userId) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = userId
    },
    handleSearch() {
      this.renderData(true)
    },
    querySearch(queryString, cb) {
      var restaurants = this.propsOrigins
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants
      cb(results)
    },
    handleSelect(item) {
      this.renderData(true)
    },
    createFilter(queryString) {
      return restaurant => {
        return (
          restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) >=
            0 ||
          restaurant.name.toLowerCase().indexOf(queryString.toLowerCase()) >= 0
        )
      }
    }
  }
}
</script>
