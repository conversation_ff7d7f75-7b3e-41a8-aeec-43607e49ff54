<template>
  <div class="banner-form-edite">
    <el-drawer
      :title="textOptTitle"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div v-loading="submitLoading">

        <div class="drawer-form">
          <el-form ref="form" :model="form" :rules="rules" label-width="100px">
            <el-form-item label="麦位图" prop="mikeCover">

              <el-col :span="8">
                <el-form-item prop="mikeCover" class="upload-small">
                  <el-upload
                    :disabled="mikeCoverUploadLoading"
                    :file-list="mikeCoverFileList"
                    :class="{'upload-but-hide': !isShowMikeCoverUpload}"
                    action=""
                    list-type="picture-card"
                    :http-request="uploadMikeCover"
                    :show-file-list="!isShowMikeCoverUpload"
                    :on-remove="handleMikeCoverFileRemove"
                    accept="image/*"
                  >
                    <i slot="default" v-loading="mikeCoverUploadLoading" class="el-icon-plus" />
                    <div slot="tip" class="el-upload__tip">麦位图</div>
                  </el-upload>
                </el-form-item>
              </el-col>
              <el-col :span="8" class="upload-small">
                <el-form-item prop="mikeIcon">
                  <el-upload
                    :disabled="mikeIconUploadLoading"
                    :file-list="mikeIconFileList"
                    :class="{'upload-but-hide': !isShowMikeIconUpload}"
                    action=""
                    list-type="picture-card"
                    :http-request="uploadMikeIcon"
                    :show-file-list="!isShowMikeIconUpload"
                    :on-remove="handleMikeIconFileRemove"
                    accept="image/*"
                  >
                    <i slot="default" v-loading="mikeIconUploadLoading" class="el-icon-plus" />
                    <div slot="tip" class="el-upload__tip">麦位图标</div>
                  </el-upload>
                </el-form-item>
              </el-col>
            </el-form-item>
            <el-form-item label="状态" prop="showcase">
              <el-select
                v-model="form.showcase"
                placeholder="状态"
                clearable
                style="width:100%;"
                class="filter-item"
              >
                <el-option label="下架" :value="false" />
                <el-option label="上架" :value="true" />
              </el-select>
            </el-form-item>
            <el-form-item label="系统" prop="sysOrigin">
              <el-select
                v-model="form.sysOrigin"
                placeholder="选择系统"
                style="width:100%;"
                class="filter-item"
              >
                <el-option
                  v-for="item in permissionsSysOriginPlatforms"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
                  <span style="float: left;margin-left:10px">{{ item.label }}</span>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="麦位名称" prop="mikeName">
              <el-select
                v-model="form.mikeName"
                placeholder="麦位名称"
                clearable
                style="width:100%;"
                class="filter-item"
              >
                <el-option label="常规麦位" :value="'常规麦位'" />
                <el-option label="特殊麦位" :value="'特殊麦位'" />
                <el-option label="聚会麦位" :value="'聚会麦位'" />
                <el-option label="尊贵麦位" :value="'尊贵麦位'" />
              </el-select>
            </el-form-item>
            <el-form-item label="麦位类型" prop="mikeType">
              <el-select
                v-model="form.mikeType"
                placeholder="麦位类型"
                clearable
                style="width:100%;"
                class="filter-item"
              >
                <el-option label="常规麦位" :value="'NORMAL_MIKE'" />
                <el-option label="特殊麦位" :value="'SPECIAL_MIKE'" />
                <el-option label="聚会麦位" :value="'PARTY_MIKE'" />
                <el-option label="尊贵麦位" :value="'HONORABLE_MIKE'" />
              </el-select>
            </el-form-item>
            <el-form-item label="15天麦位价格" prop="fifteenMikeCandy">
              <el-input v-model.trim="form.fifteenMikeCandy" type="text" placeholder="金币必须是整数" minlength="1" maxlength="150" show-word-limit />
            </el-form-item>
            <el-form-item label="永久麦位价格" prop="longMikeCandy">
              <el-input v-model.trim="form.longMikeCandy" type="text" placeholder="金币必须是整数" minlength="1" maxlength="150" show-word-limit />
            </el-form-item>
            <el-form-item label="收费类型" prop="chargeType">
              <el-select
                v-model="form.chargeType"
                placeholder="收费类型"
                clearable
                style="width:100%;"
                class="filter-item"
              >
                <el-option label="免费" :value="'FREE'" />
                <el-option label="金币" :value="'GOLD'" />
              </el-select>
            </el-form-item>
            <el-form-item label="排序" prop="depict">
              <el-input v-model.trim="form.sort" v-number placeholder="降序排列(数字越大越靠前)" />
            </el-form-item>
          </el-form>
        </div>
        <div class="drawer-footer">
          <el-button @click="handleClose()">取消</el-button>
          <el-button type="primary" @click="submitForm()">保存</el-button>
        </div>
      </div>

    </el-drawer>
  </div>
</template>
<script>

import { addMikeType, updateMikeType } from '@/api/mike-type'
import { getElementUiUploadFile } from '@/utils'
import { mapGetters } from 'vuex'
export default {
  props: {
    row: {
      type: Object,
      default: null
    },
    sysOrigin: {
      type: String,
      require: false,
      default: ''
    }
  },
  data() {
    const commonRules = [{ required: true, message: '必填字段', trigger: 'blur' }]
    return {
      mikeCoverUploadLoading: false,
      mikeCoverFileList: [],
      mikeIconUploadLoading: false,
      mikeIconFileList: [],
      form: {
        id: '',
        mikeCover: '',
        mikeIcon: '',
        showcase: '',
        mikeName: '',
        sysOrigin: '',
        mikeType: '',
        fifteenMikeCandy: '',
        longMikeCandy: '',
        chargeType: '',
        sort: ''
      },
      submitLoading: false,
      uploadLoading: false,
      rules: {
        mikeIcon: commonRules,
        mikeName: commonRules,
        showcase: commonRules,
        mikeType: commonRules,
        fifteenMikeCandy: commonRules,
        longMikeCandy: commonRules,
        chargeType: commonRules,
        sysOrigin: commonRules
      }
    }
  },
  computed: {
    textOptTitle() {
      return `${(this.row && this.row.id ? '修改' : '新增')}(${this.sysOrigin})`
    },
    isShowMikeCoverUpload() {
      return !this.form.mikeCover
    },
    isShowMikeIconUpload() {
      return !this.form.mikeIcon
    },
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    row: {
      handler(val) {
        if (!val) {
          this.listRegion()
          return
        }
        const form = Object.assign({}, val)
        this.mikeCoverFileList = getElementUiUploadFile(form.mikeCover)
        this.mikeIconFileList = getElementUiUploadFile(form.mikeIcon)
        this.form = Object.assign(this.form, form)
        this.listRegion()
      },
      immediate: true
    }
  },
  methods: {
    uploadMikeIcon(file) {
      const that = this
      that.mikeIconUploadLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.other).then(res => {
        that.mikeIconUploadLoading = false
        that.form.mikeIcon = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.mikeIconUploadLoading = false
      })
    },
    handleMikeIconFileRemove(file, fileList) {
      this.form.mikeIcon = ''
      this.mikeIconUploadLoading = false
    },
    uploadMikeCover(file) {
      const that = this
      that.mikeCoverUploadLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.other).then(res => {
        that.mikeCoverUploadLoading = false
        that.form.mikeCover = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.mikeCoverUploadLoading = false
      })
    },
    handleMikeCoverFileRemove(file, fileList) {
      this.form.mikeCover = ''
      this.mikeCoverUploadLoading = false
    },
    handleClose() {
      this.$emit('close')
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return
        }
        const submitForm = Object.assign({}, that.form)
        that.submitLoading = true

        if (!submitForm.sysOrigin) {
          submitForm.sysOrigin = that.sysOrigin
        }

        if (!submitForm.sysOrigin) {
          that.$opsMessage.fail('系统错误, 没有平台信息!')
          return
        }
        if (submitForm.id) {
          updateMikeType(submitForm).then(res => {
            that.submitLoading = false
            that.$emit('success', res)
          }).catch(er => {
            that.submitLoading = false
            that.$emit('fial', er)
          })
          return
        }
        addMikeType(submitForm).then(res => {
          that.submitLoading = false
          that.$emit('success', res)
        }).catch(er => {
          that.submitLoading = false
          console.error(er)
          that.$emit('fial', er)
        })
      })
    }
  }
}
</script>
