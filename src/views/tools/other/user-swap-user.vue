<template>
  <div class="user-swap-user">
    <el-form ref="userSwapUser" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="原始ID" prop="sourceUserId">
        <el-input v-model="form.sourceUserId" v-number placeholder="请输入原始用户长ID" />
      </el-form-item>
      <el-form-item label="目标ID" prop="targetUserId">
        <el-input v-model="form.targetUserId" v-number placeholder="请输入目标用户长ID" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :loading="checkCondtionLoading" @click="checkCondition()">验证转换条件</el-button>
      </el-form-item>
    </el-form>

    <div v-if="checkResults && checkResults.length>0" class="check-condition">
      <div class="check-tips">
        <div v-for="(item, index) in checkResults" :key="index" class="tips" :class="item.type">
          <i :class="{'el-icon-error': item.type === 'error','el-icon-warning': item.type === 'warn'}" />
          {{ item.desc }} {{ checkTips }}
        </div>
      </div>
      <el-button v-if="!notAollwSubmit" type="primary" @click="submitExecute()">提交执行</el-button>
    </div>
  </div>
</template>

<script>
import { checkUserSwapUser, executeUserSwapUser } from '@/api/tools'
export default {
  name: 'UserSwapUser',
  data() {
    return {
      checkCondtionLoading: false,
      checkResults: [],
      checkTips: '',
      notAollwSubmit: true,
      form: {
        sourceUserId: '',
        targetUserId: ''
      },
      rules: {
        sourceUserId: [{ required: true, message: '必填参数不可为空', trigger: 'blur' }],
        targetUserId: [{ required: true, message: '必填参数不可为空', trigger: 'blur' }]
      },
      tipsMap: {
        'SOURCE_USER_IS_NULL': {
          type: 'error',
          desc: '原始用户没有找到'
        },
        'TARGET_USER_IS_NULL': {
          type: 'error',
          desc: '目标用户没有找到'
        },
        'ORIGIN_NOT_EQUALS': {
          type: 'warn',
          desc: '用户来自不同系统'
        },
        'SOURCE_USER_AUTH_IS_NULL': {
          type: 'error',
          desc: '原始用户认证信息没找到'
        },
        'TARGET_USER_AUTH_IS_NULL': {
          type: 'error',
          desc: '目标用户认证信息没找到'
        },
        'AUTH_OPENID_NOT_EQUALS': {
          type: 'warn',
          desc: '注册来源OpenId不一致'
        }
      },
      submitExecuteLoading: false
    }
  },
  methods: {
    submitExecute() {
      const that = this
      that.submitExecuteLoading = true
      executeUserSwapUser(that.form).then(res => {
        that.$opsMessage.success()
        that.checkResults = []
        that.checkTips = ''
        that.notAollwSubmit = true
      }).catch(er => {
        that.submitExecuteLoading = false
      })
    },
    checkCondition() {
      const that = this
      that.$refs.userSwapUser.validate((valid) => {
        if (!valid) {
          console.error('error submit!!')
          return
        }
        // if (that.form.sourceUserId === that.form.targetUserId) {
        //   that.$opsMessage.fail('用户id不可相同')
        //   return
        // }
        that.checkCondtionLoading = true
        checkUserSwapUser(that.form).then(res => {
          that.checkCondtionLoading = false
          const result = res.body || {}
          const list = result.key || []
          that.checkResults = list.map(item => that.tipsMap[item]).filter(item => !!item)
          that.notAollwSubmit = false
          for (let index = 0; index < that.checkResults.length; index++) {
            if (that.checkResults[index].type === 'error') {
              that.notAollwSubmit = true
            }
          }

          that.checkTips = result.value || ''
        }).catch(er => {
          that.checkCondtionLoading = false
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.check-condition {
  background: rgb(243, 240, 240);
  padding: 20px;
  .check-tips {
    font-size: 18px;
    padding-bottom: 10px;
    .tips {
      margin-bottom: 10px;
    }
    .error {
      color: rgb(201, 22, 22);
      padding: 0px 10px;
    }
    .warn {
      color: rgb(201, 159, 22);
      padding: 0px 10px;
    }
  }
}
</style>
