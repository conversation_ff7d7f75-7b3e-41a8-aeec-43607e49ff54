<template>
  <div class="token-parse">
    <el-form ref="form" label-width="80px" :model="form" :rules="rules" style="margin-right: 20px;">
      <el-form-item label="模式">
        <el-radio v-model="model" label="TOKEN_PARSE">令牌解析</el-radio>
        <el-radio v-model="model" label="GET_TOKEN">获取令牌</el-radio>
      </el-form-item>
      <el-form-item v-if="model === 'TOKEN_PARSE'" label="令牌" prop="token">
        <el-input v-model="form.token" placeholder="请输入用户令牌" />
      </el-form-item>

      <el-form-item v-if="model === 'GET_TOKEN'" label="用户ID" prop="userId">
        <el-input v-model="form.userId" placeholder="请输入用户ID" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :disabled="formLoading" :loading="formLoading" @click="submit">确 定</el-button>
      </el-form-item>

      <div v-if="formDataShow">
        {{ formResult }}
      </div>
    </el-form>
  </div>
</template>

<script>
import { getAccessTokenByUserId, getUserToken } from '@/api/tools'
export default {
  name: 'ToolsOther',
  data() {
    return {
      model: 'TOKEN_PARSE',
      form: {
        token: '',
        userId: ''
      },
      rules: {
        token: [{
          required: true,
          message: '必填字段不可为空',
          trigger: 'blur'
        }],
        userId: [
          {
            required: true,
            message: '必填字段不可为空',
            trigger: 'blur'
          }
        ]
      },
      formLoading: false,
      formResult: {},
      formDataShow: false
    }
  },
  mounted() {
  },
  methods: {
    submit() {
      const that = this
      that.$refs.form.validate((valid) => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.formDataShow = false
        that.formLoading = true
        if (that.model === 'TOKEN_PARSE') {
          getAccessTokenByUserId(that.form.token).then(res => {
            that.formLoading = false
            that.formResult = res.body || {}
            that.formDataShow = true
          }).catch(er => {
            that.formLoading = false
          })
        }

        if (that.model === 'GET_TOKEN') {
          getUserToken(that.form.userId).then(res => {
            that.formLoading = false
            that.formResult = res.body || {}
            that.formDataShow = true
          }).catch(er => {
            that.formLoading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
</style>
