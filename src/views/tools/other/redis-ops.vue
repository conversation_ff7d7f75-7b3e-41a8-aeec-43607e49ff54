<template>
  <div class="redis-ops">
    <el-form ref="form" label-width="80px" :model="form" :rules="rules" style="margin-right: 20px;">
      <el-form-item label="模式">
        <el-radio v-model="model" label="CLEAR_KEYS">清理key</el-radio>
      </el-form-item>
      <el-form-item v-if="model === 'CLEAR_KEYS'" label="Key" prop="userId">
        <el-input v-model="form.userId" placeholder="请输入Key信息" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :disabled="formLoading" :loading="formLoading" @click="submit">确 定</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { clearRedisKey } from '@/api/tools'
export default {
  name: 'ToolsOther',
  data() {
    return {
      model: 'CLEAR_KEYS',
      form: {
        key: ''
      },
      rules: {
        key: [{
          required: true,
          message: '必填字段不可为空',
          trigger: 'blur'
        }]
      },
      formLoading: false,
      formResult: {}
    }
  },
  mounted() {
  },
  methods: {
    submit() {
      const that = this
      that.$refs.form.validate((valid) => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }

        that.formLoading = true
        if (that.model === 'CLEAR_KEYS') {
          clearRedisKey(that.form.key).then(res => {
            that.formLoading = false
            that.$opsMessage.success()
          }).catch(er => {
            that.formLoading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
</style>
