<template>
  <div class="user-swap-user">
    <el-form ref="userSwapUser" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="用户长ID" prop="userId">
        <el-input v-model="form.userId" v-number placeholder="请输入原始用户长ID" />
      </el-form-item>
      
      <el-form-item label="国家" prop="countryId">
          <el-select
            v-model="form.countryId"
            placeholder="请选择国家"
            filterable
            :filter-method="userFilter"
            clearabl
          >
            <el-option
              v-for="item in countryList"
              :key="item.id"
              :label="item.aliasName"
              :value="item.id"
            />
          </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :loading="checkCondtionLoading" @click="checkCondition()">确定</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { updateUserRoomRegion } from '@/api/tools'
import { getCountryAlls } from '@/api/sys'
export default {
  name: 'UserSwapUser',
  data() {
    return {
      checkCondtionLoading: false,
      listLoading: false,
      allCountryList: [],
      countryList: [],
      checkResults: [],
      checkTips: '',
      notAollwSubmit: true,
      form: {
        userId: '',
        countryId: ''
      },
      rules: {
        userId: [{ required: true, message: '必填参数不可为空', trigger: 'blur' }],
        countryId: [{ required: true, message: '必填参数不可为空', trigger: 'blur' }]
      },
      submitExecuteLoading: false
    }
  },
  created() {
    this.getUserWhiteList();
  },
  methods: {
    getUserWhiteList() {
      const that = this
      getCountryAlls().then(res => {
        that.listLoading = false
        this.allCountryList = res.body || {}
        this.userFilter()
      }).catch(er => {
        that.listLoading = false
      })
    },
    userFilter(query = '') {
      console.log(this.allCountryList);
      const arr = this.allCountryList.filter((item) => {
        return item.aliasName.includes(query) || item.alphaTwo.includes(query)
      })
      if (arr.length > 50) {
        this.countryList = arr.slice(0, 50)
      } else {
        this.countryList = arr
      }
      console.log(this.countryList);
    },
    checkCondition() {
      const that = this
      that.$refs.userSwapUser.validate((valid) => {
        if (!valid) {
          console.error('error submit!!')
          return
        }
        that.checkCondtionLoading = true
        updateUserRoomRegion(that.form).then(res => {
          that.checkCondtionLoading = false
          const result = res.body || {}
          console.log(res.body);
          that.$opsMessage.success()
        }).catch(er => {
          that.checkCondtionLoading = false
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.check-condition {
  background: rgb(243, 240, 240);
  padding: 20px;
  .check-tips {
    font-size: 18px;
    padding-bottom: 10px;
    .tips {
      margin-bottom: 10px;
    }
    .error {
      color: rgb(201, 22, 22);
      padding: 0px 10px;
    }
    .warn {
      color: rgb(201, 159, 22);
      padding: 0px 10px;
    }
  }
}
</style>
