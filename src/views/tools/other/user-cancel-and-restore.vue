<template>
  <div class="user-swap-user">
    <el-form ref="userSwapUser" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="用户长ID" prop="sourceUserId">
        <el-input v-model="form.userId" v-number placeholder="请输入原始用户长ID" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" :loading="checkCondtionLoading" @click="checkCondition()">确定</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { executeUserCancelAndRestore } from '@/api/tools'
export default {
  name: 'UserSwapUser',
  data() {
    return {
      checkCondtionLoading: false,
      form: {
        userId: '',
        countryId: '11'
      },
      rules: {
        userId: [{ required: true, message: '必填参数不可为空', trigger: 'blur' }]
      }
    }
  },
  methods: {
    checkCondition() {
      const that = this
      that.$refs.userSwapUser.validate((valid) => {
        if (!valid) {
          console.error('error submit!!')
          return
        }
        that.checkCondtionLoading = true
        executeUserCancelAndRestore(that.form).then(res => {
          that.checkCondtionLoading = false
          const result = res.body || {}
          console.log(res.body);
          that.$opsMessage.success()
        }).catch(er => {
          that.checkCondtionLoading = false
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.check-condition {
  background: rgb(243, 240, 240);
  padding: 20px;
  .check-tips {
    font-size: 18px;
    padding-bottom: 10px;
    .tips {
      margin-bottom: 10px;
    }
    .error {
      color: rgb(201, 22, 22);
      padding: 0px 10px;
    }
    .warn {
      color: rgb(201, 159, 22);
      padding: 0px 10px;
    }
  }
}
</style>
