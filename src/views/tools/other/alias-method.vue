<template>
  <div class="alias-method">
    <el-button :loading="submitFormLoading" :disabled="submitFormLoading" @click.prevent="settingRequest()">设置属性</el-button>
    <el-button v-if="attributeForm.bets.length > 0 && attributeForm.bets[0].name" :loading="submitFormLoading" :disabled="submitFormLoading" @click.prevent="submitForm()">重新运行</el-button>
    <div id="aliasMethodColumnarChart" ref="aliasMethodColumnarChart" style="width: 100%;height: 300px;" />
    <el-dialog
      title="设置概率"
      :visible.sync="paramContentVisible"
      :before-close="paramContentHandleClose"
      top="50px"
    >
      <div class="form-dialog">
        <el-form ref="attributeForm" label-width="80px" :model="attributeForm" style="margin-right: 20px;">
          <el-form-item label="概率说明">
            <div>平均概率:1.0/奖品数量={{ 1.0 / attributeForm.bets.length }}</div>
            <div>概率填充: 概率>=平均概率(进入large), 否则(进入small) </div>
            <div>采样:
              <div>1.如果设置概率全部小于平均概率,当前概率=1</div>
              <div>2.如果设置概率 (当前最大概率+当前最小概率 - 平均概率) > 平均概率 继续采用large
                出现情况: 我设置了概率0.9,0.8,0.3 但是0.3变成了大概率,由于概率设置过高导致不够平均取到的第一个概率被不断的采样;
              </div>
              <div>建议: 概率 + 库存这样比较保险安全</div>
            </div>
          </el-form-item>
          <el-form-item label="运行次数">
            <el-input v-model="attributeForm.runSize" v-number />
          </el-form-item>
          <el-row v-for="(item, index) in attributeForm.bets" :key="index" :gutter="10">
            <el-col :span="12">
              <el-form-item label="商品">
                <el-input v-model="item.name" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="概率">
                <el-input v-model="item.probability" minlength="1" maxlength="10" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cleanAttribute">清空属性</el-button>
        <el-button @click="addAttribute">新增属性</el-button>
        <el-button @click="addAttributeFill">快速填充</el-button>
        <el-button type="primary" @click="submitAttribute">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { alisMethod } from '@/api/tools'
export default {
  name: 'ToolsOther',
  data() {
    return {
      submitFormLoading: false,
      aliasMethodColumnarChart: null,
      paramContentVisible: false,
      attributeForm: {
        bets: [],
        runSize: 10000
      },
      color: ['#5470c6',
        '#91cc75',
        '#fac858',
        '#ee6666',
        '#73c0de',
        '#3ba272',
        '#fc8452',
        '#9a60b4',
        '#ea7ccc',
        '#459AF0',
        '#38C3B0',
        '#86CA5A',
        '#BFD44F',
        '#FCC248',
        '#FCE448',
        '#F58B41',
        '#F7765B',
        '#525ECD',
        '#547FDB',
        '#80FFA5',
        '#00DDFF',
        '#37A2FF',
        '#FF0087',
        '#FFBF00',
        '#65B581',
        '#FFCE34',
        '#FD665F'],
      fills: [
        { name: '鲜花', probability: '0.2' },
        { name: '橘子', probability: '0.15' },
        { name: '苹果', probability: '0.15' },
        { name: '西瓜', probability: '0.1' },
        { name: '香蕉', probability: '0.08' },
        { name: '果汁', probability: '0.05' },
        { name: '手机', probability: '0.01' },
        { name: '电脑', probability: '0.001' }
      ]
    }
  },
  mounted() {
    const that = this
    that.attributeForm.bets = [{ name: '', probability: '' }]
    that.aliasMethodColumnarChart = that.$echarts.init(that.$refs.aliasMethodColumnarChart)
  },
  methods: {
    addAttributeFill() {
      this.attributeForm.bets = Object.assign([], this.fills)
    },
    cleanAttribute() {
      this.attributeForm.bets = [{ name: '', probability: '' }]
    },
    submitAttribute() {
      const attributes = this.attributeForm.bets
      const resultModel = {}
      for (let index = 0; index < attributes.length; index++) {
        const attribute = attributes[index]
        resultModel[attribute.name] = parseFloat(attribute.probability)
      }
      this.paramContentVisible = false
      this.submitForm()
    },
    paramContentHandleClose() {
      this.paramContentVisible = false
    },
    addAttribute() {
      this.attributeForm.bets.push({ name: '', probability: '' })
    },
    settingRequest() {
      this.paramContentVisible = true
    },
    submitForm() {
      const that = this
      that.$refs.attributeForm.validate((valid) => {
        if (!valid) {
          console.error('error submit!!')
          return
        }
        that.submitFormLoading = true
        alisMethod(that.attributeForm).then(res => {
          that.submitFormLoading = false

          if (!res.body || res.body.length <= 0) {
            return
          }

          that.aliasMethodColumnarChart.setOption({
            tooltip: {},
            xAxis: {
              type: 'category',
              data: res.body.map(item => `${item.name}/${item.probability * 100}%`)
            },
            yAxis: {
              type: 'value'
            },
            label: {
              show: true,
              valueAnimation: true
            },
            series: [
              {
                data: res.body.map(item => item.lottery),
                type: 'bar'
              }
            ]
          })
        }).catch(er => {
          that.submitFormLoading = false
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.sgin-content {
  background: rgb(243, 240, 240);
  padding: 20px;
}
</style>
