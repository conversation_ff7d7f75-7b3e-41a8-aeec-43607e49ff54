<template>
  <div class="app-container">
    <el-card class="box-card" style="margin-top: 10px">
      <div slot="header" class="clearfix">
        <span>用户账户调换</span>
      </div>
      <div class="content">
        <user-swap-user />
      </div>
    </el-card>
    <el-card class="box-card" style="margin-top: 10px">
      <div slot="header" class="clearfix">
        <span>API参数签名</span>
      </div>
      <div class="content">
        <api-sign />
      </div>
    </el-card>
    <el-card class="box-card" style="margin-top: 10px">
      <div slot="header" class="clearfix">
        <span>Alias Method分析</span>
      </div>
      <div class="content">
        <alias-method />
      </div>
    </el-card>

    <el-card class="box-card" style="margin-top: 10px">
      <div slot="header" class="clearfix">
        <span>用户令牌解析</span>
      </div>
      <div class="content">
        <token-parse />
      </div>
    </el-card>

    <el-card class="box-card" style="margin-top: 10px">
      <div slot="header" class="clearfix">
        <span>Redis操作</span>
      </div>
      <div class="content">
        <redis-ops />
      </div>
    </el-card>

    <el-card class="box-card" style="margin-top: 10px">
      <div slot="header" class="clearfix">
        <span>用户房间区域变动</span>
      </div>
      <div class="content">
        <user-room-region />
      </div>
    </el-card>
    
    <el-card class="box-card" style="margin-top: 10px">
      <div slot="header" class="clearfix">
        <span>注销用户恢复</span>
      </div>
      <div class="content">
        <user-cancel-and-restore />
      </div>
    </el-card>

  </div>
</template>

<script>
import ApiSign from './api-sign'
import AliasMethod from './alias-method'
import UserSwapUser from './user-swap-user'
import TokenParse from './token-parse'
import RedisOps from './redis-ops'
import UserRoomRegion from './user-room-region'
import UserCancelAndRestore from './user-cancel-and-restore'
export default {
  name: 'ToolsOther',
  components: { ApiSign, AliasMethod, UserSwapUser, TokenParse, RedisOps, UserRoomRegion, UserCancelAndRestore },
  data() {
    return {
      userId: '',
      result: {},
      restultLoading: false
    }
  },
  methods: {

  }
}
</script>

<style scoped lang="scss">

</style>
