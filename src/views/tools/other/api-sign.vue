<template>
  <div class="api-sign">
    <el-form ref="apiSingFrom" :model="apiSingFrom" :rules="rules" label-width="80px">
      <el-form-item label="签名平台" prop="platform">
        <el-select
          v-model="apiSingFrom.platform"
          placeholder="平台"
          style="width: 100%"
          class="filter-item"
        >
          <el-option
            v-for="item in originPlatforms"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="数据内容" prop="body">
        <el-input v-model="apiSingFrom.body" />
      </el-form-item>
      <el-form-item label="签名KEY" prop="key">
        <el-input v-model="apiSingFrom.key" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :loading="submitApiSignFormLoading" @click="submitApiSignForm()">提交</el-button>
        <el-button @click.prevent="settingRequestApiSing()">设置属性</el-button>
      </el-form-item>
    </el-form>
    <div v-if="apiSingParamContent" class="sgin-content">
      {{ apiSingParamContent }}
    </div>
    <el-dialog
      title="API内容属性"
      :visible.sync="apiSingParamContentVisible"
      :before-close="apiSingParamContentHandleClose"
    >
      <div class="form-dialog-api-sing">
        <el-form label-width="80px" :model="addSingAttributeForm" style="margin-right: 20px;">

          <el-row v-for="(item, index) in addSingAttributeForm.attributes" :key="index" :gutter="10">
            <el-col :span="12">
              <el-form-item label="键">
                <el-input v-model="item.key" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="值">
                <el-input v-model="item.value" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cleanSingAttribute">清空属性</el-button>
        <el-button @click="addSingAttribute">新增属性</el-button>
        <el-button type="primary" @click="submitAddSingAttribute">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { requestApiSing } from '@/api/tools'
import { originPlatforms } from '@/constant/origin'
export default {
  name: 'ToolsOther',
  data() {
    return {
      addSingAttributeForm: {
        attributes: [
          { key: '', value: '' }
        ]
      },
      apiSingParamContentVisible: false,
      originPlatforms,
      apiSingFrom: {
        platform: 'iOS',
        body: '',
        key: ''
      },
      submitApiSignFormLoading: false,
      apiSingParamContent: null,
      rules: {
        platform: [{ required: true, message: '必填参数不可为空', trigger: 'blur' }],
        body: [{ required: true, message: '必填参数不可为空', trigger: 'blur' }],
        key: [{ required: true, message: '必填参数不可为空', trigger: 'blur' }]
      }
    }
  },
  methods: {
    cleanSingAttribute() {
      this.addSingAttributeForm.attributes = [{ key: '', value: '' }]
    },
    submitAddSingAttribute() {
      const attributes = this.addSingAttributeForm.attributes
      const resultModel = {}
      for (let index = 0; index < attributes.length; index++) {
        const attribute = attributes[index]
        resultModel[attribute.key] = attribute.value
      }
      this.apiSingFrom.body = JSON.stringify(resultModel)
      this.apiSingParamContentVisible = false
    },
    apiSingParamContentHandleClose() {
      this.apiSingParamContentVisible = false
    },
    addSingAttribute() {
      this.addSingAttributeForm.attributes.push({ key: '', value: '' })
    },
    settingRequestApiSing() {
      this.apiSingParamContentVisible = true
    },
    submitApiSignForm() {
      const that = this
      that.$refs.apiSingFrom.validate((valid) => {
        if (!valid) {
          console.error('error submit!!')
          return
        }
        that.submitApiSignFormLoading = true
        requestApiSing(that.apiSingFrom).then(res => {
          that.submitApiSignFormLoading = false
          that.apiSingParamContent = res.body
        }).catch(er => {
          that.submitApiSignFormLoading = false
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.sgin-content {
  background: rgb(243, 240, 240);
  padding: 20px;
}
</style>
