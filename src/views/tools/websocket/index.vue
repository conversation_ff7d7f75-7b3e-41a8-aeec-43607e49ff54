<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>订阅广播消息</span>
            <el-button style="float: right; padding: 3px; margin: 0 3px" type="success" @click="addRepository(socketSubForm)">添加仓库</el-button>
            <el-button style="float: right; padding: 3px; margin: 0 3px" type="success" @click="sendMessage(socketSubForm)">发送消息</el-button>
            <el-button style="float: right; padding: 3px; margin: 0 3px" type="warning" @click="subscribeSocket(socketSubForm)">订阅主题</el-button>
            <el-button style="float: right; padding: 3px; margin: 0 3px" type="danger" @click="disconnect(socketSubForm)">断开连接</el-button>
            <el-button style="float: right; padding: 3px; margin: 0 3px" type="primary" @click="createConnect(socketSubForm)">进行连接</el-button>
          </div>
          <div class="card-content">
            <div style="padding: 10px 80px;">
              创建连接 -> 订阅主题 -> 发送消息
            </div>
            <el-form ref="socketSubForm" :model="socketSubForm" :rules="socketSubFormRules" label-width="120px">
              <el-form-item label="连接端点" prop="socketEndpoint">
                <el-input v-model="socketSubForm.socketEndpoint" placeholder="连接端点" />
              </el-form-item>
              <el-form-item label="订阅地址" prop="subscribePatch">
                <el-input v-model="socketSubForm.subscribePatch" placeholder="订阅地址" />
              </el-form-item>
              <el-form-item label="发送接口" prop="sendEndpoint">
                <el-input v-model="socketSubForm.sendEndpoint" placeholder="发送接口" />
              </el-form-item>
              <el-form-item label="我的token" prop="meUserId">
                <el-input v-model="socketSubForm.meUserId" placeholder="我的token" />
              </el-form-item>
              <el-form-item label="消息内容" prop="content">
                <el-input
                  v-model="socketSubForm.content"
                  type="textarea"
                  placeholder="消息内容"
                  maxlength="300"
                  resize="none"
                  show-word-limit
                />
              </el-form-item>
              <el-form-item label="仓库端点" prop="repositoryEndpoint">
                <el-input v-model="socketSubForm.repositoryEndpoint" placeholder="仓库端点" />
              </el-form-item>
              <el-form-item label="仓库数据" prop="repositoryData">
                <el-input
                  v-model="socketSubForm.repositoryData"
                  type="textarea"
                  placeholder="插入仓库数据"
                  maxlength="300"
                  resize="none"
                  show-word-limit
                />
              </el-form-item>
              <el-form-item label="状态描述">
                {{ socketSubForm.message }}
              </el-form-item>
              <el-form-item label="消息内容">
                <div class="message-content">
                  <div v-for="(item, index) in socketSubForm.msgContents" :key="index">{{ item }}</div>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>点对点</span>
            <el-button style="float: right; padding: 3px; margin: 0 3px" type="success" @click="sendMessage(socketToUserForm)">发送消息</el-button>
            <el-button style="float: right; padding: 3px; margin: 0 3px" type="warning" @click="subscribeSocket(socketToUserForm)">订阅主题</el-button>
            <el-button style="float: right; padding: 3px; margin: 0 3px" type="danger" @click="disconnect(socketToUserForm)">断开连接</el-button>
            <el-button style="float: right; padding: 3px; margin: 0 3px" type="primary" @click="createConnect(socketToUserForm)">进行连接</el-button>
          </div>
          <div class="card-content">
            <div style="padding: 10px 80px;">
              创建连接 -> 订阅主题 -> 发送消息
            </div>
            <el-form ref="socketToUserForm" :model="socketToUserForm" :rules="socketToUserFormules" label-width="120px">
              <el-form-item label="连接端点" prop="socketEndpoint">
                <el-input v-model="socketToUserForm.socketEndpoint" placeholder="连接端点" />
              </el-form-item>
              <el-form-item label="订阅前缀" prop="subscribeUserPreffix">
                <el-input v-model="socketToUserForm.subscribeUserPreffix" placeholder="订阅前缀" />
              </el-form-item>
              <el-form-item label="订阅地址" prop="subscribePatch">
                <el-input v-model="socketToUserForm.subscribePatch" placeholder="订阅地址" />
              </el-form-item>
              <el-form-item label="发送接口" prop="sendEndpoint">
                <el-input v-model="socketToUserForm.sendEndpoint" placeholder="发送接口" />
              </el-form-item>
              <el-form-item label="我的token" prop="meUserId">
                <el-input v-model="socketToUserForm.meUserId" placeholder="我的token" />
              </el-form-item>
              <el-form-item label="接收用户ID" prop="targetUser">
                <el-input v-model="socketToUserForm.targetUser" placeholder="接收用户，长ID" />
              </el-form-item>
              <el-form-item label="消息内容" prop="content">
                <el-input
                  v-model="socketToUserForm.content"
                  type="textarea"
                  placeholder="消息内容"
                  maxlength="300"
                  resize="none"
                  show-word-limit
                />
              </el-form-item>
              <el-form-item label="状态描述">
                {{ socketToUserForm.message }}
              </el-form-item>
              <el-form-item label="消息内容">
                <div class="message-content">
                  <div v-for="(item, index) in socketToUserForm.msgContents" :key="index">{{ item }}</div>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import SockJS from 'sockjs-client'
import Stomp from 'stompjs'
export default {
  name: 'Dashboard',
  data() {
    const commonRules = [{ required: true, message: '必填字段', trigger: 'blur' }]
    return {
      socketSubForm: {
        // socketEndpoint: 'https://web.sugartimeapp.com/game_api/socket_stomp/game',
        socketEndpoint: 'http://**************:9000/socket_stomp/game',
        subscribePatch: '/topic',
        sendEndpoint: '/app/send_topic',
        repositoryEndpoint: '/app/repository_add',
        content: '',
        meUserId: '',
        repositoryData: '',

        stompClient: null,
        message: '',
        msgContents: [],
        ruleFormName: 'socketSubForm'
      },
      socketSubFormRules: {
        socketEndpoint: commonRules,
        subscribePatch: commonRules,
        sendEndpoint: commonRules,
        meUserId: commonRules
      },

      socketToUserForm: {
        socketEndpoint: 'http://**************:9000/socket_stomp/game',
        subscribeUserPreffix: '/user',
        subscribePatch: '/queue',
        sendEndpoint: '/app/send_queue',
        meUserId: '',
        targetUser: '',
        content: '',

        stompClient: null,
        message: '',
        msgContents: [],
        ruleFormName: 'socketToUserForm'
      },
      socketToUserFormules: {
        socketEndpoint: commonRules,
        subscribePatch: commonRules,
        sendEndpoint: commonRules,
        targetUser: commonRules,
        meUserId: commonRules,
        subscribeUserPreffix: commonRules
      }
    }
  },
  computed: {
  },
  methods: {
    createConnect(form) {
      const that = this
      that.$refs[form.ruleFormName].validate(valid => {
        if (!valid) {
          console.log('error submit!!')
          return
        }
        // 设置 SOCKET
        const socket = new SockJS(form.socketEndpoint + `?token=${form.meUserId}&destination=${form.subscribePatch}`)
        // 配置 STOMP 客户端
        form.stompClient = Stomp.over(socket)
        // STOMP 客户端连接
        form.stompClient.connect({}, frame => {
          form.message = '连接成功'
        })
      })
    },
    subscribeSocket(form) {
      if (!form.stompClient) {
        this.$opsMessage.fail('请先进行连接.')
        return
      }
      form.message = form.subscribePatch
      // 执行订阅消息
      form.stompClient.subscribe(form.subscribeUserPreffix ? form.subscribeUserPreffix + form.subscribePatch : form.subscribePatch, (responseBody) => {
        console.log('接收：', responseBody)
        var receiveMessage = JSON.parse(responseBody.body)
        form.msgContents.push(receiveMessage.content)
      })
    },
    disconnect(form) {
      if (!form.stompClient) {
        return
      }
      form.stompClient.disconnect(function() {
        form.message = '断开连接'
      })
    },
    sendMessage(form) {
      if (!form.stompClient) {
        this.$opsMessage.fail('请先创建连接.')
        return
      }
      if (!form.content) {
        this.$opsMessage.fail('请输入消息内容.')
        return
      }
      // 设置待发送的消息内容
      var message = {
        destination: form.subscribePatch,
        content: form.content,
        targetUser: form.targetUser
      }
      console.log('向端点:', form.sendEndpoint, '发送参数：', message)
      // 发送消息
      form.stompClient.send(form.sendEndpoint, {}, JSON.stringify(message))
    },
    addRepository(form) {
      if (!form.stompClient) {
        this.$opsMessage.fail('请先创建连接.')
        return
      }
      if (!form.repositoryData) {
        this.$opsMessage.fail('请输入消息内容.')
        return
      }
      // 设置待发送的消息内容
      var message = {
        key: 'TEST',
        body: form.repositoryData
      }
      console.log('向端点:', form.repositoryEndpoint, '发送参数：', message)
      // 发送消息
      form.stompClient.send(form.repositoryEndpoint, {}, JSON.stringify(message))
    }
  }
}
</script>

<style scoped lang="scss">
.box-card {
  margin-bottom: 10px;
  .card-content {
    padding: 20px;
  }
  .message-content {
      height: 200px;
      overflow: auto;
  }
}

</style>
