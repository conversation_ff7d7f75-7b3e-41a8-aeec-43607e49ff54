<template>
  <div class="app-container">
    <div class="upload-file">
      <div class="switch-start-filename">
        <el-switch
          v-model="startCustomFilename"
          active-text="原文件名"
          inactive-text="系统创建"
        />
      </div>
      <div class="flex-c">
        <el-upload
          action=""
          class="el-upload-file"
          :http-request="httpRequest"
          :before-upload="beforeUpload"
          :show-file-list="false"
          drag
          multiple
          accept="image/*,.svga,.svg,.apk"
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div slot="tip" class="el-upload__tip">只支持img、svga、svg、apk, apk不可超过 {{ apkMaxSize }}M, 其他文件且不超过{{ otherMaxSize }}M</div>
        </el-upload>
      </div>

      <el-row v-for="(item, index) in fileList" :key="index">
        <el-col :md="5" class="hidden-sm-only hidden-xs-only">&nbsp;</el-col>
        <el-col :md="14">
          <div class="file-info flex-b">
            <div class="name">
              <div> {{ item.name }} <i class="el-icon-document-copy cursor-pointer" @click="copyContent(item.url)" /> <a v-if="item.url" :href="item.url" target="_blank"><i class="el-icon-view cursor-pointer" /></a></div>
              <div v-if="item.showSourceName" class="source-name">{{ item.sourceName }}</div>
            </div>
            <div class="status">
              <el-button v-if="item.loading" type="text" :loading="item.loading" style="width: 30px;" />
              <div v-else class="flex-c">
                <div v-if="item.status !== 200" class="msg">失败</div>
                <i v-if="item.status === 200" class="el-icon-success font-success" />
                <i v-if="item.status !== 200" class="el-icon-error font-danger" />
              </div>
            </div>
          </div>
        </el-col>
        <el-col :md="5" class="hidden-sm-only hidden-xs-only">&nbsp;</el-col>
      </el-row>
    </div>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { copyText } from '@/utils'
export default {
  data() {
    return {
      startCustomFilename: true,
      fileList: [],
      apkMaxSize: 100,
      otherMaxSize: 5
    }
  },
  computed: {
    ...mapGetters([
      'dataGrant'
    ])
  },
  methods: {
    copyContent(text) {
      copyText(text).then(() => {
        this.$opsMessage.success()
      }).catch(er => {
        this.$opsMessage.fail()
      })
    },
    getFileDir(fileName) {
      if (!fileName) {
        return this.$application.fileBucket.back
      }

      if (fileName.endsWith('.svga')) {
        return this.$application.fileBucket.svgaCover
      }

      if (fileName.endsWith('.apk')) {
        return this.$application.fileBucket.apk
      }
      return this.$application.fileBucket.back
    },
    httpRequest(file) {
      const that = this
      const dir = that.getFileDir(file.file.name)
      file.customFilename = that.startCustomFilename === true ? file.file.name : that.$radmonFilename(file.file.name)
      const uploadFileName = `${dir}/${file.customFilename}`
      const fileItem = {
        status: 200,
        name: uploadFileName,
        url: '',
        sourceName: file.file.name,
        showSourceName: !that.startCustomFilename,
        loading: true
      }
      that.fileList.push(fileItem)
      that.$simpleUploadFlie(file, dir).then(res => {
        fileItem.status = res.res.status
        fileItem.name = res.name
        fileItem.url = that.$getAccessImgUrl(res.name)
        fileItem.loading = false
      }).catch(er => {
        console.error(er)
        fileItem.loading = false
        fileItem.status = 500
      })
    },
    validOverflowM(fileSize, targetSize) {
      return fileSize / 1024 / 1024 < targetSize
    },
    beforeUpload(file) {
      const that = this
      const maxSize = file.name.endsWith('.apk') ? that.apkMaxSize : that.otherMaxSize
      const fileSizeM = that.validOverflowM(file.size, maxSize)
      if (!fileSizeM) {
        setTimeout(() => {
          that.$opsMessage.fail(`${file.name} 超出 ${maxSize}MB`)
        }, 10)
        return false
      }
      return true
    }

  }
}
</script>

<style scoped lang="scss">
.upload-file {
  margin: auto;
  .switch-start-filename {
    padding-bottom: 10px;
  }
  .file-info {
    padding: 10px 0px;
    .source-name {
      font-size: 12px;
      color: #999999;
    }
    .status {
      .msg {
        padding: 0rem .2rem;
      }
      i {
        font-size: 22px;
      }
    }
  }
}

</style>
