<template>
  <div class="app-container gold-analyze">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <account-input v-model="listQuery.userId" placeholder="用户ID" />
      </div>
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="注册开始日期"
          end-placeholder="注册结束日期"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-card v-loading="goldAnalyzeIncomePieChartLoading" style="margin-bottom: 10px;">
      <div id="goldAnalyzeIncomePieChart" ref="goldAnalyzeIncomePieChart" style="width: 100%;height: 600px;" />
    </el-card>
    <el-card v-loading="goldAnalyzeExpenditureieChartLoading">
      <div id="goldAnalyzeExpenditureieChart" ref="goldAnalyzeExpenditureieChart" style="width: 100%;height: 600px;" />
    </el-card>
  </div>
</template>
<script>
import { goldAnalyze } from '@/api/tools'
import { pickerOptions } from '@/constant/el-const'
import { mapGetters } from 'vuex'
export default {
  name: 'GoldAnalyze',
  data() {
    return {
      pickerOptions,
      rangeDate: '',
      listQuery: {
        userId: '',
        sysOrigin: '',
        startTime: '',
        endTime: ''
      },
      searchDisabled: false,
      goldAnalyzeIncomePieChart: null,
      goldAnalyzeExpenditureieChart: null,
      goldAnalyzeExpenditureieChartLoading: false,
      goldAnalyzeIncomePieChartLoading: false,
      color: ['#5470c6',
        '#91cc75',
        '#fac858',
        '#ee6666',
        '#73c0de',
        '#3ba272',
        '#fc8452',
        '#9a60b4',
        '#ea7ccc',
        '#459AF0',
        '#38C3B0',
        '#86CA5A',
        '#BFD44F',
        '#FCC248',
        '#FCE448',
        '#F58B41',
        '#F7765B',
        '#525ECD',
        '#547FDB',
        '#80FFA5',
        '#00DDFF',
        '#37A2FF',
        '#FF0087',
        '#FFBF00',
        '#65B581',
        '#FFCE34',
        '#FD665F']
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms']),
    subtext() {
      return this.listQuery.userId ? '用户：数据分配图' : '系统：数据分配图'
    }
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    }
  },
  mounted() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    this.goldAnalyzeIncomePieChart = this.$echarts.init(this.$refs.goldAnalyzeIncomePieChart)
    this.goldAnalyzeExpenditureieChart = this.$echarts.init(this.$refs.goldAnalyzeExpenditureieChart)
  },
  methods: {
    convertChartsData(arrays) {
      if (!arrays) {
        return []
      }
      return arrays.map(item => {
        item.name = item.originName
        return item
      })
    },
    loadGoldIncome() {
      const that = this
      that.goldAnalyzeIncomePieChartLoading = true
      const param = Object.assign({}, that.listQuery)
      param.type = 0
      goldAnalyze(param)
        .then(res => {
          that.goldAnalyzeIncomePieChartLoading = false
          that.goldAnalyzeIncomePieChart.setOption({
            color: that.color,
            title: {
              text: '金币收入',
              subtext: that.subtext,
              left: 'center'
            },
            tooltip: {
              trigger: 'item'
            },
            legend: {
              orient: 'vertical',
              left: 'left',
              textStyle: { color: '#8e929b' }
            },
            series: [
              {
                type: 'pie',
                radius: '50%',
                data: that.convertChartsData(res.body),
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                  }
                }
              }
            ]
          })
        }).catch(er => {
          that.goldAnalyzeIncomePieChartLoading = false
          console.error(er)
        })
    },
    loadGoldExpenditure() {
      const that = this
      that.goldAnalyzeExpenditureieChartLoading = true
      const param = Object.assign({}, that.listQuery)
      param.type = 1
      goldAnalyze(param)
        .then(res => {
          that.goldAnalyzeExpenditureieChartLoading = false
          that.goldAnalyzeExpenditureieChart.setOption({
            color: that.color,
            title: {
              text: '金币支出',
              subtext: that.subtext,
              left: 'center'
            },
            tooltip: {
              trigger: 'item'
            },
            legend: {
              orient: 'vertical',
              left: 'left',
              textStyle: { color: '#8e929b' }
            },
            series: [
              {
                type: 'pie',
                radius: '50%',
                data: that.convertChartsData(res.body),
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                  }
                }
              }
            ]
          })
        }).catch(er => {
          that.goldAnalyzeExpenditureieChartLoading = false
          console.error(er)
        })
    },
    handleSearch() {
      if (!this.rangeDate) {
        this.$opsMessage.warn('请选择时间')
        return
      }
      const time = this.listQuery.endTime - this.listQuery.startTime
      if (time > 2592000000) {
        this.$opsMessage.warn('时间必须在1个月以内')
        return
      }
      this.loadGoldIncome()
      this.loadGoldExpenditure()
    }
  }
}
</script>
<style scoped lang="scss">
</style>
