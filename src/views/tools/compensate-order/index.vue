<template>
  <div class="app-container">
    <div v-loading="loading" class="compensate-order">
      <el-alert
        title="补偿订单"
        type="info"
        :closable="false"
        style="margin-bottom: 20px;"
      />
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item prop="userId" label="用户ID">
          <el-input v-model="form.userId" v-number />
        </el-form-item>
        <el-form-item prop="orderId" label="订单id">
          <el-input v-model.trim="form.orderId" />
        </el-form-item>
        <el-form-item label="平台" prop="sysOrigin">
          <el-select v-model="form.sysOrigin" placeholder="请选择平台">
            <el-option
              v-for="item in permissionsSysOriginPlatforms"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="支付方式" prop="platform">
          <el-select v-model="form.platform" placeholder="请选择平台">
            <el-option
              v-for="item in payPlatforms"
              :key="item.value"
              :label="item.name"
              :value="item.value"
              :disabled="!(item.value === 'APPLE' || item.value === 'GOOGLE')"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="产品CODE" prop="productCode">
          <el-input v-model="form.productCode" />
        </el-form-item>
        <el-form-item label="过期时间" prop="vipExpireTime">
          <el-date-picker
            v-model="form.vipExpireTime"
            value-format="timestamp"
            type="date"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">提交</el-button>
        </el-form-item>
      </el-form>

      <el-alert
        title="补偿糖果"
        type="info"
        :closable="false"
        style="margin-bottom: 20px;"
      />
      <el-form ref="form_candy" :model="formCandy" :rules="rulesCnady" label-width="100px">
        <el-form-item prop="userId" label="用户ID">
          <el-input v-model="formCandy.userId" v-number />
        </el-form-item>
        <el-form-item prop="candy" label="糖果">
          <el-input v-model="formCandy.candy" v-number />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmitCandy">提交</el-button>
        </el-form-item>
      </el-form>

    </div>

  </div>
</template>
<script>
import { payPlatforms } from '@/constant/origin'
import { orderPurchaseHistoryCompensate, compensateCandy } from '@/api/purchase'
import { mapGetters } from 'vuex'
function newFrom() {
  return Object.assign({}, {
    userId: '',
    orderId: '',
    platform: '',
    productCode: '',
    vipExpireTime: '',
    sysOrigin: ''
  })
}
function newFormCandy() {
  return Object.assign({}, {
    userId: '',
    candy: ''
  })
}
export default {
  name: 'CompensateOrder',
  data() {
    const commonValid = [{ required: true, message: '必填字段不可为空', trigger: 'blur' }]
    return {
      loading: false,
      payPlatforms,
      form: newFrom(),
      formCandy: newFormCandy(),
      rules: {
        userId: commonValid,
        orderId: commonValid,
        platform: commonValid,
        productCode: commonValid,
        sysOrigin: commonValid
      },
      rulesCnady: {
        userId: commonValid,
        candy: commonValid
      },
      inputFixedNumber: 0
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  methods: {
    onSubmit() {
      const that = this
      that.$refs.form.validate((valid) => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.$confirm('是否确认提交?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          that.loading = true
          orderPurchaseHistoryCompensate(that.form).then(res => {
            that.loading = false
            that.$opsMessage.success()
            that.form = newFrom()
          }).catch(er => {
            that.loading = false
            console.error(er)
          })
        }).catch(() => { })
      })
    },
    onSubmitCandy() {
      const that = this
      that.$refs['form_candy'].validate((valid) => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.$confirm('是否确认提交?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          that.loading = true
          compensateCandy(that.formCandy).then(res => {
            that.loading = false
            that.$opsMessage.success()
            that.formCandy = newFormCandy()
          }).catch(er => {
            that.loading = false
            console.error(er)
          })
        }).catch(() => { })
      })
    }
  }
}
</script>
