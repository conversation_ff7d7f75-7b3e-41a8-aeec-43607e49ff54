<template>
  <div class="app-container match-analysis">
    <div class="filter-container">
      <div class="fast-tag">
        <el-tag type="info" class="cursor-pointer" @click="search('searchCheckInDaysCache')">打卡信息</el-tag>
        <el-tag type="info" class="cursor-pointer" @click="search('searchGiftConfigCache')">礼物配置列表</el-tag>

        <el-tag type="info" class="cursor-pointer" @click="search('searchAppEnumConfigCache')">
          枚举配置列表<i class="el-icon-arrow-down el-icon--right" />
        </el-tag>
        <el-dropdown>
          <el-tag type="info" class="cursor-pointer">
            产品配置列表<i class="el-icon-arrow-down el-icon--right" />
          </el-tag>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in sysOriginPlatforms"
              :key="item.value"
              @click.native="search('searchPlatformProductConfigCache',item.value)"
            >{{ item.label }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <el-divider content-position="left">BODY</el-divider>
    <div v-loading="searchLoading" class="redis-content">
      <div v-if="showData.ttl && showData.ttl > 0">TTL:{{ showData.ttl }}</div>
      <json-viewer :value="showData.data || '没有更多的信息'" :expand-depth="4" copyable />
    </div>
  </div>
</template>
<script>
import { getAppEnumConfigCache, getPlatformProductConfigCache, getGiftConfigCache, getCheckInDaysCache } from '@/api/tools'
import { sysOriginPlatforms } from '@/constant/origin'

export default {
  name: 'RedisTools',
  data() {
    return {
      sysOriginPlatforms,
      showData: { ttl: -2, data: '' },
      searchLoading: false,
      handlerEvent: {
        searchAppEnumConfigCache(that, param) {
          that.searchLoading = true
          getAppEnumConfigCache(param).then(res => {
            that.searchLoading = false
            that.showData = res.body
          }).catch(er => {
            that.searchLoading = false
          })
        },
        searchPlatformProductConfigCache(that, param) {
          that.searchLoading = true
          getPlatformProductConfigCache(param).then(res => {
            that.searchLoading = false
            that.showData = res.body
          }).catch(er => {
            that.searchLoading = false
          })
        },
        searchGiftConfigCache(that) {
          that.searchLoading = true
          getGiftConfigCache().then(res => {
            that.searchLoading = false
            that.showData = res.body
          }).catch(er => {
            that.searchLoading = false
          })
        },
        searchCheckInDaysCache(that) {
          that.$prompt('请输入用户ID', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputPattern: /\d+/,
            inputErrorMessage: '用户ID不正确'
          }).then(({ value }) => {
            that.searchLoading = true
            getCheckInDaysCache(value).then(res => {
              that.searchLoading = false
              that.showData = res.body
            }).catch(er => {
              that.searchLoading = false
            })
          }).catch(() => {})
        }
      }
    }
  },
  methods: {
    search(event, param) {
      const that = this
      that.showData = { ttl: -2, data: '' }
      that.handlerEvent[event](that, param)
    }
  }
}
</script>
