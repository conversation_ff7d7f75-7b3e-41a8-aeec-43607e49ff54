<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;">
            <sys-origin-icon
              :icon="item.value"
              :desc="item.value"
            /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <account-input
          v-model="listQuery.userId"
          :sys-origin="listQuery.sysOrigin"
          placeholder="用户ID"
        />
      </div>
      <el-input
        v-model.trim="listQuery.familyAccount"
        placeholder="工会账号"
        style="width: 200px;"
        class="filter-item"
      />
      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="sysOrigin" label="来源系统" align="center">
        <template slot-scope="scope">
          <sys-origin-icon
            :icon="scope.row.sysOrigin"
            :desc="scope.row.sysOrigin"
          />
        </template>
      </el-table-column>
      <el-table-column label="族长" align="center">
        <template slot-scope="scope">
          <el-link
            v-if="scope.row.userBaseInfo.id"
            @click="queryUserDetails(scope.row.userBaseInfo.id)"
          >
            {{ scope.row.userBaseInfo.userNickname }} /
            {{ scope.row.userBaseInfo.account }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="工会账号" prop="familyAccount" align="center" />
      <el-table-column label="工会头像" align="center" width="150">
        <template slot-scope="scope">
          <avatar :url="scope.row.familyAvatar" />
        </template>
      </el-table-column>
      <el-table-column label="工会长ID卡" align="center" width="120">
        <template slot-scope="scope">
          <div v-if="scope.row.leaderIdCardPhoto">
            <el-image
              :src="scope.row.leaderIdCardPhoto"
              :preview-src-list="[scope.row.leaderIdCardPhoto]"
              style="width: 80px; height: 50px; border-radius: 4px;"
              fit="cover"
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </div>
          <span v-else class="text-muted">暂无</span>
        </template>
      </el-table-column>
      <el-table-column label="工会名称" prop="familyName" align="center" />
      <el-table-column label="WhatsApp" align="center" width="150">
        <template slot-scope="scope">
          <div v-if="scope.row.familyWhatapp">
            <el-link
              :href="'https://wa.me/' + scope.row.familyWhatapp.replace(/[^0-9]/g, '')"
              target="_blank"
              type="success"
              :underline="false"
            >
              <i class="el-icon-message"></i>
              {{ scope.row.familyWhatapp }}
            </el-link>
          </div>
          <span v-else class="text-muted">暂无</span>
        </template>
      </el-table-column>
      <el-table-column label="成员数" prop="memberCount" align="center" />
      <el-table-column label="工会状态" align="center" width="100">
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.familyStatus === 'NORMAL' ? 'success' : 'danger'"
            size="small"
          >
            <span v-for="(item, index) in accountStatus" :key="index">
              <span v-if="item.value === scope.row.familyStatus">{{ item.name }}</span>
            </span>
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="工会等级" align="center">
        <template slot-scope="scope">
          <span v-for="(item, index) in familyLevelKeys" :key="index">
            <span v-if="item.value == scope.row.levelKey">{{ item.name }}</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="总贡献值" prop="familyExp" align="center" />
      <el-table-column
        prop="createTime"
        label="创建时间"
        width="200"
        align="center"
      >
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click.native="handleFamilyMember(scope.row)"
          >成员列表</el-button>
          <el-button
            type="text"
            @click.native="delFamilyData(scope.row)"
          >解散工会</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <user-deatils-drawer
      v-if="userDeatilsDrawerVisible"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawerVisible = false"
    />

    <family-member
      v-if="familyMemberDialogVisible"
      :family-id="thatSelectedFamilyId"
      @close="familyMemberDialogVisible = false"
    />
  </div>
</template>

<script>
import { pageFamily, delFamily } from '@/api/family'
import Pagination from '@/components/Pagination'
import { pickerOptions } from '@/constant/el-const'
import { familyLevelKeys } from '@/constant/family'
import { accountStatus } from '@/constant/user'
import FamilyMember from '@/views/family/list/family-member'
import { mapGetters } from 'vuex'

export default {
  name: 'FamilyList',
  components: { Pagination, FamilyMember },
  data() {
    return {
      searchDisabled: false,
      pickerOptions,
      userDeatilsDrawerVisible: false,
      thatSelectedUserId: '',
      familyMemberDialogVisible: false,
      thatSelectedFamilyId: '',
      familyLevelKeys,
      accountStatus,
      list: [],
      total: 0,
      rangeDate: [],
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: '',
        familyAccount: '',
        startTime: '',
        endTime: '',
        sysOrigin: ''
      },
      listLoading: true,
      clickUserId: ''
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    }
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      pageFamily(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    queryUserDetails(id) {
      this.userDeatilsDrawerVisible = true
      this.thatSelectedUserId = id
    },
    handleFamilyMember(row) {
      this.thatSelectedFamilyId = String(row.id)
      this.familyMemberDialogVisible = true
    },
    delFamilyData(item) {
      const that = this
      that
        .$confirm('【删除后不可复原】!! 确定删除该工会吗？', '提示', {
          type: 'warning'
        })
        .then(() => {
          that.$message({
            message: '删除中，请等待结果',
            type: 'warning'
          })
          that.listLoading = true
          delFamily(item.id)
            .then(res => {
              that.listLoading = false
              that.$message({
                message: '成功删除',
                type: 'success'
              })
              that.renderData()
            })
            .catch(er => {
              console.error(er)
              that.listLoading = false
            })
        })
        .catch(er => {
          console.error(er)
          that.listLoading = false
        })
    }
  }
}
</script>
<style scoped lang="scss">
.store-table-expand {
  font-size: 0;
  label {
    width: 90px;
    color: #99a9bf;
    .el-form-item {
      margin-right: 0;
      margin-bottom: 0;
      width: 50%;
    }
  }
}

.text-muted {
  color: #909399;
  font-size: 12px;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}
</style>
