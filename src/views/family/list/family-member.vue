<template>
  <el-dialog
    title="家族成员"
    :visible="true"
    :before-close="handleClose"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    width="80%"
  >
    <div class="app-container">
      <div class="filter-container">
        <div class="filter-item">
          <account-input v-model="listQuery.userId" placeholder="用户ID" />
        </div>
        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          :disabled="searchDisabled"
          @click="handleSearch"
        >
          搜索
        </el-button>
      </div>
      <el-table
        v-loading="listLoading"
        :data="list"
        :before-close="handleClose"
        element-loading-text="Loading"
        fit
        highlight-current-row
        max-height="350px"
      >
        <el-table-column label="头像" align="center" width="150">
          <template slot-scope="scope">
            <avatar :url="scope.row.userBaseInfo.userAvatar" :gender="scope.row.userSex" />
            <div
              style="width:100%;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;"
            >
              <gender :gender="scope.row.userBaseInfo.userSex" :gender-name="scope.row.userBaseInfo.userSexName" :desc="scope.row.userBaseInfo.account" />
              ({{ scope.row.userBaseInfo.accountStatusName }})
            </div>
          </template>
        </el-table-column>
        <el-table-column label="昵称" align="center">
          <template slot-scope="scope">
            <div
              style="width:100%;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;"
            >
              <el-link v-if="scope.row.userBaseInfo.id" @click="queryUserDetails(scope.row.userBaseInfo.id)"><a :title="scope.row.userBaseInfo.userNickname"> {{ scope.row.userBaseInfo.userNickname }} </a></el-link>
              <a v-else :title="scope.row.userBaseInfo.userNickname"> {{ scope.row.userBaseInfo.userNickname }} </a>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="角色" align="center">
          <template slot-scope="scope">
            <span v-for="(item, index) in familyRoles" :key="index">
              <span v-if="item.value == scope.row.memberRole">{{ item.name }}</span>
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="加入家族时间" align="center">
          <template slot-scope="scope">
            {{ scope.row.createTime | dateFormat }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" align="center" width="120">
          <template slot-scope="scope">
            <el-button type="text" @click.native="removeMember(scope.row)">移除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="listQuery.cursor"
        :limit.sync="listQuery.limit"
        @pagination="renderData"
      />

      <user-deatils-drawer
        v-if="userDeatilsDrawerVisible"
        :user-id="thatSelectedUserId"
        @close="userDeatilsDrawerVisible=false"
      />
    </div>
  </el-dialog>
</template>

<script>
import { pageFamilyMember, removeFamilyMember } from '@/api/family'
import Pagination from '@/components/Pagination'
import { familyRoles } from '@/constant/family'
export default {
  name: 'FamilyMember',
  components: { Pagination },
  props: {
    familyId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: '',
        familyId: ''
      },
      listLoading: false,
      searchDisabled: false,
      userDeatilsDrawerVisible: false,
      thatSelectedUserId: '',
      familyRoles
    }
  },
  watch: {
    familyId: {
      handler(newVal) {
        this.listQuery.familyId = newVal
        this.renderData()
      },
      immediate: true
    }
  },
  methods: {
    renderData() {
      const that = this
      if (!that.listQuery.familyId) {
        return
      }
      that.listLoading = true
      pageFamilyMember(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleClose() {
      this.$emit('close')
    },
    handleSearch() {
      this.renderData(true)
    },
    queryUserDetails(id) {
      this.userDeatilsDrawerVisible = true
      this.thatSelectedUserId = id
    },
    removeMember(item) {
      const that = this
      that.$confirm('确定移除该成员吗？', '提示', {
        type: 'warning'
      }).then(() => {
        that.$message({
          message: '处理中，请等待结果',
          type: 'warning'
        })
        that.listLoading = true
        removeFamilyMember(item.familyId, item.id).then((res) => {
          that.listLoading = false
          that.$message({
            message: '成功移除',
            type: 'success'
          })
          that.renderData()
        }).catch(er => {
          console.error(er)
          that.listLoading = false
        })
      }).catch(er => {
        console.error(er)
        that.listLoading = false
      })
    }
  }
}
</script>
