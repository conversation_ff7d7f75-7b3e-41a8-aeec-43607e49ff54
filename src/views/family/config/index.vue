<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="item in tables" :key="item.name" :label="item.title" :name="item.component" />
      <component :is="activeName" />
    </el-tabs>
  </div>
</template>
<script>
import ConfigLevel from './config-level'
import ConfigCreateRole from './config-create-role'
import ConfigRewardRole from './config-weekly-reward'
export default {
  name: 'FamilyConfig',
  components: { ConfigLevel, ConfigCreateRole, ConfigRewardRole },
  data() {
    return {
      activeName: 'ConfigLevel',
      tables: [
        {
          title: '工会等级配置',
          component: 'ConfigLevel'
        }, {
          title: '创建工会规则',
          component: 'ConfigCreateRole'
        }, {
          title: '创建每周奖励规则',
          component: 'ConfigRewardRole'
        }
      ]
    }
  }
}
</script>
