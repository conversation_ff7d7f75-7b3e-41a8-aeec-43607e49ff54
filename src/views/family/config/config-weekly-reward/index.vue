<template>
  <div>

    <div class="filter-container">
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        添加
      </el-button>
    </div>
    <el-alert
      title="注意"
      type="warning"
      :closable="false"
    >
      一个系统只能有三条工会每周奖励规则!
    </el-alert>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="sysOrigin" label="系统" align="center">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column prop="goalExp" label="任务所需贡献值" align="center" />
      <el-table-column prop="rewardQuantity" label="奖励糖果数" align="center" />
      <el-table-column prop="sort" label="顺序" align="center" />
      <el-table-column fixed="right" label="操作" align="center">
        <template slot-scope="scope">
          <div>
            <el-button type="text" @click.native="handlUpdate(scope.row)">修改</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <el-dialog
      :title="textOptTitle"
      :visible.sync="formVisible"
      :before-close="handleClose"
      :close-on-click-modal="false"
      width="400px"
    >
      <div v-loading="submitLoading">
        <el-form ref="form" :model="form" label-width="110px" :rules="rules">
          <el-form-item
            v-if="form.id === ''"
            label="系统"
            prop="sysOrigin"
          >
            <el-select
              v-model="form.sysOrigin"
              placeholder="系统"
              style="width:100%;"
              class="filter-item"
            >
              <el-option
                v-for="(item, index) in permissionsSysOriginPlatforms"
                :key="index"
                :label="item.label"
                :value="item.value"
                class="filter-item"
              >
                <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
                <span style="float: left;margin-left:10px">{{ item.label }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="任务贡献值" prop="goalExp">
            <el-input v-model.trim="form.goalExp" type="number" />
          </el-form-item>
          <el-form-item label="奖励糖果数" prop="rewardQuantity">
            <el-input v-model.trim="form.rewardQuantity" type="number" />
          </el-form-item>
          <el-form-item label="顺序" prop="sort">
            <el-input v-model.trim="form.sort" placeholder="数字越小奖励越少" type="number" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm()">保存</el-button>
            <el-button @click="handleClose()">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { pageFamilyRewardRule, addOrUpdateFamilyRewardRule } from '@/api/family'
import { mapGetters } from 'vuex'

function getFormData() {
  return {
    sysOrigin: '',
    goalExp: 0,
    rewardQuantity: 0,
    description: '',
    sort: '',
    id: ''
  }
}
export default {
  name: 'ConfigRewardRole',
  components: {
    Pagination
  },
  data() {
    return {
      thisRow: {},
      formVisible: false,
      textOptTitle: '',
      form: getFormData(),
      submitLoading: false,
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        sysOrigin: ''
      },
      listLoading: true,
      searchLoading: false,
      rules: {
        sysOrigin: [
          { required: true, message: '请选择系统', trigger: 'blur' }
        ],
        rewardQuantity: [
          { required: true, message: '请输入奖励糖果数', trigger: 'blur' }
        ],
        goalExp: [
          { required: true, message: '请输入任务贡献值', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '请输入奖励顺序', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    this.renderData(true)
  },
  methods: {
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      pageFamilyRewardRule(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.searchLoading = that.listLoading = false
      }).catch(er => {
        that.searchLoading = that.listLoading = false
      })
    },
    handleSearch() {
      this.searchLoading = true
      this.renderData(true)
    },
    handleCreate() {
      this.textOptTitle = '新增工会奖励规则'
      this.formVisible = true
      this.form = getFormData()
    },
    handlUpdate(row) {
      this.textOptTitle = '修改工会奖励规则'
      this.formVisible = true
      this.form = Object.assign(this.form, row)
    },
    handleClose() {
      this.formVisible = false
      this.resetForm()
    },
    resetForm() {
      this.form = getFormData()
    },
    querySearch(queryString, cb) {
      var results = queryString ? this.restaurants.filter(this.createFilter(queryString)) : this.restaurants
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0) || (restaurant.name.toLowerCase().indexOf(queryString.toLowerCase()) === 0)
      }
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (valid) {
          that.submitLoading = true
          addOrUpdateFamilyRewardRule(that.form).then(res => {
            that.$opsMessage.success()
            that.submitLoading = false
            that.formVisible = false
            that.resetForm()
            that.renderData(true)
          }).catch(er => {
            that.submitLoading = false
            console.error(er)
            this.$emit('fail')
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    },
    renderDataSuccess() {
      this.$message({
        message: 'Successful',
        type: 'success'
      })
      this.renderData()
    }
  }
}
</script>
<style scoped lang="scss">
.this-level {
  padding-bottom: 30px;
  display: flex;
  > div {
    width: 150px;
  }
}

</style>
