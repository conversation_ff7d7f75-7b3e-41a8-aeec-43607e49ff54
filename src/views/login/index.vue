<template>
  <div class="login-container">
    <div class="login-box">
      <!-- 背景装饰元素 -->
      <div class="bg-decoration">
        <div class="circle circle-1"></div>
        <div class="circle circle-2"></div>
        <div class="circle circle-3"></div>
      </div>

      <el-row class="login-row">
        <el-col :md="15" :lg="17" class="left-desc icon hidden-xs-only hidden-sm-only flex-c">
          <div class="left-content">
            <div class="logo-section">
              <img src="@/assets/logo" alt="<PERSON><PERSON> Logo" class="main-logo">
              <h1 class="brand-title"><PERSON><PERSON></h1>
              <p class="brand-subtitle">Group voice chat platform</p>
            </div>
            <div class="feature-list">
              <div class="feature-item">
                <div class="feature-icon">🎙️</div>
                <span>High-quality voice chat</span>
              </div>
              <div class="feature-item">
                <div class="feature-icon">👥</div>
                <span>Group conversations</span>
              </div>
              <div class="feature-item">
                <div class="feature-icon">🌐</div>
                <span>Global community</span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :md="9" :lg="7" :sm="24" class="right-login flex-c">
          <div class="login-form-box">
            <div class="title-container">
              <div class="logo-mini">
                <img src="@/assets/logo_new.svg" alt="Marcie" class="mini-logo">
              </div>
              <h3 class="title">Welcome Back</h3>
              <p class="subtitle">Sign in to your admin account</p>
            </div>
            <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" auto-complete="on" label-position="left">
              <el-form-item prop="username">
                <span class="svg-container">
                  <svg-icon icon-class="user" />
                </span>
                <el-input
                  ref="username"
                  v-model.trim="loginForm.username"
                  placeholder="Username"
                  name="username"
                  type="text"
                  tabindex="1"
                  auto-complete="off"
                />
              </el-form-item>

              <el-form-item prop="password">
                <span class="svg-container">
                  <svg-icon icon-class="password" />
                </span>
                <el-input
                  :key="passwordType"
                  ref="password"
                  v-model="loginForm.password"
                  :type="passwordType"
                  placeholder="Password"
                  name="password"
                  tabindex="2"
                  auto-complete="off"
                  @keyup.enter.native="handleLogin"
                />
                <span class="show-pwd" @click="showPwd">
                  <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
                </span>
              </el-form-item>

              <el-button
                :loading="loading"
                :disabled="loading"
                type="primary"
                class="login-btn"
                @click.native.prevent="handleLogin"
              >Sign In</el-button>
            </el-form>

            <div class="tips">© 2025 Marcie App. All rights reserved.</div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
// import SwitchDark from '@/components/SwitchDark'
import { validUsername } from '@/utils/validate'

export default {
  name: 'Login',
  // components: { SwitchDark },
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!validUsername(value)) {
        callback(new Error('Please enter the correct user name'))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (value.length < 4) {
        callback(new Error('The password can not be less than 4 digits'))
      } else {
        callback()
      }
    }
    return {
      loginForm: {
        username: '',
        password: ''
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur', validator: validateUsername }],
        password: [{ required: true, trigger: 'blur', validator: validatePassword }]
      },
      loading: false,
      passwordType: 'password',
      redirect: undefined
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  methods: {
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      const that = this
      that.$refs.loginForm.validate(valid => {
        if (valid) {
          that.loading = true
          that.$store.dispatch('user/login', that.loginForm).then((res) => {
            that.$router.push({ path: that.redirect || '/' })
          }).catch(() => {
            that.loading = false
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg:#283443;
$light_gray:none;
$cursor: none;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

@media (max-width: 992px)  {
  .login-container {
    background-position: 0% !important;
  }
}

/* reset element-ui css */
.login-container {
  .el-input {
    display: inline-block;
    height: 47px;
    width: 85%;
    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: $light_gray;
      height: 47px;
      caret-color: $cursor;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px #e2e2e2 inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    color: #454545;
  }
}
</style>

<style lang="scss" scoped>
$primary-purple: #8B5CF6;
$secondary-purple: #A855F7;
$dark-purple: #7C3AED;
$light-purple: #C4B5FD;
$bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$dark_gray: #6B7280;
$light_gray: #F9FAFB;
$white: #FFFFFF;

.login-container {
  height: 100%;
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #8B5CF6 100%);
  overflow: hidden;
  position: relative;

  // 背景装饰元素
  .bg-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;

    .circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      animation: float 6s ease-in-out infinite;

      &.circle-1 {
        width: 200px;
        height: 200px;
        top: 10%;
        left: 10%;
        animation-delay: 0s;
      }

      &.circle-2 {
        width: 150px;
        height: 150px;
        top: 60%;
        right: 15%;
        animation-delay: 2s;
      }

      &.circle-3 {
        width: 100px;
        height: 100px;
        bottom: 20%;
        left: 20%;
        animation-delay: 4s;
      }
    }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
  }

  .login-box {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 2;

    .login-row {
      height: 100%;

      .left-desc {
        height: 100%;
        padding: 60px 40px;

        .left-content {
          max-width: 500px;
          color: $white;

          .logo-section {
            text-align: center;
            margin-bottom: 60px;

            .main-logo {
              width: 120px;
              height: 120px;
              margin-bottom: 20px;
              border-radius: 20px;
              box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            }

            .brand-title {
              font-size: 48px;
              font-weight: 700;
              margin: 0 0 10px 0;
              background: linear-gradient(45deg, $white, $light-purple);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
            }

            .brand-subtitle {
              font-size: 18px;
              opacity: 0.9;
              margin: 0;
            }
          }

          .feature-list {
            .feature-item {
              display: flex;
              align-items: center;
              margin-bottom: 20px;
              padding: 15px 20px;
              background: rgba(255, 255, 255, 0.1);
              border-radius: 12px;
              backdrop-filter: blur(10px);
              border: 1px solid rgba(255, 255, 255, 0.2);

              .feature-icon {
                font-size: 24px;
                margin-right: 15px;
                width: 40px;
                text-align: center;
              }

              span {
                font-size: 16px;
                font-weight: 500;
              }
            }
          }
        }
      }

      .right-login {
        height: 100%;
        position: relative;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-left: 1px solid rgba(255, 255, 255, 0.2);

        .login-form-box {
          width: 100%;
          padding: 40px;

          .title-container {
            text-align: center;
            margin-bottom: 40px;

            .logo-mini {
              margin-bottom: 20px;

              .mini-logo {
                width: 60px;
                height: 60px;
                border-radius: 12px;
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
              }
            }

            .title {
              font-size: 28px;
              font-weight: 700;
              color: #1F2937;
              margin: 0 0 8px 0;
            }

            .subtitle {
              font-size: 16px;
              color: $dark_gray;
              margin: 0;
            }
          }

          .login-form {
            position: relative;
            width: 100%;
            max-width: 400px;
            margin: 0 auto;

            .login-btn {
              width: 100%;
              height: 50px;
              background: linear-gradient(135deg, $primary-purple, $secondary-purple);
              border: none;
              border-radius: 12px;
              font-size: 16px;
              font-weight: 600;
              transition: all 0.3s ease;

              &:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
              }

              &:active {
                transform: translateY(0);
              }
            }
          }
        }
      }
    }
  }
  .tips {
    font-size: 12px;
    color: $dark_gray;
    text-align: center;
    margin-top: 30px;
    opacity: 0.8;
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $primary-purple;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .show-pwd {
    position: absolute;
    right: 15px;
    top: 12px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
    transition: color 0.3s ease;

    &:hover {
      color: $primary-purple;
    }
  }
}

// 响应式设计
@media (max-width: 992px) {
  .login-container {
    .login-box {
      .login-row {
        .right-login {
          .login-form-box {
            padding: 30px 20px;

            .title-container {
              .title {
                font-size: 24px;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .login-container {
    .bg-decoration {
      .circle {
        &.circle-1 {
          width: 120px;
          height: 120px;
        }

        &.circle-2 {
          width: 80px;
          height: 80px;
        }

        &.circle-3 {
          width: 60px;
          height: 60px;
        }
      }
    }
  }
}
</style>
