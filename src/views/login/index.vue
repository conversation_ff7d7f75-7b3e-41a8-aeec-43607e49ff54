<template>
  <div class="login-container">
    <div class="login-box">
      <!-- <div class="switch-dark">
        <switch-dark />
      </div> -->

      <el-row class="login-row">
        <el-col :md="15" :lg="17" class="left-desc icon hidden-xs-only hidden-sm-only flex-c">
          <div class="left-icon">
            <img src="@/assets/login-left.png" alt="">
          </div>
        </el-col>
        <el-col :md="9" :lg="7" :sm="24" class="right-login flex-c">
          <div class="login-form-box">
            <div class="title-container">
              <h3 class="title">Login</h3>
            </div>
            <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" auto-complete="on" label-position="left">
              <el-form-item prop="username">
                <span class="svg-container">
                  <svg-icon icon-class="user" />
                </span>
                <el-input
                  ref="username"
                  v-model.trim="loginForm.username"
                  placeholder="Username"
                  name="username"
                  type="text"
                  tabindex="1"
                  auto-complete="off"
                />
              </el-form-item>

              <el-form-item prop="password">
                <span class="svg-container">
                  <svg-icon icon-class="password" />
                </span>
                <el-input
                  :key="passwordType"
                  ref="password"
                  v-model="loginForm.password"
                  :type="passwordType"
                  placeholder="Password"
                  name="password"
                  tabindex="2"
                  auto-complete="off"
                  @keyup.enter.native="handleLogin"
                />
                <span class="show-pwd" @click="showPwd">
                  <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
                </span>
              </el-form-item>

              <el-button
                :loading="loading"
                :disabled="loading"
                type="primary"
                style="width:100%;"
                @click.native.prevent="handleLogin"
              >Login</el-button>
            </el-form>

            <div class="tips">Shenzhen Chuan yin Technology Co., Ltd.</div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
// import SwitchDark from '@/components/SwitchDark'
import { validUsername } from '@/utils/validate'

export default {
  name: 'Login',
  // components: { SwitchDark },
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!validUsername(value)) {
        callback(new Error('Please enter the correct user name'))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (value.length < 4) {
        callback(new Error('The password can not be less than 4 digits'))
      } else {
        callback()
      }
    }
    return {
      loginForm: {
        username: '',
        password: ''
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur', validator: validateUsername }],
        password: [{ required: true, trigger: 'blur', validator: validatePassword }]
      },
      loading: false,
      passwordType: 'password',
      redirect: undefined
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  methods: {
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      const that = this
      that.$refs.loginForm.validate(valid => {
        if (valid) {
          that.loading = true
          that.$store.dispatch('user/login', that.loginForm).then((res) => {
            that.$router.push({ path: that.redirect || '/' })
          }).catch(() => {
            that.loading = false
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg:#283443;
$light_gray:none;
$cursor: none;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

@media (max-width: 992px)  {
  .login-container {
    background-position: 0% !important;
  }
}

/* reset element-ui css */
.login-container {
  .el-input {
    display: inline-block;
    height: 47px;
    width: 85%;
    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: $light_gray;
      height: 47px;
      caret-color: $cursor;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px #e2e2e2 inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    color: #454545;
  }
}
</style>

<style lang="scss" scoped>
$bg:#eee;
$dark_gray:#889aa4;
$light_gray:none;

.login-container {
  height: 100%;
  width: 100%;
  background-color: $bg;
  overflow: hidden;
  background-image: url('./../../assets/login_back.svg');
  background-position: 50%;
  background-size: cover;
  // background-repeat: no-repeat;
  // background-position: center 110px;
  // background-size: 100%;
  .el-button {
    padding: 15px 20px !important;
  }
  .login-box {
    position: relative;
    width: 100%;
    height: 100%;
    background-color: #fffc;

    .switch-dark {
      position: absolute;
      right: 20px;
      top: 20px;
    }
    .login-row {
      height: 100%;
      .left-icon {
        max-width: 800px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .left-desc {
        height: 100%;
      }
      .right-login {
        height: 100%;
        position: relative;
        box-shadow: 2px 3px 7px #0003;
        .login-form-box {
          width: 100%;
          .login-form {
            position: relative;
            width: 100%;
            padding: 50px 40px;
            margin: 0 auto;
            overflow: hidden;
            border-radius: 10px;
            // box-shadow: 2px 3px 7px #0003;
          }
        }

      }
    }
  }
  .tips {
    font-size: 14px;
    color: $light_gray;
    margin-bottom: 10px;
    position: absolute;
    bottom: 0px;
    text-align: center;
    width: 100%;
    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;

    .title {
      font-size: 26px;
      color: $light_gray;
      margin: 0px 40px;
      // text-align: center;
      font-weight: bold;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }
}
</style>
