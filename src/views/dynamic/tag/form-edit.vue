<template>
  <div class="banner-form-edite">
    <el-dialog
      :title="textOptTitle"
      :visible="true"
      :before-close="handleClose"
      :close-on-click-modal="false"
      width="550px"
      top="50px"
    >
      <div v-loading="submitLoading">
        <div class="form-contetn">
          <el-form ref="form" :model="form" :rules="rules" label-width="110px" style="margin-right: 50px;">
            <el-form-item label="背景" prop="imageUrl">
              <el-form-item prop="cover" class="upload-small">
                <el-upload
                  :disabled="coverUploadLoading"
                  :file-list="coverFileList"
                  :class="{'upload-but-hide': !isShowCoverUpload}"
                  action=""
                  list-type="picture-card"
                  :http-request="uploadCover"
                  :show-file-list="!isShowCoverUpload"
                  :on-remove="handleCoverFileRemove"
                  accept="image/*"
                >
                  <i slot="default" v-loading="coverUploadLoading" class="el-icon-plus" />
                </el-upload>
              </el-form-item>
            </el-form-item>
            <el-form-item label="系统" prop="sysOrigin">
              <el-select
                v-model="form.sysOrigin"
                placeholder="系统"
                style="width:100%;"
                class="filter-item"
              >
                <el-option
                  v-for="item in sysOriginPlatforms"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
                  <span style="float: left;margin-left:10px">{{ item.label }}</span>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="标签" prop="tag">
              <el-input v-model.trim="form.tag" type="text" />
            </el-form-item>
            <el-form-item label="描述" prop="description">
              <el-input v-model.trim="form.description" type="text" />
            </el-form-item>
            <el-form-item label="语言" prop="languageType">
              <el-select
                v-model="form.languageType"
                placeholder="语言"
                style="width:100%;"
                class="filter-item"
              >
                <el-option
                  v-for="item in specifyLanguage"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="状态" prop="del">
              <el-select
                v-model="form.del"
                placeholder="状态"
                clearable
                style="width:100%;"
                class="filter-item"
              >
                <el-option label="下架" :value="true" />
                <el-option label="上架" :value="false" />
              </el-select>
            </el-form-item>
            <el-form-item label="排序" prop="depict">
              <el-input v-model.trim="form.sort" v-number placeholder="降序排列(数字越大越靠前)" />
            </el-form-item>
          </el-form>
        </div>
        <div slot="footer" style="text-align: right;">
          <el-button @click="handleClose()">取消</el-button>
          <el-button type="primary" @click="submitForm()">保存</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>

import { addOrUpdate } from '@/api/dynamic'
import { sysOriginPlatforms } from '@/constant/origin'
import { getElementUiUploadFile } from '@/utils'
import { specifyLanguage } from '@/constant/type'
export default {
  props: {
    row: {
      type: Object,
      default: null
    }
  },
  data() {
    const commonRules = [{ required: true, message: '必填字段', trigger: 'blur' }]
    return {
      coverUploadLoading: false,
      coverFileList: [],
      sysOriginPlatforms,
      specifyLanguage,
      form: {
        id: '',
        imageUrl: '',
        sysOrigin: '',
        tag: '',
        description: '',
        del: '',
        sort: '',
        languageType: ''
      },
      submitLoading: false,
      uploadLoading: false,
      rules: {
        imageUrl: commonRules,
        tag: commonRules,
        description: commonRules,
        sysOrigin: commonRules,
        sort: commonRules,
        languageType: commonRules
      }
    }
  },
  computed: {
    textOptTitle() {
      return this.row && this.row.id ? '修改' : '新增'
    },
    isShowCoverUpload() {
      return !this.form.imageUrl
    }
  },
  watch: {
    row: {
      handler(val) {
        if (!val) {
          return
        }
        const form = Object.assign({}, val)
        this.coverFileList = getElementUiUploadFile(form.imageUrl)
        this.form = Object.assign(this.form, form)
      },
      immediate: true
    }
  },
  methods: {
    handlealertCoverFileRemove(file, fileList) {
      this.form.imageUrl = ''
      this.alertCoverUploadLoading = false
    },
    uploadCover(file) {
      const that = this
      that.coverUploadLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.other).then(res => {
        that.coverUploadLoading = false
        that.form.imageUrl = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.coverUploadLoading = false
      })
    },
    handleCoverFileRemove(file, fileList) {
      this.form.imageUrl = ''
      this.coverUploadLoading = false
    },
    handleClose() {
      this.$emit('close')
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return
        }
        const submitForm = Object.assign({}, that.form)
        that.submitLoading = true
        addOrUpdate(submitForm).then(res => {
          that.submitLoading = false
          that.$emit('success', res)
        }).catch(er => {
          that.submitLoading = false
          that.$emit('fial', er)
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.form-contetn {
    max-height: 550px;
    overflow: auto;
}
.form-autocomplete {
   li {
    line-height: normal;
    padding: 7px;

    .name {
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .label {
      font-size: 12px;
      color: #b4b4b4;
    }

    .highlighted .label {
      color: #ddd;
    }
  }
}
</style>
