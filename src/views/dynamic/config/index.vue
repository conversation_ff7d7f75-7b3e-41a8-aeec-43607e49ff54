<template>
  <div class="push-form">
    <el-form ref="form" :model="form" label-width="100px">
      <el-alert
        type="info"
        :closable="false"
      >
        <span>热门动态权重计分设置</span>
      </el-alert>
      <el-form-item prop="likeScore" label="点赞增加分数">
        <el-input v-model="form.likeScore" type="number" />
      </el-form-item>
      <el-form-item prop="commentScore" label="评论增加分数">
        <el-input v-model="form.commentScore" type="number" />
      </el-form-item>
      <el-alert
        type="info"
        :closable="false"
      >
        <span>财富、魅力等级多少级才能发布动态</span>
      </el-alert>
      <el-form-item prop="levelLimit" label="等级限制">
        <el-input v-model="form.levelLimit" type="number" />
      </el-form-item>
      <el-alert
        type="info"
        :closable="false"
      >
        <span>每个用户当天允许发送多少条动态</span>
      </el-alert>
      <el-form-item prop="quantityLimit" label="动态数量">
        <el-input v-model="form.quantityLimit" type="number" />
      </el-form-item>
      <el-alert
        type="info"
        :closable="false"
      >
        <span>用户当天发送动态数量达到限制,继续发送则每条动态支付多少金币？</span>
      </el-alert>
      <el-form-item prop="dynamicFees" label="金币数">
        <el-input v-model="form.dynamicFees" type="number" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">修改</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { getPopularConfig, savePopularConfig } from '@/api/dynamic'

export default {
  name: 'DynamicPopularConfig',
  data() {
    return {
      form: {
        commentScore: '',
        likeScore: '',
        levelLimit: '',
        quantityLimit: '',
        dynamicFees: ''
      }
    }
  },
  created() {
    const that = this
    that.renderData()
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      getPopularConfig().then(res => {
        const { body } = res
        that.form = body
        that.listLoading = false
      })
    },
    onSubmit() {
      const that = this
      that.$refs.form.validate((valid) => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.$confirm('是否确定修改配置值?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          savePopularConfig(that.form).then(res => {
            that.$message({
              type: 'success',
              message: 'Successful'
            })
            that.renderData()
          }).catch(er => {
            that.$opsMessage.success()
          })
        }).catch(() => {
          that.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">
  .msg-text {
    color: #54c988;
    background-color: #f6f5f5;
  }
</style>
