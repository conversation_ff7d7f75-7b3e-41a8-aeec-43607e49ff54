<template>

  <el-popover
    placement="bottom"
    title="选中道具"
    width="500"
    trigger="click"
  >
    <div class="select-props">
      <div v-for="item in 50" :key="item" class="item" :class="{'select-item': item === 1}">
        <div class="img">
          <div class="svgaplayer-preview">
            <svgaplayer
              type="popover"
              :url="'http://dev.img.sugartimeapp.com/other/manager-186bfa08-9f81-47e6-8864-9a206e955c3c.svga'"
              icon-size="28px"
            />
          </div>
          <el-image
            style="width:100%;height:100%;"
            :src="'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'"
            :lazy="item > 20"
          />
        </div>
        <div class="name nowrap-ellipsis">
          <a title="1232">asdqweqweqweasdqweqweqweasdqweqweqwe</a>
        </div>
      </div>
    </div>
    <div class="opetion">
      <el-form ref="form" :model="form" :rules="formRules" label-width="110px" style="margin:30px; 50px 0px 0px">
        <el-form-item label="类型名称" prop="name">
          <el-input v-model.trim="form.name" type="text" />
        </el-form-item>
      </el-form>
    </div>
    <el-button slot="reference">click</el-button>
  </el-popover>

</template>
<script>
export default {
  name: 'SearchUserInput',
  data() {
    return {
      form: false,
      formRules: []
    }
  },
  methods: {

  }
}
</script>
<style scoped lang="scss">
.select-props {
  max-height: 300px;
  overflow: auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  .item {
    padding: 5px;
    position: relative;
    width: 110px;
    .img {
      width: 100px;
      height: 100px;
      cursor: pointer;
    }
    .svgaplayer-preview {
      position: absolute;
      z-index: 999;
      top: 0px;
      right: 10px;
    }
  }
  .select-item {
    .img {
     border: 5px solid #ff4949;
    }
  }
}
</style>
