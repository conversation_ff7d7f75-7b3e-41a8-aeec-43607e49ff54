<template>
  <div class="app-container">

    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="归属系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in permissionsSysOriginPlatforms"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.propsType"
        placeholder="道具类型"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in propsCommodityTypes"
          :key="index"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="listQuery.shelfStatus"
        placeholder="上/下架"
        clearable
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option v-for="item in showcaseTypes" :key="item.value" :label="item.name" :value="item.value" />
      </el-select>

      <el-select
        v-model="listQuery.currencyTypes"
        placeholder="付费类型"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option v-for="item in propsCurrencyType" :key="item.value" :label="item.name" :value="item.value" />
      </el-select>

      <el-input
        v-model.trim="listQuery.id"
        v-number
        placeholder="商品ID"
        clearable
        style="width: 200px;"
        class="filter-item"
      />

      <el-input
        v-model.trim="listQuery.sourceId"
        v-number
        placeholder="资源ID"
        clearable
        style="width: 200px;"
        class="filter-item"
      />

      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        添加
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
      @cell-mouse-enter="handleMouseEnter"
    >
      <el-table-column type="expand">
        <template slot-scope="scope">
          <h2>资源信息</h2>
          <el-form v-if="scope.row.propsSource" label-position="left" class="store-table-expand" style="margin-left:20px;">
            <el-form-item>
              <el-col :span="12">
                <el-form-item label="ID">
                  <span>{{ scope.row.propsSource.id }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="编号">
                  <span>{{ scope.row.propsSource.code }}</span>
                </el-form-item>
              </el-col>
            </el-form-item>
            <el-form-item>
              <el-col :span="12">
                <el-form-item label="名称">
                  <span>{{ scope.row.propsSource.name }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="低价">
                  <span>{{ scope.row.propsSource.amount }}</span>
                </el-form-item>
              </el-col>
            </el-form-item>
            <el-form-item v-if="scope.row.commodity.propsType !== 'NOBLE_VIP'">
              <el-col :span="12">
                <el-form-item label="上/下架状态">
                  <span>{{ scope.row.propsSource.del ? '下架' : '上架' }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="低价">
                  <el-tag>{{ scope.row.salePrice }}</el-tag>
                </el-form-item>
              </el-col>
            </el-form-item>
            <el-form-item v-if="scope.row.commodity.propsType === 'NOBLE_VIP'">
              <el-col :span="12">
                <el-form-item label="上/下架状态">
                  <span>{{ scope.row.propsSource.del ? '下架' : '上架' }}</span>
                </el-form-item>
              </el-col>
            </el-form-item>
            <el-form-item v-if="scope.row.commodity.propsType === 'NOBLE_VIP'">
              <el-col :span="12">
                <el-form-item label="原低价">
                  <el-tag>{{ scope.row.salePrice }}</el-tag>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="折后低价">
                  <el-tag>{{ scope.row.saleDiscountPrice }}</el-tag>
                </el-form-item>
              </el-col>
            </el-form-item>
          </el-form>
          <div v-if="scope.row.commodity.propsType === 'NOBLE_VIP'">
            <h2>贵族能力</h2>
            <el-form v-if="scope.row.propsAbility && scope.row.propsAbility.nobleVipAbility" label-position="left" class="store-table-expand" style="margin-left:20px;">
              <el-form-item>
                <el-col :span="12">
                  <el-form-item label="ID">
                    <span>{{ scope.row.propsAbility.nobleVipAbility.id }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="VIP类型">
                    <span>{{ scope.row.propsAbility.nobleVipAbility.vipType }}</span>
                  </el-form-item>
                </el-col>
              </el-form-item>
              <el-form-item>
                <el-col :span="12">
                  <el-form-item label="VIP等级">
                    <span>{{ scope.row.propsAbility.nobleVipAbility.vipLevel }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="成员数量">
                    <span>{{ scope.row.propsAbility.nobleVipAbility.roomMaxMember }}</span>
                  </el-form-item>
                </el-col>
              </el-form-item>
              <el-form-item>
                <el-col :span="12">
                  <el-form-item label="登录奖励数">
                    <span>{{ scope.row.propsAbility.nobleVipAbility.loginRewardsAmount }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="道具">

                    <div class="vip-props flex-l">

                      <div v-if="scope.row.propsAbility.avatarFrame" class="props">
                        <div class="preview-img">
                          <el-image
                            style="width: 100%; height: 100%"
                            :src="scope.row.propsAbility.avatarFrame.cover"
                            :preview-src-list="[scope.row.propsAbility.avatarFrame.cover]"
                          />
                          <div class="preview-svga">
                            <svgaplayer
                              type="popover"
                              :url="scope.row.propsAbility.avatarFrame.sourceUrl"
                            />
                          </div>
                          <div class="props-lable">头像框</div>
                        </div>
                      </div>

                      <div v-if="scope.row.propsAbility.car" class="props">
                        <div class="preview-img">
                          <el-image
                            style="width: 100%; height: 100%"
                            :src="scope.row.propsAbility.car.cover"
                            :preview-src-list="[scope.row.propsAbility.car.cover]"
                          />
                          <div class="preview-svga">
                            <svgaplayer
                              type="popover"
                              :url="scope.row.propsAbility.car.sourceUrl"
                            />
                          </div>
                          <div class="props-lable">座驾</div>
                        </div>
                      </div>

                      <div v-if="scope.row.propsAbility.chatBubble" class="props">
                        <div class="preview-img">
                          <el-image
                            style="width: 100%; height: 100%"
                            :src="scope.row.propsAbility.chatBubble.cover"
                            :preview-src-list="[scope.row.propsAbility.chatBubble.cover]"
                          />
                          <div class="preview-svga">
                            <svgaplayer
                              type="popover"
                              :url="scope.row.propsAbility.chatBubble.sourceUrl"
                            />
                          </div>
                          <div class="props-lable">气泡框</div>
                        </div>
                      </div>

                      <div v-if="scope.row.propsAbility.dataCard" class="props">
                        <div class="preview-img">
                          <el-image
                            style="width: 100%; height: 100%"
                            :src="scope.row.propsAbility.dataCard.cover"
                            :preview-src-list="[scope.row.propsAbility.dataCard.cover]"
                          />
                          <div class="preview-svga">
                            <svgaplayer
                              type="popover"
                              :url="scope.row.propsAbility.dataCard.sourceUrl"
                            />
                          </div>
                          <div class="props-lable">资料卡</div>
                        </div>
                      </div>
                    </div>
                  </el-form-item>
                </el-col>
              </el-form-item>
            </el-form>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="commodity.id" width="200" label="ID" align="center" />
      <el-table-column label="平台" width="80" align="center">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.commodity.sysOrigin" :desc="scope.row.commodity.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column prop="currencyTypesName" label="付费类型" align="center" width="80" />
      <el-table-column prop="commodity.validDays" label="有效天数" align="center" min-width="100" />
      <el-table-column label="封面" width="80" align="center">
        <template slot-scope="scope">
          <div v-if="scope.row.propsSource" class="preview-img">
            <el-image
              style="width: 100%; height: 100%"
              :src="scope.row.propsSource.cover"
              :preview-src-list="[scope.row.propsSource.cover]"
            />
            <div class="preview-svga">
              <svgaplayer
                type="popover"
                :url="scope.row.propsSource.sourceUrl"
              />
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column width="100" label="上/下架" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.commodity.shelfStatus"
            :active-value="true"
            :inactive-value="false"
            @change="handleSwitchChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column prop="commodity.label" width="100" label="标签" align="center" min-width="100" />
      <el-table-column prop="commodity.sort" width="50" label="序号" align="center" min-width="60" />
      <el-table-column prop="commodity.createTime" width="200" label="创建时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.commodity.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column prop="commodity.updateTime" width="200" label="修改时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.commodity.updateTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="100">
        <template slot-scope="scope">
          <el-button type="text" @click.native="handleUpdate(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <form-edit
      v-if="formEditVisable"
      :row="thisRow"
      @close="formEditVisable=false"
      @success="formEditSuccess"
    />
  </div>
</template>

<script>
import { pagePropsStore, addOrUpdatePropsStore } from '@/api/props'
import Pagination from '@/components/Pagination'
import { propsCurrencyType, propsCommodityTypes } from '@/constant/type'
import FormEdit from './form-edit'
import { mapGetters } from 'vuex'

export default {
  components: { Pagination, FormEdit },
  data() {
    return {
      propsCommodityTypes,
      showcaseTypes: [
        { value: false, name: '下架' },
        { value: true, name: '上架' }
      ],
      formEditVisable: false,
      thisRow: {},
      thatSelectedUserId: {},
      propsCurrencyType,
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        id: '',
        sourceId: '',
        sysOrigin: 'HALAR',
        propsType: 'AVATAR_FRAME',
        shelfStatus: true,
        currencyTypes: ''
      },
      listLoading: true
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      that.listLoading = true
      if (isClean === true) {
        that.listQuery.cursor = 1
      }
      pagePropsStore(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      }).catch(er => {
        that.listLoading = false
      })
    },
    handleCreate() {
      this.thisRow = {
        commodity: {
          shelfStatus: false,
          sysOrigin: this.listQuery.sysOrigin,
          propsType: this.listQuery.propsType
        }
      }
      this.formEditVisable = true
    },
    handleUpdate(row) {
      this.formEditVisable = true
    },
    handleSwitchChange(row) {
      addOrUpdatePropsStore(row.commodity)
        .then(res => {})
        .catch(er => {
          row.shelfStatus = !row.shelfStatus
        })
    },
    handleSearch() {
      this.renderData(true)
    },
    queryUserDetails(row) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = row.id
    },
    handleMouseEnter(row) {
      this.thisRow = row
      this.thatSelectedUserId = row.id
    },
    formEditSuccess() {
      this.formEditVisable = false
      this.renderData(false)
    }
  }
}
</script>
<style scoped lang="scss">
 .store-table-expand {
    font-size: 0;
    label {
    width: 90px;
    color: #99a9bf;
    .el-form-item {
      margin-right: 0;
      margin-bottom: 0;
      width: 50%;
    }
   }
   .preview-img {
    .props-lable {
      position: absolute;
      bottom: 0px;
      width: 100%;
      color: #FFFFFF;
      padding: 2px 5px;
      line-height: 12px;
      border-radius: 5px;
      font-size: 12px;
      background-color: rgba(0, 0, 0, 0.2);
    }
   }
 }
</style>
