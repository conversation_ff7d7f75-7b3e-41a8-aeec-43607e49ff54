<template>
  <div class="props-store-edit">
    <el-dialog
      :title="textOptTitle"
      :visible="true"
      :before-close="handleClose"
      :close-on-click-modal="false"
      :width="isNobleVip? '930px' : '450px'"
      top="50px"
    >
      <div v-loading="submitLoading">
        <el-form ref="form" :model="form" :rules="formRules" label-width="110px" style="margin-right:50px;">
          <el-row :gutter="10">
            <el-col :md="24" :lg="isNobleVip ? 12 : 24">
              <el-form-item label="归属系统" prop="sysOrigin">
                <el-select
                  v-model="form.sysOrigin"
                  placeholder="归属系统"
                  style="width: 100%"
                  :disabled="true"
                >
                  <el-option
                    v-for="(item, index) in permissionsSysOriginPlatforms"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
                    <span style="float: left;margin-left:10px">{{ item.label }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="道具类型" prop="propsType">
                <el-select
                  v-model="form.propsType"
                  placeholder="道具类型"
                  style="width: 100%"
                  :disabled="true"
                >
                  <el-option
                    v-for="(item, index) in propsCommodityTypes"
                    :key="index"
                    :label="item.name"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="道具资源" :prop="sourceMap[form.propsType].isChangeSource ? 'tmpSourceIndex' : ''">
                <el-row v-loading="sourceMap[form.propsType].loading" :gutter="10">
                  <el-col v-if="sourceMap[form.propsType].isChangeSource" :span="15">
                    <el-form-item :prop="isUpdate ? 'tmpSourceIndex' : ''">
                      <el-select
                        v-model="form.tmpSourceIndex"
                        placeholder="类型"
                        style="width:100%;"
                        class="filter-item"
                        @change="changePropsSouce"
                      >
                        <el-option v-for="(item, index) in sourceMap[form.propsType].list" :key="item.id" :label="item.name" :value="index">
                          <div style="float: left;">
                            <img :src="item.cover" width="40px" height="40px">
                          </div>
                          <div style="float: left;margin-left:10px">
                            {{ item.name }}
                          </div>
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <div style="width: 36px; height: 36px">
                      <el-image
                        style="width: 100%; height: 100%"
                        :src="sourceSelect.cover"
                        :preview-src-list="[sourceSelect.cover]"
                      >
                        <div slot="error" class="image-slot" style="text-align: center;">
                          <i class="el-icon-picture-outline" />
                        </div>
                      </el-image>
                    </div>
                  </el-col>
                  <el-col :span="4">
                    <svgaplayer
                      type="popover"
                      :url="sourceSelect.sourceUrl"
                    />
                  </el-col>
                </el-row>
                <div>
                  <el-button v-if="!sourceMap[form.propsType].isChangeSource" type="text" @click="clickChangeSource(form.propsType)">更换资源</el-button>
                </div>
              </el-form-item>
              <el-form-item label="付费类型" prop="tmpCurrencyTypes">
                <el-select
                  v-model="form.tmpCurrencyTypes"
                  placeholder="付费类型"
                  style="width: 100%"
                  multiple
                >
                  <el-option v-for="item in propsCurrencyType" :key="item.value" :disabled="isNobleVip && item.value !== 'GOLD'" :label="item.name" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="有效天数" prop="tmpValidDays">
                <el-select
                  v-model="form.tmpValidDays"
                  placeholder="付费类型"
                  style="width: 100%"
                  class="filter-item"
                  multiple
                >
                  <el-option v-for="item in propsValidDays" :key="item.value" :label="item.name" :value="item.value" />
                </el-select>
              </el-form-item>

              <el-form-item label="标签" prop="label">
                <el-input v-model.trim="form.label" placeholder="请输入标签" />
              </el-form-item>
              <el-row v-if="form.propsType === 'NOBLE_VIP'">
                <el-col :span="12">
                  <el-form-item label="折扣率" prop="discount">
                    <el-input v-model="form.discount" placeholder="折扣率" @change="calculateDiscountPrice()" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="序号" prop="sort">
                    <el-input v-model.trim="form.sort" v-number placeholder="序号越大越靠前" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-if="form.propsType !== 'NOBLE_VIP'">
                <el-col :span="12">
                  <el-form-item label="折扣率" prop="discount">
                    <el-input v-model="form.discount" placeholder="折扣率" :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="序号" prop="sort">
                    <el-input v-model.trim="form.sort" v-number placeholder="序号越大越靠前" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="上/下架" prop="shelfStatus">
                <el-radio-group v-model="form.shelfStatus">
                  <el-radio :label="true">上架</el-radio>
                  <el-radio :label="false">下架</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col v-if="isNobleVip" :md="24" :lg="12">
              <el-form-item label="车辆">
                <el-row v-loading="sourceMap['RIDE'].loading" :gutter="10">
                  <el-col v-if="sourceMap['RIDE'].isChangeSource" :span="15">
                    <el-form-item>
                      <el-select
                        v-model="form.tmpCarIndex"
                        placeholder="类型"
                        style="width:100%;"
                        class="filter-item"
                        @change="changeCarSouce"
                      >
                        <el-option v-for="(item, index) in sourceMap['RIDE'].list" :key="item.id" :label="item.name" :value="index">
                          <div style="float: left;">
                            <img :src="item.cover" width="40px" height="40px">
                          </div>
                          <div style="float: left;margin-left:10px">
                            {{ item.name }}
                          </div>
                        </el-option>
                      </el-select>
                    </el-form-item>

                  </el-col>
                  <el-col :span="4">
                    <div style="width: 36px; height: 36px">
                      <el-image
                        style="width: 100%; height: 100%"
                        :src="carSouceSelect.cover"
                        :preview-src-list="[carSouceSelect.cover]"
                      >
                        <div slot="error" class="image-slot" style="text-align: center;">
                          <i class="el-icon-picture-outline" />
                        </div>
                      </el-image>
                    </div>
                  </el-col>
                  <el-col :span="4">
                    <svgaplayer
                      type="popover"
                      :url="carSouceSelect.sourceUrl"
                    />
                  </el-col>
                </el-row>
                <div>
                  <el-button v-if="!sourceMap['RIDE'].isChangeSource" type="text" @click="clickChangeSource('RIDE')">更换资源</el-button>
                </div>
              </el-form-item>
              <el-form-item label="头像框">
                <el-row v-loading="sourceMap['AVATAR_FRAME'].loading" :gutter="10">
                  <el-col v-if="sourceMap['AVATAR_FRAME'].isChangeSource" :span="15">
                    <el-form-item>
                      <el-select
                        v-model="form.tmpAvatarIndex"
                        placeholder="类型"
                        style="width:100%;"
                        class="filter-item"
                        @change="changeAvatarFrameSource"
                      >
                        <el-option v-for="(item, index) in sourceMap['AVATAR_FRAME'].list" :key="item.id" :label="item.name" :value="index">
                          <div style="float: left;">
                            <img :src="item.cover" width="40px" height="40px">
                          </div>
                          <div style="float: left;margin-left:10px">
                            {{ item.name }}
                          </div>
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <div style="width: 36px; height: 36px">
                      <el-image
                        style="width: 100%; height: 100%"
                        :src="avatarSouceSelect.cover"
                        :preview-src-list="[avatarSouceSelect.cover]"
                      >
                        <div slot="error" class="image-slot" style="text-align: center;">
                          <i class="el-icon-picture-outline" />
                        </div>
                      </el-image>
                    </div>
                  </el-col>
                  <el-col :span="4">
                    <svgaplayer
                      type="popover"
                      :url="avatarSouceSelect.sourceUrl"
                    />
                  </el-col>
                </el-row>
                <div>
                  <el-button v-if="!sourceMap['AVATAR_FRAME'].isChangeSource" type="text" @click="clickChangeSource('AVATAR_FRAME')">更换资源</el-button>
                </div>
              </el-form-item>
              <el-form-item label="资料卡">
                <el-row v-loading="sourceMap['DATA_CARD'].loading" :gutter="10">
                  <el-col v-if="sourceMap['DATA_CARD'].isChangeSource" :span="15">
                    <el-form-item>
                      <el-select
                        v-model="form.tmpDataCardIndex"
                        placeholder="类型"
                        style="width:100%;"
                        class="filter-item"
                        @change="changeDataCardSource"
                      >
                        <el-option v-for="(item, index) in sourceMap['DATA_CARD'].list" :key="item.id" :label="item.name" :value="index">
                          <div style="float: left;">
                            <img :src="item.cover" width="40px" height="40px">
                          </div>
                          <div style="float: left;margin-left:10px">
                            {{ item.name }}
                          </div>
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <div style="width: 36px; height: 36px">
                      <el-image
                        style="width: 100%; height: 100%"
                        :src="dataCardSouceSelect.cover"
                        :preview-src-list="[dataCardSouceSelect.cover]"
                      >
                        <div slot="error" class="image-slot" style="text-align: center;">
                          <i class="el-icon-picture-outline" />
                        </div>
                      </el-image>
                    </div>
                  </el-col>
                  <el-col :span="4">
                    <svgaplayer
                      type="popover"
                      :url="dataCardSouceSelect.sourceUrl"
                    />
                  </el-col>
                </el-row>
                <div>
                  <el-button v-if="!sourceMap['DATA_CARD'].isChangeSource" type="text" @click="clickChangeSource('DATA_CARD')">更换资源</el-button>
                </div>
              </el-form-item>
              <el-form-item label="聊天气泡">
                <el-row v-loading="sourceMap['CHAT_BUBBLE'].loading" :gutter="10">
                  <el-col v-if="sourceMap['CHAT_BUBBLE'].isChangeSource" :span="15">
                    <el-form-item>
                      <el-select
                        v-model="form.tmpChatBubbleIndex"
                        placeholder="类型"
                        style="width:100%;"
                        class="filter-item"
                        @change="changeChatBubbleSource"
                      >
                        <el-option v-for="(item, index) in sourceMap['CHAT_BUBBLE'].list" :key="item.id" :label="item.name" :value="index">
                          <div style="float: left;">
                            <img :src="item.cover" width="40px" height="40px">
                          </div>
                          <div style="float: left;margin-left:10px">
                            {{ item.name }}
                          </div>
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <div style="width: 36px; height: 36px">
                      <el-image
                        style="width: 100%; height: 100%"
                        :src="chatBubbleSouceSelect.cover"
                        :preview-src-list="[chatBubbleSouceSelect.cover]"
                      >
                        <div slot="error" class="image-slot" style="text-align: center;">
                          <i class="el-icon-picture-outline" />
                        </div>
                      </el-image>
                    </div>
                  </el-col>
                  <el-col :span="4">
                    <svgaplayer
                      type="popover"
                      :url="chatBubbleSouceSelect.sourceUrl"
                    />
                  </el-col>
                </el-row>
                <div>
                  <el-button v-if="!sourceMap['CHAT_BUBBLE'].isChangeSource" type="text" @click="clickChangeSource('CHAT_BUBBLE')">更换资源</el-button>
                </div>
              </el-form-item>
              <el-form-item label="VIP类型" prop="propsAbility.vipType">
                <el-select
                  v-model="form.propsAbility.vipType"
                  placeholder="贵族类型"
                  style="width: 100%"
                  class="filter-item"
                >
                  <el-option v-for="item in nobleVipTabs" :key="item.value" :label="item.name" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="登录奖励" prop="propsAbility.loginRewardsAmount">
                    <el-input v-model="form.propsAbility.loginRewardsAmount" placeholder="登录奖励" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="VIP等级" prop="propsAbility.vipLevel">
                    <el-input v-model="form.propsAbility.vipLevel" placeholder="VIP等级" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="12">
                  <el-form-item label="管理员数" prop="propsAbility.adminNumber">
                    <el-input v-model="form.propsAbility.adminNumber" placeholder="管理员数" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="房间成员数" prop="propsAbility.roomMaxMember">
                    <el-input v-model="form.propsAbility.roomMaxMember" placeholder="房间成员数" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
          <el-form-item>
            <el-button type="primary" @click="submitForm()">保存</el-button>
            <el-button @click="handleClose()">取消</el-button>
          </el-form-item>
        </el-form>

      </div>
    </el-dialog>
  </div>
</template>
<script>
import { listSysOriginTypeList, addOrUpdatePropsStore } from '@/api/props'
import { propsTypes, propsCurrencyType, propsValidDays, nobleVipTabs, propsCommodityTypes } from '@/constant/type'
import { deepClone } from '@/utils'
import { mapGetters } from 'vuex'
export default {
  props: {
    row: {
      type: Object,
      require: false,
      default: () => {}
    }
  },
  data() {
    const commonRules = [
      { required: true, message: '必填字段不可为空', trigger: 'blur' }
    ]
    return {
      discountPrice: '',
      propsCommodityTypes,
      nobleVipTabs,
      isChangeNobleSouce: false,
      isCangeAvatarFrameSouce: false,
      isCangeDataCardSouce: false,
      isChangeCarSouce: false,
      isChangeChatBubbleSouce: false,
      propsValidDays,
      propsCurrencyType,
      svgaplayerVisable: false,
      coverUploadLoading: false,
      coverFileList: [],
      sourceUploadLoading: false,
      sourceUrlFileList: [],
      propsTypes,
      submitLoading: false,
      uploadLoading: false,
      form: {
        id: '',
        sysOrigin: '',
        sourceId: '',
        propsType: '',
        currencyTypes: '',
        validDays: '',
        discount: 0,
        shelfStatus: true,
        sort: 0,
        label: '',
        del: false,
        propsAbility: {
          id: '',
          vipType: '',
          vipLevel: '',
          adminNumber: '',
          loginRewardsAmount: '',
          roomMaxMember: '',
          avatarFrameId: '',
          chatBubbleId: '',
          carId: '',
          dataCardId: ''
        },
        tmpCurrencyTypes: [],
        tmpValidDays: [],
        tmpSourceIndex: '',
        tmpAvatarIndex: '',
        tmpDataCardIndex: '',
        tmpChatBubbleIndex: '',
        tmpFloatPictureIndex: '',
        tmpCarIndex: ''
      },
      formRules: {
        sort: commonRules,
        propsType: commonRules,
        sysOrigin: commonRules,
        tmpTypeIndex: commonRules,
        tmpCurrencyTypes: commonRules,
        tmpValidDays: commonRules,
        validDays: commonRules,
        discount: commonRules,
        shelfStatus: commonRules,
        'propsAbility.vipType': commonRules,
        'propsAbility.vipLevel': commonRules,
        'propsAbility.adminNumber': commonRules,
        'propsAbility.loginRewardsAmount': commonRules,
        'propsAbility.roomMaxMember': commonRules,
        'propsAbility.avatarFrameId': commonRules,
        'propsAbility.carId': commonRules,
        'propsAbility.chatBubbleId': commonRules,
        'propsAbility.dataCardId': commonRules,
        tmpCarIndex: commonRules,
        tmpDataCardIndex: commonRules,
        tmpAvatarIndex: commonRules,
        tmpChatBubbleIndex: commonRules,
        tmpFloatPictureIndex: commonRules,
        tmpSourceIndex: commonRules
      },
      sourceTypeList: [],
      selectType: {},
      isLoadSourceTypeList: false,
      listTypeLoading: false,

      sourceSelect: {},
      carSouceSelect: {},
      avatarSouceSelect: {},
      dataCardSouceSelect: {},
      chatBubbleSouceSelect: {},

      sourceMap: {
        'AVATAR_FRAME': {
          loading: false,
          isLoadSourceTypeList: false,
          isChangeSource: false,
          list: []
        },
        'RIDE': {
          loading: false,
          isLoadSourceTypeList: false,
          isChangeSource: false,
          list: []
        },
        'NOBLE_VIP': {
          loading: false,
          isLoadSourceTypeList: false,
          isChangeSource: false,
          list: []
        },
        'THEME': {
          loading: false,
          isLoadSourceTypeList: false,
          isChangeSource: false,
          list: []
        },
        'LAYOUT': {
          loading: false,
          isLoadSourceTypeList: false,
          isChangeSource: false,
          list: []
        },
        'CHAT_BUBBLE': {
          loading: false,
          isLoadSourceTypeList: false,
          isChangeSource: false,
          list: []
        },
        'FLOAT_PICTURE': {
          loading: false,
          isLoadSourceTypeList: false,
          isChangeSource: false,
          list: []
        },
        'DATA_CARD': {
          loading: false,
          isLoadSourceTypeList: false,
          isChangeSource: false,
          list: []
        }
      }
    }
  },
  computed: {
    isNobleVip() {
      return this.form && this.form.propsType === 'NOBLE_VIP'
    },
    rowSourceUrl() {
      return this.row ? this.row.propsSource.sourceUrl : ''
    },
    isShowCoverUpload() {
      return !this.form.cover
    },
    isShowSourceUpload() {
      return !this.form.sourceUrl
    },
    textOptTitle() {
      return this.form && this.form.id ? '修改' : '添加'
    },
    isUpdate() {
      return !!(this.form && this.form.id)
    },
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    row: {
      handler(newVal) {
        if (!newVal) {
          return
        }
        const newForm = deepClone(newVal.commodity)
        newForm.tmpValidDays = newForm.validDays ? newForm.validDays.split(',').map(item => Number(item)) : []
        newForm.tmpCurrencyTypes = newForm.currencyTypes ? newForm.currencyTypes.split(',') : []
        newForm.discount = newForm.discount || 0
        newForm.sort = newForm.sort || 0
        newForm.propsAbility = deepClone(newVal.propsAbility && newVal.propsAbility.nobleVipAbility ? newVal.propsAbility.nobleVipAbility : this.form.propsAbility)

        if (newVal.propsSource) {
          this.sourceSelect = {
            id: newVal.propsSource.id,
            cover: newVal.propsSource.cover,
            sourceUrl: newVal.propsSource.sourceUrl
          }
        }

        if (newVal.propsAbility) {
          const dataCard = newVal.propsAbility.dataCard
          if (dataCard) {
            this.dataCardSouceSelect = {
              id: dataCard.id,
              cover: dataCard.cover,
              sourceUrl: dataCard.sourceUrl
            }
          }

          const car = newVal.propsAbility.car
          if (car) {
            this.carSouceSelect = {
              id: car.id,
              cover: car.cover,
              sourceUrl: car.sourceUrl
            }
          }

          const avatarFrame = newVal.propsAbility.avatarFrame
          if (avatarFrame) {
            this.avatarSouceSelect = {
              id: avatarFrame.id,
              cover: avatarFrame.cover,
              sourceUrl: avatarFrame.sourceUrl
            }
          }

          const chatBubble = newVal.propsAbility.chatBubble
          if (chatBubble) {
            this.chatBubbleSouceSelect = {
              id: chatBubble.id,
              cover: chatBubble.cover,
              sourceUrl: chatBubble.sourceUrl
            }
          }
        }

        this.form = newForm
        this.calculateDiscountPrice()
      },
      immediate: true
    }
  },
  mounted() {
    if (!this.isUpdate) {
      if (this.isNobleVip) {
        for (const key in this.sourceMap) {
          this.sourceMap[key].isChangeSource = true
          this.loadSourceType(key)
        }
        return
      }
      this.sourceMap[this.form.propsType].isChangeSource = true
      this.loadSourceType(this.form.propsType)
    }
  },
  methods: {
    clickChangeSource(type) {
      this.sourceMap[type].isChangeSource = !this.sourceMap[type].isChangeSource
      const sourceData = this.sourceMap[type]
      if (sourceData.isChangeSource === true && sourceData.isLoadSourceTypeList === false) {
        this.loadSourceType(type)
      }
    },
    changePropsSouce(index) {
      this.sourceSelect = this.sourceMap[this.form.propsType].list[index]
      this.form.sourceId = this.sourceSelect.id
    },
    changeCarSouce(index) {
      this.carSouceSelect = this.sourceMap['RIDE'].list[index]
      this.form.propsAbility.carId = this.carSouceSelect.id
    },
    changeAvatarFrameSource(index) {
      this.avatarSouceSelect = this.sourceMap['AVATAR_FRAME'].list[index]
      this.form.propsAbility.avatarFrameId = this.avatarSouceSelect.id
    },
    changeDataCardSource(index) {
      this.dataCardSouceSelect = this.sourceMap['DATA_CARD'].list[index]
      this.form.propsAbility.dataCardId = this.dataCardSouceSelect.id
    },
    changeChatBubbleSource(index) {
      this.chatBubbleSouceSelect = this.sourceMap['CHAT_BUBBLE'].list[index]
      this.form.propsAbility.chatBubbleId = this.chatBubbleSouceSelect.id
    },
    loadSourceType(type) {
      const that = this
      that.sourceMap[type].loading = true
      listSysOriginTypeList(that.form.sysOrigin, type).then(res => {
        that.sourceMap[type].loading = false
        that.sourceMap[type].isLoadSourceTypeList = true
        that.sourceMap[type].list = res.body || []
      }).catch(er => {
        that.sourceMap[type].loading = false
        console.error(er)
      })
    },
    uploadCover(file) {
      const that = this
      that.coverUploadLoading = true
      that.$simpleUploadFlie(file).then(res => {
        that.coverUploadLoading = false
        that.form.cover = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.coverUploadLoading = false
      })
    },
    handleCoverFileRemove(file, fileList) {
      this.form.cover = ''
      this.coverUploadLoading = false
    },
    sourceUpload(file) {
      const that = this
      that.sourceUploadLoading = true
      that.$simpleUploadFlie(file).then(res => {
        that.sourceUploadLoading = false
        that.form.sourceUrl = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.sourceUploadLoading = false
      })
    },
    handleSourceFileRemove(file, fileList) {
      this.form.sourceUrl = ''
      this.sourceUploadLoading = false
    },
    handleClose() {
      this.$emit('close')
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return
        }
        that.form.currencyTypes = that.form.tmpCurrencyTypes.join(',')
        that.form.validDays = that.form.tmpValidDays.join(',')
        that.submitLoading = true
        addOrUpdatePropsStore(that.form).then(res => {
          that.submitLoading = false
          that.$emit('success', res)
        }).catch(er => {
          that.submitLoading = false
          that.$emit('fail')
          console.error(er)
        })
      })
    },
    clickSvgaplayer() {
      this.svgaplayerVisable = true
    },
    calculateDiscountPrice() {
      if (this.form.discount >= 0 && this.form.discount <= 1) {
        return
      }
      this.form.discount = 1
    }
  }
}
</script>
<style scoped lang="scss">

</style>
