<template>
  <div class="send-game-coupon">
    <el-alert title="赠送：游戏券" style="margin-bottom: 10px;" :closable="false" />
    <el-form ref="form" :model="propsGiveForm" :rules="formRules" label-width="85px">
      <el-form-item label="接收人" prop="receiverAccounts">
        <el-input v-model="propsGiveForm.receiverAccounts" placeholder="请输入用户 【短账号】 ，多个短账号用英文','分割;(一批最多15个)短账号" />
      </el-form-item>
      <el-form-item label="选择系统" prop="sysOrigin">
        <el-form-item>
          <el-select
            v-model="propsGiveForm.sysOrigin"
            placeholder="系统"
            style="width:100%;"
            class="filter-item"
          >
            <el-option v-for="item in permissionsSysOriginPlatforms" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form-item>
      <el-form-item label="数量" prop="content">
        <el-input v-model="propsGiveForm.content" v-number placeholder="请输入游戏券数量" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :loading="submitLoading" @click="onGiveSubmit">发送</el-button>
      </el-form-item>
    </el-form>

  </div>
</template>
<script>

import { sendPropsGiveUser } from '@/api/props'
import { mapGetters } from 'vuex'

export default {
  name: 'SendGameCoupon',
  data() {
    const commonRules = [
      { required: true, message: '必填字段不可为空', trigger: 'blur' }
    ]
    return {
      formRules: {
        // receiverId: commonRules,
        receiverAccounts: commonRules,
        content: commonRules,
        sysOrigin: commonRules
      },
      propsGiveForm: {
        // receiverId: '',
        receiverAccounts: '',
        type: 'GAME_COUPON',
        content: '',
        secondaryType: '',
        sysOrigin: ''
      },
      userInfo: {},
      submitLoading: false
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
  },
  methods: {
    selectPropsSourcePopover(item) {
      this.selectProps = item
      this.propsGiveForm.content = item.id
    },
    onGiveSubmit() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return
        }
        that.submitLoading = true
        sendPropsGiveUser(that.propsGiveForm).then(res => {
          if (res.errorCode === 0) {
            if (res.body) {
              that.$opsMessage.fail('无效短账号: ' + res.body + ' 请处理后再将当前批次所有短账号重新发送.')
              that.submitLoading = false
              return
            }
            that.$opsMessage.success()
            that.selectProps = {}
            that.propsGiveForm.content = ''
            that.submitLoading = false
            return
          }
          that.$opsMessage.fail(res.errorMsg)
          that.submitLoading = false
        }).catch(er => {
          that.submitLoading = false
          console.error(er)
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">
.payer-source {
  position: relative;
  .paler-icon {
    position:absolute;
    left: 0px;
    top: -10px;
  }
  img {
    width: 100px;
    height: 100px;
  }
}
</style>
