<template>
  <div class="send-props">
    <el-alert title="赠送道具" style="margin-bottom: 10px;" :closable="false" />
    <el-form ref="form" :model="propsGiveForm" :rules="formRules" label-width="85px">
      <el-form-item label="接收人" prop="receiverAccounts">
        <el-input v-model="propsGiveForm.receiverAccounts" placeholder="请输入用户 【短账号】 ，多个短账号用英文','分割;(一批最多15个)短账号" />
      </el-form-item>
      <el-form-item label="选择系统" prop="sysOrigin">
        <el-form-item>
          <el-select
            v-model="propsGiveForm.sysOrigin"
            placeholder="系统"
            style="width:100%;"
            class="filter-item"
            @change="changeSysOrigin"
          >
            <el-option v-for="item in permissionsSysOriginPlatforms" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form-item>
      <el-form-item label="选择类型" prop="secondaryType">
        <el-select
          v-model="propsGiveForm.secondaryType"
          placeholder="类型"
          style="width:100%;"
          class="filter-item"
          @change="changePropsType"
        >
          <el-option v-for="item in propsTypes" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="选择资源" prop="content">
        <div v-if="selectProps.cover" class="payer-source">
          <div class="paler-icon">
            <svgaplayer
              type="popover"
              :url="selectProps.sourceUrl || ''"
            />
          </div>
          <img :src="selectProps.cover" alt="cover">
        </div>
        <props-source-select-popover
          v-loading="propsTypeMap[propsGiveForm.secondaryType] ? propsTypeMap[propsGiveForm.secondaryType].loading : false"
          :data="propsTypeMap[propsGiveForm.secondaryType] ? propsTypeMap[propsGiveForm.secondaryType].list : []"
          :property="selectPopoverProperty"
          :visible="visiblePopover"
          :show-filter="true"
          @select="selectPropsSourcePopover"
        />
      </el-form-item>
      <el-form-item v-if="propsGiveForm.secondaryType == 'NOBLE_VIP'" label="贵族来源" prop="vipOrigin">
        <el-select
          v-model="propsGiveForm.vipOrigin"
          placeholder="贵族来源"
          style="width:100%;"
          class="filter-item"
        >
          <el-option label="系统赠送" value="SYSTEM_GIVE" />
          <el-option label="购买或朋友赠送" value="BUY_OR_GIVE" />
          <el-option label="活动奖励" value="ACTIVITY_AWARD" />
        </el-select>
      </el-form-item>
      <el-form-item label="有效天数" prop="exchangeDays">
        <el-input v-model="propsGiveForm.exchangeDays" v-number placeholder="有效天数" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :loading="submitLoading" @click="onGiveSubmit">发送</el-button>
      </el-form-item>
    </el-form>

  </div>
</template>
<script>

import { listNotFamilyBySysOriginType, sendPropsGiveUser } from '@/api/props'
import { propsTypes } from '@/constant/type'
import PropsSourceSelectPopover from '@/components/data/PropsSourceSelectPopover'
import { mapGetters } from 'vuex'

export default {
  name: 'SendProps',
  components: { PropsSourceSelectPopover },
  data() {
    const commonRules = [
      { required: true, message: '必填字段不可为空', trigger: 'blur' }
    ]
    const propsTypeMap = {}
    propsTypes.forEach(item => {
      propsTypeMap[item.value] = {
        list: [],
        loading: false,
        loadData: false
      }
    })
    return {
      propsTypes,
      visiblePopover: false,
      propsTypeMap,
      selectPopoverProperty: {
        label: 'name',
        cover: 'cover',
        svgaUrl: 'sourceUrl',
        value: 'id'
      },
      selectProps: {},
      submitLoading: false,
      formRules: {
        // receiverId: commonRules,
        receiverAccounts: commonRules,
        exchangeDays: commonRules,
        content: commonRules,
        secondaryType: commonRules,
        sysOrigin: commonRules
      },
      propsGiveForm: {
        sysOrigin: '',
        // receiverId: '',
        receiverAccounts: '',
        type: 'PROPS',
        content: '',
        exchangeDays: '',
        secondaryType: '',
        vipOrigin: null
      },
      userInfo: {}
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    this.propsTypes = this.propsTypes.filter(item => !(item.value === 'CUSTOMIZE' || item.value === 'FRAGMENTS'))
  },
  methods: {
    changeSysOrigin(val) {
      const that = this
      that.propsGiveForm.secondaryType = ''
      that.selectProps = {}
    },
    changePropsType(val) {
      const that = this
      that.propsGiveForm.vipOrigin = null
      if (!that.propsGiveForm.sysOrigin) {
        return
      }
      that.selectProps = {}
      that.propsGiveForm.content = ''
      const propsTypeItem = that.propsTypeMap[val]
      propsTypeItem.list = []
      // 区分系统,如果要做缓存需要改造支持多系统资源
      // if (propsTypeItem && propsTypeItem.loadData === true) {
      //   return
      // }
      propsTypeItem.loading = true
      listNotFamilyBySysOriginType(that.propsGiveForm.sysOrigin, val).then(res => {
        propsTypeItem.loading = false
        propsTypeItem.loadData = true
        propsTypeItem.list = (res.body || [])
      }).catch(er => {
        propsTypeItem.loading = false
        console.error(er)
      })
    },
    selectPropsSourcePopover(item) {
      this.selectProps = item
      this.propsGiveForm.content = item.id
    },
    onGiveSubmit() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return
        }
        that.submitLoading = true
        sendPropsGiveUser(that.propsGiveForm).then(res => {
          if (res.errorCode === 0) {
            if (res.body) {
              that.$opsMessage.fail('无效短账号: ' + res.body + ' 请处理后再将当前批次所有短账号重新发送.')
              that.submitLoading = false
              return
            }
            that.$opsMessage.success()
            that.selectProps = {}
            that.propsGiveForm.content = ''
            that.submitLoading = false
            return
          }
          that.$opsMessage.fail(res.errorMsg)
          that.submitLoading = false
        }).catch(er => {
          that.submitLoading = false
          console.error(er)
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">
.payer-source {
  position: relative;
  .paler-icon {
    position:absolute;
    left: 0px;
    top: -10px;
  }
  img {
    width: 100px;
    height: 100px;
  }
}
</style>
