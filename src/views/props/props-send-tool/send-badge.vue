<template>
  <div class="send-props">
    <el-alert title="赠送徽章" style="margin-bottom: 10px;" :closable="false" />
    <el-form ref="form" :model="propsGiveForm" :rules="formRules" label-width="85px">
      <el-form-item label="接收人" prop="receiverAccounts">
        <el-input v-model="propsGiveForm.receiverAccounts" placeholder="请输入用户 【短账号】 ，多个短账号用英文','分割;(一批最多15个)短账号" />
      </el-form-item>
      <el-form-item label="选择系统" prop="sysOrigin">
        <el-form-item>
          <el-select
            v-model="propsGiveForm.sysOrigin"
            placeholder="系统"
            style="width:100%;"
            class="filter-item"
            @change="changeSysOrigin"
          >
            <el-option v-for="item in permissionsSysOriginPlatforms" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form-item>
      <el-form-item v-if="propsGiveForm.sysOrigin" label="选择类型" prop="secondaryType">
        <el-form-item>
          <el-select
            v-model="propsGiveForm.secondaryType"
            placeholder="类型"
            style="width:100%;"
            class="filter-item"
            @change="changetType"
          >
            <el-option v-for="item in badgeTypes" :key="item.value" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form-item>
      <el-form-item label="选择资源" prop="content">
        <div v-if="selectProps.selectUrl" class="payer-source">
          <div class="paler-icon">
            <svgaplayer
              type="popover"
              :url="selectProps.animationUrl || ''"
            />
          </div>
          <img :src="selectProps.selectUrl" alt="cover">
        </div>
        <props-source-select-popover
          v-if="propsGiveForm.sysOrigin && propsGiveForm.secondaryType"
          v-loading="listLoading"
          :data="list"
          :property="selectPopoverProperty"
          :visible="visiblePopover"
          @select="selectPropsSourcePopover"
        />
      </el-form-item>
      <el-form-item label="有效天数" prop="exchangeDays">
        <el-input v-model="propsGiveForm.exchangeDays" v-number placeholder="有效天数" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :loading="submitLoading" @click="onGiveSubmit">发送</el-button>
      </el-form-item>
    </el-form>

  </div>
</template>
<script>

import { sendPropsGiveUser } from '@/api/props'
import { listBadgePictureBySysOrigin } from '@/api/badge'
import { badgeTypes } from '@/constant/origin'
import PropsSourceSelectPopover from '@/components/data/PropsSourceSelectPopover'
import { mapGetters } from 'vuex'

export default {
  name: 'SendBadge',
  components: { PropsSourceSelectPopover },
  data() {
    const commonRules = [
      { required: true, message: '必填字段不可为空', trigger: 'blur' }
    ]

    return {
      list: [],
      listLoading: false,
      badgeTypes,
      visiblePopover: false,
      selectPopoverProperty: {
        label: 'badgeName',
        cover: 'selectUrl',
        svgaUrl: 'animationUrl',
        value: 'badgeConfigId'
      },
      selectProps: {},
      submitLoading: false,
      formRules: {
        // receiverId: commonRules,
        receiverAccounts: commonRules,
        exchangeDays: commonRules,
        content: commonRules,
        secondaryType: commonRules,
        sysOrigin: commonRules
      },
      propsGiveForm: {
        // receiverId: '',
        receiverAccounts: '',
        type: 'BADGE',
        content: '',
        exchangeDays: '',
        secondaryType: '',
        sysOrigin: ''
      },
      userInfo: {}
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
  },
  methods: {
    changetType(type) {
      const that = this
      that.selectProps = {}
      that.propsGiveForm.content = ''
      that.list = []
      that.listLoading = true
      listBadgePictureBySysOrigin(that.propsGiveForm.sysOrigin, type).then(res => {
        that.listLoading = false
        that.list = res.body || []
      }).catch(er => {
        that.listLoading = false
        console.error(er)
      })
    },
    changeSysOrigin() {
      this.selectProps = {}
      this.propsGiveForm.secondaryType = ''
      this.propsGiveForm.content = ''
      this.list = []
    },
    selectPropsSourcePopover(item) {
      this.selectProps = item
      this.propsGiveForm.content = item.badgeConfigId
    },
    onGiveSubmit() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return
        }
        that.submitLoading = true
        sendPropsGiveUser(that.propsGiveForm).then(res => {
          if (res.errorCode === 0) {
            if (res.body) {
              that.$opsMessage.fail('无效短账号: ' + res.body + ' 请处理后再将当前批次所有短账号重新发送.')
              that.submitLoading = false
              return
            }
            that.$opsMessage.success()
            that.selectProps = {}
            that.propsGiveForm.content = ''
            that.submitLoading = false
            return
          }
          that.$opsMessage.fail(res.errorMsg)
          that.submitLoading = false
        }).catch(er => {
          that.submitLoading = false
          console.error(er)
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">
.payer-source {
  position: relative;
  .paler-icon {
    position:absolute;
    left: 0px;
    top: -10px;
  }
  img {
    width: 100px;
    height: 100px;
  }
}
</style>
