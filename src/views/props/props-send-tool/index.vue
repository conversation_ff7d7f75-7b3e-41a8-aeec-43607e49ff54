<template>
  <div class="app-container">
    <el-row :gutter="10">
      <el-col v-if="buttonPermissions.includes('send:props')">
        <send-props />
      </el-col>
      <el-col v-if="buttonPermissions.includes('send:props:badge')">
        <send-badge />
      </el-col>

    </el-row>
  </div>
</template>
<script>

import SendProps from './send-props'
import SendBadge from './send-badge'
import { mapGetters } from 'vuex'

export default {
  components: { SendProps, SendBadge },
  data() {
    return {}
  },
  computed: {
    ...mapGetters(['buttonPermissions'])
  },
  created() {
  }
}
</script>
