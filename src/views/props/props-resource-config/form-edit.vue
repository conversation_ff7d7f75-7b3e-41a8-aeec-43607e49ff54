<template>
  <div class="props-config-edit">
    <el-dialog
      :title="textOptTitle"
      :visible="true"
      :before-close="handleClose"
      :close-on-click-modal="false"
      width="450px"
    >
      <div v-loading="submitLoading">
        <el-form ref="form" :model="form" :rules="ruleVlide()" label-width="110px" style="margin-right:50px;">
          <el-form-item label="系统" prop="sysOrigin">
            <el-select
              v-model="form.sysOrigin"
              placeholder="归属系统"
              style="width: 100%;"
            >
              <el-option
                v-for="(item, index) in permissionsSysOriginPlatforms"
                :key="index"
                :label="item.label"
                :value="item.value"
              >
                <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
                <span style="float: left;margin-left:10px">{{ item.label }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="类型" prop="type">
            <el-select
              v-model="form.type"
              placeholder="类型"
              style="width:100%;"
              class="filter-item"
              @change="selectdTheme"
            >
              <el-option v-for="item in propsTypes" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="封面" prop="cover">
            <el-upload
              :disabled="coverUploadLoading"
              :file-list="coverFileList"
              :class="{'upload-but-hide': !isShowCoverUpload}"
              action=""
              list-type="picture-card"
              :http-request="uploadCover"
              :show-file-list="!isShowCoverUpload"
              :on-remove="handleCoverFileRemove"
              accept="image/*"
            >
              <i slot="default" v-loading="coverUploadLoading" class="el-icon-plus" />
            </el-upload>
          </el-form-item>
          <el-form-item v-show="selectedTypeName === 'RED_PACKET'" label="封面预览图" prop="sourceUrl">
            <el-upload
              :disabled="sourceUploadLoading"
              :file-list="sourceUrlFileList"
              :class="{'upload-but-hide': !isShowSourceUpload}"
              action=""
              list-type="picture-card"
              :http-request="sourceUpload"
              :show-file-list="!isShowSourceUpload"
              :on-remove="handleSourceFileRemove"
              accept="image/*"
            >
              <i slot="default" v-loading="sourceUploadLoading" class="el-icon-plus" />
            </el-upload>
          </el-form-item>
          <el-form-item v-show="selectedTypeName === 'RED_PACKET'" label="房间发送页图" prop="roomSendCoverUrl">
            <el-upload
              :disabled="roomSendCoverUrlUploadLoading"
              :file-list="roomSendCoverFileList"
              :class="{'upload-but-hide': !isShowRoomSendCoverUpload}"
              action=""
              list-type="picture-card"
              :http-request="roomSendUploadCover"
              :show-file-list="!isShowRoomSendCoverUpload"
              :on-remove="handleRoomSendCoverFileRemove"
              accept="image/*"
            >
              <i slot="default" v-loading="roomSendCoverUrlUploadLoading" class="el-icon-plus" />
            </el-upload>
          </el-form-item>
          <el-form-item v-show="selectedTypeName === 'RED_PACKET'" label=" 房间拆红包大图" prop="roomNotOpenedUrl">
            <el-upload
              :disabled="roomNotOpenedUrlUploadLoading"
              :file-list="roomNotOpenedUrlFileList"
              :class="{'upload-but-hide': !isShowRoomNotOpenedUrlUpload}"
              action=""
              list-type="picture-card"
              :http-request="roomNotOpenedUrlUploadCover"
              :show-file-list="!isShowRoomNotOpenedUrlUpload"
              :on-remove="handleRoomNotOpenedUrlFileRemove"
              accept="image/*"
            >
              <i slot="default" v-loading="roomNotOpenedUrlUploadLoading" class="el-icon-plus" />
            </el-upload>
          </el-form-item>
          <el-form-item v-show="selectedTypeName === 'RED_PACKET'" label="房间红包已打开大图" prop="roomOpenedUrl">
            <el-upload
              :disabled="roomOpenedUrlUploadLoading"
              :file-list="roomOpenedUrlFileList"
              :class="{'upload-but-hide': !isShowRoomOpenedUrlUpload}"
              action=""
              list-type="picture-card"
              :http-request="roomOpenedUrlUploadCover"
              :show-file-list="!isShowRoomOpenedUrlUpload"
              :on-remove="handleRoomOpenedUrlFileRemove"
              accept="image/*"
            >
              <i slot="default" v-loading="roomOpenedUrlUploadLoading" class="el-icon-plus" />
            </el-upload>
          </el-form-item>
          <el-form-item v-show="selectedTypeName === 'RED_PACKET'" label="IM发送页图" prop="imSendCoverUrl">
            <el-upload
              :disabled="imSendCoverUrlUploadLoading"
              :file-list="imSendCoverUrlFileList"
              :class="{'upload-but-hide': !isShowImSendCoverUrlUpload}"
              action=""
              list-type="picture-card"
              :http-request="imSendCoverUrlUploadCover"
              :show-file-list="!isShowImSendCoverUrlUpload"
              :on-remove="handleImSendCoverUrlFileRemove"
              accept="image/*"
            >
              <i slot="default" v-loading="imSendCoverUrlUploadLoading" class="el-icon-plus" />
            </el-upload>
          </el-form-item>
          <el-form-item v-show="selectedTypeName === 'RED_PACKET'" label="IM/房间封面未打开小图" prop="imNotOpenedUrl">
            <el-upload
              :disabled="imNotOpenedUrlUploadLoading"
              :file-list="imNotOpenedUrlFileList"
              :class="{'upload-but-hide': !isShowImNotOpenedUrlUpload}"
              action=""
              list-type="picture-card"
              :http-request="imNotOpenedUrlUploadCover"
              :show-file-list="!isShowImNotOpenedUrlUpload"
              :on-remove="handleImNotOpenedUrlFileRemove"
              accept="image/*"
            >
              <i slot="default" v-loading="imNotOpenedUrlUploadLoading" class="el-icon-plus" />
            </el-upload>
          </el-form-item>
          <el-form-item v-show="selectedTypeName === 'RED_PACKET'" label="IM/房间封面已打开小图" prop="imOpenedUrl">
            <el-upload
              :disabled="imOpenedUrlUploadLoading"
              :file-list="imOpenedUrlFileList"
              :class="{'upload-but-hide': !isShowImOpenedUrlUpload}"
              action=""
              list-type="picture-card"
              :http-request="imOpenedUrlUploadCover"
              :show-file-list="!isShowImOpenedUrlUpload"
              :on-remove="handleImOpenedUrlFileRemove"
              accept="image/*"
            >
              <i slot="default" v-loading="imOpenedUrlUploadLoading" class="el-icon-plus" />
            </el-upload>
          </el-form-item>

          <el-form-item v-show="selectedTypeName === 'RED_PACKET'" label="IM/房间封面已打开小图2" prop="imOpenedUrlTwo">
            <el-upload
              :disabled="imOpenedUrlTwoUploadLoading"
              :file-list="imOpenedUrlTwoFileList"
              :class="{'upload-but-hide': !isShowImOpenedUrlTwoUpload}"
              action=""
              list-type="picture-card"
              :http-request="imOpenedUrlTwoUploadCover"
              :show-file-list="!isShowImOpenedUrlTwoUpload"
              :on-remove="handleImOpenedUrlTwoFileRemove"
              accept="image/*"
            >
              <i slot="default" v-loading="imOpenedUrlTwoUploadLoading" class="el-icon-plus" />
            </el-upload>
          </el-form-item>

          <el-form-item v-show="isSvga && form.type !== 'RED_PACKET'" label="svga" prop="sourceUrl">
            <el-upload
              :disabled="sourceUploadLoading"
              :class="{'upload-but-hide': !isShowSourceUpload}"
              action=""
              :http-request="sourceUpload"
              :on-remove="handleSourceFileRemove"
              :show-file-list="!isShowSourceUpload"
              :file-list="sourceUrlFileList"
              accept=".svga,.pag"
            >
              <div class="upload-but">
                <el-button :loading="sourceUploadLoading" size="small" type="primary">点击上传</el-button>
                <div slot="tip" class="el-upload__tip">
                  只能上传svga/pag文件
                </div>
              </div>
            </el-upload>
            <svgaplayer v-if="!isShowSourceUpload" :url="form.sourceUrl" />
          </el-form-item>
          <el-form-item v-show="selectedTypeName === 'CHAT_BUBBLE'" label="iOS 资源 " prop="sourceUrl">
            <el-upload
              :disabled="sourceUploadLoading"
              :file-list="sourceUrlFileList"
              :class="{'upload-but-hide': !isShowSourceUpload}"
              action=""
              list-type="picture-card"
              :http-request="sourceUpload"
              :show-file-list="!isShowSourceUpload"
              :on-remove="handleSourceFileRemove"
              accept="image/*"
            >
              <i slot="default" v-loading="sourceUploadLoading" class="el-icon-plus" />
            </el-upload>
          </el-form-item>
          <el-form-item v-show="selectedTypeName === 'CHAT_BUBBLE'" label="Android 资源 " prop="expand">
            <el-upload
              :disabled="expandUploadLoading"
              :file-list="expandFileList"
              :class="{'upload-but-hide': !isShowExpandUpload}"
              action=""
              list-type="picture-card"
              :http-request="expandUpload"
              :show-file-list="!isShowExpandUpload"
              :on-remove="handleExpandFileRemove"
              accept="image/*"
            >
              <i slot="default" v-loading="expandUploadLoading" class="el-icon-plus" />
            </el-upload>
          </el-form-item>
          <el-form-item v-if="isBackground" label="房间装扮背景" prop="sourceUrl">
            <el-upload
              :disabled="sourceUploadLoading"
              :file-list="sourceUrlFileList"
              :class="{'upload-but-hide': !isShowSourceUpload}"
              action=""
              list-type="picture-card"
              :http-request="sourceUpload"
              :show-file-list="!isShowSourceUpload"
              :on-remove="handleSourceFileRemove"
              accept="image/*"
            >
              <i slot="default" v-loading="sourceUploadLoading" class="el-icon-plus" />
            </el-upload>
          </el-form-item>
          <el-form-item label="名称" prop="name">
            <el-select
              v-if="form.type === 'NOBLE_VIP'"
              v-model="form.name"
              placeholder="贵族类型"
              style="width: 100%"
              class="filter-item"
            >
              <el-option v-for="item in nobleVipTabs" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>
            <el-input v-else v-model.trim="form.name" type="text" />
          </el-form-item>
          <el-form-item v-if="!isUpdate" label="编码" prop="code">
            <el-input v-model.trim="form.code" type="text" placeholder="编码添加后不能修改" />
          </el-form-item>
          <el-form-item v-if="isPrice" label="底价" prop="amount">
            <el-input v-model.trim="form.amount" v-number type="text" />
          </el-form-item>
          <el-form-item  label="admin free" prop="amount">
            <el-switch
              v-model="form.adminFree"
              active-text="是"
              inactive-text="否"
            ></el-switch>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm()">保存</el-button>
            <el-button @click="handleClose()">取消</el-button>
          </el-form-item>
        </el-form>

      </div>
    </el-dialog>
  </div>
</template>
<script>
import { addPropsSource, updatePropsSource } from '@/api/props'
import { propsTypes, nobleVipTabs } from '@/constant/type'
import { deepClone, getElementUiUploadFile } from '@/utils'
import { mapGetters } from 'vuex'

export default {
  props: {
    row: {
      type: Object,
      require: false,
      default: () => {}
    }
  },
  data() {
    const commonRules = [
      { required: true, message: '必填字段不可为空', trigger: 'blur' }
    ]
    return {
      nobleVipTabs,
      svgaplayerVisable: false,
      coverUploadLoading: false,
      expandUploadLoading: false,
      sourceUploadLoading: false,
      roomSendCoverUrlUploadLoading: false,
      roomNotOpenedUrlUploadLoading: false,
      roomOpenedUrlUploadLoading: false,
      imSendCoverUrlUploadLoading: false,
      imNotOpenedUrlUploadLoading: false,
      imOpenedUrlUploadLoading: false,
      imOpenedUrlTwoUploadLoading: false,
      coverFileList: [],
      sourceUrlFileList: [],
      expandFileList: [],
      roomSendCoverFileList: [],
      roomNotOpenedUrlFileList: [],
      roomOpenedUrlFileList: [],
      imSendCoverUrlFileList: [],
      imNotOpenedUrlFileList: [],
      imOpenedUrlFileList: [],
      imOpenedUrlTwoFileList: [],
      propsTypes,
      isSvga: false,
      isPrice: true,
      isBackground: false,
      submitLoading: false,
      uploadLoading: false,
      selectedTypeName: '',
      form: {
        id: '',
        cover: '',
        sourceUrl: '',
        name: '',
        type: '',
        code: '',
        amount: '',
        expand: '',
        sysOrigin: '',
        roomSendCoverUrl: '',
        roomOpenedUrl: '',
        roomNotOpenedUrl: '',
        imSendCoverUrl: '',
        imOpenedUrl: '',
        imOpenedUrlTwo: '',
        imNotOpenedUrl: '',
        adminFree: false
      },
      formRules: {
        sysOrigin: commonRules,
        cover: commonRules,
        sourceUrl: commonRules,
        name: commonRules,
        type: commonRules,
        code: commonRules,
        amount: commonRules
      },
      formRules2: {
        sysOrigin: commonRules,
        cover: commonRules,
        name: commonRules,
        type: commonRules,
        code: commonRules,
        amount: commonRules
      },
      formRules3: {
        sysOrigin: commonRules,
        cover: commonRules,
        roomSendCoverUrl: commonRules,
        roomOpenedUrl: commonRules,
        roomNotOpenedUrl: commonRules,
        imSendCoverUrl: commonRules,
        imOpenedUrl: commonRules,
        imOpenedUrlTwo: commonRules,
        imNotOpenedUrl: commonRules,
        sourceUrl: commonRules,
        name: commonRules,
        type: commonRules,
        code: commonRules,
        amount: commonRules
      },
      formRulesCustomize: {
        sysOrigin: commonRules,
        cover: commonRules,
        name: commonRules,
        type: commonRules,
        code: commonRules
      }
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms']),
    isShowRoomSendCoverUpload() {
      return !this.form.roomSendCoverUrl
    },
    isShowRoomNotOpenedUrlUpload() {
      return !this.form.roomNotOpenedUrl
    },
    isShowRoomOpenedUrlUpload() {
      return !this.form.roomOpenedUrl
    },
    isShowImSendCoverUrlUpload() {
      return !this.form.imSendCoverUrl
    },
    isShowImNotOpenedUrlUpload() {
      return !this.form.imNotOpenedUrl
    },
    isShowImOpenedUrlUpload() {
      return !this.form.imOpenedUrl
    },
    isShowImOpenedUrlTwoUpload() {
      return !this.form.imOpenedUrlTwo
    },
    isShowCoverUpload() {
      return !this.form.cover
    },
    isShowSourceUpload() {
      return !this.form.sourceUrl
    },
    isShowExpandUpload() {
      return this.form.type === 'CHAT_BUBBLE' && !this.form.expand
    },
    textOptTitle() {
      return this.row && this.row.id ? '修改' : '添加'
    },
    isUpdate() {
      return this.row && this.row.id
    }
  },
  watch: {
    row: {
      handler(newVal) {
        if (!newVal) {
          return
        }

        this.form = deepClone(newVal)
        if (this.form.sourceUrl) {
          this.sourceUrlFileList = getElementUiUploadFile(this.form.sourceUrl)
        }
        if (this.form.cover) {
          this.coverFileList = getElementUiUploadFile(this.form.cover)
        }
        if (this.form.expand) {
          this.expandFileList = getElementUiUploadFile(this.form.expand)
        }
        if (this.form.roomSendCoverUrl) {
          this.roomSendCoverFileList = getElementUiUploadFile(this.form.roomSendCoverUrl)
        }
        if (this.form.roomNotOpenedUrl) {
          this.roomNotOpenedUrlFileList = getElementUiUploadFile(this.form.roomNotOpenedUrl)
        }
        if (this.form.roomOpenedUrl) {
          this.roomOpenedUrlFileList = getElementUiUploadFile(this.form.roomOpenedUrl)
        }
        if (this.form.imSendCoverUrl) {
          this.imSendCoverUrlFileList = getElementUiUploadFile(this.form.imSendCoverUrl)
        }
        if (this.form.imNotOpenedUrl) {
          this.imNotOpenedUrlFileList = getElementUiUploadFile(this.form.imNotOpenedUrl)
        }
        if (this.form.imOpenedUrl) {
          this.imOpenedUrlFileList = getElementUiUploadFile(this.form.imOpenedUrl)
        }
        if (this.form.imOpenedUrlTwo) {
          this.imOpenedUrlTwoFileList = getElementUiUploadFile(this.form.imOpenedUrlTwo)
        }
        this.isSvga = this.form.type !== 'THEME' && this.form.type !== 'LAYOUT' && this.form.type !== 'CHAT_BUBBLE' && this.form.type !== 'RED_PACKET'
        this.selectedTypeName = this.form.type
        this.isBackground = this.form.type === 'LAYOUT'
      },
      immediate: true
    }
  },
  methods: {
    ruleVlide() {
      if (this.selectedTypeName === 'RED_PACKET') {
        return this.formRules3
      }
      if (this.selectedTypeName === 'CUSTOMIZE' || this.selectedTypeName === 'FRAGMENTS') {
        this.form.amount = 0
        return this.formRulesCustomize
      }
      return !this.isSvga ? this.formRules2 : this.formRules
    },
    uploadCover(file) {
      const that = this
      that.coverUploadLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.svgaCover).then(res => {
        that.coverUploadLoading = false
        that.form.cover = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.coverUploadLoading = false
      })
    },
    handleCoverFileRemove(file, fileList) {
      this.form.cover = ''
      this.coverUploadLoading = false
    },
    roomSendUploadCover(file) {
      const that = this
      that.roomSendCoverUrlUploadLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.svgaCover).then(res => {
        that.roomSendCoverUrlUploadLoading = false
        that.form.roomSendCoverUrl = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.roomSendCoverUrlUploadLoading = false
      })
    },
    handleRoomSendCoverFileRemove(file, fileList) {
      this.form.roomSendCoverUrl = ''
      this.roomSendCoverUrlUploadLoading = false
    },
    roomNotOpenedUrlUploadCover(file) {
      const that = this
      that.roomNotOpenedUrlUploadLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.svgaCover).then(res => {
        that.roomNotOpenedUrlUploadLoading = false
        that.form.roomNotOpenedUrl = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.roomNotOpenedUrlUploadLoading = false
      })
    },
    handleRoomNotOpenedUrlFileRemove(file, fileList) {
      this.form.roomNotOpenedUrl = ''
      this.roomNotOpenedUrlUploadLoading = false
    },
    roomOpenedUrlUploadCover(file) {
      const that = this
      that.roomOpenedUrlUploadLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.svgaCover).then(res => {
        that.roomOpenedUrlUploadLoading = false
        that.form.roomOpenedUrl = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.roomOpenedUrlUploadLoading = false
      })
    },
    handleRoomOpenedUrlFileRemove(file, fileList) {
      this.form.roomOpenedUrl = ''
      this.roomOpenedUrlUploadLoading = false
    },

    imSendCoverUrlUploadCover(file) {
      const that = this
      that.imSendCoverUrlUploadLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.svgaCover).then(res => {
        that.imSendCoverUrlUploadLoading = false
        that.form.imSendCoverUrl = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.imSendCoverUrlUploadLoading = false
      })
    },
    handleImSendCoverUrlFileRemove(file, fileList) {
      this.form.imSendCoverUrl = ''
      this.imSendCoverUrlUploadLoading = false
    },

    imNotOpenedUrlUploadCover(file) {
      const that = this
      that.imNotOpenedUrlUploadLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.svgaCover).then(res => {
        that.imNotOpenedUrlUploadLoading = false
        that.form.imNotOpenedUrl = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.imNotOpenedUrlUploadLoading = false
      })
    },
    handleImNotOpenedUrlFileRemove(file, fileList) {
      this.form.imNotOpenedUrl = ''
      this.imNotOpenedUrlUploadLoading = false
    },

    imOpenedUrlUploadCover(file) {
      const that = this
      that.imOpenedUrlUploadLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.svgaCover).then(res => {
        that.imOpenedUrlUploadLoading = false
        that.form.imOpenedUrl = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.imOpenedUrlUploadLoading = false
      })
    },
    handleImOpenedUrlFileRemove(file, fileList) {
      this.form.imOpenedUrl = ''
      this.imOpenedUrlUploadLoading = false
    },

    imOpenedUrlTwoUploadCover(file) {
      const that = this
      that.imOpenedUrlTwoUploadLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.svgaCover).then(res => {
        that.imOpenedUrlTwoUploadLoading = false
        that.form.imOpenedUrlTwo = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.imOpenedUrlTwoUploadLoading = false
      })
    },
    handleImOpenedUrlTwoFileRemove(file, fileList) {
      this.form.imOpenedUrlTwo = ''
      this.imOpenedUrlTwoUploadLoading = false
    },

    sourceUpload(file) {
      const that = this
      that.sourceUploadLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.svgasource).then(res => {
        that.sourceUploadLoading = false
        that.form.sourceUrl = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.sourceUploadLoading = false
      })
    },
    handleSourceFileRemove(file, fileList) {
      this.form.sourceUrl = ''
      this.sourceUploadLoading = false
    },
    expandUpload(file) {
      const that = this
      that.expandUploadLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.svgasource).then(res => {
        that.expandUploadLoading = false
        that.form.expand = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.expandUploadLoading = false
      })
    },
    handleExpandFileRemove(file, fileList) {
      this.form.expand = ''
      this.expandUploadLoading = false
    },
    handleClose() {
      this.$emit('close')
    },
    selectdTheme(typeName) {
      this.isSvga = true
      this.isPrice = true
      this.isBackground = false
      this.selectedTypeName = typeName
      if (typeName === 'THEME' || typeName === 'CHAT_BUBBLE') {
        this.isSvga = false
        return
      }
      if (typeName === 'LAYOUT') {
        this.isSvga = false
        this.isBackground = true
        return
      }
      if (typeName === 'CUSTOMIZE' || typeName === 'FRAGMENTS') {
        this.isPrice = false
        return
      }
    },
    submitForm() {
      const that = this

      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.submitLoading = true
        if (that.isUpdate) {
          updatePropsSource(that.form).then(res => {
            that.submitLoading = false
            that.$emit('success', res)
          }).catch(er => {
            that.submitLoading = false
            that.$emit('fail')
            console.error(er)
          })
          return
        }
        addPropsSource(that.form).then(res => {
          that.submitLoading = false
          that.$emit('success', res)
        }).catch(er => {
          that.submitLoading = false
          that.$emit('fail')
          console.error(er)
        })
      })
    },
    clickSvgaplayer() {
      this.svgaplayerVisable = true
    }
  }
}
</script>
<style scoped lang="scss">

</style>
