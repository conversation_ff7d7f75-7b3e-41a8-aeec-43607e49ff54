<template>
  <div class="app-container">

    <div v-if="buttonPermissions.includes('props:config:query')">
      <div class="filter-container">
        <el-select
          v-model="listQuery.sysOrigin"
          placeholder="归属系统"
          style="width: 120px"
          class="filter-item"
          @change="handleSearch"
        >
          <el-option
            v-for="(item, index) in permissionsSysOriginPlatforms"
            :key="index"
            :label="item.label"
            :value="item.value"
          >
            <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
            <span style="float: left;margin-left:10px">{{ item.label }}</span>
          </el-option>
        </el-select>

        <el-select
          v-model="listQuery.type"
          placeholder="类型"
          style="width: 120px"
          class="filter-item"
          @change="handleSearch"
        >
          <el-option v-for="item in propsTypes" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>

        <el-select
          v-model="listQuery.del"
          placeholder="上/下架"
          style="width: 120px"
          class="filter-item"
          clearable
          @change="handleSearch"
        >
          <el-option v-for="item in showcaseTypes" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>

        <el-input
          v-model.trim="listQuery.id"
          v-number
          placeholder="ID"
          clearable
          style="width: 200px;"
          class="filter-item"
        />

        <el-input
          v-model.trim="listQuery.name"
          placeholder="名称"
          clearable
          style="width: 200px;"
          class="filter-item"
        />

        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleSearch"
        >
          搜索
        </el-button>
        <el-button
          v-if="buttonPermissions.includes('props:config:add')"
          class="filter-item"
          type="primary"
          icon="el-icon-edit"
          @click="handleCreate"
        >
          添加
        </el-button>
        <el-button
          v-if="buttonPermissions.includes('props:config:sales:total')"
          class="filter-item"
          type="primary"
          icon="el-icon-monitor"
          @click="clickQuerySalesOverview"
        >
          销售情况
        </el-button>
      </div>
      <el-table
        v-loading="listLoading"
        :data="list"
        element-loading-text="Loading"
        fit
        highlight-current-row
        @cell-mouse-enter="handleMouseEnter"
      >
        <el-table-column prop="id" width="200" label="ID" align="center" />
        <el-table-column prop="code" width="150" label="编号" align="center" />
        <el-table-column prop="typeName" width="80" label="类型" align="center" />
        <el-table-column prop="name" label="名称" align="center" />
        <el-table-column label="资源" width="80" align="center">
          <template slot-scope="scope">
            <div class="preview-img">
              <el-image
                style="width: 100%; height: 100%"
                :src="scope.row.cover"
                :preview-src-list="[scope.row.cover]"
              />
              <div :v-if="scope.row.type != 'THMEM'" class="preview-svga">
                <svgaplayer
                  type="popover"
                  :url="scope.row.sourceUrl"
                />
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column width="200" label="上/下架" align="center">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.del"
              :active-value="false"
              :inactive-value="true"
              @change="handleSwitchChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="amount" width="80" label="底价" align="center" />
        <el-table-column v-if="buttonPermissions.includes('props:config:edit') || buttonPermissions.includes('props:config:sales:single')" fixed="right" label="操作" align="center" width="200">
          <template slot-scope="scope">
            <el-button v-if="buttonPermissions.includes('props:config:edit')" type="text" @click.native="handleUpdate(scope.row)">编辑</el-button>
            <el-button v-if="buttonPermissions.includes('props:config:sales:single')" type="text" @click.native="clickQuerySales(scope.row)">销售情况</el-button>
          </template>
        </el-table-column>
        <template>
            <el-table-column prop="adminFree" label="admin free" align="center">
              <template #default="scope">
                <span>{{ scope.row.adminFree ? '是' : '否' }}</span>
              </template>
            </el-table-column>
        </template>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="listQuery.cursor"
        :limit.sync="listQuery.limit"
        @pagination="renderData"
      />

      <form-edit
        v-if="formEditVisable"
        :row="thisRow"
        @success="formEditSuccess"
        @close="formEditVisable=false"
      />
      <sales-mothly-charts-dialog
        v-if="salesMothlyChartsDialogVisible"
        :props-id="thisRow.id"
        @close="salesMothlyChartsDialogVisible=false"
      />
      <props-sales-overview-charts-dialog
        v-if="salesOverviewChartsDialogVisible"
        @close="salesOverviewChartsDialogVisible=false"
      />
    </div>
    <div v-else style="text-align: center;">
      抱歉您无权查看，请联系管理员开通查看权限 【道具管理-资源配置-查看，props:config:query】
    </div>
  </div>
</template>

<script>
import { pagePropsSource, offShelfPropsSource } from '@/api/props'
import Pagination from '@/components/Pagination'
import { propsTypes } from '@/constant/type'
import FormEdit from './form-edit'
import SalesMothlyChartsDialog from './sales-mothly-charts-dialog'
import PropsSalesOverviewChartsDialog from '@/components/data/PropsSalesOverviewCharts/dialog'
import { mapGetters } from 'vuex'

export default {
  components: { Pagination, FormEdit, SalesMothlyChartsDialog, PropsSalesOverviewChartsDialog },
  data() {
    return {
      salesOverviewChartsDialogVisible: false,
      salesMothlyChartsDialogVisible: false,
      showcaseTypes: [
        { value: true, name: '下架' },
        { value: false, name: '上架' }
      ],
      formEditVisable: false,
      thisRow: {},
      thatSelectedUserId: {},
      propsTypes,
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        id: '',
        name: '',
        type: 'AVATAR_FRAME',
        del: false,
        sysOrigin: ''
      },
      listLoading: true
    }
  },
  computed: {
    ...mapGetters(['buttonPermissions', 'permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    this.renderData(true)
  },
  methods: {
    renderData(isClean) {
      const that = this
      that.listLoading = true
      if (isClean === true) {
        that.listQuery.cursor = 1
      }
      pagePropsSource(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleCreate() {
      this.thisRow = null
      this.formEditVisable = true
    },
    handleUpdate(row) {
      this.formEditVisable = true
    },
    clickQuerySales(row) {
      this.thisRow = row
      this.salesMothlyChartsDialogVisible = true
    },
    handleSwitchChange(row) {
      offShelfPropsSource(row.id, row.del)
        .then(res => {})
        .catch(er => {
          row.del = !row.del
        })
    },
    handleSearch() {
      this.renderData(true)
    },
    queryUserDetails(row) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = row.id
    },
    handleMouseEnter(row) {
      this.thisRow = row
      this.thatSelectedUserId = row.id
    },
    formEditSuccess() {
      this.formEditVisable = false
      this.renderData(false)
    },
    clickQuerySalesOverview() {
      this.salesOverviewChartsDialogVisible = true
    }
  }
}
</script>
