<template>
  <div class="register-logout-count-charts">
    <div id="charts" ref="charts" :style="'width: 100%;height:'+ height +';'" />
  </div>
</template>

<script>

export default {
  props: {
    height: {
      type: String,
      default: '600px'
    },
    chartsData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      charts: null
    }
  },
  watch: {
    chartsData: {
      immediate: true,
      handler(newVal) {
        if (this.charts) {
          this.charts.clear()
          this.renderCharts()
        }
      }
    }
  },
  created() {
  },
  mounted() {
    const that = this
    that.$nextTick(() => {
      that.charts = that.$echarts.init(that.$refs.charts)
      that.renderCharts()
      window.addEventListener('resize', () => {
        that.charts.resize()
      })
    })
  },
  methods: {
    proccessData() {
      const that = this
      const charts = []
      if (that.chartsData.length > 0) {
        that.chartsData.forEach(item => {
          charts.push([item.date, item.saleQuantity || 0])
        })
      }
      return charts
    },
    renderCharts() {
      const that = this
      that.charts.setOption({
        color: ['#66b3ff', '#ce90e8', '#ff9c6e', '#5cdbd3'],
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(50,50,50,0.5)',
          axisPointer: {
            type: 'cross',
            label: {
              lineStyle: { color: '#009688' },
              crossStyle: { color: '#008acd' },
              shadowStyle: { color: 'rgba(200,200,200,0.2)' }
            }
          }
        },
        grid: {
          x: 20,
          y: 50,
          x2: 20,
          y2: 0,
          containLabel: true,
          borderColor: '#eee'
        },
        toolbox: { color: ['#1e90ff', '#1e90ff', '#1e90ff', '#1e90ff'], effectiveColor: '#ff4500' },
        xAxis: [
          {
            show: true,
            type: 'category',
            boundaryGap: false,
            splitArea: {
              show: true,
              areaStyle: { color: ['rgba(250,250,250,0.1)', 'rgba(200,200,200,0.1)'] }
            },
            axisLabel: {
              rotate: 38
            },
            axisLine: { lineStyle: { color: '#b7bdc7' }},
            splitLine: { lineStyle: { color: ['#eee'] }},
            splitNumber: 24
          }
        ],
        yAxis: [{
          type: 'value',
          axisTick: { show: true, length: 0 },
          splitNumber: 5,
          splitLine: { lineStyle: { color: ['#eee'] }},
          axisLine: { lineStyle: { color: '#b7bdc7' }}
        }],
        legend: {
          y: 10,
          textStyle: { color: '#8e929b' }
        },
        series: [
          {
            name: '每日销售情况',
            type: 'line',
            stack: '每日销售情况',
            smooth: 0.6,
            symbol: 'none',
            symbolSize: 10,
            data: that.proccessData(),
            areaStyle: {}
          }]
      }, true)
    }
  }
}
</script>
