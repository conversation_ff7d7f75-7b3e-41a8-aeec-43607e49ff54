<template>
  <div class="props-config-edit">
    <el-dialog
      title="销售报表"
      :visible="true"
      :before-close="handleClose"
      :close-on-click-modal="false"
      width="80%"
    >
      <div class="charts-body">
        <div class="filter-container">
          <el-select
            v-model="listQuery.sysOrigin"
            placeholder="系统"
            style="width: 120px"
            class="filter-item"
            @change="renderData"
          >
            <el-option
              v-for="item in permissionsSysOriginPlatforms"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
              <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
              <span style="float: left;margin-left:10px">{{ item.label }}</span>
            </el-option>
          </el-select>
          <div class="filter-item">
            <el-date-picker
              v-model="listQuery.date"
              :clearable="false"
              value-format="yyyyMM"
              type="month"
              placeholder="选择月"
              :editable="false"
              @change="renderData"
            />
          </div>
          <div class="filter-item">
            月销售总额: {{ sale.monthTotal || '-' }}、年售额:  {{ sale.yearTotal || '-' }}
          </div>
        </div>
        <div v-loading="loading">
          <line-graph-charts :charts-data="sale.propsSaleQuotas" height="300px" />
        </div>

      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getPropsSaleSinge } from '@/api/statistics'
import { formatDate } from '@/utils'
import { mapGetters } from 'vuex'
import LineGraphCharts from './line-graph-charts'
export default {
  components: { LineGraphCharts },
  props: {
    propsId: {
      type: String,
      require: false,
      default: ''
    }
  },
  data() {
    return {
      listQuery: {
        sysOrigin: '',
        dateType: 'DAY',
        date: formatDate(new Date(), 'yyyyMM'),
        propsSourceId: ''
      },
      loading: false,
      sale: {}
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    propsId: {
      handler(newVal) {
        if (!newVal) {
          return
        }
        if (this.permissionsSysOriginPlatforms && this.permissionsSysOriginPlatforms.length > 0) {
          this.listQuery.sysOrigin = this.permissionsSysOriginPlatforms[0].value
        }
        this.listQuery.propsSourceId = newVal
        this.renderData()
      },
      immediate: true
    }
  },
  methods: {
    renderData() {
      const that = this
      that.loading = true
      getPropsSaleSinge(that.listQuery).then(res => {
        that.loading = false
        that.sale = res.body || {}
      }).catch(er => {
        that.loading = false
      })
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
<style scoped lang="scss">

</style>
