<template>
  <div class="props-activity-rule-config-edit-rule-drawer">
    <el-drawer
      :title="textOptTitle"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div class="drawer-form">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-position="right"
          label-width="80px"
        >
          <el-form-item label="系统" prop="sysOrigin">
            <el-select
              v-model="form.sysOrigin"
              placeholder="系统"
              style="width:100%;"
              clearable
              :disabled="isUpdate"
            >
              <el-option
                v-for="item in permissionsSysOriginPlatforms"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                class="filter-item"
              >
                <span style="float: left;">
                  <sys-origin-icon :icon="item.value" :desc="item.value"
                /></span>
                <span style="float: left;margin-left:10px">{{
                  item.label
                }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="activityType" label="类型">
            <el-select
              v-model="form.activityType"
              placeholder="类型"
              clearable
              style="width:100%;"
              :disabled="isUpdate"
              @change="changeActivityType"
            >
              <el-option
                v-for="item in propActivityTypes"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-if="form.sysOrigin && activeComponent">
            <component
              :is="activeComponent"
              ref="ruleForm"
              :tips="activeTips"
              :sys-origin="form.sysOrigin"
              :form="tmpRuleSetting"
            />
          </el-form-item>
          <el-form-item label="规则描述" prop="ruleDescription">
            <el-input
              v-model.trim="form.ruleDescription"
              type="textarea"
              resize="none"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
          <el-form-item
            v-if="form.activityType !== 'CRYSTAL'"
            label="排序"
            prop="sort"
          >
            <el-input v-model.trim="form.sort" v-number />
          </el-form-item>
        </el-form>
      </div>
      <div class="drawer-footer">
        <el-button @click="handleClose()">取消</el-button>
        <el-button
          type="primary"
          :loading="submitLoading"
          :disabled="submitLoading"
          @click="submitForm()"
          >保存</el-button
        >
      </div>
    </el-drawer>
  </div>
</template>
<script>
import { savePropsActivityRuleConfig } from "@/api/props";
import FirstChargeReward from "./components/first-charge-reward";
import InputQuantity from "./components/input-quantity";
import InputQuantityMark from "./components/input-quantity-mark";
import LuckyBox from "./components/lucky-box";
import GameWeeklyTasks from "./components/game-weekly-tasks";
import Crystal from "./components/crystal";
import GameFruit from "./components/game-fruit";
import { mapGetters } from "vuex";
import { deepClone } from "@/utils";
import { propActivityTypes, propActivityTypesHelp } from "@/constant/type";
export default {
  name: "PropsActivityRuleConfigEditRuleDrawer",
  components: {
    FirstChargeReward,
    InputQuantity,
    LuckyBox,
    Crystal,
    GameWeeklyTasks,
    InputQuantityMark,
    GameFruit
  },
  props: {
    row: {
      type: Object,
      required: true,
      default: () => {}
    }
  },
  data() {
    return {
      activeComponent: "",
      activeTips: "",
      tmpRuleSetting: {},
      ruleTables: {
        THE_BIG_WHEEL: {
          component: "InputQuantity",
          form: {
            quantity: ""
          }
        },
        Independence_Day_of_India: {
          component: "InputQuantity",
          form: {
            quantity: ""
          }
        },
        ACTIVE_AGENT_ANCHOR_COUNT_REWARD: {
          component: "InputQuantity",
          form: {
            quantity: ""
          }
        },
        ACTIVE_AGENT_MONTH_TARGET_REWARD: {
          component: "InputQuantity",
          form: {
            quantity: ""
          }
        },
        ACTIVE_ANCHOR_MONTH_TARGET_REWARD: {
          component: "InputQuantity",
          form: {
            quantity: ""
          }
        },
        ACTIVE_ANCHOR_DAY_TARGET_REWARD: {
          component: "InputQuantity",
          form: {
            quantity: ""
          }
        },
        ACTIVITY_WORLD_CUP_REWARDS: {
          component: "InputQuantity",
          form: {
            quantity: ""
          }
        },
        SVIP_REWARD: {
          component: "InputQuantityMark",
          form: {
            quantity: "",
            mark: ""
          }
        },
        FIRST_CHARGE_REWARD: {
          component: "FirstChargeReward",
          form: {
            productId: ""
          }
        },
        INVITE_USER_REWARDS: {
          component: "InputQuantity",
          form: {
            quantity: ""
          }
        },
        STAR: {
          component: "InputQuantity",
          form: {
            quantity: ""
          }
        },
        CONSUMPTION_ACTIVITY: {
          component: "InputQuantity",
          form: {
            quantity: ""
          }
        },
        CUMULATIVE_RECHARGE: {
          component: "InputQuantity",
          form: {
            quantity: ""
          }
        },
        ROOM_REWARD: {
          component: "InputQuantity",
          form: {
            quantity: ""
          }
        },
        CP_REWARD: {
          component: "InputQuantity",
          form: {
            quantity: ""
          }
        },
        WEEK_CP_GIFT: {
          component: "InputQuantity",
          form: {
            quantity: "0"
          }
        },
        CRYSTAL_TOP: {
          component: "InputQuantity",
          form: {
            quantity: ""
          }
        },
        CRYSTAL_LUCKY_BOX: {
          component: "InputQuantity",
          form: {
            quantity: ""
          }
        },
        WEEK_STAR: {
          component: "InputQuantity",
          form: {
            quantity: "0"
          }
        },
        GAME_EXPLOSION_CRYSTAL: {
          component: "InputQuantity",
          form: {
            quantity: "0"
          }
        },
        WEEK_KING: {
          component: "InputQuantity",
          form: {
            quantity: "0"
          }
        },
        WEEK_QUEEN: {
          component: "InputQuantity",
          form: {
            quantity: "0"
          }
        },
        ROOM_PK: {
          component: "InputQuantity",
          form: {
            quantity: "0"
          }
        },
        GAME_KING: {
          component: "InputQuantity",
          form: {
            quantity: "0"
          }
        },
        DAILY_REGISTER: {
          component: "InputQuantity",
          form: {
            quantity: "0"
          }
        },
        LUCKY_GIFT_REWARD: {
          component: "InputQuantity",
          form: {
            quantity: "0"
          }
        },
        APRIL_ACTIVITY_FRAGMENT: {
          component: "InputQuantity",
          form: {
            quantity: "0"
          }
        },
        CUMULATIVE_RECHARGE_LOTTERY: {
          component: "InputQuantity",
          tips: "抽奖要求充值金额(列如: 9.9)",
          form: {
            quantity: ""
          }
        },
        LUCKY_BOX: {
          component: "LuckyBox",
          form: [
            { id: 1, quantity: "", opportunityNumber: "" },
            { id: 2, quantity: "", opportunityNumber: "" },
            { id: 3, quantity: "", opportunityNumber: "" }
          ]
        },
        CRYSTAL: {
          component: "Crystal",
          form: {
            level: "",
            milestone: "",
            smallIcon: "",
            mediumIcon: "",
            sourceUrl: ""
          }
        },
        WEEKLY_GAME_TASKS: {
          component: "GameWeeklyTasks",
          form: {
            gameConfId: "",
            target: ""
          }
        },
        ACTIVITY_FRIENDSHIP_CARD_REWARDS: {
          component: "InputQuantity",
          form: {
            quantity: ""
          }
        },
        GAME_KTV_WEEK_RANK_REWARD: {
          component: "InputQuantity",
          form: { quantity: "0" }
        },
        AGENT_ACTIVE_WEEK_REWARD: {
          component: "InputQuantity",
          form: { quantity: "0" }
        },
        AGENT_ACTIVE_MONTH_REWARD: {
          component: "InputQuantity",
          form: { quantity: "0" }
        },

        GAME_FRUIT_BOX_REWARD_WIN: {
          component: "GameFruit",
          form: {
            quantity: "",
            status: ""
          }
        },
        GAME_FRUIT_BOX_REWARD_TIMES: {
          component: "GameFruit",
          form: {
            quantity: "",
            status: ""
          }
        }
      },
      propActivityTypes,
      propActivityTypesHelp,
      form: {
        id: "",
        sort: "",
        jsonData: "",
        sysOrigin: "",
        activityType: "",
        ruleDescription: ""
      },
      submitLoading: false,
      rules: {
        sysOrigin: [{ required: true, message: "必填字段", trigger: "blur" }],
        activityType: [
          { required: true, message: "必填字段", trigger: "blur" }
        ],
        jsonData: [{ required: true, message: "必填字段", trigger: "blur" }],
        sort: [{ required: true, message: "必填字段", trigger: "blur" }]
      }
    };
  },
  computed: {
    isUpdate() {
      return !!(this.row && this.row.id);
    },
    textOptTitle() {
      return this.isUpdate ? "修改" : "添加";
    },
    ...mapGetters(["permissionsSysOriginPlatforms"])
  },
  watch: {
    row: {
      handler(newVal) {
        if (!newVal) {
          return;
        }
        this.form = deepClone(newVal);
        this.changeActivityType();
        if (this.form.jsonData) {
          this.tmpRuleSetting = JSON.parse(this.form.jsonData);
        }
      },
      immediate: true
    }
  },
  created() {},
  methods: {
    handleClose() {
      this.$emit("close");
    },
    changeActivityType() {
      const that = this;
      const item = that.ruleTables[that.form.activityType];
      if (!item) {
        that.activeComponent = "";
        that.activeTips = "";
        that.tmpRuleSetting = {};
        return;
      }
      that.activeComponent = item.component;
      that.activeTips = item.tips;
      that.tmpRuleSetting = deepClone(item.form);
    },
    submitForm() {
      const that = this;
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error("error submit!!");
          return false;
        }

        if (that.form.sysOrigin && that.activeComponent) {
          that.$refs.ruleForm
            .validateForm()
            .then(() => {
              that.form.jsonData = JSON.stringify(that.tmpRuleSetting);
              if (this.form.activityType === "CRYSTAL") {
                that.form.sort = that.tmpRuleSetting.level;
              }
              that.submitLoading = true;
              savePropsActivityRuleConfig(that.form)
                .then(res => {
                  that.submitLoading = false;
                  that.$emit("success");
                })
                .catch(er => {
                  that.submitLoading = false;
                  that.$emit("fial", er);
                });
            })
            .catch(() => {
              console.error("ruleForm error submit!!");
            });
        } else {
          if (this.form.activityType === "CRYSTAL") {
            that.form.sort = that.tmpRuleSetting.level;
          }
          that.submitLoading = true;
          savePropsActivityRuleConfig(that.form)
            .then(res => {
              that.submitLoading = false;
              that.$emit("success");
            })
            .catch(er => {
              that.submitLoading = false;
              that.$emit("fial", er);
            });
        }
      });
    }
  }
};
</script>
<style scoped lang="scss">
.content {
  padding: 20px;
}
</style>
