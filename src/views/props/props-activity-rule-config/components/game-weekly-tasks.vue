<template>
  <div class="game-weekly-tasks">
    <div v-if="tips" class="blockquote">{{ tips }}</div>
    <el-form
      ref="ruleForm"
      v-loading="gameListLoading"
      class="rule-form"
      :model="form"
      :rules="ruleForm"
      label-position="top"
    >
      <el-form-item prop="gameConfId" label="游戏" style="margin-bottom: 10px">
        <el-select
          v-model="form.gameConfId"
          placeholder="请选择游戏"
          clearable
          style="width:100%;"
        >
          <el-option
            v-for="(item, index) in gameList"
            :key="index"
            :value="item.id"
            :label="item.name"
          >
            <div class="flex-l"><img :src="item.cover" width="30" height="30">&nbsp;&nbsp;{{ item.name }}</div>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="target" label="目标">
        <el-input v-model.trim="form.target" v-number placeholder="目标" />
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { flowGameMatchConfig } from '@/api/sys'
export default {
  name: 'PropsActivityRuleConfigEditRuleGameWeekyTasks',
  components: {},
  props: {
    tips: {
      type: String,
      required: false,
      default: ''
    },
    sysOrigin: {
      type: String,
      required: true
    },
    form: {
      type: Object,
      required: true,
      default: () => {}
    }
  },
  data() {
    return {
      ruleForm: {
        gameConfId: [{ required: true, message: '必填字段', trigger: 'blur' }],
        target: [{ required: true, message: '必填字段', trigger: 'blur' }]
      },
      gameList: [],
      gameListLoading: false
    }
  },
  computed: {
  },
  watch: {
    form: {
      handler(newVal) {
        if (!newVal || newVal.length <= 0) {
          return
        }
        this.ruleForm.list = newVal
      },
      immediate: true
    }
  },
  created() {
    this.loadGame()
  },
  methods: {
    validateForm() {
      const that = this
      return new Promise((resolve, reject) => {
        that.$refs.ruleForm.validate(valid => {
          if (!valid) {
            reject()
            return false
          }
          resolve()
        })
      })
    },
    checkGameExists(gameConfId) {
      return !!(this.gameMap[gameConfId] && this.gameMap[gameConfId].id)
    },
    clickDelRow(item, index) {
      this.ruleForm.list.splice(index, 1)
    },
    loadGame() {
      const that = this
      that.gameListLoading = true
      flowGameMatchConfig({ sysOrigin: that.sysOrigin, limit: 50 }).then(res => {
        const { body } = res
        that.gameListLoading = false
        that.gameList = body || []
      }).catch(er => {
        that.gameListLoading = false
        console.error('flowGameMatchConfig', er)
      })
    }
  }
}
</script>
<style scoped lang="scss">
.clean {
  cursor: pointer;
  i {
    font-size: 28px;
  }
}
.clean-tips {
  font-size: 10px;
  color: #999;
}
</style>
