<template>
  <div class="input-quantity-mark">
    <div v-if="tips" class="blockquote">{{ tips }}</div>
    <el-form
      ref="ruleForm"
      class="rule-form"
      :model="form"
      :rules="ruleValidateForm"
      label-position="top"
    >

      <el-form-item
        class="filter-item"
        label="标识"
        prop="mark"
      >
        <el-input v-model.trim="form.mark" />
      </el-form-item>
      <el-form-item
        class="filter-item"
        label="数量"
        prop="quantity"
      >
        <el-input v-model.trim="form.quantity" v-number />
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'PropsActivityRuleConfigEditRuleInputQuantityMark',
  components: {},
  props: {
    tips: {
      type: String,
      required: false,
      default: ''
    },
    sysOrigin: {
      type: String,
      required: true
    },
    form: {
      type: Object,
      required: true,
      default: () => {}
    }
  },
  data() {
    return {
      ruleValidateForm: {
        quantity: [{ required: true, message: '必填字段', trigger: 'blur' }],
        mark: [{ required: true, message: '必填字段', trigger: 'blur' }]
      }
    }
  },
  computed: {
  },
  created() {
  },
  methods: {
    validateForm() {
      const that = this
      return new Promise((resolve, reject) => {
        that.$refs.ruleForm.validate(valid => {
          if (!valid) {
            reject()
            return false
          }
          resolve()
        })
      })
    }

  }
}
</script>

