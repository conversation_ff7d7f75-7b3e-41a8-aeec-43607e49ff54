<template>
  <div class="input-quantity">
    <div v-if="tips" class="blockquote">{{ tips }}</div>
    <el-form
      ref="ruleForm"
      class="rule-form"
      :model="form"
      :rules="ruleValidateForm"
      label-position="top"
    >

      <el-form-item
        class="filter-item"
        label="等级"
        prop="level"
        style="margin-bottom: 10px;"
      >
        <el-select
          v-model="form.level"
          placeholder="请选择等级"
          clearable
          style="width:100%;"
        >
          <el-option
            v-for="item in crystalLevels"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item
        class="filter-item"
        label="里程碑"
        prop="milestone"
        style="margin-bottom: 10px;"
      >
        <el-input v-model.trim="form.milestone" v-number type="text" />
      </el-form-item>

      <el-form-item
        class="filter-item"
        label="图标Icon"
      >
        <el-col :span="12">
          <el-form-item prop="smallIcon" style="margin-bottom: 10px;">
            <el-upload
              :disabled="uploadSmallIconLoading"
              :file-list="uploadSmallIconFileList"
              :class="{'upload-but-hide': !isShowUploadSmallIcon}"
              action=""
              list-type="picture-card"
              :http-request="uploadSmallIconCover"
              :show-file-list="!isShowUploadSmallIcon"
              :on-remove="removeUploadSmallIconCover"
              accept="image/*"
            >
              <i slot="default" v-loading="uploadSmallIconLoading" class="el-icon-plus" />
            </el-upload>
            <span>小号</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="mediumIcon" style="margin-bottom: 10px;">
            <el-upload
              :disabled="uploadMediumIconLoading"
              :file-list="uploadMediumIconFileList"
              :class="{'upload-but-hide': !isShowUploadMediumIcon}"
              action=""
              list-type="picture-card"
              :http-request="uploadMediumIconCover"
              :show-file-list="!isShowUploadMediumIcon"
              :on-remove="removeUploadMediumIconCover"
              accept="image/*"
            >
              <i slot="default" v-loading="uploadMediumIconLoading" class="el-icon-plus" />
            </el-upload>
            <span>中号</span>
          </el-form-item>
        </el-col>
      </el-form-item>
      <el-form-item
        class="filter-item"
        label="动画资源图"
        prop="sourceUrl"
      >
        <el-upload
          :disabled="uploadSouceUrlLoading"
          :class="{'upload-but-hide': !isShowUploadSourceUrl}"
          action=""
          :http-request="uloadSourceUrl"
          :on-remove="removeSourceFile"
          :show-file-list="!isShowUploadSourceUrl"
          :file-list="uploadSouceUrlFileList"
          accept=".svga,.pag"
        >
          <div class="upload-but">
            <el-button :loading="uploadSouceUrlLoading" size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传svga/pag文件</div>
          </div>
        </el-upload>
        <svgaplayer v-if="!isShowUploadSourceUrl" :url="form.sourceUrl" />

      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { getElementUiUploadFile } from '@/utils'
export default {
  name: 'PropsActivityRuleConfigEditRuleCrystal',
  components: {},
  props: {
    tips: {
      type: String,
      required: false,
      default: ''
    },
    sysOrigin: {
      type: String,
      required: true
    },
    form: {
      type: Object,
      required: true,
      default: () => {}
    }
  },
  data() {
    return {
      uploadSmallIconLoading: false,
      uploadSmallIconFileList: [],
      uploadMediumIconLoading: false,
      uploadMediumIconFileList: [],
      uploadSouceUrlLoading: false,
      uploadSouceUrlFileList: [],
      crystalLevels: [
        { value: 1, name: '1' },
        { value: 2, name: '2' },
        { value: 3, name: '3' },
        { value: 4, name: '4' },
        { value: 5, name: '5' }
      ],
      ruleValidateForm: {
        milestone: [{ required: true, message: '必填字段', trigger: 'blur' }],
        level: [{ required: true, message: '必填字段', trigger: 'blur' }],
        smallIcon: [{ required: true, message: '必填字段', trigger: 'blur' }],
        mediumIcon: [{ required: true, message: '必填字段', trigger: 'blur' }],
        sourceUrl: [{ required: true, message: '必填字段', trigger: 'blur' }]
      }
    }
  },
  computed: {
    isShowUploadSmallIcon() {
      return !this.form.smallIcon
    },
    isShowUploadMediumIcon() {
      return !this.form.mediumIcon
    },
    isShowUploadSourceUrl() {
      return !this.form.sourceUrl
    }
  },
  watch: {
    form: {
      handler(newVal) {
        if (!newVal) {
          return
        }
        if (this.form.smallIcon) {
          this.uploadSmallIconFileList = getElementUiUploadFile(this.form.smallIcon)
        }
        if (this.form.mediumIcon) {
          this.uploadMediumIconFileList = getElementUiUploadFile(this.form.mediumIcon)
        }
        if (this.form.sourceUrl) {
          this.uploadSouceUrlFileList = getElementUiUploadFile(this.form.sourceUrl)
        }
      },
      immediate: true
    }
  },
  created() {
  },
  methods: {
    validateForm() {
      const that = this
      return new Promise((resolve, reject) => {
        that.$refs.ruleForm.validate(valid => {
          if (!valid) {
            reject()
            return false
          }
          resolve()
        })
      })
    },
    uploadSmallIconCover(file) {
      const that = this
      that.uploadSmallIconLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.svgaCover).then(res => {
        that.uploadSmallIconLoading = false
        that.form.smallIcon = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.uploadSmallIconLoading = false
      })
    },
    removeUploadSmallIconCover(file, fileList) {
      this.form.smallIcon = ''
      this.uploadSmallIconLoading = false
    },
    uploadMediumIconCover(file) {
      const that = this
      that.uploadMediumIconLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.svgaCover).then(res => {
        that.uploadMediumIconLoading = false
        that.form.mediumIcon = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.uploadMediumIconLoading = false
      })
    },
    removeUploadMediumIconCover(file, fileList) {
      this.form.mediumIcon = ''
      this.uploadMediumIconLoading = false
    },
    uloadSourceUrl(file) {
      const that = this
      that.uploadSouceUrlLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.svgasource).then(res => {
        that.uploadSouceUrlLoading = false
        that.form.sourceUrl = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.uploadSouceUrlLoading = false
      })
    },
    removeSourceFile(file, fileList) {
      this.form.sourceUrl = ''
      this.uploadSouceUrlLoading = false
    }

  }
}
</script>

