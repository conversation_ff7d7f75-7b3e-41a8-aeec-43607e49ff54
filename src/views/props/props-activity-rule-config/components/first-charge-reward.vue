<template>
  <div class="first-charge-reward">
    <div v-if="tips" class="blockquote">{{ tips }}</div>
    <el-form
      ref="ruleForm"
      class="rule-form"
      :model="form"
      :rules="ruleValidateForm"
      label-position="top"
    >

      <el-form-item
        class="filter-item"
        label="内购产品"
        prop="productId"
      >
        <el-select
          v-model="form.productId"
          v-loading="firstChargeRewardsLoading"
          placeholder="请选择产品"
          clearable
          style="width:100%;"
        >
          <el-option
            v-for="(item, index) in firstChargeRewards"
            :key="index"
            :label="item.unitPrice + ' - ' + item.productId + ' -' + item.description "
            :value="item.productId"
          />
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { listFirstChargeRewards } from '@/api/product'
export default {
  name: 'PropsActivityRuleConfigEditRuleFirstChargeReward',
  components: {},
  props: {
    tips: {
      type: String,
      required: false,
      default: ''
    },
    sysOrigin: {
      type: String,
      required: true
    },
    form: {
      type: Object,
      required: true,
      default: () => {}
    }
  },
  data() {
    return {
      ruleValidateForm: {
        productId: [{ required: true, message: '必填字段', trigger: 'blur' }]
      },
      firstChargeRewards: [],
      firstChargeRewardsLoading: false
    }
  },
  computed: {
  },
  // watch: {
  //   form: {
  //     handler(newVal) {
  //       if (!newVal) {
  //         return
  //       }
  //       this.ruleForm = deepClone(newVal)
  //     },
  //     immediate: true
  //   }
  // },
  created() {
    this.loadFirstChargeRewards()
  },
  methods: {
    validateForm() {
      const that = this
      return new Promise((resolve, reject) => {
        that.$refs.ruleForm.validate(valid => {
          if (!valid) {
            reject()
            return false
          }
          resolve()
        })
      })
    },
    loadFirstChargeRewards() {
      const that = this
      that.firstChargeRewardsLoading = true
      listFirstChargeRewards(this.sysOrigin).then((res) => {
        that.firstChargeRewardsLoading = false
        this.firstChargeRewards = res.body || []
      }).catch(er => {
        that.firstChargeRewardsLoading = false
      })
    }
  }
}
</script>

