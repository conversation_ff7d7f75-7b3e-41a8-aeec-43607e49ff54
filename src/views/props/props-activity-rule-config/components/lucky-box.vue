<template>
  <div class="lucky-box">
    <div v-if="tips" class="blockquote">{{ tips }}</div>
    <el-form
      ref="ruleForm"
      class="rule-form"
      :model="ruleForm"
      label-position="top"
      :inline="true"
    >
      <el-col v-for="(item, index) in ruleForm.boxs" :key="index">
        <el-form-item
          style="margin-bottom: 20px;"
          :prop="'boxs.'+ index + '.quantity'"
          :rules="{required: true, message: '必填字段', trigger: 'blur'}"
        >
          <el-input v-model.trim="item.quantity" v-number :placeholder="(index + 1) + '、消耗糖果'" />
        </el-form-item>
        <el-form-item
          :prop="'boxs.'+ index + '.opportunityNumber'"
          :rules="{required: true, message: '必填字段', trigger: 'blur'}"
        >
          <el-input v-model.trim="item.opportunityNumber" v-number :placeholder="(index + 1) + '、抽奖次数'" />
        </el-form-item>
      </el-col>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'PropsActivityRuleConfigEditRuleLuckyBox',
  components: {},
  props: {
    tips: {
      type: String,
      required: false,
      default: ''
    },
    sysOrigin: {
      type: String,
      required: true
    },
    form: {
      type: Array,
      required: true,
      default: () => {}
    }
  },
  data() {
    return {
      ruleForm: {
        boxs: [
          { id: 1, quantity: '', opportunityNumber: '' },
          { id: 2, quantity: '', opportunityNumber: '' },
          { id: 3, quantity: '', opportunityNumber: '' }
        ]
      }
    }
  },
  computed: {
  },
  watch: {
    form: {
      handler(newVal) {
        if (!newVal || newVal.length <= 0) {
          return
        }
        this.ruleForm.boxs = newVal
      },
      immediate: true
    }
  },
  created() {
  },
  methods: {
    validateForm() {
      const that = this
      return new Promise((resolve, reject) => {
        that.$refs.ruleForm.validate(valid => {
          if (!valid) {
            reject()
            return false
          }
          resolve()
        })
      })
    }

  }
}
</script>

