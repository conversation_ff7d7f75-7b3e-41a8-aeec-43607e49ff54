<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.activityType"
        placeholder="活动类型"
        clearable
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in propActivityTypes"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-input
        v-model.trim="listQuery.id"
        placeholder="ID"
        clearable
        style="width: 200px;"
        class="filter-item"
      />
      <el-input
        v-model.trim="listQuery.ruleDescription"
        placeholder="规则描述"
        style="width: 200px;"
        class="filter-item"
      />
      <el-input
        v-model.trim="listQuery.resourceGroupId"
        v-number
        placeholder="活动组ID"
        style="width: 200px;"
        class="filter-item"
      />
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        新增
      </el-button>
    </div>
    <el-row v-loading="listLoading" :gutter="10">
      <el-col v-for="(item, index) in list" :key="index" :xs="24" :md="12" :lg="8">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <sys-origin-icon :icon="item.sysOrigin" size="20" />
            {{ item.activityTypeName }}
            <div style="float: right; padding: 3px 0">
              <el-dropdown>
                <span class="el-dropdown-link">
                  操作选项<i class="el-icon-arrow-down el-icon--right" />
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item @click.native="clickEditRule(item)">编辑规则</el-dropdown-item>
                  <el-dropdown-item @click.native="clickAssociatedResources(item)">关联资源</el-dropdown-item>
                  <el-dropdown-item @click.native="handlDel(item.id)">删除</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
          <div class="card-content">
            <div class="title">规则信息</div>
            <div class="contetn">
              <el-row>
                <el-col :span="12">
                  ID：{{ item.id }}
                </el-col>
                <el-col :span="12">
                  <div class="flex-l">
                    <div>排序：</div>
                    <div>
                      <div class="sort">{{ item.sort }}</div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="12" class="nowrap-ellipsis">
                  描述：{{ item.ruleDescription }}
                </el-col>
                <el-col :span="12" class="nowrap-ellipsis">
                  规则：
                  <el-tooltip class="item" effect="light">
                    <div slot="content">
                      <div v-if="item.jsonData">
                        <json-viewer :value="JSON.parse(item.jsonData)" :expand-depth="4" sort />
                      </div>
                      <div v-else>请选择类型</div>
                    </div>
                    <i class="el-icon-question" style="font-size:20px;cursor:pointer;" />
                  </el-tooltip>
                </el-col>
              </el-row>
            </div>
            <div class="title">资源信息1（{{ item.resourceGroupId }}）</div>
            <div class="content flex-l">
              <props-row :list="item.activityRewards" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <associated-resources
      v-if="associatedResourcesVisable"
      :sys-origin="clickRow.sysOrigin"
      @select="selectSource"
      @close="associatedResourcesVisable=false;"
    />

    <edit-rule-drawer
      v-if="editRuleDrawerVisible"
      :row="clickRow"
      @success="editRuleSuccess"
      @close="editRuleDrawerVisible=false;"
    />
  </div>
</template>

<script>
import { pagePropsActivityRuleConfig, savePropsActivityRuleConfig, delPropsActivityRuleConfig } from '@/api/props'
import Pagination from '@/components/Pagination'
import AssociatedResources from '@/components/data/AssociatedResources'
import { mapGetters } from 'vuex'
import EditRuleDrawer from './edit-rule-drawer'
import { propActivityTypes } from '@/constant/type'
import PropsRow from '@/components/data/PropsRow'

export default {
  name: 'PropsActivityRuleConfig',
  components: { Pagination, AssociatedResources, EditRuleDrawer, PropsRow },
  data() {
    return {
      propActivityTypes,
      clickRow: {},
      associatedResourcesVisable: false,
      editRuleDrawerVisible: false,
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        id: '',
        sysOrigin: 'MARCIE',
        activityType: 'STAR',
        resourceGroupId: ''
      },
      formVisible: false,
      listLoading: true
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        this.listQuery.cursor = 1
        this.list = []
      }
      that.listLoading = true
      pagePropsActivityRuleConfig(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        const list = body.records || []
        list.sort(function(a, b) { return a.sort - b.sort })
        that.list = list
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    selectSource(source) {
      const that = this
      that.associatedResourcesVisable = false
      that.clickRow.resourceGroupId = source.id
      that.submit(that.clickRow)
    },
    submit(from) {
      const that = this
      savePropsActivityRuleConfig(from).then(res => {
        this.$message({
          message: 'Successful',
          type: 'success'
        })
        that.fileList = []
        that.renderData()
      }).catch(er => {
        that.$emit('fial', er)
      })
    },
    clickAssociatedResources(item) {
      this.associatedResourcesVisable = true
      this.clickRow = item
    },
    // 删除
    handlDel(id) {
      this.$confirm('确认删除吗？', '提示', {
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        delPropsActivityRuleConfig(id).then((res) => {
          this.listLoading = false
          this.$message({
            message: '删除成功',
            type: 'success'
          })
          this.renderData()
        })
      }).catch(() => {

      })
    },
    handleCreate() {
      this.clickRow = {}
      this.editRuleDrawerVisible = true
    },
    clickEditRule(row) {
      this.clickRow = row
      this.editRuleDrawerVisible = true
    },
    editRuleSuccess() {
      this.editRuleDrawerVisible = false
      this.$opsMessage.success()
      this.renderData()
    }
  }
}
</script>
<style scoped lang="scss">
.rule-form {
  max-height: 400px;
  overflow: auto;
}
.box-card {
  margin-bottom: 10px;
  height: 340px;
  overflow: hidden;
  .card-content {
    line-height: 30px;
    .title {
      font-weight: bold;
    }
    .content {
      overflow: auto;
      white-space:nowrap;
      .origin-source {
        padding: 5px;
        text-align: center;
        width: 70px;
        height: 100px;
        img {
          width: 50px;
          height: 50px;
          cursor: pointer;
        }
      }

    }
    .sort {
      background: #efe3e9;
      width: 24px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      border-radius: 100%;
      font-weight: bold;
    }
  }
}
</style>
