<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="归属系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in permissionsSysOriginPlatforms"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <account-input v-model="listQuery.userId" :sys-origin="listQuery.sysOrigin" placeholder="反馈用户ID" />
      </div>
      <el-select
        v-model="listQuery.approvalStatus"
        placeholder="审核状态"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in approvalStatusArray"
          :key="index"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="listQuery.updateUserId"
        placeholder="后台成员"
        style="width: 120px"
        class="filter-item"
        clearable
        filterable
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in members"
          :key="index"
          :label="item.nickname"
          :value="item.id"
        />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="batchProcessing"
      >
        批量处理
      </el-button>

    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
      default-expand-all
      @selection-change="selsUserChange"
    >
      <el-table-column type="expand">
        <template slot-scope="scope">
          <el-row class="row-expand">
            <el-col :span="24" class="expand-col">
              <div v-if="handleImageUrls(scope.row) && handleImageUrls(scope.row).length > 0">
                <div class="flex-l">
                  <div v-for="(item, index) in handleImageUrls(scope.row)" :key="index" class="feedback-img" style="margin-right: 10px;">
                    <el-image
                      :src="item"
                      :preview-src-list="handleImageUrls(scope.row)"
                    />
                  </div>
                </div>
              </div>
              
              <div v-if="handleVideoUrls(scope.row) && handleVideoUrls(scope.row).length > 0">
                <div class="flex-l">
                  <div v-for="(item, index) in handleVideoUrls(scope.row)" :key="index" class="feedback-video" style="margin-right: 10px;">
                    <!-- <video controls width="100%">  
                      <source :src="item" type="video/mp4"> 
                    </video> -->
                  <video controls
                    style="width:100px;height:150px;"
                    :src="item"
                    muted
                    @click="handleVideoOpenClick(item)"> 
                  </video>
                  </div>
                </div>
              </div>
            </el-col>
            <el-col :span="12" class="expand-col">
              手机型号: <span>{{ scope.row.originPhoneModel }}</span>
            </el-col>
            <el-col :span="12" class="expand-col">
              App版本: <span>{{ scope.row.appVersion }}</span>
            </el-col>
            <el-col :span="24" class="expand-col">
              设备号: <span>{{ scope.row.imei }}</span>
            </el-col>
            <el-col :span="24" class="expand-col">
              反馈内容: {{ scope.row.content }}
            </el-col>
            <el-col v-if="scope.row.approvalRemarks" :span="24" class="expand-col">
              备注: <span class="font-danger">{{ scope.row.approvalRemarks }}</span>
            </el-col>
            <el-col v-if="scope.row.updateNickname" :span="24" class="expand-col">
              最近处理人: {{ scope.row.updateNickname }}
            </el-col>
          </el-row>
        </template>
      </el-table-column>
      <el-table-column v-if="isAllowApproval" type="selection" width="55" />
      <el-table-column prop="sysOrigin" label="系统" align="center" width="60">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column label="反馈用户" align="center" min-width="200">
        <template slot-scope="scope">
          <div v-if="scope.row.userProfile">
            <user-table-exhibit :user-profile="scope.row.userProfile" />
          </div>
          <span v-else style="color:rgb(216 157 116)">未登录</span>
        </template>
      </el-table-column>
      <el-table-column prop="level" label="处理状态" align="center" min-width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.approvalStatus === 0">未处理</span>
          <span v-else-if="scope.row.approvalStatus === 1" style="color:rgb(72 196 104)"> 已处理 </span>
          <span v-else>待处理</span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center" width="200">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" label="修改时间" align="center" width="200">
        <template slot-scope="scope">
          {{ scope.row.updateTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <div v-if="scope.row.approvalStatus === 0">
            <!-- <el-button type="text" @click.native="handlDel(scope.row.id)">删除</el-button> -->
            <el-button type="text" @click="feedback(scope.row)">处理</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <user-deatils-drawer
      v-if="userDeatilsDrawer"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawer=false"
    />

    <feedback-drawer
      v-if="userFeedbackDrawer"
      :row="thatRow"
      @success="renderData"
      @close="userFeedbackDrawer=false"
    />
    
  <el-dialog
      :visible.sync="dialogVideo"
      v-if="dialogVideo"
      width="850px"
      :before-close="handleVideoClose">
      <div id="video-mm">
        <video-player
          :options="videoOptions"
          class="video-css"
          ref="closeVideo"
        >
        </video-player>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getFeedbackTable, batchProcessFeedback } from '@/api/table'
import Pagination from '@/components/Pagination'
import { approvalStatusArray } from '@/constant/type'
import feedbackDrawer from '@/components/data/Feedback'
import { mapGetters } from 'vuex'
import { listMembers } from '@/api/team'
import VideoPlayer from '@/components/videoPlayer'

export default {
  name: 'Feedback',
  components: { Pagination, feedbackDrawer, VideoPlayer },
  data() {
    return {
      members: [],
      numbersLoading: false,
      thatRow: {},
      approvalStatusArray,
      userDeatilsDrawer: false,
      thatSelectedUserId: '',
      userFeedbackDrawer: false,
      list: [],
      checkList: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: '',
        sysOrigin: '',
        approvalStatus: '',
        updateUserId: ''
      },
      listLoading: true,
      dialogVideo: false,
      videoOptions: {
        controls: false,
        fluid: true,
        sources: [
          {
            src: null,
            type: 'video/mp4'
          }
        ]
      }
    }
  },
  computed: {
    isAllowApproval() {
      return this.listQuery.approvalStatus === 0
    },
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    dialogVideo(val) {
      if (!val) {
        this.$refs.closeVideo.otherCloseVideo()
      }
    }
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
    that.loadMembers()
  },
  methods: {
    loadMembers() {
      const that = this
      that.numbersLoading = true
      listMembers().then(res => {
        const { body } = res
        that.members = body
        that.numbersLoading = false
      }).catch(er => {
        that.numbersLoading = false
      })
    },
    handleImageUrls(row) {
      return row.imageUrls ? row.imageUrls.split(',') : []
    },
    handleVideoUrls(row) {
      return row.videoUrls ? row.videoUrls.split(',') : []
    },
    handleVideoClose(done) {
      done()
    },
    handleVideoOpenClick(url) {
      const that = this
      if (!url) {
        return
      }
      that.dialogVideo = true
      that.videoOptions.sources[0].src = url
    }
    ,
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      getFeedbackTable(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    queryUserDetails(userId) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = userId
    },
    feedback(row) {
      this.userFeedbackDrawer = true
      this.thatRow = row
    },
    selsUserChange(checkList) {
      this.checkList = checkList
    },
    getApprovalParams() {
      const that = this
      const approvalParams = []
      that.checkList.forEach(item => {
        approvalParams.push({
          userId: item.userId,
          id: item.id,
          approvalRemarks: '感谢您的反馈意见',
          approvalStatus: 1
        })
      })
      return approvalParams
    },
    batchProcessing() {
      const that = this
      if (that.listQuery.approvalStatus !== 0) {
        that.$opsMessage.warn('选择审核状态下拉框为【未处理】,再操作')
        return
      }
      if (that.checkList.length === 0) {
        that.$opsMessage.warn('请勾选审批项')
        return
      }
      that.$confirm('确认审核选中记录吗？', '提示', {
        type: 'warning'
      }).then(() => {
        that.listLoading = true
        batchProcessFeedback({ processFeedbacks: that.getApprovalParams() }).then(res => {
          this.listLoading = false
          this.$message({
            message: '批量处理成功',
            type: 'success'
          })
          this.renderData()
        }).catch(() => { that.listLoading = false })
      }).catch(() => {})
    }
  }
}
</script>
<style scoped lang="scss">
.feedback-img {
  position: relative;
  width: 50px;
  height: 50px;
  overflow: hidden;
  img {
    width: 100%;
    height: 100%;
  }
}
.feedback-video {
  position: relative;
  width: 100px;
  height: 150px;
  overflow: hidden;
  video {
    width: 100%;
    height: 100%;
  }
}
.row-expand {
  padding: 20px;
  .expand-col {
    margin-bottom: 10px;
  }
}
</style>
