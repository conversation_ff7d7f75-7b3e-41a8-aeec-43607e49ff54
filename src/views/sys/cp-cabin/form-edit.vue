<template>
  <div class="cp-cabin-form-edite">
    <el-drawer
      :title="textOptTitle"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div v-loading="submitLoading">

        <div class="drawer-form">
          <el-form ref="form" :model="form" :rules="rules" label-width="120px">
            <el-form-item prop="cover" label="小屋图标">
              <el-upload
                :disabled="coverUploadLoading"
                :file-list="coverFileList"
                :class="{'upload-but-hide': !isShowCoverUpload}"
                action=""
                list-type="picture-card"
                :http-request="uploadCover"
                :show-file-list="!isShowCoverUpload"
                :on-remove="handleCoverFileRemove"
                accept="image/*"
              >
                <i slot="default" v-loading="coverUploadLoading" class="el-icon-plus" />
              </el-upload>
            </el-form-item>
            <el-form-item prop="lockCover" label="小屋图标加锁">
              <el-upload
                :disabled="lockCoverUploadLoading"
                :file-list="lockCoverFileList"
                :class="{'upload-but-hide': !isShowLockCoverUpload}"
                action=""
                list-type="picture-card"
                :http-request="uploadLockCover"
                :show-file-list="!isShowLockCoverUpload"
                :on-remove="handleLockCoverFileRemove"
                accept="image/*"
              >
                <i slot="default" v-loading="lockCoverUploadLoading" class="el-icon-plus" />
              </el-upload>
            </el-form-item>
            <el-form-item prop="personCover" label="个人资料页图">
              <el-upload
                :disabled="personCoverUploadLoading"
                :file-list="personUrlFileList"
                :class="{'upload-but-hide': !isShowPersonCoverUpload}"
                action=""
                list-type="picture-card"
                :http-request="uploadPersonCover"
                :show-file-list="!isShowPersonCoverUpload"
                :on-remove="handlePersonCoverFileRemove"
                accept="image/*"
              >
                <i slot="default" v-loading="personCoverUploadLoading" class="el-icon-plus" />
              </el-upload>
            </el-form-item>
            <el-form-item prop="cardCover" label="个人卡片">
              <el-upload
                :disabled="cardCoverUploadLoading"
                :file-list="cardUrlFileList"
                :class="{'upload-but-hide': !isShowCardCoverUpload}"
                action=""
                list-type="picture-card"
                :http-request="uploadCardCover"
                :show-file-list="!isShowCardCoverUpload"
                :on-remove="handleCardCoverFileRemove"
                accept="image/*"
              >
                <i slot="default" v-loading="cardCoverUploadLoading" class="el-icon-plus" />
              </el-upload>
            </el-form-item>
            <el-form-item prop="backgroundCover" label="空间背景图">
              <el-upload
                :disabled="backgroundCoverUploadLoading"
                :file-list="backgroundUrlFileList"
                :class="{'upload-but-hide': !isShowBackgroundCoverUpload}"
                action=""
                list-type="picture-card"
                :http-request="uploadBackgroundCover"
                :show-file-list="!isShowBackgroundCoverUpload"
                :on-remove="handleBackgroundCoverFileRemove"
                accept="image/*"
              >
                <i slot="default" v-loading="backgroundCoverUploadLoading" class="el-icon-plus" />
              </el-upload>
            </el-form-item>
            <el-form-item label="闪光mini资源" prop="sourceMiniUrl">
              <el-upload
                :disabled="sourceMiniUploadLoading"
                :class="{'upload-but-hide': !isShowSourceMiniUpload}"
                action=""
                :http-request="sourceMiniUpload"
                :on-remove="handleSourceMiniFileRemove"
                :show-file-list="!isShowSourceMiniUpload"
                :file-list="sourceMiniUrlFileList"
                accept=".svga,.pag"
              >
                <div class="upload-but">
                  <el-button :loading="sourceMiniUploadLoading" size="small" type="primary">点击上传</el-button>
                  <div slot="tip" class="el-upload__tip">
                    只能上传svga/pag文件
                  </div>
                </div>
              </el-upload>
              <svgaplayer v-if="!isShowSourceMiniUpload" :url="form.sourceMiniUrl" />
            </el-form-item>
            <el-form-item label="资源" prop="sourceUrl">
              <el-upload
                :disabled="sourceUploadLoading"
                :class="{'upload-but-hide': !isShowSourceUpload}"
                action=""
                :http-request="sourceUpload"
                :on-remove="handleSourceFileRemove"
                :show-file-list="!isShowSourceUpload"
                :file-list="sourceUrlFileList"
                accept=".svga,.pag"
              >
                <div class="upload-but">
                  <el-button :loading="sourceUploadLoading" size="small" type="primary">点击上传</el-button>
                  <div slot="tip" class="el-upload__tip">
                    只能上传svga/pag文件
                  </div>
                </div>
              </el-upload>
              <svgaplayer v-if="!isShowSourceUpload" :url="form.sourceUrl" />
            </el-form-item>
            <el-form-item label="名称" prop="name">
              <el-input v-model.trim="form.name" placeholder="名称" />
            </el-form-item>
            <el-form-item label="解锁小屋CP值" prop="cabinUnlockValue">
              <el-input v-model.trim="form.cabinUnlockValue" v-number placeholder="解锁小屋CP值" />
            </el-form-item>
            <el-form-item label="排序" prop="sort">
              <el-input v-model.trim="form.sort" v-number placeholder="降序排列(数字越大越靠前)" />
            </el-form-item>
          </el-form>
        </div>
        <div class="drawer-footer">
          <el-button @click="handleClose()">取消</el-button>
          <el-button type="primary" @click="submitForm()">保存</el-button>
        </div>
      </div>

    </el-drawer>
  </div>
</template>
<script>

import { updateCpCabin, addCpCabin } from '@/api/cp-cabin'
import { getElementUiUploadFile } from '@/utils'
import { mapGetters } from 'vuex'
export default {
  props: {
    row: {
      type: Object,
      default: null
    },
    sysOrigin: {
      type: String,
      require: false,
      default: ''
    }
  },
  data() {
    const commonRules = [{ required: true, message: '必填字段', trigger: 'blur' }]
    return {
      coverUploadLoading: false,
      sourceUploadLoading: false,
      sourceMiniUploadLoading: false,
      lockCoverUploadLoading: false,
      personCoverUploadLoading: false,
      cardCoverUploadLoading: false,
      backgroundCoverUploadLoading: false,
      coverFileList: [],
      lockCoverFileList: [],
      sourceUrlFileList: [],
      sourceMiniUrlFileList: [],
      personUrlFileList: [],
      cardUrlFileList: [],
      backgroundUrlFileList: [],
      loading: false,
      form: {
        id: '',
        name: '',
        cover: '',
        lockCover: '',
        personCover: '',
        cardCover: '',
        backgroundCover: '',
        sourceUrl: '',
        sourceMiniUrl: '',
        sysOrigin: '',
        sort: '',
        cabinUnlockValue: ''
      },
      submitLoading: false,
      uploadLoading: false,
      rules: {
        cover: commonRules,
        lockCover: commonRules,
        personCover: commonRules,
        cardCover: commonRules,
        backgroundCover: commonRules,
        sourceUrl: commonRules,
        sourceMiniUrl: commonRules,
        sort: commonRules,
        name: commonRules,
        cabinUnlockValue: commonRules
      }
    }
  },
  computed: {
    textOptTitle() {
      return `${(this.row && this.row.id ? '修改' : '新增')}(${this.sysOrigin})`
    },
    isShowCoverUpload() {
      return !this.form.cover
    },
    isShowLockCoverUpload() {
      return !this.form.lockCover
    },
    isShowPersonCoverUpload() {
      return !this.form.personCover
    },
    isShowCardCoverUpload() {
      return !this.form.cardCover
    },
    isShowBackgroundCoverUpload() {
      return !this.form.backgroundCover
    },
    isShowSourceUpload() {
      return !this.form.sourceUrl
    },
    isShowSourceMiniUpload() {
      return !this.form.sourceMiniUrl
    },
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    row: {
      handler(val) {
        if (!val) {
          return
        }
        const form = Object.assign({}, val)
        this.coverFileList = getElementUiUploadFile(form.cover)
        this.lockCoverFileList = getElementUiUploadFile(form.lockCover)
        this.sourceUrlFileList = getElementUiUploadFile(form.sourceUrl)
        this.sourceMiniUrlFileList = getElementUiUploadFile(form.sourceMiniUrl)
        this.personUrlFileList = getElementUiUploadFile(form.personCover)
        this.cardUrlFileList = getElementUiUploadFile(form.cardCover)
        this.backgroundUrlFileList = getElementUiUploadFile(form.backgroundCover)
        this.form = Object.assign(this.form, form)
      },
      immediate: true
    }
  },
  methods: {
    uploadLockCover(file) {
      const that = this
      that.lockCoverUploadLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.other).then(res => {
        that.lockCoverUploadLoading = false
        that.form.lockCover = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.lockCoverUploadLoading = false
      })
    },
    handleLockCoverFileRemove(file, fileList) {
      this.form.lockCover = ''
      this.lockCoverUploadLoading = false
    },
    uploadCover(file) {
      const that = this
      that.coverUploadLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.other).then(res => {
        that.coverUploadLoading = false
        that.form.cover = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.coverUploadLoading = false
      })
    },
    handleCoverFileRemove(file, fileList) {
      this.form.cover = ''
      this.coverUploadLoading = false
    },
    uploadPersonCover(file) {
      const that = this
      that.personCoverUploadLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.other).then(res => {
        that.personCoverUploadLoading = false
        that.form.personCover = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.personCoverUploadLoading = false
      })
    },
    handlePersonCoverFileRemove(file, fileList) {
      this.form.personCover = ''
      this.personCoverUploadLoading = false
    },
    uploadCardCover(file) {
      const that = this
      that.cardCoverUploadLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.other).then(res => {
        that.cardCoverUploadLoading = false
        that.form.cardCover = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.cardCoverUploadLoading = false
      })
    },
    handleCardCoverFileRemove(file, fileList) {
      this.form.cardCover = ''
      this.cardCoverUploadLoading = false
    },
    uploadBackgroundCover(file) {
      const that = this
      that.backgroundCoverUploadLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.other).then(res => {
        that.backgroundCoverUploadLoading = false
        that.form.backgroundCover = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.backgroundCoverUploadLoading = false
      })
    },
    handleBackgroundCoverFileRemove(file, fileList) {
      this.form.backgroundCover = ''
      this.backgroundCoverUploadLoading = false
    },
    sourceUpload(file) {
      const that = this
      that.sourceUploadLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.other).then(res => {
        that.sourceUploadLoading = false
        that.form.sourceUrl = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.sourceUploadLoading = false
      })
    },
    handleSourceFileRemove(file, fileList) {
      this.form.sourceUrl = ''
      this.sourceUploadLoading = false
    },
    sourceMiniUpload(file) {
      const that = this
      that.sourceMiniUploadLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.other).then(res => {
        that.sourceMiniUploadLoading = false
        that.form.sourceMiniUrl = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.sourceMiniUploadLoading = false
      })
    },
    handleSourceMiniFileRemove(file, fileList) {
      this.form.sourceMiniUrl = ''
      this.sourceMiniUploadLoading = false
    },
    handleClose() {
      this.$emit('close')
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return
        }
        const submitForm = Object.assign({}, that.form)
        that.submitLoading = true

        if (!submitForm.sysOrigin) {
          submitForm.sysOrigin = that.sysOrigin
        }

        if (!submitForm.sysOrigin) {
          that.$message.error('系统错误, 没有平台信息!')
          return
        }
        if (submitForm.id) {
          updateCpCabin(submitForm).then(res => {
            that.submitLoading = false
            that.$emit('success', res)
          }).catch(er => {
            that.submitLoading = false
            that.$emit('fial', er)
          })
          return
        }
        addCpCabin(submitForm).then(res => {
          that.submitLoading = false
          that.$emit('success', res)
        }).catch(er => {
          that.submitLoading = false
          console.error(er)
          that.$emit('fial', er)
        })
      })
    }
  }
}
</script>
