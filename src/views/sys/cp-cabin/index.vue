<template>
  <div class="app-container">

    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width:120px;"
        class="filter-item"
        @change="changeSysOrigin"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        新增
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
      @cell-mouse-enter="handleMouseEnter"
    >
      <el-table-column label="归属系统" align="center">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column prop="name" label="名称" align="center" />
      <el-table-column label="封面" align="center" width="400">
        <template slot-scope="scope">
          <el-image
            :lazy="true"
            style="width: 400px; height: 150px"
            :src="scope.row.cover"
            :preview-src-list="[scope.row.cover]"
          />
        </template>
      </el-table-column>
      <el-table-column prop="cabinUnlockValue" label="解锁小屋CP值" align="center" />
      <el-table-column prop="sort" label="排序" align="center" />
      <el-table-column fixed="right" label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button type="text" @click.native="handleUpdate()">修改</el-button>
          <el-button type="text" @click.native="handlDel(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <form-edit
      v-if="formEditVisible"
      :row="thatRow"
      :sys-origin="listQuery.sysOrigin"
      @close="formEditVisible = false"
      @success="formEditSuccess"
    />
  </div>
</template>

<script>
import { cpCabinTable, deleteCpCabin } from '@/api/cp-cabin'
import Pagination from '@/components/Pagination'
import { sysOriginPlatforms } from '@/constant/origin'
import FormEdit from './form-edit.vue'
import { mapGetters } from 'vuex'

export default {
  components: { Pagination, FormEdit },
  data() {
    return {
      thatRow: {},
      sysOriginPlatforms,
      loading: false,
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        sysOrigin: 'HALAR'
      },
      formEditVisible: false,
      listLoading: true
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        this.listQuery.cursor = 1
        this.listQuery.list = []
      }
      that.listLoading = true
      cpCabinTable(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    renderDataSuccess() {
      this.$message({
        message: '操作成功',
        type: 'success'
      })
      this.renderData()
    },
    changeSysOrigin() {
      this.handleSearch()
    },
    // 删除
    handlDel(row) {
      this.$confirm('确认删除吗？', '提示', {
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        deleteCpCabin(row.id).then((res) => {
          this.listLoading = false
          this.$message({
            message: '删除成功',
            type: 'success'
          })
          this.renderData()
        })
      }).catch(() => {

      })
    },
    handleCreate() {
      this.thatRow = null
      this.formEditVisible = true
    },
    handleUpdate() {
      this.formEditVisible = true
    },
    handleMouseEnter(row) {
      this.thatRow = row
    },
    formEditSuccess() {
      this.formEditVisible = false
      this.renderData()
    }
  }
}
</script>
<style scoped lang="scss">
.popover-content {
  max-width: 300px;
  line-height: 20px;
}
</style>
