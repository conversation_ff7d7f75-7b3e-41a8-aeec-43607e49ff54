<template>
  <div class="app-container">

    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width:120px;"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        新增
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="归属系统" align="center">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column label="图片" align="center">
        <template slot-scope="scope">
          <el-image
            style="width: 60px; height: 80px"
            :src="scope.row.cardBack"
            :preview-src-list="[scope.row.cardBack]"
          />
        </template>
      </el-table-column>
      <el-table-column prop="cardType" label="类型" align="center" />
      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          <div>
            <span v-if="scope.row.showcase === false">下架</span>
            <span v-if="scope.row.showcase === true">上架</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="cardMoney" label="金额" align="center" />
      <el-table-column prop="createTime" label="创建时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="100">
        <template slot-scope="scope">
          <el-button type="text" @click.native="handleUpdate(scope.row)">修改</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
    <el-dialog
      :title="textOptTitle"
      :visible.sync="formVisible"
      :before-close="handleClose"
      width="400px"
    >
      <div v-loading="submitLoading">
        <el-form ref="form" :model="form" :rules="rules" label-width="110px">
          <el-form-item label="系统" prop="sysOrigin">
            <el-select
              v-model="form.sysOrigin"
              placeholder="系统"
              style="width:100%;"
              class="filter-item"
            >
              <el-option
                v-for="item in permissionsSysOriginPlatforms"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
                <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
                <span style="float: left;margin-left:10px">{{ item.label }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="图片" prop="cardBack">
            <el-upload
              class="upload-demo"
              action=""
              :http-request="httpRequest"
              :show-file-list="false"
              accept="image/png,image/jpg,image/jpeg"
            >
              <el-image v-if="form.cardBack" :src="form.cardBack" fit="fill" style="width: 80px; height: 100px" />
              <el-button v-loading="uploadLoading" size="small" type="primary">点击上传</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item label="状态" prop="showcase">
            <el-select
              v-model="form.showcase"
              placeholder="状态"
              clearable
              style="width:100%;"
              class="filter-item"
            >
              <el-option label="上架" :value="true" />
              <el-option label="下架" :value="false" />
            </el-select>
          </el-form-item>

          <el-form-item label="卡片" prop="cardType">
            <el-select
              v-model="form.cardType"
              placeholder="卡片"
              style="width: 100%;"
              class="filter-item"
              clearable
            >
              <el-option
                v-for="(item, index) in cards"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="金额" prop="cardMoney">
            <el-input v-model.trim="form.cardMoney" type="number" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm()">保存</el-button>
            <el-button @click="handleClose()">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { userFriendshipCardConfigTable, addUserFriendshipCardConfig, updateUserFriendshipCardConfig } from '@/api/sys'
import Pagination from '@/components/Pagination'
import { getAddressUrl } from '@/api/oss'
import { mapGetters } from 'vuex'

function getFormData() {
  return {
    id: '',
    sysOrigin: '',
    cardBack: '',
    showcase: '',
    cardType: '',
    cardMoney: ''
  }
}
export default {
  name: 'SysUserFriendshipCard',
  components: { Pagination },
  data() {
    return {
      cards: [
        { label: '知己', value: 'CONFIDANT' },
        { label: '兄弟', value: 'BROTHER' },
        { label: '姐妹', value: 'SISTER' },
        { label: '兄弟、姐妹', value: 'BROTHER_SISTER' },
        { label: '亲密的朋友', value: 'CLOSE_FRIEND' }
      ],
      list: [],
      total: 0,
      listQuery: {
        sysOrigin: 'HALAR',
        cursor: 1,
        limit: 20
      },
      formVisible: false,
      textOptTitle: '',
      form: getFormData(),
      submitLoading: false,
      rules: {
        sysOrigin: [
          { required: true, message: '请选择系统', trigger: 'change' }
        ],
        cardType: [
          { required: true, message: '请选择卡片类型', trigger: 'blur' }
        ],
        cardBack: [
          { required: true, message: '请上传图片', trigger: 'change' }
        ],
        showcase: [
          { required: true, message: '请选择状态', trigger: 'blur' }
        ],
        cardMoney: [
          { required: true, message: '请填写金额', trigger: 'blur' }
        ]
      },
      listLoading: true,
      uploadLoading: false
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    this.renderData()
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      userFriendshipCardConfigTable(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData()
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (valid) {
          that.submitLoading = true
          if (that.form.id) {
            updateUserFriendshipCardConfig(that.form).then(res => {
              that.submitLoading = false
              that.formVisible = false
              that.form = getFormData()
              that.renderData()
            }).catch(er => {
              that.submitLoading = false
              console.error(er)
              this.$emit('fail')
            })
            return
          }
          addUserFriendshipCardConfig(that.form).then(res => {
            that.submitLoading = false
            that.formVisible = false
            that.form = getFormData()
            that.renderData()
          }).catch(er => {
            that.submitLoading = false
            console.error(er)
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    },
    handleClose() {
      this.formVisible = false
    },
    handleCreate() {
      this.formVisible = true
      this.textOptTitle = '添加'
      this.form = getFormData()
    },
    handleUpdate(row) {
      this.textOptTitle = '修改'
      this.formVisible = true
      this.form = Object.assign(this.form, row)
    },
    httpRequest(file) {
      const that = this
      that.uploadLoading = true
      this.$simpleUploadFlie(file, 'other').then(res => {
        that.form.cardBack = that.$getAccessImgUrl(res.name)
        that.uploadLoading = false
      }).catch(er => {
        that.uploadLoading = false
      })
    }
  }
}
</script>
