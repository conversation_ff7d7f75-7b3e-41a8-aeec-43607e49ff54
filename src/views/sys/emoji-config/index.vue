<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="item in tables" :key="item.name" :label="item.title" :name="item.component" />
      <component :is="activeName" />
    </el-tabs>
  </div>
</template>
<script>
import EmojiGroup from './group'
import EmojiConf from './emoji'
export default {
  name: 'GiftConfig',
  components: { EmojiGroup, EmojiConf },
  data() {
    return {
      activeName: 'EmojiGroup',
      tables: [
        {
          title: '分组',
          component: 'EmojiGroup'
        },
        {
          title: '资源',
          component: 'EmojiConf'
        }
      ]
    }
  }
}
</script>
