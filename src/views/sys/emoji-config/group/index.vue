<template>
  <div class="app-container-group">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.shelfStatus"
        placeholder="状态"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="上架" :value="1" />
        <el-option label="下架" :value="0" />
      </el-select>
      <el-select
        v-model="listQuery.ktvStatus"
        placeholder="ktv表情"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="是" :value="1" />
        <el-option label="否" :value="0" />
      </el-select>
      <el-input
        v-model.trim="listQuery.groupCode"
        placeholder="编码"
        style="width: 200px;"
        class="filter-item"
      />
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>

      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="clickCreate"
      >
        新增
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="sysOrigin" label="系统" align="center" width="100">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column prop="groupCode" label="编码" align="center" />
      <el-table-column prop="groupName" label="名称" align="center" />
      <el-table-column label="封面" align="center" width="150">
        <template slot-scope="scope">
          <avatar :url="scope.row.cover" />
        </template>
      </el-table-column>
      <el-table-column label="售卖价格" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.amount > 0">{{ scope.row.amount }}</span>
          <span v-else>免费</span>
        </template>
      </el-table-column>
      <el-table-column width="200" label="上/下架" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.shelfStatus"
            @change="handleSwitchChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column width="100" label="ktv表情" align="center">
        <template slot-scope="scope">
          <div>
            <el-tag v-if="scope.row.ktvStatus === 1">是</el-tag>
            <el-tag v-else-if="scope.row.ktvStatus === 0">否</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="排序" prop="sort" align="center" />
      <el-table-column prop="createTime" label="创建时间" width="200" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="100">
        <template slot-scope="scope">
          <div>
            <el-button type="text" @click.native="clickEdit(scope.row)">修改</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <form-edit v-if="formEditVisiable" :row="thisClickRow" @close="formEditVisiable=false;thisClickRow={}" @success="formEditSuccess" />

  </div>
</template>

<script>
import { pageGroupTable, switchGroupShelfStatus } from '@/api/sys-emoji'
import { productConfigShowcase } from '@/constant/type'
import Pagination from '@/components/Pagination'
import FormEdit from './form-edit'
import { mapGetters } from 'vuex'

export default {
  name: 'EmojiConfig',
  components: { Pagination, FormEdit },
  data() {
    return {
      thisClickRow: {},
      formEditVisiable: false,
      productConfigShowcase,
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        type: '',
        sysOrigin: '',
        shelfStatus: '',
        groupCode: ''
      },
      listLoading: true
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      pageGroupTable(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSwitchChange(row) {
      const that = this
      switchGroupShelfStatus(row.id, row.shelfStatus).then(res => {
        that.$emit('success', 'Successful')
        that.renderData(false)
      }).catch(er => {
        row.shelfStatus = row.shelfStatus ? 0 : 1
        console.error(er)
      })
    },
    handleSearch() {
      this.searchLoading = true
      this.renderData(true)
    },
    clickCreate() {
      this.thisClickRow = null
      this.formEditVisiable = true
    },
    clickEdit(row) {
      this.thisClickRow = Object.assign({}, row)
      this.formEditVisiable = true
    },
    formEditSuccess() {
      this.handleSearch()
      this.formEditVisiable = false
    }
  }
}
</script>
<style scoped lang="scss">
 .store-table-expand {
    font-size: 0;
    label {
    width: 90px;
    color: #99a9bf;
    .el-form-item {
      margin-right: 0;
      margin-bottom: 0;
      width: 50%;
    }
   }
 }
</style>
