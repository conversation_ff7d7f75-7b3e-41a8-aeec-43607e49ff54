<template>
  <div class="props-config-edit">
    <el-dialog
      :title="textOptTitle"
      :visible="true"
      :before-close="handleClose"
      :close-on-click-modal="false"
      width="450px"
    >
      <div v-loading="submitLoading">
        <el-form ref="form" :model="form" label-width="110px" :rules="formRules" style="margin-right: 20px;">
          <el-form-item
            v-if="!form.id"
            label="系统"
            prop="sysOrigin"
          >
            <el-select
              v-model="form.sysOrigin"
              placeholder="系统"
              style="width:100%;"
              class="filter-item"
            >
              <el-option
                v-for="(item, index) in permissionsSysOriginPlatforms"
                :key="index"
                :label="item.label"
                :value="item.value"
                class="filter-item"
              >
                <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
                <span style="float: left;margin-left:10px">{{ item.label }}</span>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item v-if="!form.id" label="分组Code" prop="groupCode">
            <el-input v-model.trim="form.groupCode" placeholder="分组Code" />
          </el-form-item>

          <el-form-item label="分组名称" prop="groupName">
            <el-input v-model.trim="form.groupName" placeholder="分组名称" />
          </el-form-item>

          <el-form-item v-model="form.cover" label="封面" prop="cover">
            <el-upload
              :disabled="coverUploadLoading"
              :file-list="coverFileList"
              :class="{'upload-but-hide': !isShowCoverUpload}"
              action=""
              list-type="picture-card"
              :http-request="uploadCover"
              :show-file-list="!isShowCoverUpload"
              :on-remove="handleCoverFileRemove"
              accept="image/*"
            >
              <i slot="default" v-loading="coverUploadLoading" class="el-icon-plus" />
            </el-upload>
          </el-form-item>
          <el-form-item label="售卖价格" prop="amount">
            <el-input v-model.trim="form.amount" type="number" :placeholder="'不填或填0都为免费'" />
          </el-form-item>
          <el-form-item label="上/下架" prop="shelfStatus">
            <el-radio-group v-model="form.shelfStatus">
              <el-radio :label="1">上架</el-radio>
              <el-radio :label="0">下架</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="ktv表情" prop="ktvStatus">
            <el-radio-group v-model="form.ktvStatus">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="权重" prop="sort">
            <el-input v-model.trim="form.sort" v-number placeholder="数字越大越靠前" />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="submitForm()">保存</el-button>
            <el-button @click="handleClose()">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import { addOrUpdateGroup } from '@/api/sys-emoji'
import { deepClone, getElementUiUploadFile } from '@/utils'
import { mapGetters } from 'vuex'
export default {
  props: {
    row: {
      type: Object,
      require: false,
      default: () => {}
    }
  },
  data() {
    const commonRules = [
      { required: true, message: '必填字段不可为空', trigger: 'blur' }
    ]
    return {
      submitLoading: false,
      uploadLoading: false,
      coverUploadLoading: false,
      coverFileList: [],
      form: {
        id: '',
        cover: '',
        groupName: '',
        groupCode: '',
        sort: '',
        sysOrigin: '',
        shelfStatus: '',
        ktvStatus: '',
        amount: ''
      },
      formRules: {
        cover: commonRules,
        groupName: commonRules,
        groupCode: commonRules,
        sort: commonRules,
        sysOrigin: commonRules,
        shelfStatus: commonRules,
        ktvStatus: commonRules
      }
    }
  },
  computed: {
    isShowCoverUpload() {
      return !this.form.cover
    },
    isShowSourceUpload() {
      return !this.form.sourceUrl
    },
    textOptTitle() {
      return this.row && this.row.id ? '修改' : '添加'
    },
    isUpdate() {
      return this.row && this.row.id
    },
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    row: {
      handler(newVal) {
        if (!newVal) {
          this.form.shelfStatus = 0
          this.form.ktvStatus = 0
          return
        }

        this.form = deepClone(newVal)
        if (this.form.cover) {
          this.coverFileList = getElementUiUploadFile(this.form.cover)
        }
      },
      immediate: true
    }
  },
  methods: {
    uploadCover(file) {
      const that = this
      that.coverUploadLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.svgaCover).then(res => {
        that.coverUploadLoading = false
        that.form.cover = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.coverUploadLoading = false
      })
    },
    handleCoverFileRemove(file, fileList) {
      this.form.cover = ''
      this.coverUploadLoading = false
    },
    handleClose() {
      this.$emit('close')
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.submitLoading = true
        addOrUpdateGroup(that.form).then(res => {
          that.submitLoading = false
          that.$emit('success', res)
        }).catch(er => {
          that.submitLoading = false
          that.$emit('fail')
          console.error(er)
        })
        return
      })
    }
  }
}
</script>
<style scoped lang="scss">

</style>
