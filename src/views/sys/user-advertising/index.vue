<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="item in tables" :key="item.name" :label="item.title" :name="item.component" />
      <component :is="activeName" />
    </el-tabs>
  </div>
</template>
<script>
import AswatUserAdvertising from './aswat'
export default {
  name: 'UserAdvertising',
  components: { AswatUserAdvertising },
  data() {
    return {
      activeName: 'AswatUserAdvertising',
      tables: [
        {
          title: 'Aswat',
          component: 'AswatUserAdvertising'
        }
      ]
    }
  }
}
</script>
