<template>
  <div v-loading="listLoading" class="push-form">
    <el-form ref="form" :model="form" style="padding: 0.3rem;">
      <el-form-item prop="viewableAdvertisement" label="一个用户最多可查看广告"  >
        <el-col :span="4">
        <el-input v-model="form.viewableAdvertisement"/>
        </el-col>
        <span style="color: #8D0502;display: inline-block;"> &nbsp;条/天</span>
      </el-form-item>
      <el-form-item prop="timeInterval" label="间隔多少s（秒）可以查看下一个">
        <el-col :span="3">
        <el-input v-model="form.timeInterval" />
        </el-col>
        <span style="color: #8D0502;display: inline-block;">&nbsp;&nbsp;秒</span>
      </el-form-item>
      <el-form-item prop="gold" label="查看一次得到金币" >
        <el-col :span="4">
        <el-input v-model="form.gold"/>
        </el-col>
      </el-form-item>
      <el-form-item prop="advertisingSwitch" label="广告开关">
        <el-col :span="2">
          <el-switch
            v-model="form.advertisingSwitch"
            active-color="#ff4949"
            :active-text="form.advertisingSwitch ? '已结束' : '运行中'"
            inactive-color="#13ce66"
          />
        </el-col>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="onSubmit">修改</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { querySysUserAdvertisingConfigDTO, updateSysUserAdvertisingConfigDTO } from '@/api/sys'

export default {
  name: 'AswatUserAdvertising',
  data() {
    return {
      form: {
        sysOrigin: 'MARCIE',
        advertisingSwitch: false,
        viewableAdvertisement: '',
        timeInterval: '',
        gold: ''

      },
      listLoading: false,
      loading: false,
      regions: [],
      sysOrigin: 'MARCIE'
    }
  },
  created() {
    const that = this
    that.renderData()
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      querySysUserAdvertisingConfigDTO('MARCIE').then(res => {
        const { result } = res
        that.form = result
        that.listLoading = false
      })
    },
    onSubmit() {
      const that = this
      that.$refs.form.validate((valid) => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.$confirm('是否确定修改配置值?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          updateSysUserAdvertisingConfigDTO(that.form).then(res => {
            that.$message({
              type: 'success',
              message: '操作成功'
            })
            that.renderData()
          }).catch(er => {
            that.$message.error('操作失败')
          })
        }).catch(() => {
          that.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">
input {
  width: 2rem;
}

</style>
