<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="item in tables" :key="item.name" :label="item.title" :name="item.component" />
      <component :is="activeName" />
    </el-tabs>
  </div>
</template>
<script>
import AswatInviteUserRule from './aswat'
import YahllaInviteUserRule from './yahlla'
export default {
  name: 'InviteUserRule',
  components: { AswatInviteUserRule, YahllaInviteUserRule },
  data() {
    return {
      activeName: 'AswatInviteUserRule',
      tables: [
        {
          title: 'Aswat',
          component: 'AswatInviteUserRule'
        }, {
          title: 'Yahlla',
          component: 'YahllaInviteUserRule'
        }
      ]
    }
  }
}
</script>
