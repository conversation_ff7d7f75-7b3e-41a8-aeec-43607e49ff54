<template>
  <div class="push-form">
    <el-form ref="form" :model="form" label-width="100px">
      <el-alert
        type="info"
        :closable="false"
      >
        <span>超过邀请时间{{ form.days }}天，则不发佣金</span>
      </el-alert>
      <el-form-item prop="days" label="有效天数">
        <el-input v-model="form.days" type="number" />
      </el-form-item>
      <el-alert
        type="info"
        :closable="false"
      >
        <span>赚取的佣金比例(单位:%)</span>
      </el-alert>
      <el-form-item prop="proportion" label="比例">
        <el-input v-model="form.proportion" type="number" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">修改</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { updateSysInviteUserConfig, querySysInviteUserConfig } from '@/api/sys'

export default {
  name: 'YahllaInviteUserRule',
  data() {
    return {
      form: {
        days: '',
        proportion: '',
        sysOrigin: 'MARCIE'
      }
    }
  },
  created() {
    const that = this
    that.renderData()
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      querySysInviteUserConfig('MARCIE').then(res => {
        const { body } = res
        that.form = body
        that.listLoading = false
      })
    },
    onSubmit() {
      const that = this
      that.$refs.form.validate((valid) => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.$confirm('是否确定修改配置值?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          updateSysInviteUserConfig(that.form).then(res => {
            that.$message({
              type: 'success',
              message: 'Successful'
            })
            that.renderData()
          }).catch(er => {
            that.$opsMessage.success()
          })
        }).catch(() => {
          that.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">
  .msg-text {
    color: #54c988;
    background-color: #f6f5f5;
  }
</style>
