<template>
  <el-drawer
    :title="title"
    :visible="true"
    :before-close="handleClose"
    :close-on-press-escape="false"
    :wrapper-closable="false"
    :modal-append-to-body="true"
    :append-to-body="true"
    custom-class="drawer-auto-layout"
  >
    <div class="drawer-form">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="formData"
        label-position="right"
        label-width="70px"
      >
        <el-form-item label="账号" prop="loginName">
          <div v-if="isAdd">
            <el-input
              v-model.trim="formData.loginName"
              placeholder="请输入登录账号"
            />
          </div>
          <div v-else>{{ formData.loginName }}</div>
        </el-form-item>

        <el-form-item label="昵称" prop="nickname">
          <el-input
            v-model.trim="formData.nickname"
            placeholder="请输入用户昵称"
          />
        </el-form-item>

        <el-form-item label="电话" prop="phone">
          <el-input
            v-model="formData.phone"
            v-number
            placeholder="请输入手机号码"
          />
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model.trim="formData.email"
            placeholder="请输入电子邮箱"
          />
        </el-form-item>

        <el-form-item label="角色" prop="roleIds">
          <!--multiple 取消角色多选支持-->
          <el-select
            v-model="formData.roleIds"
            v-loading="roleLoading"
            style="width:100%"
            class="filter-item"
            multiple
            placeholder="请选择角色"
          >
            <el-option
              v-for="item in roles"
              :key="item.id"
              :label="item.roleName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <div class="drawer-footer">
        <el-button @click="handleClose">
          取消
        </el-button>
        <el-button
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit"
        >
          提交
        </el-button>
      </div>
    </div>

  </el-drawer>
</template>
<script>
import { getRoles, addUser, updateUser } from '@/api/ops-system'
export default {
  name: 'EditUser',
  props: {
    updateData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      roleLoading: false,
      submitLoading: false,
      roles: [],
      formData: {
        loginName: '',
        nickname: '',
        phone: '',
        email: '',
        roleIds: []
      },
      rules: {
        loginName: [
          { required: true, message: '用户名必填', trigger: 'blur' }
        ],
        nickname: [
          {
            required: true,
            message: '用户昵称必填',
            trigger: 'blur'
          }
        ],
        roleIds: [{ required: true, message: '角色必填', trigger: 'blur' }]
      }
    }
  },
  computed: {
    isAdd() {
      return this.updateData === null
    },
    title() {
      return this.isAdd ? '创建' : '修改'
    }
  },
  watch: {
    updateData: {
      immediate: true,
      deep: true,
      handler(newVal) {
        Object.assign(this.formData, newVal)
        if (this.formData && this.formData.userRoles) {
          this.formData.roleIds = this.formData.userRoles.map(item => item.roleId)
        }
      }
    }
  },
  created() {
    const that = this
    that.roleLoading = true
    getRoles().then(res => {
      that.roleLoading = false
      const { body } = res
      that.roles = body
    }).catch(er => {
      that.roleLoading = false
    })
  },
  methods: {
    handleClose() {
      if (this.submitLoading === true) {
        this.$opsMessage.warn('Processing submission!')
        return
      }
      this.$emit('close')
    },
    handleSubmit() {
      const that = this
      that.$refs.dataForm.validate(valid => {
        if (valid) {
          that.submitLoading = true
          if (that.isAdd) {
            addUser(that.formData).then(res => {
              that.submitLoading = false
              this.$emit('success')
            }).catch(er => {
              that.submitLoading = false
              console.error(er)
              this.$emit('fail')
            })
            return
          }
          updateUser(that.formData).then(res => {
            that.submitLoading = false
            this.$emit('success')
          }).catch(er => {
            that.submitLoading = false
            console.error(er)
            this.$emit('fail')
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    }
  }
}
</script>
