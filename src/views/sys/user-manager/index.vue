<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.loginName"
        placeholder="账号"
        style="width: 200px;"
        class="filter-item"
      />
      <el-input
        v-model="listQuery.nickname"
        placeholder="昵称"
        style="width: 200px;"
        class="filter-item"
      />
      <el-select
        v-model="listQuery.status"
        placeholder="状态"
        clearable
        style="width: 90px"
        class="filter-item"
      >
        <el-option
          v-for="item in accountStatus"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="listLoading"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-edit"
        @click="clickCreateUser"
      >
        添加
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
      @cell-mouse-enter="handleMouseEnter"
    >
      <el-table-column prop="id" label="ID" align="center" />
      <el-table-column prop="loginName" label="账号" align="center" />
      <el-table-column prop="nickname" label="昵称" align="center" />
      <el-table-column prop="nickname" label="角色" align="center">
        <template slot-scope="scope">
          {{ scope.row.userRoles | joinRolesFilter }}
        </template>
      </el-table-column>
      <el-table-column
        prop="phone"
        label="手机号码"
        width="110"
        align="center"
      />
      <el-table-column prop="email" label="邮箱" width="110" align="center" />

      <el-table-column
        prop="createTime"
        label="创建时间"
        width="200"
        align="center"
      >
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>

      <el-table-column prop="nickname" label="状态" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="0"
            :inactive-value="1"
            active-text="正常"
            inactive-text="禁用"
            @change="clickSwitchChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="150">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="clickUpdateClick(scope.row)"
          >修改</el-button>
          <el-button type="text" size="small" @click="clickRestPwd(scope.row)">重置密码</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <edit-user
      v-if="editUserVisible"
      :update-data="thisActiveRow"
      @close="editUserVisible = false"
      @success="editUserSuccess"
    />

  </div>
</template>

<script>
import { pageUsers, switchUserStatus, restPassword } from '@/api/ops-system'
import Pagination from '@/components/Pagination'
import EditUser from './edit-user'
export default {
  name: 'UserManager',
  components: { Pagination, EditUser },
  filters: {
    joinRolesFilter(roles) {
      if (!roles || roles.length <= 0) {
        return ''
      }
      return roles.map(role => role.roleName).join(',')
    }
  },
  data() {
    return {
      editUserVisible: false,
      activeRow: null,
      roles: ['超级管理员', '管理员'],
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        loginName: '',
        nickname: '',
        status: ''
      },
      accountStatus: [
        { value: 0, name: '正常' },
        { value: 1, name: '禁用' }
      ],
      listLoading: false
    }
  },
  computed: {
    thisActiveRow() {
      return this.activeRow ? Object.assign({}, this.activeRow) : null
    }
  },
  created() {
    this.renderData(true)
  },
  methods: {
    renderData(isClean) {
      const that = this
      that.listLoading = true
      if (isClean === true) {
        that.listQuery.cursor = 1
        that.list = []
      }
      pageUsers(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    clickCreateUser() {
      const that = this
      that.editUserVisible = true
      that.activeRow = null
    },
    clickUpdateClick(row) {
      const that = this
      that.editUserVisible = true
    },
    handleSearch() {
      this.renderData(true)
    },
    clickSwitchChange(row) {
      switchUserStatus(row.id, row.status)
        .then(res => {})
        .catch(er => {
          if (row.status === 0) {
            row.status = 1
          } else if (row.status === 1) {
            row.status = 0
          }
        })
    },
    clickRestPwd(row) {
      this.$confirm(`是否确认重置 "${row.nickname}" 登陆密码?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        restPassword(row.id).then(res => {
          this.$opsMessage.success()
        })
      }).catch(() => {
        this.$opsMessage.info('已取消删除')
      })
    },
    handleMouseEnter(row) {
      this.activeRow = row
    },
    editUserSuccess() {
      this.editUserVisible = false
      this.renderData()
    }
  }
}
</script>
