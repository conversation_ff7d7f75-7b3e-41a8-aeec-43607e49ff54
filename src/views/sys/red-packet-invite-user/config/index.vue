<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="item in tables" :key="item.name" :label="item.title" :name="item.component" />
      <component :is="activeName" />
    </el-tabs>
  </div>
</template>
<script>
import AswatInviteUserConfig from './red-packet-invite-user-aswat'
import TwoFunInviteUserConfig from './red-packet-invite-user-2fun'
import TarabInviteUserConfig from './red-packet-invite-user-tarab'
import HalarInviteUserConfig from './red-packet-invite-user-halar'
export default {
  name: 'RedPacketInviteConfig',
  components: { AswatInviteUserConfig, TwoFunInviteUserConfig, TarabInviteUserConfig, HalarInviteUserConfig},
  data() {
    return {
      activeName: 'HalarInviteUserConfig',
      tables: [
        // {
        //   title: 'Aswat',
        //   component: 'AswatInviteUserConfig'
        // },
        // {
        //   title: '2Fun',
        //   component: 'TwoFunInviteUserConfig'
        // },
        // {
        //   title: 'Tarab',
        //   component: 'TarabInviteUserConfig'
        // },
        {
          title: 'Halar',
          component: 'HalarInviteUserConfig'
        }
      ]
    }
  }
}
</script>
