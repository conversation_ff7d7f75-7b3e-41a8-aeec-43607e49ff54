<template>
  <div v-loading="listLoading" class="push-form">
    <el-form ref="form" :model="form" style="padding: 0.3rem;">
      金币余额多少可以参与活动 <span>&nbsp;&nbsp;&nbsp;&nbsp;</span>
      <el-form-item prop="goldCoinBalance" label="" style="display: inline-block;">
        <el-input v-model="form.goldCoinBalance"/>
      </el-form-item>
      <span style="color: #8D0502">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;或者</span> <!-- 输出一个空格 -->
      注册时间大于多少天可以参与活动 <span>&nbsp;&nbsp;&nbsp;&nbsp;</span>
      <el-form-item prop="regDateMax" label="" style="display: inline-block;">
        <el-input v-model="form.regDateMax" v-number type="number"/>
      </el-form-item>
      <el-form-item prop="cashNumberMax" label="现金奖项人次(最少~最多)">
        <el-col :span="2">
          <el-input v-model="form.cashNumberMin" v-number type="number" />
        </el-col>
        <el-col :span="2" style="margin: 0rem .2rem;">
          <el-input v-model="form.cashNumberMax" v-number type="number" />
        </el-col>
      </el-form-item>
      <el-form-item prop="otherNumberMax" label="其他奖项人次(最少~最多)">
        <el-col :span="2">
          <el-input v-model="form.otherNumberMin" v-number type="number" />
        </el-col>
        <el-col :span="2" style="margin: 0rem .2rem;">
          <el-input v-model="form.otherNumberMax" v-number type="number" />
        </el-col>
      </el-form-item>
      <el-form-item prop="totalAmount" label="红包总金额">
        <el-col :span="2">
          <el-input v-model="form.totalAmount" v-number type="number" />
        </el-col>
      </el-form-item>
      <el-form-item prop="openRedPacketMax" label="开红包金额(最少~最多)">
        <el-col :span="2">
          <el-input v-model="form.openRedPacketMin" />
        </el-col>
        <el-col :span="2" style="margin: 0rem .2rem;">
          <el-input v-model="form.openRedPacketMax" />
        </el-col>
      </el-form-item>
      <el-form-item prop="withdrawThresholdCount" label="‘直接提现’奖项用户最少邀请多少人才能有机会触发">
        <el-col :span="2">
          <el-input v-model="form.withdrawThresholdCount" v-number type="number" />
        </el-col>
      </el-form-item>
      <el-form-item prop="withdrawTriggerProportion" label="‘直接提现’奖项多少次全局抽奖出一次">
        <el-col :span="2">
          <el-input v-model="form.withdrawTriggerProportion" v-number type="number" />
        </el-col>
      </el-form-item>

      <el-form-item prop="ipFrequency" label="一个ip最多可集满红包次数">
        <el-col :span="2">
          <el-input v-model="form.ipFrequency" v-number type="number" />
        </el-col>
      </el-form-item>
      <el-form-item prop="deviceFrequency" label="一个设备最多可集满红包次数">
        <el-col :span="2">
          <el-input v-model="form.deviceFrequency" v-number type="number" />
        </el-col>
      </el-form-item>
      <el-form-item prop="dayFrequency" label="用户账号一天最多可集满红包次数">
        <el-col :span="2">
          <el-input v-model="form.dayFrequency" v-number type="number" />
        </el-col>
      </el-form-item>
      <el-form-item prop="totalFrequency" label="用户账号总可集满次数">
        <el-col :span="2">
          <el-input v-model="form.totalFrequency" v-number type="number" />
        </el-col>
      </el-form-item>
      <el-form-item prop="newUserHelpDays" label="新用户可助力天节点">
        <el-col :span="4">
          <el-input v-model="form.newUserHelpDays" placeholder="例如:1,7,14 多个用英语逗号分隔" />
        </el-col>
      </el-form-item>
      <el-form-item prop="regionList" label="支持区域">
        <el-select v-model="form.regionList" multiple placeholder="请选择" style="width: 100%;" @change="$forceUpdate()">
          <el-option
            v-for="(item, index) in regions"
            :key="index"
            :label="item.regionName"
            :value="item.regionCode"
          />
        </el-select>
      </el-form-item>

      <el-form-item prop="checkClose" label="活动状态">
        <el-col :span="2">
          <el-switch
            v-model="form.checkClose"
            active-color="#ff4949"
            :active-text="form.checkClose ? '已结束' : '运行中'"
            inactive-color="#13ce66"
          />
        </el-col>
      </el-form-item>

      <el-form-item prop="bayWindow" label="红包飘窗开启状态">
        <el-col :span="2">
          <el-switch
            v-model="form.bayWindow"
            active-color="#ff4949"
            :active-text="form.bayWindow ? '关闭' : '开启'"
            inactive-color="#13ce66"
          />
        </el-col>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">修改</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { regionConfigTable } from '@/api/sys'
import { getConfigBySysOrigin, saveConfig } from '@/api/red-packet-invite-user'

export default {
  name: 'AswatInviteUserConfig',
  data() {
    return {
      form: {
        sysOrigin: 'TWO_FUN',
        cashNumberMin: '',
        cashNumberMax: '',
        otherNumberMin: '',
        otherNumberMax: '',
        totalAmount: '',
        openRedPacketMin: '',
        openRedPacketMax: '',
        withdrawThresholdCount: '',
        regionCodeArray: '',
        withdrawTriggerProportion: '',
        checkClose: false,
        bayWindow: false,
        ipFrequency: '',
        deviceFrequency: '',
        dayFrequency: '',
        totalFrequency: '',
        newUserHelpDays: '',
        regionList: []
      },
      listLoading: false,
      loading: false,
      regions: [],
      sysOrigin: 'TWO_FUN'
    }
  },
  created() {
    const that = this
    that.renderData()
    that.listRegion()
  },
  methods: {
    renderData() {
      const that = this
      getConfigBySysOrigin('TWO_FUN').then(res => {
        if (res.body != null) {
          that.form = res.body
          that.form.regionList = res.body.regionCodeArray.split(',')
        }
        that.listLoading = false
      }).catch(() => {
        that.listLoading = false
      })
    },
    listRegion() {
      const that = this
      that.loading = true
      regionConfigTable({ 'sysOrigin': that.sysOrigin }).then(res => {
        that.regions = res.body || []
        that.loading = false
      }).catch(er => {
        that.loading = false
      })
    },
    onSubmit() {
      const that = this
      that.$refs.form.validate((valid) => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.$confirm('确定修改配置值?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          saveConfig(that.form).then(res => {
            that.$message({
              type: 'success',
              message: '操作成功'
            })
          }).catch(er => {
            that.$message.error('操作失败')
          })
        }).catch(() => {
          that.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">
input {
  width: 2rem;
}

</style>
