<template>
  <div :class="'app-container'">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <account-input v-model="listQuery.userId" :sys-origin="listQuery.sysOrigin" placeholder="用户ID" />
      </div>
      <div class="filter-item">
        <account-input v-model="listQuery.inviteUserId" :sys-origin="listQuery.sysOrigin" placeholder="邀请人用户ID" />
      </div>
      <div class="filter-item">
        <el-input v-model="listQuery.id" placeholder="请输入红包ID" />
      </div>
      <div class="filter-item">
        <el-input v-model="listQuery.imei" placeholder="请输入设备号" />
      </div>
      <div class="filter-item">
        <el-input v-model="listQuery.ip" placeholder="请输入IP" />
      </div>
      <el-select
        v-model="listQuery.status"
        placeholder="红包状态"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="未完成" value="UNDONE" />
        <el-option label="正常完成" value="COMPLETE" />
        <el-option label="提前完成" value="BEFORE_COMPLETE" />
      </el-select>
      <el-select
        v-model="listQuery.redPacketSource"
        placeholder="红包来源"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="用户邀请" value="USER_INVITATION" />
        <el-option label="房间" value="ROOM" />
        <el-option label="首页" value="HOME" />
        <el-option label="主播代理中心" value="TEAM" />
        <el-option label="BD中心" value="BD" />
        <el-option label="每日任务" value="TASK" />
        <el-option label="礼物" value="GIFT" />
        <el-option label="游戏" value="GAME" />
        <el-option label="我的" value="ME" />
        <el-option label="飘窗" value="BROADCAST" />
      </el-select>

      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="日期开始"
          end-placeholder="日期结束"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >

      <el-table-column prop="id" label="ID" align="center" />
      <el-table-column label="用户" align="center" min-width="80">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column label="邀请人" align="center" min-width="80">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.inviteUserProfile" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column prop="statusName" label="红包状态" align="center" />
      <el-table-column prop="totalAmount" label="总额" align="center" />
      <el-table-column prop="currentAmount" label="当前收集金额" align="center" />
      <el-table-column prop="redPacketSourceName" label="开红包来源" align="center" />
      <el-table-column prop="inviteMemberCount" label="当前红包邀请成员数" align="center" />
      <el-table-column prop="imei" label="操作人设备号" align="center" />
      <el-table-column prop="ip" label="操作人IP" align="center" />
      <el-table-column prop="createTime" label="创建时间" align="center" width="200">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column prop="violationOrNot" label="是否违规">
        <template v-slot="{ row }">
          <span v-if="row.violationOrNot==false">是</span>
          <span v-else>否</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
  </div>
</template>

<script>
import { pageRedPacket } from '@/api/red-packet-invite-user'
import { pickerOptions } from '@/constant/el-const'
import { mapGetters } from 'vuex'
import Pagination from '@/components/Pagination'
export default {
  name: 'RedPacketInviteLog',
  components: { Pagination },
  data() {
    return {
      rangeDate: [],
      pickerOptions,
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: '',
        inviteUserId: '',
        status: '',
        redPacketSource: '',
        id: '',
        sysOrigin: 'HALAR',
        awardType: '',
        startTime: '',
        endTime: '',
        imei: '',
        ip: ''
      },
      listLoading: false,
      list: []
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    }
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.listQuery.cursor = 1
        that.list = []
      }
      that.listLoading = true
      pageRedPacket(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      }).catch(er => {
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    }
  }
}
</script>
<style scoped lang="scss">
  .user-css {
    display: flex;
    align-items: center;
    color: #909399;
    font-size: .15rem;
    line-height: .3rem;
    padding: 0.02rem;
    border-radius: 0.04rem;
  }
  .load-more {
    padding: 20px;
    text-align: center;
  }
</style>
