<template>
  <div :class="'app-container'">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <account-input v-model="listQuery.userId" :sys-origin="listQuery.sysOrigin" placeholder="用户ID" />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="sysOrigin" label="系统" align="center" width="80">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column label="用户" align="center" min-width="80">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column prop="incomeAmount" label="抽中目标" align="center" />
      <el-table-column prop="expenditureBalance" label="已兑换目标" align="center" />
      <el-table-column prop="balance" label="剩余目标" align="center" />
      <el-table-column prop="createTime" label="创建时间" align="center" width="160">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click.native="editExchange(scope.row)">兑换目标</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
    <exchange
      v-if="editExchangeFormVisible"
      :id="thatSelectedId"
      @close="editExchangeFormVisible=false"
      @success="renderDataSuccess"
    />
  </div>
</template>

<script>
import { pageTargetTransitBalance } from '@/api/red-packet-invite-user'
import { mapGetters } from 'vuex'
import Pagination from '@/components/Pagination'
import Exchange from './exchange'
export default {
  name: 'RedPacketInviteUserTarget',
  components: { Pagination, Exchange },
  data() {
    return {
      thatSelectedId: '',
      editExchangeFormVisible: false,
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: '',
        sysOrigin: 'MARCIE'
      },
      listLoading: false,
      list: []
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.listQuery.cursor = 1
        that.list = []
      }
      that.listLoading = true
      pageTargetTransitBalance(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      }).catch(er => {
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    editExchange(item) {
      this.thatSelectedId = item.id
      this.editExchangeFormVisible = true
    },
    renderDataSuccess() {
      this.$message({
        message: '操作成功',
        type: 'success'
      })
      this.renderData()
    }
  }
}
</script>
<style scoped lang="scss">
  .user-css {
    display: flex;
    align-items: center;
    color: #909399;
    font-size: .15rem;
    line-height: .3rem;
    padding: 0.02rem;
    border-radius: 0.04rem;
  }
  .load-more {
    padding: 20px;
    text-align: center;
  }
</style>
