<template>
  <div class="edit-user-gold-coin-reward-form">
    <el-dialog
      title="兑换目标"
      :visible="true"
      width="450px"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formData"
        :rules="rules"
        :model="formData"
        label-position="left"
        label-width="70px"
        style="width: 300px; margin-left:50px;"
      >

        <el-form-item label="数量" prop="target">
          <el-input
            v-model.trim="formData.target"
            v-number
            maxlength="8"
            placeholder="需要奖励的金币数量"
          />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose()">
          取消
        </el-button>
        <el-button
          v-loading="listLoading"
          type="primary"
          @click="handleSubmit()"
        >
          兑换
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { reduceTarget } from '@/api/red-packet-invite-user'

export default {
  name: 'RedPacketInviteUserTargetExchange',
  props: {
    id: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      listLoading: false,
      rules: {
        target: [
          {
            required: true,
            message: '必填字段不可为空',
            trigger: 'blur'
          }
        ]
      },
      formData: {
        target: '',
        id: ''
      }
    }
  },
  methods: {
    handleSubmit() {
      const that = this
      that.$refs.formData.validate((valid) => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.listLoading = true
        that.formData.id = that.id
        reduceTarget(that.formData).then(res => {
          that.$emit('success', Object.assign({}, that.formData))
          that.handleClose()
        }).catch(err => {
          that.listLoading = false
          that.$emit('fial', err)
        })
      })
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
