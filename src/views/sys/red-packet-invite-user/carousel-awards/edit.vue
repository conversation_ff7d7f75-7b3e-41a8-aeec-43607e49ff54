<template>
  <div class="edit-user-gold-coin-reward-form">
    <el-dialog
      title="配置奖项"
      :visible="true"
      width="450px"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formData"
        :rules="rules"
        :model="formData"
        label-position="left"
        label-width="70px"
        style="width: 300px; margin-left:50px;"
      >
        <el-form-item label="奖品" prop="type">
          <el-select
            v-model="formData.type"
            placeholder="类型"
            clearable
            style="width:100%;"
            class="filter-item"
          >
            <el-option label="美金" :value="'USD'" />
            <el-option label="立即提现" :value="'WITHDRAWAL'" />
            <el-option label="目标" :value="'TARGET'" />
            <el-option label="钻石" :value="'DIAMOND'" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="formData.type === 'USD'" label="数量" prop="quantity">
          <el-input
            v-model.trim="formData.quantity"
            maxlength="8"
            placeholder="数量"
          />
        </el-form-item>
        <el-form-item v-if="formData.type === 'TARGET' || formData.type === 'DIAMOND'" label="数量" prop="quantity">
          <el-input
            v-model.trim="formData.quantity"
            v-number
            maxlength="8"
            placeholder="数量"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose()">
          取消
        </el-button>
        <el-button
          v-loading="listLoading"
          type="primary"
          @click="handleSubmit()"
        >
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { saveCarouselAwards } from '@/api/red-packet-invite-user'

export default {
  name: 'RedPacketInviteUserTargetExchange',
  props: {
    row: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      listLoading: false,
      rules: {
        quantity: [
          {
            required: true,
            message: '必填字段不可为空',
            trigger: 'blur'
          }
        ],
        type: [
          {
            required: true,
            message: '请选择奖品类型',
            trigger: 'blur'
          }
        ]
      },
      formData: {
        sysOrigin: '',
        id: '',
        type: '',
        quantity: ''
      }
    }
  },
  watch: {
    row: {
      handler(val) {
        console.log(!val.id)
        if (!val.id) {
          this.formData.sysOrigin = val.sysOrigin
          return
        }
        const form = Object.assign({}, val)
        this.formData = Object.assign(this.formData, form)
      },
      immediate: true
    }
  },
  methods: {
    handleSubmit() {
      const that = this
      if (that.formData.type === 'WITHDRAWAL') {
        that.formData.quantity = 0
      }
      that.$refs.formData.validate((valid) => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.listLoading = true
        saveCarouselAwards(that.formData).then(res => {
          that.$emit('success', Object.assign({}, that.formData))
          that.handleClose()
        }).catch(err => {
          that.listLoading = false
          that.$emit('fial', err)
        })
      })
    },
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>
