<template>
  <div :class="'app-container'">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        v-if="!(list && list.length >= 6)"
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        新增
      </el-button>
    </div>
    <el-alert
      title="务必配置六项奖励,不能多不能少且必须包含一个0.01的现金奖项"
      type="warning"
    />
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="sysOrigin" label="系统" align="center" width="80">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column prop="typeDesc" label="奖品类型" align="center" />
      <el-table-column label="数量" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.type !== 'WITHDRAWAL'">{{ scope.row.quantity }}</span>
          <span v-else>随机</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click.native="edit(scope.row)">编辑</el-button>
          <el-button type="text" @click.native="handleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <carousel-awards-edit
      v-if="editFormVisible"
      :row="thatSelectedRow"
      @close="editFormVisible=false"
      @success="renderDataSuccess"
    />
  </div>
</template>

<script>
import { listCarouselAwards, deleteCarouselAwards } from '@/api/red-packet-invite-user'
import CarouselAwardsEdit from './edit'
import { mapGetters } from 'vuex'
export default {
  name: 'RedPacketInviteCarouselAwards',
  components: { CarouselAwardsEdit },
  data() {
    return {
      thatSelectedRow: '',
      editFormVisible: false,
      listQuery: {
        sysOrigin: 'HALAR'
      },
      listLoading: false,
      list: []
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.listQuery.cursor = 1
        that.list = []
      }
      that.listLoading = true
      listCarouselAwards(that.listQuery.sysOrigin).then(res => {
        const { body } = res
        that.list = body
        that.listLoading = false
      }).catch(er => {
        that.listLoading = false
      })
    },
    edit(item) {
      this.thatSelectedRow = item
      this.editFormVisible = true
    },
    handleCreate() {
      const that = this
      this.thatSelectedRow = { 'sysOrigin': that.listQuery.sysOrigin }
      this.editFormVisible = true
    },
    handleDelete(id) {
      this.$confirm('确认删除吗？', '提示', {
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        deleteCarouselAwards(id).then((res) => {
          this.listLoading = false
          this.$message({
            message: '删除成功',
            type: 'success'
          })
          this.renderData()
        })
      }).catch(() => {

      })
    },
    handleSearch() {
      this.renderData(true)
    },
    renderDataSuccess() {
      this.$message({
        message: '操作成功',
        type: 'success'
      })
      this.renderData()
    }
  }
}
</script>
<style scoped lang="scss">
  .user-css {
    display: flex;
    align-items: center;
    color: #909399;
    font-size: .15rem;
    line-height: .3rem;
    padding: 0.02rem;
    border-radius: 0.04rem;
  }
  .load-more {
    padding: 20px;
    text-align: center;
  }
</style>
