<template>
  <div :class="'app-container'">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <account-input v-model="listQuery.userId" :sys-origin="listQuery.sysOrigin" placeholder="用户ID" />
      </div>
      <div class="filter-item">
        <el-input v-model="listQuery.redPacketId" placeholder="请输入红包ID" />
      </div>
      <el-select
        v-model="listQuery.awardType"
        placeholder="奖品类型"
        style="width: 120px"
        class="filter-item"
        clearable
        @change="handleSearch"
      >
        <el-option label="美金" value="USD" />
        <el-option label="立即提现" value="WITHDRAWAL" />
        <el-option label="目标" value="TARGET" />
        <el-option label="钻石" value="DIAMOND" />
      </el-select>

      <div class="filter-item">
        <el-date-picker
          v-model="rangeDate"
          value-format="timestamp"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="日期开始"
          end-placeholder="日期结束"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >

      <el-table-column prop="id" label="ID" align="center" />
      <el-table-column label="用户" align="center" min-width="80">
        <template slot-scope="scope">
          <user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" />
        </template>
      </el-table-column>
      <el-table-column prop="redPacketId" label="红包ID" align="center" />
      <el-table-column prop="awardTypeName" label="奖励类型" align="center" />
      <el-table-column prop="awardNumber" label="奖励数量" align="center" />
      <el-table-column prop="totalAmount" label="红包总额" align="center" />
      <el-table-column prop="currentAmount" label="当前红包进度额度" align="center" />
      <el-table-column prop="remainFrequency" label="剩余抽奖次数" align="center" />
      <el-table-column prop="imei" label="抽奖人设备号" align="center" />
      <el-table-column prop="ip" label="抽奖人IP" align="center" />
      <el-table-column prop="createTime" label="创建时间" align="center" width="160">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
  </div>
</template>

<script>
import { pageRedPacketDrawLog } from '@/api/red-packet-invite-user'
import { pickerOptions } from '@/constant/el-const'
import { mapGetters } from 'vuex'
import Pagination from '@/components/Pagination'
export default {
  name: 'RedPacketInviteDrawLog',
  components: { Pagination },
  data() {
    return {
      rangeDate: [],
      pickerOptions,
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: '',
        type: '',
        redPacketId: '',
        sysOrigin: 'HALAR',
        awardType: '',
        startTime: '',
        endTime: ''
      },
      listLoading: false,
      list: []
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    rangeDate: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.listQuery.startTime = newVal[0]
          this.listQuery.endTime = newVal[1]
          return
        }
        this.listQuery.startTime = ''
        this.listQuery.endTime = ''
      }
    }
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isReset) {
      const that = this
      if (isReset === true) {
        that.listQuery.cursor = 1
        that.list = []
      }
      that.listLoading = true
      pageRedPacketDrawLog(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      }).catch(er => {
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    }
  }
}
</script>
<style scoped lang="scss">
  .user-css {
    display: flex;
    align-items: center;
    color: #909399;
    font-size: .15rem;
    line-height: .3rem;
    padding: 0.02rem;
    border-radius: 0.04rem;
  }
  .load-more {
    padding: 20px;
    text-align: center;
  }
</style>
