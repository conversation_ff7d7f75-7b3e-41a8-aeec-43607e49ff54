<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.roleName"
        placeholder="角色名称"
        style="width: 200px;"
        class="filter-item"
      />
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="listLoading"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        添加
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
      @cell-mouse-enter="handleMouseEnter"
    >
      <el-table-column prop="id" label="ID" align="center" />
      <el-table-column prop="roleName" label="角色" align="center" />
      <el-table-column prop="remark" label="备注" align="center" />
      <el-table-column
        prop="createTime"
        label="创建时间"
        width="200"
        align="center"
      >
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column
        prop="updateTime"
        label="修改时间"
        width="200"
        align="center"
      >
        <template slot-scope="scope">
          {{ scope.row.updateTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="180">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="handlerGrant(scope.row)"
          >菜单授权</el-button>
          <el-button
            type="text"
            size="small"
            @click="handleUpdateClick(scope.row)"
          >修改</el-button>
          <el-button type="text" size="small" @click="clickDelRole(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <edit-role
      v-if="editRoleVisible"
      :update-data="thisActiveRow"
      @close="editRoleVisible = false"
      @success="editRoleSuccess"
    />

    <authorize-role
      v-if="dialogGrantVisible"
      :menu-ids="menuIds"
      :submit-loading="grantSubmitLoading"
      @close="dialogGrantVisible = false; menuIds = []"
      @submit="handleAuthorizeRoleSubmit"
    />
  </div>
</template>

<script>
import { pageRoleTable, delRole, updateRoleMenus } from '@/api/ops-system'
import Pagination from '@/components/Pagination'
import EditRole from '@/views/sys/role-manager/edit-role'
import AuthorizeRole from '@/views/sys/role-manager/authorize-role'

export default {
  name: 'RoleManager',
  components: { Pagination, EditRole, AuthorizeRole },
  data() {
    return {
      activeRow: null,
      editRoleVisible: false,
      dialogGrantVisible: false,
      grantSubmitLoading: false,
      menuIds: [],
      roles: ['超级管理员', '管理员'],
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        roleName: ''
      },
      accountStatus: [
        { value: 0, name: '正常' },
        { value: 1, name: '禁用' }
      ],
      listLoading: false
    }
  },
  computed: {
    thisActiveRow() {
      return this.activeRow ? Object.assign({}, this.activeRow) : null
    }
  },
  created() {
    this.renderData(true)
  },
  methods: {
    renderData(isClean) {
      const that = this
      that.listLoading = true
      if (isClean === true) {
        that.listQuery.cursor = 1
        that.list = []
      }
      pageRoleTable(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handlerGrant(row) {
      this.menuIds = row.menuIds
      this.dialogGrantVisible = true
    },
    handleCreate() {
      const that = this
      that.editRoleVisible = true
      that.activeRow = null
    },
    handleUpdateClick(row) {
      const that = this
      that.editRoleVisible = true
    },
    handleSearch() {
      this.renderData(true)
    },
    handleMouseEnter(row) {
      this.activeRow = row
    },
    editRoleSuccess() {
      this.editRoleVisible = false
      this.renderData()
    },
    clickDelRole(id) {
      const that = this
      that.$confirm('此操作将永删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delRole(id).then(res => {
          that.$opsMessage.success()
          that.renderData()
        })
      }).catch(() => {
        that.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    handleAuthorizeRoleSubmit(menusIds) {
      const that = this
      that.grantSubmitLoading = true
      updateRoleMenus({
        id: this.thisActiveRow.id, menuIds: menusIds || []
      }).then(res => {
        that.grantSubmitLoading = false
        that.dialogGrantVisible = false
        that.renderData()
      }).catch(er => {
        that.grantSubmitLoading = false
      })
    }
  }
}
</script>
