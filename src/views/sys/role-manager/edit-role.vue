<template>
  <div class="edit-role">
    <el-drawer
      :title="title"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div class="drawer-form">
        <el-form
          ref="dataForm"
          :rules="rules"
          :model="formData"
          label-position="right"
          label-width="70px"
        >
          <el-form-item label="角色名" prop="roleName">
            <el-input v-model.trim="formData.roleName" placeholder="请输入角色名" />
          </el-form-item>

          <el-form-item label="备注" prop="remark">
            <el-input
              v-model.trim="formData.remark"
              rows="6"
              resize="none"
              type="textarea"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-form>
      </div>
      <div class="drawer-footer">
        <el-button @click="handleClose">
          取消
        </el-button>
        <el-button
          :loading="submitLoading"
          :disabled="submitLoading"
          type="primary"
          @click="handleSubmit"
        >
          提交
        </el-button>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import { addRole, updateRole } from '@/api/ops-system'
export default {
  name: 'EditRole',
  props: {
    updateData: {
      type: Object,
      default: null
    }
  },
  data() {
    const validateRemark = (rule, value, callback) => {
      if (value && value.length > 100) {
        callback(new Error('max 100 character'))
      } else {
        callback()
      }
    }
    return {
      roles: ['超级管理员', '管理员'],
      formData: {
        id: '',
        remark: '',
        roleName: ''
      },
      rules: {
        roleName: [
          { required: true, message: '角色名必填', trigger: 'change' }
        ],
        remark: [{ required: false, message: '备注必须在100字符以内', trigger: 'blur', validator: validateRemark }]
      },
      submitLoading: false
    }
  },
  computed: {
    isAdd() {
      return this.updateData === null
    },
    title() {
      return this.isAdd ? '创建' : '修改'
    }
  },
  watch: {
    updateData: {
      immediate: true,
      deep: true,
      handler(newVal) {
        Object.assign(this.formData, newVal)
      }
    }
  },
  methods: {
    handleClose() {
      if (this.submitLoading === true) {
        this.$opsMessage.warn('Processing submission!')
        return
      }
      this.$emit('close')
    },
    handleSubmit() {
      const that = this
      that.$refs.dataForm.validate(valid => {
        if (valid) {
          that.submitLoading = true
          if (that.isAdd) {
            addRole(that.formData).then(res => {
              that.submitLoading = false
              this.$emit('success')
            }).catch(er => {
              that.submitLoading = false
              console.error(er)
              this.$emit('fail')
            })
            return
          }
          updateRole(that.formData).then(res => {
            that.submitLoading = false
            this.$emit('success')
          }).catch(er => {
            that.submitLoading = false
            console.error(er)
            this.$emit('fail')
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    }
  }
}
</script>
