<template>
  <div class="authorize-role">

    <el-drawer
      title="角色授权"
      :visible="true"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div class="drawer-form">
        <div class="auto-tree">
          <el-tree
            ref="tree"
            v-loading="treeLoading"
            :data="menus"
            :expand-on-click-node="false"
            node-key="id"
            show-checkbox
            :props="treeProps"
          />
        </div>

        <div class="drawer-footer">
          <el-button @click="handleClose"> 取消 </el-button>
          <el-button type="primary" :loading="submitLoading" :disabled="submitLoading" @click="handleSubmit">提交</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import { allMenus } from '@/api/ops-system'
import { delArrays } from '@/utils'
export default {
  name: 'AuthorizeRole',
  props: {
    menuIds: {
      type: Array,
      default: () => []
    },
    submitLoading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      treeLoading: false,
      menusList: [],
      menus: [],
      treeProps: {
        label: 'menuName'
      }
    }
  },
  created() {
    this.renderTreeData()
  },
  methods: {
    renderTreeData() {
      const that = this
      that.treeLoading = true
      allMenus().then(res => {
        that.treeLoading = false
        that.menusList = res.body || []
        that.menus = that.menuToTree(res.body)
        if (that.menuIds && that.menuIds.length > 0) {
          const excludeMenuIds = []
          that.menusList.forEach(item => {
            if (that.menuIds.some(id => item.id === id)) {
              excludeMenuIds.push(item.parentId)
            }
          })
          that.$refs.tree.setCheckedKeys(delArrays(that.menuIds, excludeMenuIds), true)
        }
      }).catch(er => {
        that.treeLoading = false
      })
    },
    menuToTree(list) {
      if (!list || list.length === 0) {
        return
      }

      const resultMenus = []
      const childrenMenus = []
      list.forEach(item => {
        if (!item.parentId) {
          resultMenus.push(item)
        } else {
          childrenMenus.push(item)
        }
      })

      resultMenus.forEach(item => {
        findChildren(item)
      })

      function findChildren(item) {
        childrenMenus.forEach(childrenItem => {
          if (item.id === childrenItem.parentId) {
            if (item.children) {
              item.children.push(childrenItem)
            } else {
              item.children = [childrenItem]
            }
            findChildren(childrenItem)
          }
        })
      }
      return resultMenus
    },
    handleClose() {
      if (this.submitLoading === true) {
        this.$opsMessage.warn('Processing submission!')
        return
      }
      this.$emit('close')
    },
    handleSubmit() {
      const halfCheckedKeys = this.$refs.tree.getHalfCheckedKeys()
      this.$emit('submit', halfCheckedKeys.concat(this.$refs.tree.getCheckedKeys()))
    }
  }
}
</script>
