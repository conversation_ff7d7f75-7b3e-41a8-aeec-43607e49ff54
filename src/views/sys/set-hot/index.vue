<template>
  <div class="app-container">
    <div class="filter-container">
      <div class="filter-item">
        <search-room-input @success="searchRoomSuccess" @fail="searchRoomFail" @load="loadSearchRoom" />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :loading="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        新增
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
      @cell-mouse-enter="handleMouseEnter"
    >
      <el-table-column label="归属系统" align="center">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column prop="roomProfile.roomName" label="房间名称" align="center">
        <template slot-scope="scope">
          <el-link v-if="scope.row.roomProfile.roomName" @click="queryRoomDetails(scope.row.roomProfile.id)">
            {{ scope.row.roomProfile.roomName }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="房间封面" align="center" width="150">
        <template slot-scope="scope">
          <el-image
            :lazy="true"
            style="width: 50px; height: 50px"
            :src="scope.row.roomProfile.roomCover"
            :preview-src-list="[scope.row.roomProfile.roomCover]"
          >
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline" />
            </div>
          </el-image>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="weights" label="权重" align="center" /> -->
      <el-table-column label="时间" align="center" width="200">
        <template slot-scope="scope">
          <div>{{ scope.row.startTime | dateFormat('yyyy-MM-dd HH:mm:ss') }}</div>
          <div>{{ scope.row.expiredTime | dateFormat('yyyy-MM-dd HH:mm:ss') }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="statusDesc" label="状态" align="center" />
      <el-table-column fixed="right" label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button type="text" @click.native="handleUpdate()">编辑</el-button>
          <el-button type="text" @click.native="handlDel(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <form-edit
      v-if="formEditVisible"
      :row="thatRow"
      @close="formEditVisible = false"
      @success="formEditSuccess"
    />

    <room-deatils-drawer
      v-if="roomDeatilsDrawerVisible"
      :room-id="roomId"
      @close="roomDeatilsDrawerVisible=false"
    />
  </div>
</template>

<script>
import { pageSetHotRoom, removeSetHotRoom } from '@/api/sys'
import Pagination from '@/components/Pagination'
import { sysOriginPlatforms } from '@/constant/origin'
import FormEdit from './form-edit.vue'
import { mapGetters } from 'vuex'
import RoomDeatilsDrawer from '@/components/data/RoomDeatilsDrawer'

export default {
  components: { Pagination, FormEdit, RoomDeatilsDrawer },
  data() {
    return {
      searchDisabled: false,
      thatRow: {},
      // ---
      sysOriginPlatforms,
      txtVal: 0,
      activeGiftId: '',
      pushTextHistoryLoading: false,
      pushTextHistoryVisible: false,
      pushTextHistory: [],
      list: [],
      delarr: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        showcase: 1,
        sysOrigin: 'MARCIE'
      },
      formEditVisible: false,
      textOptTitle: '',
      listLoading: true,
      roomDeatilsDrawerVisible: false,
      roomId: ''
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        this.listQuery.cursor = 1
        this.listQuery.list = []
      }
      that.listLoading = true
      pageSetHotRoom(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    renderDataSuccess() {
      this.$opsMessage.success()
      this.renderData()
    },
    queryUserDetails(row) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = row.id
    },
    // 删除
    handlDel(row) {
      this.$confirm('确认删除吗？', '提示', {
        type: 'warning'
      }).then(() => {
        this.listLoading = true
        removeSetHotRoom(row.roomId).then((res) => {
          this.listLoading = false
          this.$opsMessage.success()
          this.renderData()
        })
      }).catch(() => {
        this.listLoading = false
      })
    },
    handleCreate() {
      this.thatRow = null
      this.formEditVisible = true
    },
    handleUpdate() {
      this.formEditVisible = true
    },
    handleMouseEnter(row) {
      this.thatRow = row
    },
    formEditSuccess() {
      this.formEditVisible = false
      this.renderData()
    },
    loadSearchRoom() {
      this.searchDisabled = true
    },
    searchRoomSuccess(res) {
      this.searchDisabled = false
      if (!res) {
        return
      }
      this.listQuery.roomId = res.id
    },
    searchRoomFail() {
      this.listQuery.roomId = ''
      this.searchDisabled = false
    },
    queryRoomDetails(roomId) {
      this.roomDeatilsDrawerVisible = true
      this.roomId = roomId
    }
  }
}
</script>
<style scoped lang="scss">
.popover-content {
  max-width: 300px;
  line-height: 20px;
}
</style>
