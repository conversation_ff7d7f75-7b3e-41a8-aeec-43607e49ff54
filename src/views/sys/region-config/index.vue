<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="item in tables" :key="item.name" :label="item.title" :name="item.component" />
      <component :is="activeName" />
    </el-tabs>
  </div>
</template>
<script>
import RegionConfig from './region'
import RegionAssistConfig from './assist'
export default {
  name: 'GameConfigIndex',
  components: { RegionConfig, RegionAssistConfig },
  data() {
    return {
      activeName: 'RegionConfig',
      tables: [
        {
          title: '区域列表',
          component: 'RegionConfig'
        },
        {
          title: '辅助配置',
          component: 'RegionAssistConfig'
        }
      ]
    }
  }
}
</script>
