<template>
  <div class="app-container-assist">

    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width:120px;"
        class="filter-item"
        @change="handleSysOriginSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select v-model="listQuery.regionId" v-loading="loading" clearable style="width:150px;" class="filter-item" placeholder="请选择" @change="handleSearch">
        <el-option
          v-for="(item, index) in regions"
          :key="index"
          :label="item.regionName"
          :value="item.id"
        />
      </el-select>
      <el-select
        v-model="listQuery.type"
        clearable
        placeholder="业务类型"
        style="width:250px;"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option v-for="(item) in regionAssistTypes" :key="item.value" :label="item.name" :value="item.value" />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        新增
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column label="区域" prop="regionName" align="center" />
      <el-table-column label="业务类型" align="center" prop="typeName" />
      <el-table-column label="值" align="center" min-width="100">
        <template slot-scope="scope">
          {{ getDesc(scope.row) }}
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="50">
        <template slot-scope="scope">
          <el-dropdown>
            <span class="el-dropdown-link">
              <i class="el-icon-more" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="handleUpdate(scope.row)">修改</el-dropdown-item>
              <el-dropdown-item @click.native="handleDelete(scope.row)">删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <el-drawer
      :title="textOptTitle + '('+listQuery.sysOrigin+')'"
      :visible.sync="formVisible"
      :before-close="handleClose"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="drawer-auto-layout"
    >
      <div v-loading="submitLoading">
        <div class="drawer-form">
          <el-form ref="form" :model="form" :rules="rules" label-width="110px">
            <el-form-item label-width="0">
              <el-col :md="12" :sm="24" class="col-margin">
                <el-form-item prop="type" label="业务类型">
                  <el-select
                    v-model="form.type"
                    placeholder="业务类型"
                    style="width:100%;"
                    class="filter-item"
                    :disabled="form.id !== ''"
                  >
                    <el-option v-for="(item) in regionAssistTypes" :key="item.value" :label="item.name" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="12" :sm="24">
                <el-form-item prop="regionId" label="区域">
                  <el-select v-model="form.regionId" v-loading="loading" style="width:100%;" class="filter-item" placeholder="请选择" :disabled="form.id !== ''">
                    <el-option
                      v-for="(item, index) in regions"
                      :key="index"
                      :label="item.regionName"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>

            </el-form-item>
            <el-form-item v-if="'DIAMOND_EXCHANGE_GOLD' === form.type" label-width="110px">
              <el-input v-model="form.data" v-number placeholder="钻石兑换金币比例(%)" />
            </el-form-item>
            <el-form-item v-if="'MIN_EXCHANGE_DIAMOND' === form.type" label-width="110px">
              <el-input v-model="form.data" v-number placeholder="钻石起兑最小数量" />
            </el-form-item>
            <el-form-item v-if="'WITHDRAW_PROPORTION_TIPS' === form.type" label-width="0">
              <el-col :md="12" :sm="24">
                <el-form-item label=">=美元">
                  <el-input v-model="data.start" v-number placeholder="大于等于多少美元" />
                </el-form-item>
              </el-col>
              <el-col :md="12" :sm="24">
                <el-form-item label="<=美元">
                  <el-input v-model="data.end" v-number placeholder="小于等于多少美元" />
                </el-form-item>
              </el-col>
              <el-col :md="12" :sm="24">
                <el-form-item label="1$:金币">
                  <el-input v-model="data.value" placeholder="1$可获得金币数量" />
                </el-form-item>
              </el-col>
              <el-col :md="12" :sm="24">
                <el-form-item label="提示文案">
                  <el-input v-model="data.text" placeholder="提示文案" />
                </el-form-item>
              </el-col>
            </el-form-item>

            <el-form-item v-if="'WITHDRAW_PROPORTION_DIAMOND_TIPS' === form.type" label-width="0">
              <el-col :md="12" :sm="24">
                <el-form-item label=">=钻石">
                  <el-input v-model="data.start" v-number placeholder="大于等于多少钻石" />
                </el-form-item>
              </el-col>
              <el-col :md="12" :sm="24">
                <el-form-item label="<=钻石">
                  <el-input v-model="data.end" v-number placeholder="小于等于多少钻石" />
                </el-form-item>
              </el-col>
              <el-col :md="12" :sm="24">
                <el-form-item label="1钻石:金币">
                  <el-input v-model="data.value" placeholder="1钻石可获得金币数量" />
                </el-form-item>
              </el-col>
              <el-col :md="12" :sm="24">
                <el-form-item label="提示文案">
                  <el-input v-model="data.text" placeholder="提示文案" />
                </el-form-item>
              </el-col>
            </el-form-item>

            <el-form-item v-if="'WITHDRAW_PROPORTION_DIAMOND_USD_TIPS' === form.type" label-width="0">
              <el-col :md="12" :sm="24">
                <el-form-item label=">=钻石">
                  <el-input v-model="data.start" v-number placeholder="大于等于多少钻石" />
                </el-form-item>
              </el-col>
              <el-col :md="12" :sm="24">
                <el-form-item label="<=钻石">
                  <el-input v-model="data.end" v-number placeholder="小于等于多少钻石" />
                </el-form-item>
              </el-col>
              <el-col :md="12" :sm="24">
                <el-form-item label="1$：钻石">
                  <el-input v-model="data.value" placeholder="1美金需要多少钻石数量" />
                </el-form-item>
              </el-col>
              <el-col :md="12" :sm="24">
                <el-form-item label="提示文案">
                  <el-input v-model="data.text" placeholder="提示文案" />
                </el-form-item>
              </el-col>
            </el-form-item>

            <el-form-item label-width="0">
              <el-col v-if="'RESIDUE_TARGET_EXCHANGE_PROPORTION' === form.type" :md="12" :sm="24">
                <el-form-item :label="(form.data > 0 ? form.data : '?') + '目标=1美元$'" prop="data">
                  <el-input v-model="form.data" v-number placeholder="目标数量" />
                </el-form-item>
              </el-col>
              <el-col
                v-if="'ROOM_CONTRIBUTION_ACTIVITY_RATIO' === form.type || 'GIFT_TO_OWN_GOLD_RATIO' === form.type || 'GIFT_TO_OTHER_GOLD_RATIO' === form.type || 'HOST_TERMINATION_FEE' === form.type || 'WITHDRAWAL_COMMISSION_RATIO' === form.type
                   ||'GIFT_TO_OWN_GOLD_RATIO_AGENCY' === form.type||'GIFT_TO_OTHER_GOLD_RATIO_AGENCY' === form.type ||'GIFT_TARGET_RATIO' === form.type"
                :md="12"
                :sm="24">
                <el-form-item label="值" prop="data">
                  <el-input v-model="form.data" v-number placeholder="请输入内容" />
                </el-form-item>
              </el-col>
            </el-form-item>
            <el-form-item v-if="'ROOM_CONTRIBUTION_ACTIVITY_RATIO' === form.type" label-width="0">
              <el-col :md="12" :sm="24">
                <el-form-item label="图片">
                  <upload-image
                    v-model="form.imgUrl"
                    :file-dir="$application.fileBucket.back"
                  />
                </el-form-item>
              </el-col>
            </el-form-item>
          </el-form>
        </div>
        <div class="drawer-footer">
          <el-button type="primary" @click="submitForm()">保存</el-button>
          <el-button @click="handleClose()">取消</el-button>
        </div>
      </div>
    </el-drawer>

  </div>
</template>

<script>
import { regionAssistConfigTable, updateRegionAssistConfig, addRegionAssistConfig, deleteRegionAssistConfig, regionConfigTable } from '@/api/sys'
import { regionAssistTypes } from '@/constant/type'
import { mapGetters } from 'vuex'

function getFormData() {
  return {
    id: '',
    sysOrigin: '',
    regionId: '',
    type: '',
    data: '',
    imgUrl: ''
  }
}
export default {
  name: 'RegionAssistConfig',
  data() {
    return {
      list: [],
      regionAssistTypes,
      regions: [],
      loading: false,
      listQuery: {
        sysOrigin: 'HALAR',
        type: '',
        regionId: ''
      },
      formVisible: false,
      textOptTitle: '',
      form: getFormData(),
      submitLoading: false,
      rules: {
        regionId: [
          { required: true, message: '请选择区域', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择业务类型', trigger: 'blur' }
        ],
        data: [
          { required: true, message: '请填写值', trigger: 'blur' }
        ]
      },
      data: {},
      listLoading: true
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    this.listRegion()
    this.renderData()
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      regionAssistConfigTable(that.listQuery).then(res => {
        const { body } = res
        that.list = body
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData()
    },
    handleSysOriginSearch() {
      this.listRegion()
      this.renderData()
    },
    listRegion() {
      const that = this
      that.loading = true
      regionConfigTable({ 'sysOrigin': that.listQuery.sysOrigin }).then(res => {
        that.regions = res.body || []
        that.loading = false
      }).catch(er => {
        that.loading = false
      })
    },
    submitForm() {
      const that = this
      if (that.form.type === 'WITHDRAW_PROPORTION_TIPS' || that.form.type === 'WITHDRAW_PROPORTION_DIAMOND_TIPS' || that.form.type === 'WITHDRAW_PROPORTION_DIAMOND_USD_TIPS') {
        that.form.data = JSON.stringify(that.data)
      }
      that.$refs.form.validate(valid => {
        if (valid) {
          that.submitLoading = true
          that.form.sysOrigin = that.listQuery.sysOrigin

          if (that.form.id) {
            updateRegionAssistConfig(that.form).then(res => {
              that.submitLoading = false
              that.formVisible = false
              that.form = getFormData()
              that.renderData()
            }).catch(er => {
              that.submitLoading = false
              console.error(er)
              this.$emit('fail')
            })
            return
          }
          addRegionAssistConfig(that.form).then(res => {
            that.submitLoading = false
            that.formVisible = false
            that.form = getFormData()
            that.renderData()
          }).catch(er => {
            that.submitLoading = false
            console.error(er)
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    },
    handleClose() {
      this.formVisible = false
    },
    handleCreate() {
      this.formVisible = true
      this.textOptTitle = '添加'
      this.form = getFormData()
    },
    getDesc(row) {
      if (row.type === 'WITHDRAW_PROPORTION_TIPS') {
        const obj = JSON.parse(row.data)
        return '(1$ : ' + obj.value + ') 范围:' + obj.start + '~' + obj.end + ' 提示:' + obj.text
      }
      if (row.type === 'WITHDRAW_PROPORTION_DIAMOND_TIPS' || row.type === 'WITHDRAW_PROPORTION_DIAMOND_USD_TIPS') {
        const obj = JSON.parse(row.data)
        return '(1钻石 : ' + obj.value + ') 范围:' + obj.start + '~' + obj.end + ' 提示:' + obj.text
      }
      return row.data
    },
    handleUpdate(row) {
      this.textOptTitle = '修改'
      this.formVisible = true

      const resForm = Object.assign({}, row)
      this.form = resForm
      if (resForm.type === 'WITHDRAW_PROPORTION_TIPS' || row.type === 'WITHDRAW_PROPORTION_DIAMOND_TIPS' || row.type === 'WITHDRAW_PROPORTION_DIAMOND_USD_TIPS') {
        this.data = JSON.parse(resForm.data)
      }
    },
    handleDelete(row) {
      const that = this
      that.$confirm('确认删除吗？', '提示', {
        type: 'warning'
      }).then(() => {
        that.listLoading = true
        deleteRegionAssistConfig(row.id).then((res) => {
          that.listLoading = false
          that.$message.success('删除成功')
          that.renderData()
        })
      }).catch(() => {

      })
    }
  }
}
</script>

