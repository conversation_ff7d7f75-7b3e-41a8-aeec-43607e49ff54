<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.resourceName"
        placeholder="资源名"
        style="width: 200px;"
        class="filter-item"
      />
      <el-select v-model="listQuery.method" placeholder="请求方式" clearable style="width: 120px" class="filter-item">
        <el-option v-for="item in requestTypes" :key="item" :label="item" :value="item" />
      </el-select>
      <el-select v-model="listQuery.authType" placeholder=" 权限类型" clearable style="width: 120px" class="filter-item">
        <el-option v-for="(item,index) in grantTypes" :key="index" :label="item.name" :value="item.value" />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="listLoading"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-refresh-left"
        :loading="resetResourcesLoading"
        :disabled="resetResourcesLoading"
        @click="handleResetResouces"
      >
        刷新资源
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="id" label="ID" align="center" width="95" />
      <el-table-column prop="resourceName" label="资源名称" align="center" />
      <el-table-column prop="method" label="请求方式" align="center" />
      <el-table-column prop="mapping" label="映射路径" align="center" />
      <el-table-column prop="authType" label="授权类型" align="center" />
      <el-table-column prop="perm" label="权限标识" align="center" />
      <el-table-column prop="updateTime" label="修改时间" width="200" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />
  </div>
</template>

<script>
import { pageResources, resetResources } from '@/api/ops-system'
import Pagination from '@/components/Pagination'

export default {
  name: 'ResourcesManager',
  components: { Pagination },
  data() {
    return {
      resetResourcesLoading: false,
      requestTypes: ['GET', 'POST', 'PUT', 'DELETE'],
      grantTypes: [{ value: '1', name: '需要登录' }, { value: '2', name: '无需鉴权' }, { value: '3', name: '需要鉴权' }],
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        resourceName: '',
        authType: '',
        method: ''
      },
      listLoading: false
    }
  },
  created() {
    this.renderData(true)
  },
  methods: {
    renderData(isClean) {
      const that = this
      that.listLoading = true
      if (isClean === true) {
        that.listQuery.cursor = 1
      }
      pageResources(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    handleResetResouces() {
      const that = this
      that.resetResourcesLoading = true
      resetResources().then(res => {
        that.resetResourcesLoading = false
        that.$opsMessage.success()
      }).catch(er => {
        that.resetResourcesLoading = false
      })
    }
  }
}
</script>
