<template>
  <div class="app-container">

    <div class="filter-container">
      <el-input
        v-model.trim="listQuery.countryName"
        clearable
        placeholder="英文国家名称"
        style="width: 200px;"
        class="filter-item"
      />
      <el-input
        v-model.trim="listQuery.aliasName"
        clearable
        placeholder="别名"
        style="width: 200px;"
        class="filter-item"
      />
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >
      <el-table-column prop="countryName" label="国家名称" align="center" width="200">
        <template slot-scope="scope">
          <div v-if="scope.row.top === 1" class="mark-block mark-danger">置顶</div>
          <div>{{ scope.row.countryName }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="aliasName" label="别名" align="center" />
      <el-table-column width="200" label="国旗" align="center">
        <template slot-scope="scope">
          <!--          <flag-icon :code="scope.row.alphaTwo" :tooltip="scope.row.enName" />-->
          <el-image
            style="width: 45px; height: 30px"
            :src="scope.row.nationalFlag"
            :preview-src-list="[scope.row.nationalFlag]"
          >
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline" />
            </div>
          </el-image>
        </template>
      </el-table-column>
      <el-table-column prop="open" label="开放状态" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.open === true">
            已开放
          </span>
          <span v-else> 未开放</span>
        </template>
      </el-table-column>
      <el-table-column prop="phonePrefix" label="手机号码前缀" align="center" />
      <el-table-column prop="sort" label="排序" align="center" />
      <el-table-column prop="createTime" label="创建时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button type="text" @click.native="handleUpdate(scope.row)">修改</el-button>
          <el-button v-if="scope.row.top === 0" type="text" @click="toTop(scope.row.id,1)">置顶</el-button>
          <el-button v-if="scope.row.top === 1" type="text" @click="toTop(scope.row.id,0)">取消置顶</el-button>
          <el-button v-if="scope.row.open === false" type="text" @click="toOpen(scope.row.id,true)">开放国家</el-button>
          <el-button v-if="scope.row.open === true" type="text" @click="toOpen(scope.row.id,false)">取消开放</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <el-dialog
      :title="textOptTitle"
      :visible.sync="formVisible"
      :before-close="handleClose"
      width="400px"
    >
      <div v-loading="submitLoading">
        <el-form ref="form" :model="form" :rules="rules" label-width="110px">
          <el-form-item label="国旗" prop="nationalFlag">
            <el-upload
              class="upload-demo"
              action=""
              :http-request="httpRequest"
              :show-file-list="false"
              accept="image/png,image/jpg,image/jpeg"
            >
              <el-image v-if="form.nationalFlag" :src="form.nationalFlag" fit="fill" style="width: 50px; height: 50px" />
              <el-button v-loading="uploadLoading" size="small" type="primary">点击上传</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item label="国家名称" prop="countryName">
            <el-input v-model.trim="form.countryName" type="text" />
          </el-form-item>
          <el-form-item label="别名">
            <el-input v-model.trim="form.aliasName" type="text" />
          </el-form-item>
          <el-form-item label="手机号前缀" prop="phonePrefix">
            <el-input v-model.trim="form.phonePrefix" type="number" />
          </el-form-item>
          <el-form-item label="权重序号" prop="sort">
            <el-input v-model.trim="form.sort" type="number" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm()">保存</el-button>
            <el-button @click="handleClose()">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { updateSysCountryCode, pageSysCountryCode } from '@/api/sys-dictionary'
import Pagination from '@/components/Pagination'
import { getAddressUrl } from '@/api/oss'

function getFormData() {
  return {
    id: '',
    nationalFlag: '',
    enName: '',
    aliasName: '',
    phonePrefix: '',
    sort: ''
  }
}

export default {
  name: 'CountryCodeInfo',
  components: { Pagination },
  data() {
    return {
      formVisible: false,
      textOptTitle: '',
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 30,
        countryName: '',
        aliasName: ''
      },
      form: getFormData(),
      submitLoading: false,
      rules: {
        nationalFlag: [
          { required: true, message: '请上传国旗', trigger: 'change' }
        ],
        enName: [
          { required: true, message: '请填写国家名称', trigger: 'blur' }
        ],
        phonePrefix: [
          { required: true, message: '请填写手机号前缀', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '请填写权重序号', trigger: 'blur' }
        ]
      },
      listLoading: true,
      uploadLoading: false
    }
  },
  created() {
    this.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      that.listLoading = true
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      pageSysCountryCode(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    handleClose() {
      // 去除校验
      this.$refs.form.clearValidate()
      this.formVisible = false
    },
    handleUpdate(row) {
      this.textOptTitle = '修改'
      this.formVisible = true
      this.form = Object.assign(this.form, row)
    },
    httpRequest(file) {
      const that = this
      that.uploadLoading = true
      this.$simpleUploadFlie(file).then(res => {
        that.uploadLoading = false
        getAddressUrl(res.name).then((result) => {
          that.form.nationalFlag = result.result
        })
      }).catch(er => {
        that.uploadLoading = false
      })
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (valid) {
          that.submitLoading = true
          if (that.form.id) {
            updateSysCountryCode(that.form).then(res => {
              that.submitLoading = false
              that.formVisible = false
              that.form = getFormData()
              that.renderData()
            }).catch(er => {
              that.submitLoading = false
              that.$emit('fial', er)
            })
          }
        } else {
          console.error('error submit!!')
          return false
        }
      })
    },
    toTop(id, top) {
      const that = this
      that.listLoading = true
      updateSysCountryCode({
        id: id,
        top: top
      }).then((res) => {
        that.listLoading = false
        that.$opsMessage.success()
        that.renderData(false)
      }).catch(() => { that.listLoading = false })
    },
    toOpen(id, open) {
      const that = this
      that.listLoading = true
      updateSysCountryCode({
        id: id,
        open: open
      }).then((res) => {
        that.listLoading = false
        that.$opsMessage.success()
        that.renderData(false)
      }).catch(() => { that.listLoading = false })
    }
  }
}
</script>
<style scoped lang="scss">
  .bottom {
    color: #666;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 25px;
    .line {
      display: flex;
      >div {
        width: 100%;
      }
    }
  }

</style>
