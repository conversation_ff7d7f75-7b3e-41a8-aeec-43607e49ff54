<template>
  <div class="start-page-form-edite">
    <el-dialog
      :title="textOptTitle"
      :visible="true"
      :before-close="handleClose"
      width="550px"
      top="50px"
    >
      <div v-loading="submitLoading">
        <div class="form-contetn">
          <el-form ref="form" :model="form" :rules="rules" label-width="110px" style="margin-right: 50px;">
            <el-form-item label="封面" prop="cover">

              <el-form-item prop="cover" class="upload">
                <el-upload
                  :disabled="coverUploadLoading"
                  :file-list="coverFileList"
                  :class="{'upload-but-hide': !isShowCoverUpload}"
                  action=""
                  list-type="picture-card"
                  :http-request="uploadCover"
                  :show-file-list="!isShowCoverUpload"
                  :on-remove="handleCoverFileRemove"
                  accept="image/*"
                >
                  <i slot="default" v-loading="coverUploadLoading" class="el-icon-plus" />
                </el-upload>
              </el-form-item>
            </el-form-item>
            <el-form-item label="系统" prop="sysOrigin">
              <el-select
                v-model="form.sysOrigin"
                placeholder="系统"
                style="width:100%;"
                class="filter-item"
              >
                <el-option
                  v-for="item in sysOriginPlatforms"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
                  <span style="float: left;margin-left:10px">{{ item.label }}</span>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="类型" prop="type">
              <el-select
                v-model="form.type"
                placeholder="活动类型"
                clearable
                style="width:100%;"
                class="filter-item"
              >
                <el-option
                  v-for="item in appStartPagePlanTypes"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                >
                  <span>{{ item.name }}</span>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="过期时间" prop="expireTime">
              <el-date-picker
                v-model="form.expireTime"
                value-format="timestamp"
                placeholder="选择日期时间"
                style="width:100%;"
              />
            </el-form-item>
          </el-form>
        </div>
        <div slot="footer" style="text-align: right;">
          <el-button @click="handleClose()">取消</el-button>
          <el-button type="primary" @click="submitForm()">保存</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>

import { updateData, addData } from '@/api/start-page'
import { sysOriginPlatforms } from '@/constant/origin'
import { getElementUiUploadFile } from '@/utils'
import { appStartPagePlanTypes } from '@/constant/type'
export default {
  props: {
    row: {
      type: Object,
      default: null
    }
  },
  data() {
    const commonRules = [{ required: true, message: '必填字段', trigger: 'blur' }]
    return {
      appStartPagePlanTypes,
      coverUploadLoading: false,
      coverFileList: [],
      sysOriginPlatforms,
      form: {
        id: '',
        cover: '',
        type: '',
        sysOrigin: '',
        expireTime: ''
      },
      submitLoading: false,
      uploadLoading: false,
      rules: {
        cover: commonRules,
        type: commonRules,
        sysOrigin: commonRules,
        expireTime: commonRules
      }
    }
  },
  computed: {
    textOptTitle() {
      return this.row && this.row.id ? '修改' : '新增'
    },
    isShowCoverUpload() {
      return !this.form.cover
    }
  },
  watch: {
    row: {
      handler(val) {
        if (!val) {
          return
        }
        const form = Object.assign({}, val)
        this.coverFileList = getElementUiUploadFile(form.cover)
        this.form = Object.assign(this.form, form)
      },
      immediate: true
    }
  },
  methods: {
    uploadCover(file) {
      const that = this
      that.coverUploadLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.startPage).then(res => {
        that.coverUploadLoading = false
        that.form.cover = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.coverUploadLoading = false
      })
    },
    handleCoverFileRemove(file, fileList) {
      this.form.cover = ''
      this.coverUploadLoading = false
    },
    handleClose() {
      this.$emit('close')
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return
        }
        const submitForm = Object.assign({}, that.form)
        that.submitLoading = true
        if (submitForm.id) {
          updateData(submitForm).then(res => {
            that.submitLoading = false
            that.$emit('success', res)
          }).catch(er => {
            that.submitLoading = false
            that.$emit('fial', er)
          })
          return
        }
        addData(submitForm).then(res => {
          that.submitLoading = false
          that.$emit('success', res)
        }).catch(er => {
          that.submitLoading = false
          console.error(er)
          that.$emit('fial', er)
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.form-contetn {
    max-height: 550px;
    overflow: auto;
}
.form-autocomplete {
   li {
    line-height: normal;
    padding: 7px;

    .name {
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .label {
      font-size: 12px;
      color: #b4b4b4;
    }

    .highlighted .label {
      color: #ddd;
    }
  }
}
</style>
