<template>
  <div class="app-container-match">

    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width:120px;"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.showcase"
        placeholder="状态"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option label="上架" :value="true" />
        <el-option label="下架" :value="false" />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        新增
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
      @cell-mouse-enter="handleMouseEnter"
    >
      <el-table-column type="expand">
        <template slot-scope="scope">
          <div class="models">
            <div class="flex-c">
              <div v-for="(item, index) in scope.row.models" :key="index" style="margin: 0px 20px">
                <div class="cover">
                  <el-image
                    :lazy="true"
                    style="width: 200px; height: 100px"
                    :src="item.backgroundCover"
                    :preview-src-list="[item.backgroundCover]"
                  >
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline" />
                    </div>
                  </el-image>
                </div>
                <div class="info flex-c">
                  <div style="margin: 10px"> 玩家: {{ item.players }}</div>
                  <div style="margin: 10px">门票: {{ item.amounts }}</div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="封面" align="center" width="60">
        <template slot-scope="scope">
          <el-image
            :lazy="true"
            style="width: 50px; height: 50px"
            :src="scope.row.cover"
            :preview-src-list="[scope.row.cover]"
          />
        </template>
      </el-table-column>
      <el-table-column label="背景" align="center" width="110">
        <template slot-scope="scope">
          <el-image
            :lazy="true"
            style="width: 100px; height: 50px"
            :src="scope.row.backgroundCover"
            :preview-src-list="[scope.row.backgroundCover]"
          />
        </template>
      </el-table-column>
      <el-table-column prop="name" label="名称" align="center" />
      <el-table-column prop="gameCode" label="游戏编号" align="center" />
      <el-table-column label="系统" align="center">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column prop="sort" label="排序" align="center" />
      <el-table-column label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button type="text" @click.native="handleUpdate()">修改</el-button>
          <el-button type="text" @click.native="handlDel(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <form-edit
      v-if="formEditVisible"
      :row="thatRow"
      @close="formEditVisible = false"
      @success="formEditSuccess"
    />
  </div>
</template>

<script>
import { flowGameMatchConfig, delGameMatchConfig } from '@/api/sys'
import { sysOriginPlatforms } from '@/constant/origin'
import FormEdit from './form-edit.vue'
import { mapGetters } from 'vuex'

export default {
  components: { FormEdit },
  data() {
    return {
      thatRow: {},
      sysOriginPlatforms,
      list: [],
      listQuery: {
        limit: 50,
        showcase: true,
        sysOrigin: 'HALAR'
      },
      formEditVisible: false,
      textOptTitle: '',
      listLoading: true
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        this.listQuery.cursor = 1
        this.listQuery.list = []
      }
      that.listLoading = true
      flowGameMatchConfig(that.listQuery).then(res => {
        that.listLoading = false
        const { body } = res
        that.list = body || []
      }).catch(er => {
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    renderDataSuccess() {
      this.$opsMessage.success()
      this.renderData()
    },
    queryUserDetails(row) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = row.id
    },
    // 删除
    handlDel(row) {
      const that = this
      that.$confirm('确认删除吗？', '提示', {
        type: 'warning'
      }).then(() => {
        that.listLoading = true
        delGameMatchConfig(row.id).then((res) => {
          that.listLoading = false
          that.$opsMessage.success()
          that.renderData()
        })
      }).catch(() => {

      })
    },
    handleCreate() {
      this.thatRow = null
      this.formEditVisible = true
    },
    handleUpdate() {
      this.formEditVisible = true
    },
    handleMouseEnter(row) {
      this.thatRow = row
    },
    formEditSuccess() {
      this.formEditVisible = false
      this.renderData()
    }
  }
}
</script>
<style scoped lang="scss">
.popover-content {
  max-width: 300px;
  line-height: 20px;
}
</style>
