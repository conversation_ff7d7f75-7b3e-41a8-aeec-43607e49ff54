<template>
  <div class="game-form-edite">
    <el-dialog
      :title="textOptTitle"
      :visible="true"
      :before-close="handleClose"
      :close-on-click-modal="false"
      width="550px"
      top="50px"
    >
      <div v-loading="submitLoading">
        <div class="form-contetn">
          <el-form ref="form" :model="form" :rules="rules" label-width="110px" style="margin-right: 50px;">
            <el-form-item label="封面" prop="cover">
              <el-upload
                :disabled="coverUploadLoading"
                :file-list="coverFileList"
                :class="{'upload-but-hide': !isShowCoverUpload}"
                action=""
                list-type="picture-card"
                :http-request="uploadCover"
                :show-file-list="!isShowCoverUpload"
                :on-remove="handleCoverFileRemove"
                accept="image/*"
              >
                <i slot="default" v-loading="coverUploadLoading" class="el-icon-plus" />
              </el-upload>
            </el-form-item>
            <el-form-item label="系统">
              <el-select
                v-model="form.sysOrigin"
                placeholder="选择系统"
                style="width:100%;"
                class="filter-item"
                disabled
                @change="changeSysOrigin"
              >
                <el-option
                  v-for="item in permissionsSysOriginPlatforms"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
                  <span style="float: left;margin-left:10px">{{ item.label }}</span>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="游戏源" prop="gameOrigin">
              <el-select
                v-model="form.gameOrigin"
                placeholder="游戏源"
                style="width:100%;"
                class="filter-item"
              >
                <el-option v-for="item in gameOrigins" :key="item.value" :label="item.name" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="客户端" prop="clientOrigin">
              <el-select
                v-model="form.clientOrigin"
                placeholder="客户端"
                style="width:100%;"
                class="filter-item"
              >
                <el-option v-for="item in clientOrigins" :key="item.value" :label="item.name" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="是否自研" prop="self">
              <el-select
                v-model="form.self"
                placeholder="是否自研"
                clearable
                style="width:100%;"
                class="filter-item"
              >
                <el-option label="是" :value="true" />
                <el-option label="否" :value="false" />
              </el-select>自研游戏下架会影响到其他系统
            </el-form-item>
            <el-form-item v-if="form.self" label="自研游戏Code" prop="selfGameCode">
              <el-input v-model.trim="form.selfGameCode" type="text" placeholder="请输入自研游戏Code" minlength="1" maxlength="150" show-word-limit />
            </el-form-item>
            <el-form-item label="名称" prop="name">
              <el-input v-model.trim="form.name" type="text" placeholder="请输入名称" minlength="1" maxlength="150" show-word-limit />
            </el-form-item>
            <el-form-item label="编号" prop="gameCode">
              <el-input v-model.trim="form.gameCode" type="text" placeholder="请输入编号" minlength="1" maxlength="150" show-word-limit />
            </el-form-item>
            <el-form-item label="金额" prop="gameCode">
              <el-input v-model.trim="form.amounts" type="text" placeholder="金币必须是整数多个逗号隔改" minlength="1" maxlength="150" show-word-limit />
            </el-form-item>
            <el-form-item label="是否全屏" prop="fullScreen">
              <el-select
                v-model="form.fullScreen"
                placeholder="是否全屏"
                clearable
                style="width:100%;"
                class="filter-item"
              >
                <el-option label="是" :value="true" />
                <el-option label="否" :value="false" />
              </el-select>
            </el-form-item>
            <el-form-item label="宽" prop="width">
              <el-input v-model.trim="form.width" type="text" placeholder="宽度" />
            </el-form-item>
            <el-form-item label="高" prop="height">
              <el-input v-model.trim="form.height" type="text" placeholder="高度" />
            </el-form-item>
            <el-form-item label="状态" prop="showcase">
              <el-select
                v-model="form.showcase"
                placeholder="状态"
                clearable
                style="width:100%;"
                class="filter-item"
              >
                <el-option label="下架" :value="false" />
                <el-option label="上架" :value="true" />
              </el-select>
            </el-form-item>
            <el-form-item label="排序" prop="sort">
              <el-input-number v-model.trim="form.sort" v-number style="width: 100%;" :min="0" :max="99999" placeholder="降序排列(数字越大越靠前)" />
            </el-form-item>
            <el-form-item label="区域" prop="regionList">
              <el-select v-model="form.regionList" v-loading="loading" multiple clearable placeholder="请选择" style="width:100%;" class="filter-item">
                <el-option
                  v-for="(item, index) in regions"
                  :key="index"
                  :label="item.regionName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div slot="footer" style="text-align: right;">
          <el-button @click="handleClose()">取消</el-button>
          <el-button type="primary" @click="submitForm()">保存</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>

import { saveOrUpdateGameConfig, regionConfigTable } from '@/api/sys'
import { getElementUiUploadFile } from '@/utils'
import { mapGetters } from 'vuex'
export default {
  props: {
    row: {
      type: Object,
      default: null
    },
    listquery: {
      type: String,
      default: null
    }
  },
  data() {
    const commonRules = [{ required: true, message: '必填字段', trigger: 'blur' }]
    return {
      gameOrigins: [
        { value: 'THIRD_PARTY', name: '第三方游戏' },
        { value: 'APP', name: '原生游戏' },
        { value: 'H5', name: 'H5游戏' },
        { value: 'BAISHUN', name: 'Baishun' },
        { value: 'LINGXIAN', name: 'LingXian' },
        { value: 'HOTGAME', name: 'HOTGAME' }
      ],
      clientOrigins: [
        { value: 'ANDROID', name: '安卓' },
        { value: 'IOS', name: '苹果' },
        { value: 'COMMON', name: '通用' }
      ],
      coverUploadLoading: false,
      coverFileList: [],
      regions: [],
      loading: false,
      form: {
        id: '',
        cover: '',
        sysOrigin: 'YOLO',
        name: '',
        gameCode: '',
        showcase: false,
        sort: 0,
        amounts: '',
        gameOrigin: '',
        height: '',
        width: '',
        clientOrigin: 'COMMON',
        regionList: [],
        fullScreen: '',
        self: '',
        selfGameCode: ''
      },
      submitLoading: false,
      uploadLoading: false,
      rules: {
        cover: commonRules,
        sysOrigin: commonRules,
        gameOrigin: commonRules,
        name: commonRules,
        showcase: commonRules,
        sort: commonRules,
        amounts: commonRules,
        clientOrigin: commonRules,
        fullScreen: commonRules,
        self: commonRules
      }
    }
  },
  computed: {
    textOptTitle() {
      return this.row && this.row.id ? '修改' : '新增'
    },
    isShowCoverUpload() {
      return !this.form.cover
    },
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  watch: {
    row: {
      handler(val) {
        this.listRegion()
        if (!val) {
          return
        }
        const form = Object.assign({}, val)
        if (form.regions) {
          form.regionList = form.regions.split(',')
        }
        this.coverFileList = getElementUiUploadFile(form.cover)
        this.form = Object.assign(this.form, form)
        if (this.form.selfGameCode) {
          this.form.self = true
          return
        }
        this.form.self = false
      },
      immediate: true
    }
  },
  methods: {
    uploadCover(file) {
      const that = this
      that.coverUploadLoading = true
      that.$simpleUploadFlie(file, that.$application.fileBucket.other).then(res => {
        that.coverUploadLoading = false
        that.form.cover = that.$getAccessImgUrl(res.name)
      }).catch(er => {
        that.coverUploadLoading = false
      })
    },
    handleCoverFileRemove(file, fileList) {
      this.form.cover = ''
      this.coverUploadLoading = false
    },
    listRegion() {
      const that = this
      that.loading = true
      that.form.sysOrigin = that.listquery
      regionConfigTable({ 'sysOrigin': that.form.sysOrigin }).then(res => {
        that.regions = res.body || []
        that.loading = false
      }).catch(er => {
        that.loading = false
      })
    },
    handleClose() {
      this.$emit('close')
    },
    changeSysOrigin() {
      this.listRegion()
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return
        }
        if (that.form.self && !that.form.selfGameCode) {
          that.$opsMessage.info('必须填写自研游戏code')
        }
        const submitForm = Object.assign({}, that.form)
        that.submitLoading = true
        const amountsArrays = submitForm.amounts.split(',')
        submitForm.amounts = amountsArrays.filter(item => !isNaN(parseFloat(item)) && isFinite(item)).join(',')
        saveOrUpdateGameConfig(submitForm).then(res => {
          that.submitLoading = false
          that.$emit('success', res)
        }).catch(er => {
          that.submitLoading = false
          that.$emit('fial', er)
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.form-contetn {
    max-height: 550px;
    overflow: auto;
}
.form-autocomplete {
   li {
    line-height: normal;
    padding: 7px;

    .name {
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .label {
      font-size: 12px;
      color: #b4b4b4;
    }

    .highlighted .label {
      color: #ddd;
    }
  }
}
</style>
