<template>
  <div class="app-container-room">

    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width:120px;"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.gameOrigin"
        placeholder="游戏源"
        style="width:120px;"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option v-for="item in gameOrigins" :key="item.value" :label="item.name" :value="item.value" />
      </el-select>
      <el-select
        v-model="listQuery.showcase"
        placeholder="状态"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option label="上架" :value="true" />
        <el-option label="下架" :value="false" />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        新增
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
      @cell-mouse-enter="handleMouseEnter"
    >
      <el-table-column label="系统" align="center">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.sysOrigin" :desc="scope.row.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column label="封面" align="center" width="60">
        <template slot-scope="scope">
          <el-image
            :lazy="true"
            style="width: 50px; height: 50px"
            :src="scope.row.cover"
            :preview-src-list="[scope.row.cover]"
          />
        </template>
      </el-table-column>
      <el-table-column prop="name" label="名称" align="center" />
      <el-table-column prop="gameCode" label="游戏编号" align="center" />
      <el-table-column prop="amounts" label="金额" align="center" />
      <el-table-column label="客户端" align="center">
        <template slot-scope="scope">
          <span v-for="(item, index) in clientOrigins" :key="index">
            <el-tag v-if="item.value == scope.row.clientOrigin">{{ item.name }}</el-tag>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="是否全屏" align="center">
        <template slot-scope="scope">
          <el-tag type="success">{{ scope.row.fullScreen ? '是' : '否' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="sort" label="排序" align="center" />
      <el-table-column fixed="right" label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button v-if="scope.row.selfGameCode == 'GAME_FRUIT'" type="text" @click.native="dataSetting(scope.row)">数据配置</el-button>
          <el-button type="text" @click.native="handleUpdate()">修改</el-button>
          <el-button type="text" @click.native="handlDel(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <fruit-edit
      v-if="configDataShow"
      :code="code"
      :sys-origin="sysOrigin"
      @close="configDataShow = false"
      @success="formEditSuccess"
    />

    <form-edit
      v-if="formEditVisible"
      :row="thatRow"
      :listquery="listQuery.sysOrigin"
      @close="formEditVisible = false"
      @success="formEditSuccess"
    />
  </div>
</template>

<script>
import { pageGameConfig, deleteGameConfig } from '@/api/sys'
import fruitEdit from '@/views/sys/game-config/fruit/edit.vue'
import Pagination from '@/components/Pagination'
import { sysOriginPlatforms } from '@/constant/origin'
import FormEdit from './form-edit.vue'
import { mapGetters } from 'vuex'

export default {
  components: { Pagination, FormEdit, fruitEdit },
  data() {
    return {
      gameOrigins: [
        { value: 'THIRD_PARTY', name: '第三方游戏' },
        { value: 'APP', name: '原生游戏' },
        { value: 'H5', name: 'H5游戏' },
        { value: 'BAISHUN', name: 'Baishun' },
        { value: 'LINGXIAN', name: 'LingXian' },
        { value: 'HOTGAME', name: 'HOTGAME' }
      ],
      clientOrigins: [
        { value: 'ANDROID', name: '安卓' },
        { value: 'IOS', name: '苹果' },
        { value: 'COMMON', name: '通用' }
      ],
      thatRow: {},
      configDataShow: false,
      sysOriginPlatforms,
      list: [],
      total: 0,
      code: '',
      sysOrigin: '',
      listQuery: {
        cursor: 1,
        limit: 20,
        showcase: true,
        sysOrigin: 'YOLO',
        gameOrigin: 'H5'
      },
      formEditVisible: false,
      textOptTitle: '',
      listLoading: true
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData(true)
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        this.listQuery.cursor = 1
        this.listQuery.list = []
      }
      that.listLoading = true
      pageGameConfig(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    renderDataSuccess() {
      this.$opsMessage.success()
      this.renderData()
    },
    queryUserDetails(row) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = row.id
    },
    // 删除
    handlDel(row) {
      const that = this
      that.$confirm('确认删除吗？', '提示', {
        type: 'warning'
      }).then(() => {
        that.listLoading = true
        deleteGameConfig(row.id, row.sysOrigin).then((res) => {
          that.listLoading = false
          that.$opsMessage.success()
          that.renderData()
        })
      }).catch(() => {

      })
    },
    handleCreate() {
      this.thatRow = null
      this.formEditVisible = true
    },
    handleUpdate() {
      this.formEditVisible = true
    },
    dataSetting(row) {
      this.configDataShow = true
      this.sysOrigin = row.sysOrigin
      this.code = row.name
    },
    handleMouseEnter(row) {
      this.thatRow = row
    },
    formEditSuccess() {
      this.formEditVisible = false
      this.renderData()
    }
  }
}
</script>
<style scoped lang="scss">
.popover-content {
  max-width: 300px;
  line-height: 20px;
}
</style>
