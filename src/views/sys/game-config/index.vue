<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="item in tables" :key="item.name" :label="item.title" :name="item.component" />
      <component :is="activeName" />
    </el-tabs>
  </div>
</template>
<script>
import GameConfigRoom from './room'
import GameConfigMatch from './match'
export default {
  name: 'GameConfigIndex',
  components: { GameConfigRoom, GameConfigMatch },
  data() {
    return {
      activeName: 'GameConfigRoom',
      tables: [
        {
          title: '房间',
          component: 'GameConfigRoom'
        }
        // {
        //   title: '匹配',
        //   component: 'GameConfigMatch'
        // }
      ]
    }
  }
}
</script>
