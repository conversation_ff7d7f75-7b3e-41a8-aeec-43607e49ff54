<template>
  <div class="app-container">

    <div class="filter-container">
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate()"
      >
        新增
      </el-button>
    </div>
    <div v-loading="listLoading" class="content">
      <div v-for="(item, index) in list" :key="index">
        <div class="item">
          {{ item }}
          <i class="el-icon-delete-solid" @click="handlDel(item)" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { userRechargeBlackList, addUserRechargeBlackList, delUserRechargeBlackList } from '@/api/sys'
export default {
  data() {
    return {
      list: [],
      listLoading: true
    }
  },
  created() {
    const that = this
    that.renderData()
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      userRechargeBlackList().then(res => {
        that.listLoading = false
        const { body } = res
        that.list = body || []
      }).catch(er => {
        that.listLoading = false
      })
    },
    handlDel(content) {
      this.$confirm('确认删除吗？', '提示', {
        type: 'warning'
      }).then(() => {
        delUserRechargeBlackList(content).then((res) => {
          this.$opsMessage.success()
          this.renderData()
        })
      }).catch(() => {
      })
    },
    handleCreate() {
      const that = this
      this.$prompt('输入用户id', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(({ value }) => {
        addUserRechargeBlackList(value).then(res => {
          that.$opsMessage.success()
          that.renderData()
        }).catch(er => {
        })
      }).catch(() => {})
    }
  }
}
</script>

<style scoped lang="scss">
.content {
  .item {
    background-color: #9e9ee64f;
    color: #909399;
    margin-bottom:5px;
    padding:10px;
    border-radius: 5px;
    position: relative;
    i {
      font-size: 24px;
      cursor: pointer;
      color: #fa0e0e;
      position: absolute;
      right: 10px;
      top: 5px;
    }
  }
}
</style>
