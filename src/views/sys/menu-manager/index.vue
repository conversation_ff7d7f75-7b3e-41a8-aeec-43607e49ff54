<template>
  <div class="app-container">
    <el-row :gutter="10">
      <el-col :md="10">
        <menu-tree
          ref="tree"
          @addNode="addNode"
          @removeNode="removeNode"
          @addRootNode="addRootNode"
          @restTree="restTree"
          @updateNode="updateNode"
          @loadSuccess="loadSuccessMenu"
        />
      </el-col>
      <el-col :sm="14">
        <menu-edit
          :node-data="nodeData"
          :event="event"
          @success="menuEditSuccess"
        />
      </el-col>
    </el-row>

  </div>
</template>
<script>
import MenuTree from './menu-tree'
import MenuEdit from './menu-edit'

export default {
  name: 'MenuManager',
  components: { MenuTree, MenuEdit },
  data() {
    return {
      event: 'addRoot',
      nodeData: {}
    }
  },
  methods: {
    addNode(data) {
      this.event = 'addChildren'
      this.nodeData = data
    },
    removeNode(data) {
      this.event = 'addRoot'
      this.nodeData = {}
    },
    addRootNode() {
      this.event = 'addRoot'
      this.nodeData = {}
    },
    restTree() {
    },
    updateNode(data) {
      this.event = 'updateNode'
      this.nodeData = data
      // console.log('clickNode', data)
    },
    menuEditSuccess(event, data) {
      if (event === 'update') {
        this.$refs.tree.updateNodeData(data.id, data)
        return
      }
      if (event === 'create') {
        this.$refs.tree.restTree()
        return
      }
    },
    loadSuccessMenu(data) {
    }
  }
}
</script>

