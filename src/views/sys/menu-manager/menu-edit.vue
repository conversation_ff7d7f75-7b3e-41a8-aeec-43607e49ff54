<template>
  <div class="meun-edit">
    <el-form ref="dataForm" v-loading="formLoading" :model="formData" :rules="rules" label-width="80px">
      <el-form-item label="当前事件">
        <el-tag>{{ eventName }}</el-tag>
      </el-form-item>
      <el-form-item label="菜单名称" prop="menuName">
        <el-input
          v-model.trim="formData.menuName"
          placeholder="请输入菜单名称"
        />
      </el-form-item>
      <el-form-item label="权限名称" prop="alias">
        <el-input
          v-model.trim="formData.alias"
          placeholder="请输入权限名称"
        />
      </el-form-item>
      <el-form-item label="菜单类型" prop="menuType">
        <el-radio-group v-model="formData.menuType">
          <el-radio :label="1">目录</el-radio>
          <el-radio :label="2">菜单</el-radio>
          <el-radio :label="3">按钮</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="菜单图标">
        <el-input v-model="formData.icon" placeholder="icon code" disabled>
          <template v-if="formData.icon" slot="prepend">
            <svg-icon :icon-class="formData.icon" style="font-size:20px;" />
          </template>
          <el-button
            slot="append"
            icon="el-icon-help"
            @click="iconsVisible = true;"
          >选择图标</el-button>
        </el-input>
      </el-form-item>
      <el-form-item label="菜单状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio :label="0">正常</el-radio>
          <el-radio :label="1">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="顺序权重">
        <el-input
          v-model="formData.sort"
          v-number
          placeholder="请输入顺序权重"
        />
      </el-form-item>
      <el-form-item v-if="isMenuTypeToMenu" label="菜单路径" prop="path">
        <el-input
          v-model.trim="formData.path"
          placeholder="请输入菜单路径"
        />
      </el-form-item>
      <el-form-item label="菜单路由" prop="router">
        <el-input
          v-model.trim="formData.router"
          placeholder="请输入菜单路由"
        />
      </el-form-item>
      <el-form-item v-if="isMenuTypeToMenu" label="对应资源">
        <el-select
          v-model="formData.resourceIds"
          multiple
          style="width:100%"
          class="filter-item"
          placeholder="请选择角色"
        >
          <el-option
            v-for="item in resources"
            :key="item.id"
            :label="item.resourceName +'，'+ item.perm"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">提交</el-button>
      </el-form-item>
    </el-form>
    <iconsPanel
      :visible.sync="iconsVisible"
      @iconClick="handlerIconClick"
    />
  </div>
</template>
<script>
import IconsPanel from '@/components/IconsPanel'
import { deepClone } from '@/utils'
import { addMenu, updateMenu, getResources, getMenu } from '@/api/ops-system'

const defaultFormData = () => {
  return {
    menuName: '',
    alias: '',
    menuType: 1,
    icon: '',
    id: '',
    parentId: '0',
    path: '',
    resourceIds: [],
    router: '',
    status: 0,
    sort: 0
  }
}
export default {
  name: 'MenuEdit',
  components: { IconsPanel },
  props: {
    nodeData: {
      type: Object,
      default: null
    },
    /**
     * addRoot: 添加根节点
     * addChildren：添加子节点
     * updateNode: 更新节点
     */
    event: {
      type: String,
      default: 'addRoot'
    }
  },
  data() {
    return {
      formLoading: false,
      submitLoading: false,
      iconsVisible: false,
      formData: defaultFormData(),
      rules: {
        menuName: [{ required: true, message: '菜单名称必填', trigger: 'blur' }],
        menuType: [{ required: true, message: '菜单类型必填', trigger: 'blur' }],
        status: [{ required: true, message: '菜单状态必填', trigger: 'blur' }]
      },
      resources: []
    }
  },
  computed: {
    isMenuTypeToMenu() {
      return this.formData.menuType === 2
    },
    isUpdate() {
      return this.event === 'updateNode'
    },
    isAdd() {
      return this.event === 'addChildren'
    },
    isAddRoot() {
      return this.event === 'addRoot'
    },
    eventName() {
      if (this.isAdd) {
        return `向 ”${this.nodeData.menuName}“ 添加子节点`
      }
      if (this.isUpdate) {
        return `修改 ”${this.nodeData.menuName}“ 节点`
      }

      if (this.isAddRoot) {
        return '添加根节点'
      }
      return '错误的事件'
    }
  },
  watch: {
    nodeData: {
      immediate: true,
      deep: true,
      handler(newVal) {
        const that = this
        if (this.isUpdate) {
          that.formData = deepClone(newVal)
          that.formLoading = true
          getMenu(newVal.id).then(res => {
            that.formLoading = false
            const { body } = res
            that.formData = body
          }).catch(er => {
            that.formLoading = false
          })
          return
        }
        that.formData = defaultFormData()
        if (that.isAdd) {
          that.formData.parentId = newVal.id
          return
        }
      }
    }
  },
  created() {
    this.renderResources()
  },
  methods: {
    renderResources() {
      getResources().then(res => {
        const { body } = res
        this.resources = body || []
      })
    },
    handlerIconClick(value) {
      this.formData.icon = value
    },
    handleSubmit() {
      const that = this
      that.$refs.dataForm.validate(valid => {
        if (valid) {
          that.submitLoading = true
          if (that.isAdd || this.isAddRoot) {
            addMenu(that.formData).then(res => {
              that.submitLoading = false
              this.$emit('success', 'create', deepClone(that.formData))
              const parentId = that.formData.parentId
              that.formData = defaultFormData()
              that.formData.parentId = parentId
            }).catch(er => {
              that.submitLoading = false
              console.error(er)
              this.$emit('fail')
            })
            return
          }
          updateMenu(that.formData).then(res => {
            that.submitLoading = false
            that.$emit('success', 'update', deepClone(that.formData))
          }).catch(er => {
            that.submitLoading = false
            console.error(er)
            that.$emit('fail')
          })
        } else {
          console.error('error submit!!')
          return false
        }
      })
    }
  }
}
</script>
