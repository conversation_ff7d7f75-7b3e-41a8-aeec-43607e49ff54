<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate('IP')"
      >
        新增IP
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate('PHONE_MODEL')"
      >
        新增手机型号
      </el-button>
    </div>
    <div v-loading="listLoading" class="content">
      <div v-for="(item, index) in list" :key="index">
        <div class="item">
          {{ item }}
          <i class="el-icon-delete-solid" @click="handlDel(item)" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  requestBlackList,
  addBlackListPhoneModel,
  addBlackListIP,
  delBlackList
} from '@/api/sys'
export default {
  data() {
    return {
      list: [],
      listQuery: {
        cursor: 1,
        limit: 20,
        sysOrigin: '',
        startTime: '',
        endTime: ''
      },
      listLoading: true
    }
  },
  created() {
    const that = this
    that.renderData()
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      requestBlackList()
        .then(res => {
          that.listLoading = false
          const { body } = res
          that.list = body || []
        })
        .catch(er => {
          that.listLoading = false
        })
    },
    handlDel(content) {
      this.$confirm('确认删除吗？', '提示', {
        type: 'warning'
      })
        .then(() => {
          delBlackList(content).then(res => {
            this.$opsMessage.success()
            this.renderData()
          })
        })
        .catch(() => {})
    },
    handleCreate(type) {
      const that = this
      let tips = '错误的参数类型'
      if (type === 'IP') {
        tips =
          '请输入IP, 格式如:127.0.0.1 或 127.0.0 每少一位表示多屏蔽一个网络段'
      }

      if (type === 'PHONE_MODEL') {
        tips = '请输入手机型号,最终系统保存为大写'
      }
      this.$prompt(tips, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(({ value }) => {
          if (type === 'IP') {
            addBlackListIP(value)
              .then(res => {
                that.$opsMessage.success()
                that.renderData()
              })
              .catch(er => {})
            return
          }

          if (type === 'PHONE_MODEL') {
            addBlackListPhoneModel(value)
              .then(res => {
                that.$opsMessage.success()
                that.renderData()
              })
              .catch(er => {})
            return
          }
          that.$opsMessage.fail('Param type Error.')
        })
        .catch(() => {})
    }
  }
}
</script>

<style scoped lang="scss">
.content {
  .item {
    background-color: #9e9ee64f;
    color: #909399;
    margin-bottom: 5px;
    padding: 10px;
    border-radius: 5px;
    position: relative;
    i {
      font-size: 24px;
      cursor: pointer;
      color: #fa0e0e;
      position: absolute;
      right: 10px;
      top: 5px;
    }
  }
}
</style>
