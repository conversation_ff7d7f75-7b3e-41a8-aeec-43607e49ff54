<template>
  <div class="banner-form-edite">
    <el-dialog
      :title="textOptTitle"
      :visible="true"
      :before-close="handleClose"
      :close-on-click-modal="false"
      width="550px"
      top="50px"
    >
      <div v-loading="submitLoading">
        <div class="form-contetn">
          <el-form ref="form" :model="form" :rules="rules" label-width="110px" style="margin-right: 50px;">
            <el-form-item v-if="!isUpdate" label="房间ID" prop="id">
              <search-room-input @success="searchRoomSuccess" @fail="searchRoomFail" @load="loadSearchRoom" />
            </el-form-item>
            <el-form-item label="置顶区域" prop="region">

              <select-system-region
                ref="regionSelectPolicy"
                v-model="form.region"
                :sys-origin="form.sysOrigin"
                placeholder="请选择区域"
              />

            </el-form-item>

            <el-form-item label="是否永久">
              <el-radio-group v-model="permanent">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="永远展示">
              <el-radio-group v-model="form.foreverOnline">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="form.startTime"
                :picker-options="pickerOptions"
                type="datetime"
                value-format="timestamp"
                placeholder="选择日期时间"
                style="width:100%;"
              />
            </el-form-item>
            <el-form-item v-if="permanent === 0" label="过期时间" prop="expiredTime">
              <el-date-picker
                v-model="form.expiredTime"
                :picker-options="pickerOptions"
                type="datetime"
                value-format="timestamp"
                placeholder="选择日期时间"
                style="width:100%;"
              />
            </el-form-item>
            <el-form-item label="权重" prop="weights">
              <el-input v-model.trim="form.weights" v-number type="text" placeholder="越大越靠前" />
            </el-form-item>
          </el-form>
        </div>
        <div slot="footer" style="text-align: right;">
          <el-button @click="handleClose()">取消</el-button>
          <el-button type="primary" :disabled="searchDisabled" @click="submitForm()">保存</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>

import { addOrUpdateSetTopRoom } from '@/api/sys'

export default {
  props: {
    row: {
      type: Object,
      default: null
    }
  },
  data() {
    const commonRules = [{ required: true, message: '必填字段', trigger: 'blur' }]
    return {
      permanent: 0,
      form: {
        id: '',
        sysOrigin: '',
        startTime: '',
        expiredTime: '',
        weights: '',
        region: ''
      },
      searchDisabled: false,
      submitLoading: false,
      uploadLoading: false,
      rules: {
        id: commonRules,
        startTime: commonRules,
        expiredTime: commonRules,
        weights: commonRules,
        region: commonRules,
        foreverOnline: commonRules
      },
      pickerOptions: {
        disabledDate(date) {
          return date.getTime() < Date.now()
        }
      }
    }
  },
  computed: {
    isUpdate() {
      return !!(this.row && this.row.id)
    },
    textOptTitle() {
      return this.isUpdate ? '修改' : '添加'
    }

  },
  watch: {
    row: {
      handler(val) {
        if (!val) {
          return
        }
        this.form = Object.assign(this.form, val)
        this.permanent = this.form.expiredTime ? 0 : 1
      },
      immediate: true
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    submitForm() {
      const that = this
      that.$refs.form.validate(valid => {
        if (!valid) {
          console.error('error submit!!')
          return
        }

        if (that.permanent === 0) {
          const time = that.form.expiredTime - that.form.startTime
          const minTimeRange = 1000 * 60 * 10
          if (time < minTimeRange) {
            that.$opsMessage.fail('开始时间~过期时间间隔必须>=10分钟以上!!!')
            return
          }
        }
        if (that.permanent === 1) {
          that.form.expiredTime = null
        }
        that.submitLoading = true
        addOrUpdateSetTopRoom(that.form).then(res => {
          that.submitLoading = false
          that.$emit('success', res)
        }).catch(er => {
          that.submitLoading = false
          console.error(er)
          that.$emit('fial', er)
        })
      })
    },
    loadSearchRoom() {
      this.searchDisabled = true
    },
    searchRoomSuccess(res) {
      this.searchDisabled = false
      if (!res) {
        return
      }
      this.form.id = res.id
      this.form.sysOrigin = res.sysOrigin
    },
    searchRoomFail() {
      this.form.id = ''
      this.form.sysOrigin = ''
      this.searchDisabled = false
    }
  }
}
</script>

<style scoped lang="scss">
.form-contetn {
    max-height: 550px;
    overflow: auto;
}
.form-autocomplete {
   li {
    line-height: normal;
    padding: 7px;

    .name {
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .label {
      font-size: 12px;
      color: #b4b4b4;
    }

    .highlighted .label {
      color: #ddd;
    }
  }
}
</style>
