<template>
  <div class="app-container">

    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="归属系统"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="(item, index) in permissionsSysOriginPlatforms"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <el-select
        v-model="listQuery.shelf"
        placeholder="上/下架"
        style="width: 120px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option v-for="item in showcaseTypes" :key="item.value" :label="item.name" :value="item.value" />
      </el-select>

      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleSearch"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        添加
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
      @cell-mouse-enter="handleMouseEnter"
    >
      <el-table-column type="expand">
        <template slot-scope="scope">
          <h2>宠物信息</h2>
          <el-form v-if="scope.row.petPool" label-position="left" class="store-table-expand form-item">
            <el-form-item>
              <el-col :span="12">
                <el-form-item label="ID">
                  <span>{{ scope.row.petPool.id }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="编号">
                  <span>{{ scope.row.petPool.petCode }}</span>
                </el-form-item>
              </el-col>
            </el-form-item>
            <el-form-item>
              <el-col :span="12">
                <el-form-item label="名称">
                  <span>{{ scope.row.petPool.petName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="序号">
                  <span>{{ scope.row.petPool.level }}</span>
                </el-form-item>
              </el-col>
            </el-form-item>
            <el-form-item>
              <el-col :span="12">
                <el-form-item label="上/下架状态">
                  <span>{{ scope.row.petPool.shelf ? '上架' : '下架' }}</span>
                </el-form-item>
              </el-col>
            </el-form-item>
          </el-form>
          <h2>阶段配置</h2>
          <el-form v-for="(item, index) in scope.row.petStages" :key="item.id" label-position="left" class="store-table-expand form-item">
            <el-form-item>
              <el-col :span="12">
                <el-form-item label="ID">
                  <span>{{ item.id }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="等级">
                  <span>{{ item.level }}</span>
                </el-form-item>
              </el-col>
            </el-form-item>
            <el-form-item>
              <el-col :span="12">
                <el-form-item label="资源图">
                  <div style="width:200px">
                    <div class="preview-img">
                      <div v-if="index < scope.row.petStages.length-1">
                        <el-image
                          style="width: 100%; height: 100%"
                          :src="item.cover"
                          :preview-src-list="[item.cover]"
                        />
                        <div class="preview-svga">
                          <svgaplayer
                            type="popover"
                            :url="item.sourceUrl"
                          />
                        </div>
                      </div>
                      <div v-else>
                        <img
                          style="width: 100%; height: 100%;cursor: pointer;"
                          src="@/assets/gift_box.png"
                        >
                      </div>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="喂养次数">
                  <span>{{ item.upgradeFeedingNum }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="喂养粮食数">
                  <span>{{ item.foodFeedingNum }}</span>
                </el-form-item>
              </el-col>
            </el-form-item>
            <el-form-item>
              <el-col :span="12">
                <el-form-item label="产生收益">
                  <span>{{ item.revenue }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="收益数量">
                  <span>{{ item.revenueNum }}</span>
                </el-form-item>
              </el-col>
            </el-form-item>
            <el-form-item>
              <el-col :span="12">
                <el-form-item label="收益间隔分钟">
                  <span>{{ item.revenueIntervalMinute }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="喂食间隔分钟">
                  <span>{{ item.feedingIntervalMinute }}</span>
                </el-form-item>
              </el-col>
            </el-form-item>
            <el-form-item>
              <el-col :span="12">
                <el-form-item label="免费喂食次数">
                  <span>{{ item.freeFeedingNum }}</span>
                </el-form-item>
              </el-col>
            </el-form-item>
            <el-form-item>
              <el-col :span="12">
                <el-form-item label="喂养加速粮食">
                  <span>{{ item.accelerateFeed }}</span>
                </el-form-item>
              </el-col>
            </el-form-item>
          </el-form>
          <h2>解锁条件</h2>
          <el-form v-for="(item, index) in scope.row.petUnlockConditions" :key="index" label-position="left" class="store-table-expand form-item">
            <el-form-item>
              {{ getPetUnlockConditionMsg(item) }}
            </el-form-item>
          </el-form>
          <h2>养成奖励</h2>
          <el-form label-position="left" class="store-table-expand form-item">
            <el-form-item>
              <div class="reward flex-l">
                <div v-for="(sItem) in (scope.row.rewards || {}).rewardConfigList" :key="sItem.id" class="origin-source">
                  <div v-if="sItem.type === 'GOLD'">
                    <img src="@/assets/gold_icon.png" style="width: 50px; height: 50px">
                    <div class="nowrap-ellipsis"><a :title="sItem.content">{{ sItem.content }}</a></div>
                  </div>
                  <div v-if="sItem.type === 'DIAMOND'">
                    <img src="@/assets/diamond_icon.png">
                    <div class="nowrap-ellipsis"><a :title="sItem.content">{{ sItem.content }}</a></div>
                  </div>
                  <div v-if="sItem.type === 'ROOM_BADGE'">
                    <img src="@/assets/room_badge_icon.png">
                    <div class="nowrap-ellipsis"><a :title="sItem.content">{{ sItem.content }}</a></div>
                  </div>
                  <div v-if="sItem.type === 'SPECIAL_ID'">
                    <img src="@/assets/special_id.png" style="width:50px;height:50px;cursor: pointer;">
                    <div class="nowrap-ellipsis"><a :title="sItem.content">{{ sItem.content }}</a></div>
                  </div>
                  <div v-if="sItem.type === 'PROPS' || sItem.type === 'GIFT' || sItem.type === 'BADGE'" class="preview-img">
                    <el-image
                      style="width: 50px; height: 50px"
                      :src="sItem.cover"
                      :preview-src-list="[sItem.cover]"
                    >
                      <div slot="error" class="image-slot">
                        <i class="el-icon-picture-outline" />
                      </div>
                    </el-image>
                    <div class="preview-svga">
                      <svgaplayer
                        type="popover"
                        :url="sItem.sourceUrl"
                      />
                    </div>
                    <div v-if="sItem.type === 'BADGE'" class="nowrap-ellipsis">
                      <a :title="sItem.quantity ? sItem.quantity + '天' : '永久'">
                        <span>{{ sItem.type === 'ROOM_BADGE' ? 'R' : 'U' }}</span>{{ sItem.quantity ? sItem.quantity + '天' : '永久' }}
                      </a>
                    </div>
                    <div v-else class="nowrap-ellipsis">
                      <a :title="sItem.quantity">{{ sItem.quantity }} 天</a>
                    </div>
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-form>

        </template>
      </el-table-column>
      <el-table-column label="平台" width="80" align="center">
        <template slot-scope="scope">
          <sys-origin-icon :icon="scope.row.petPool.sysOrigin" :desc="scope.row.sysOrigin" />
        </template>
      </el-table-column>
      <el-table-column label="阶段图" align="center" width="300">
        <template slot-scope="scope">
          <div class="flex-l">
            <div v-for="(item, index) in scope.row.petStages" :key="index" class="preview-img">
              <div v-if="index < scope.row.petStages.length-1">
                <el-image
                  style="width: 100%; height: 100%"
                  :src="item.cover"
                  :preview-src-list="[item.cover]"
                />
                <div class="preview-svga">
                  <svgaplayer
                    type="popover"
                    :url="item.sourceUrl"
                  />
                </div>
              </div>
              <div v-else>
                <img
                  style="width: 100%; height: 100%;cursor: pointer;"
                  src="@/assets/gift_box.png"
                >
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="解锁条件" align="center">
        <template slot-scope="scope">
          <div v-if="scope.row.petUnlockConditions">
            <div v-for="(item, index) in scope.row.petUnlockConditions" :key="index">
              {{ getPetUnlockConditionMsg(item) }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="养成奖励" align="center" width="300">
        <template slot-scope="scope">
          <div class="reward flex-l">
            <div v-for="(sItem, sIndex) in (scope.row.rewards || {}).rewardConfigList" :key="sIndex" class="origin-source">
              <div v-if="sItem.type === 'GOLD'">
                <img src="@/assets/gold_icon.png" style="width: 50px; height: 50px">
                <div class="nowrap-ellipsis"><a :title="sItem.content">{{ sItem.content }}</a></div>
              </div>
              <div v-else-if="sItem.type === 'DIAMOND'">
                <img src="@/assets/diamond_icon.png">
                <div class="nowrap-ellipsis"><a :title="sItem.content">{{ sItem.content }}</a></div>
              </div>
              <div v-else-if="sItem.type === 'ROOM_BADGE'">
                <img src="@/assets/room_badge_icon.png">
                <div class="nowrap-ellipsis"><a :title="sItem.content">{{ sItem.content }}</a></div>
              </div>
              <div v-else-if="sItem.type === 'SPECIAL_ID'">
                <img src="@/assets/special_id.png" style="width:50px;height:50px;cursor: pointer;">
                <div class="nowrap-ellipsis"><a :title="sItem.content">{{ sItem.content }}</a></div>
              </div>
              <div v-else class="preview-img">
                <el-image
                  style="width: 50px; height: 50px"
                  :src="sItem.cover"
                  :preview-src-list="[sItem.cover]"
                >
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline" />
                  </div>
                </el-image>
                <div class="preview-svga">
                  <svgaplayer
                    type="popover"
                    :url="sItem.sourceUrl"
                  />
                </div>
                <div v-if="sItem.type === 'BADGE'" class="nowrap-ellipsis">
                  <a :title="sItem.quantity ? sItem.quantity + '天' : '永久'">
                    <span>{{ sItem.type === 'ROOM_BADGE' ? 'R' : 'U' }}</span>{{ sItem.quantity ? sItem.quantity + '天' : '永久' }}
                  </a>
                </div>
                <div v-else class="nowrap-ellipsis">
                  <a :title="sItem.quantity">{{ sItem.quantity }} 天</a>
                </div>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="petPool.level" label="序号" width="80" align="center" />
      <el-table-column width="200" label="上/下架" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.petPool.shelf"
            :active-value="true"
            :inactive-value="false"
            @change="handleSwitchChange(scope.row)"
          />
        </template>
      </el-table-column>

      <el-table-column prop="petPool.createTime" width="200" label="时间" align="center">
        <template slot-scope="scope">
          <div>创建: {{ scope.row.petPool.createTime | dateFormat }}</div>
          <div>修改: {{ scope.row.petPool.createTime | dateFormat }}</div>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" align="center" width="100">
        <template slot-scope="scope">
          <el-button type="text" @click.native="handleUpdate(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <form-edit
      v-if="formEditVisable"
      :row="thisRow"
      @close="formEditVisable=false"
      @success="formEditSuccess"
    />
  </div>
</template>

<script>
import { pagePetPool, offShelf } from '@/api/pet'
import Pagination from '@/components/Pagination'
import { petUnlockConditions, unitConditions } from '@/constant/type'
import FormEdit from './form-edit'
import { sysOriginPlatforms } from '@/constant/origin'
import { mapGetters } from 'vuex'

export default {
  components: { Pagination, FormEdit },
  data() {
    return {
      sysOriginPlatforms,
      showcaseTypes: [
        { value: false, name: '下架' },
        { value: true, name: '上架' }
      ],
      formEditVisable: false,
      thisRow: {},
      thatSelectedUserId: {},
      list: [],
      total: 0,
      listQuery: {
        cursor: 1,
        limit: 20,
        id: '',
        sysOrigin: '',
        shelf: true
      },
      listLoading: true
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    getPetUnlockConditionMsg(item) {
      const conditions = petUnlockConditions.filter(pc => pc.value === item.conditionType)
      const nameCondition = conditions[0] ? conditions[0].name : '?'
      const units = unitConditions.filter(pc => pc.value === item.unit)
      const nameUnit = units[0] ? units[0].name : '?'
      return `${nameCondition} ${nameUnit} ${item.quantity || '?'}`
    },
    renderData(isClean) {
      const that = this
      that.listLoading = true
      if (isClean === true) {
        that.listQuery.cursor = 1
      }
      pagePetPool(that.listQuery).then(res => {
        const { body } = res
        that.total = body.total || 0
        that.list = body.records
        that.listLoading = false
      })
    },
    handleCreate() {
      this.thisRow = null
      this.formEditVisable = true
    },
    handleUpdate(row) {
      this.formEditVisable = true
    },
    handleSwitchChange(row) {
      offShelf(row.petPool.id, row.petPool.shelf)
        .then(res => {})
        .catch(er => {
          row.petPool.shelf = !row.petPool.shelf
        })
    },
    handleSearch() {
      this.renderData(true)
    },
    queryUserDetails(row) {
      this.userDeatilsDrawer = true
      this.thatSelectedUserId = row.id
    },
    handleMouseEnter(row) {
      this.thisRow = row
      this.thatSelectedUserId = row.id
    },
    formEditSuccess() {
      this.formEditVisable = false
      this.renderData(false)
    }
  }
}
</script>
<style scoped lang="scss">
 .store-table-expand {
    font-size: 0;
    label {
    width: 90px;
    color: #99a9bf;
    .el-form-item {
      margin-right: 0;
      margin-bottom: 0;
      width: 50%;
    }
   }
 }
.reward {
    overflow: auto;
    white-space:nowrap;
    .origin-source {
      padding: 5px;
      text-align: center;
      width: 70px;
      height: 100px;
      img {
        width: 50px;
        height: 50px;
        cursor: pointer;
      }
    }

}

.form-item {
  background-color: rgb(244, 250, 250);
  padding: 20px;
  margin-bottom: 10px;
}
</style>
