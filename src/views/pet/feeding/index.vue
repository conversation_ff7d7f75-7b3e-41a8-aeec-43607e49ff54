<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.sysOrigin"
        placeholder="系统"
        style="width: 200px"
        class="filter-item"
        @change="handleSearch"
      >
        <el-option
          v-for="item in permissionsSysOriginPlatforms"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <span style="float: left;"> <sys-origin-icon :icon="item.value" :desc="item.value" /></span>
          <span style="float: left;margin-left:10px">{{ item.label }}</span>
        </el-option>
      </el-select>
      <div class="filter-item">
        <account-input v-model="listQuery.userId" :sys-origin="listQuery.sysOrigin" placeholder="用户ID" />
      </div>
      <div class="filter-item">
        <el-input
          v-model.trim="listQuery.petId"
          placeholder="宠物ID"
          clearable
          style="width: 200px;"
        />
      </div>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        :disabled="searchDisabled"
        @click="handleSearch"
      >
        搜索
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      fit
      highlight-current-row
    >

      <el-table-column label="用户名称" align="center" show-overflow-tooltip min-width="220">
        <template slot-scope="scope">
          <div class="box-header flex-l">
            <div class="avatar">
              <avatar :url="scope.row.userBaseInfo.userAvatar" :gender="scope.row.userBaseInfo.userSex" />
            </div>
            <div class="info nowrap-ellipsis">
              <div class="nickname">
                <el-link @click="queryUserDetails(scope.row.userBaseInfo.id)"><a :title="scope.row.userBaseInfo.userNickname"> {{ scope.row.userBaseInfo.userNickname }} </a></el-link>
              </div>
              <div class="sex-account">
                <gender :gender="scope.row.userBaseInfo.userSex" :gender-name="scope.row.userSexName" :desc="scope.row.userBaseInfo.account" />
                ({{ scope.row.userBaseInfo.accountStatusName }})
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="feedingNum" label="喂粮数量" align="center" />
      <el-table-column label="喂养类型" align="left">
        <template slot-scope="scope">
          <div v-if="scope.row.feedType === 0">免费</div>
          <div v-if="scope.row.feedType === 1">正常</div>
          <div v-if="scope.row.feedType === 2">加速</div>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime" align="left">
        <template slot-scope="scope">
          {{ scope.row.createTime | dateFormat }}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.cursor"
      :limit.sync="listQuery.limit"
      @pagination="renderData"
    />

    <user-deatils-drawer
      v-if="userDeatilsDrawerVisible"
      :user-id="thatSelectedUserId"
      @close="userDeatilsDrawerVisible=false"
    />

    <room-deatils-drawer
      v-if="roomDeatilsDrawerVisible"
      :room-id="roomId"
      @close="roomDeatilsDrawerVisible=false"
    />
  </div>
</template>

<script>

import { pagePetFeeding } from '@/api/pet'
import Pagination from '@/components/Pagination'
import RoomDeatilsDrawer from '@/components/data/RoomDeatilsDrawer'
import { mapGetters } from 'vuex'

export default {
  name: 'ActivityRoom',
  components: { Pagination, RoomDeatilsDrawer },
  data() {
    return {
      submitLoading: false,
      showSubmitBut: false,
      searchDisabled: false,
      userDeatilsDrawerVisible: false,
      roomDeatilsDrawerVisible: false,
      thatSelectedUserId: '',
      list: [],
      total: 0,
      roomId: '',
      listQuery: {
        cursor: 1,
        limit: 20,
        userId: '',
        petId: '',
        sysOrigin: ''
      },
      listLoading: true,
      clickUserId: ''
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatforms'])
  },
  created() {
    const that = this
    const querySystem = this.permissionsSysOriginPlatforms[0]
    if (!querySystem) {
      return
    }
    that.listQuery.sysOrigin = querySystem.value
    that.renderData()
  },
  methods: {
    renderData(isClean) {
      const that = this
      if (isClean === true) {
        that.list = []
        that.listQuery.cursor = 1
      }
      that.listLoading = true
      pagePetFeeding(that.listQuery).then(res => {
        that.listLoading = false
        const { body } = res
        that.total = body.total || 0
        that.list = body.records || []
      }).catch(er => {
        that.listLoading = false
        console.error(er)
      })
    },
    handleSearch() {
      this.renderData(true)
    },
    queryUserDetails(id) {
      this.userDeatilsDrawerVisible = true
      this.thatSelectedUserId = id
    }
  }
}
</script>
