<!--废弃-->
<template>
  <div class="app-container">
    <el-container>
      <el-header class="u-header">
        <div class="header-content">
          <div class="avatar">
            <el-image
              style="width: 60px; height: 60px;"
              :src="userBaseInfo.userAvatar"
              :preview-src-list="[userBaseInfo.userAvatar]"
            />
          </div>
          <div class="right">
            <div class="text">{{ userBaseInfo.userNickname }}、{{ userBaseInfo.userSexName }}
              <span v-if="userBaseInfo.bornyear">{{ userBaseInfo.bornyear }}-{{ userBaseInfo.bornMonth }}-{{ userBaseInfo.bornDay }}</span>
              <span v-if="userBaseInfo.age">({{ userBaseInfo.age }})</span>
            </div>
            <div class="text">
              <el-tag type="info">{{ userBaseInfo.userTypeName }}</el-tag>
              <el-tag>糖果:{{ balance.candyNumber || 0 }}</el-tag>
              <el-tag>会员：{{ balance.vipStatusName }}
                <span v-if="balance.vipEvent > 0">{{ balance.vipExpireTime }}</span>
              </el-tag>
            </div>
          </div>
        </div>
      </el-header>
      <el-container>
        <el-main>
          <div class="u-main-item">
            <h3>基本信息</h3>
            <div class="content base-info">
              <div class="base-info-item">
                <div>ID：{{ userBaseInfo.id }}</div>
                <div>国家：<flag-icon :code="userBaseInfo.countryCode" size="26" :name="userBaseInfo.countryName" /></div>
                <div>邮箱: {{ userRegisterInfo.email }}</div>
              </div>
              <div class="base-info-item">
                <div>手机号码：{{ userAuthInfo.cellphoneNumber }}</div>
                <div>FacebookId: {{ userAuthInfo.facebookid }}</div>
                <div>GoogleId: {{ userAuthInfo.googleId }}</div>
              </div>
              <div class="base-info-item">
                <div>AppleId：{{ userAuthInfo.appleId }}</div>
                <div>注册平台: {{ userAuthInfo.originPlatform }}</div>
                <div>机型: {{ userAuthInfo.originPhoneModel }}</div>
              </div>
            </div>
            <div class="content base-info">
              <div class="base-info-item">
                <div>居住地址：{{ userRegisterInfo.residentialAddress }}</div>
              </div>
            </div>
          </div>
          <div class="u-main-item">
            <h3>照片墙</h3>
            <div class="content photo-wall">
              <div v-for="item in photoWalls" :key="item.id" class="photo-wall-item">
                <el-image
                  :src="item.resourceUrl"
                  :preview-src-list="photoWallUrls"
                />
              </div>
            </div>
          </div>
          <!-- <div class="u-main-item">
            <h3>购买记录</h3>
            <div class="content">
              <el-table
                :data="purchasingList"
                stripe
                style="width: 100%"
              >
                <el-table-column
                  prop="date"
                  label="id"
                  width="180"
                />
                <el-table-column
                  prop="name"
                  label="环境"
                  width="180"
                />
                <el-table-column
                  prop="address"
                  label="产品"
                />
              </el-table>
            </div>
          </div> -->
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import { getUserBaseInfo, getUserPhotoWallNormal, getUserRegister, getUserAuthInfo, getSubscriptionBalance } from '@/api/app-user'
export default {
  name: 'UserDeatils',
  data() {
    return {
      userId: '',
      userBaseInfo: {},
      photoWalls: [],
      photoWallUrls: [],
      userRegisterInfo: {},
      userAuthInfo: {},
      balance: {},
      purchasingList: []
    }
  },
  created() {
    const that = this
    that.userId = that.$route.params.id
    getUserBaseInfo(that.userId).then(res => {
      that.userBaseInfo = res.body
    }).catch(er => {})

    getUserPhotoWallNormal(that.userId).then(res => {
      this.photoWalls = res.body || []
      this.photoWallUrls = this.photoWalls.map(photoWall => photoWall.resourceUrl)
    }).catch(er => {})

    getUserRegister(that.userId).then(res => {
      this.userRegisterInfo = res.body
    }).catch(er => {})

    getUserAuthInfo(that.userId).then(res => {
      this.userAuthInfo = res.body
    }).catch(er => {})

    getSubscriptionBalance(that.userId).then(res => {
      this.balance = res.body || {}
    }).catch(er => {})
  },
  methods: {

  }
}
</script>
<style scoped lang="scss">
    .u-header{
        .header-content{
            height: 60px;
            display: flex;
            justify-content: flex-start;
            .avatar{
                width: 60px;
                height: 60px;
            }
            .right{
                line-height: 25px;
                padding: 0px 0px 0px 10px;
                div{
                    height: 30px;
                }
               .text{
                  div{
                      display: inline-block;
                  }
               }
            }
        }
    }

.u-main-item{
    .content{
        padding: 0px 20px;
    }
    .base-info{
        display: flex;
        justify-content: flex-start;
        .base-info-item{
            div{
                padding: 10px 30px 10px 0px;
            }
        }
    }
    .photo-wall{
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        .photo-wall-item{
            margin-right: 10px;
            width: 180px;
            height: 130px
        }
    }
}
</style>
