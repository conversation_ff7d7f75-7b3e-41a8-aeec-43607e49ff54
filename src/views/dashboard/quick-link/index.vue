<template>
  <div class="quick-link">
    <el-card>
      <div class="content">
        <el-row :gutter="12">
          <el-col v-for="(item,index) in links" :key="index" class="item-card" :sm="6" @click.native="clickCard(item)">
            <el-card shadow="hover">
              {{ item.title }}
            </el-card>
          </el-col>
        </el-row>
      </div>
      <el-divider />
      <div class="dashboard-text">开发使用</div>
      <div class="content">
        <el-row :gutter="12">
          <el-col v-for="(item,index) in devLinks" :key="index" class="item-card" :sm="6" @click.native="clickCard(item)">
            <el-card shadow="hover">
              {{ item.title }}
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>

  </div>
</template>

<script>
export default {
  name: 'QuickLink',
  data() {
    return {
      links: [
        { title: '测试后台管理', link: 'http://test.back.sugartimeapp.com/' },
        { title: '测试经纪人后台', link: 'http://test.broker.sugartimeapp.com/' },
        { title: '线上后台', link: 'http://admin.sugartimeapp.com/' },
        { title: '线上经纪人后台', link: 'http://broker.sugartimeapp.com/' },
        { title: '线上IM后台', link: 'http://im.sugartimeapp.com/' }
      ],
      devLinks: [
        { title: '开发经纪人后台', link: 'http://dev.broker.sugartimeapp.com/' },
        { title: '开发环境jenkins', link: 'http://dev.jenkins.sugartimeapp.com/' },
        { title: '测试环境jenkins', link: 'http://test.jenkins.sugartimeapp.com/' },
        { title: '开发环境supervisor', link: 'http://**************:9001/' },
        { title: '测试环境supervisor', link: 'http://*************:9001/' },
        { title: '线上环境supervisor', link: 'http://*************:9001/' },
        { title: '图谱API文档', link: 'http://cloud.doc.tuputech.com/' }
      ]
    }
  },
  methods: {
    clickCard(item) {
      location.href = item.link
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  margin-top: 20px;
  .item-card {
    margin-bottom: 20px;
  }
}
</style>
