<template>
  <div class="my-native region-card">
    <div class="card">
      <div class="title">我的导航</div>
      <div class="subtitle">最近访问</div>
      <div class="content ">
        <el-row :gutter="10">
          <el-col v-for="(item, index) in dashboard.recentPreviews" :key="index" :md="4" :sm="8" :xs="12">
            <div
              @click="clickRecentPreviews(item)"
              @mouseleave="mouseoutRecentPreviews(item)"
              @mouseenter="mouseoverRecentPreviews(item)"
            >
              <div class="tag region-tag-color flex-c">
                <a :href="item.type === 'CUSTOM' ? item.link : 'javascript:void(0);'" :target="item.type === 'CUSTOM'?'_blank': ''" style="width: 100%" @click="clickCustomNavigations(item)">
                  <div class="nowrap-ellipsis" style="width: 100%">
                    {{ item.name }}
                  </div>
                </a>
                <!-- <i v-if="mouseoveRecentSeletedId === item.id" class="el-icon-error" style="font-size: 18px;" @click.stop="removeRecentPreviews(item)" /> -->
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <div class="native-content">

        <div class="native-item">
          <div class="subtitle">自定义快捷</div>
          <div class="content ">
            <el-row :gutter="10">
              <el-col v-for="(item, index) in dashboard.customNavigations" :key="index" :md="4" :sm="8" :xs="12">
                <div
                  @mouseleave="mouseoutCustomNavigations(item)"
                  @mouseenter="mouseoverCustomNavigations(item)"
                >
                  <div class="tag region-tag-color flex-c">
                    <a :href="item.link" target="_blank" style="width: 100%" @click="clickCustomNavigations(item)">
                      <div class="nowrap-ellipsis" style="width: 100%">
                        {{ item.name }}
                      </div>
                    </a>
                    <i v-if="mouseoveCustomNavigationId === item.id" class="el-icon-error" style="font-size: 18px;" @click.stop="removeCustomNavigations(item)" />
                  </div>
                </div>
              </el-col>
              <el-col :md="4" :sm="8" :xs="12">
                <el-popover v-model="customNavigationFormVisible" width="400" trigger="click">
                  <div class="add-native">
                    <!-- <el-tabs v-model="activeAddNative">
                      <el-tab-pane v-for="item in addNativeTables" :key="item.component" :label="item.title" :name="item.component" />
                      <component :is="activeAddNative" />
                    </el-tabs> -->
                    <div class="blockquote">您可以使用此功能自由添加任意页面至快捷入口，目前仅支持 http 或 https 协议</div>
                    <el-form ref="customNavigationForm" :model="customNavigationForm" :rules="customNavigationFormRules">
                      <el-form-item label="入口名称" prop="name">
                        <el-input v-model="customNavigationForm.name" placeholder="自定义导航入口名称" minlength="1" maxlength="30" show-word-limit />
                      </el-form-item>
                      <el-form-item label="入口链接" prop="link">
                        <el-input v-model="customNavigationForm.link" placeholder="自定义导航入口链接" minlength="1" maxlength="300" show-word-limit />
                      </el-form-item>
                      <el-form-item>
                        <el-button type="primary" :loading="customNavigationFormLoading" :disabled="customNavigationFormLoading" @click="onSubmitCustomNavigationForm">添加</el-button>
                      </el-form-item>
                    </el-form>
                  </div>
                  <div slot="reference">
                    <div class="tag region-tag-color flex-c">
                      <div class="nowrap-ellipsis">
                        <i class="el-icon-circle-plus-outline" style="margin: 0px 3px; font-weight: bold;" /><strong>添加快捷入口</strong>
                      </div>
                    </div>
                  </div>
                </el-popover>

              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { addDashboardCustom, removeDashboardPreviews, removeDashboardCustom, addDashboardPreviews } from '@/api/ops-system'
export default {
  name: 'MyNavigation',
  data() {
    var checkLink = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入支持 http 或 https 协议'))
      }

      if (value.startsWith('http://') || value.startsWith('https://')) {
        return callback()
      }
      return callback(new Error('请输入支持 http 或 https 协议'))
    }
    return {
      addNativeTables: [
        { title: '搜索添加', component: 'DailyPurchase' },
        { title: '自定义添加', component: 'MonthlyPurchase' }
      ],
      customNavigationFormVisible: false,
      customNavigationFormLoading: false,
      customNavigationForm: {
        id: '',
        type: 'CUSTOM',
        name: '',
        link: ''
      },
      customNavigationFormRules: {
        name: [{ required: true, message: '必填字段', trigger: 'blur' }],
        link: [{ required: true, trigger: 'blur', validator: checkLink }]
      },
      mouseoveRecentSeletedId: '',
      mouseoveCustomNavigationId: ''
    }
  },
  computed: {
    ...mapGetters(['buttonPermissions', 'name', 'dashboard'])
  },
  created() {
  },
  methods: {
    loadDashboard() {
      this.$store.dispatch('user/getUserDashboard').then(res => {
        console.log('success')
      }).catch(er => {
        console.error('loadDashboard', er)
      })
    },
    clickCustomNavigations(item) {
      if (item.type === 'CUSTOM') {
        addDashboardPreviews(item)
      }
    },
    clickRecentPreviews(item) {
      if (item.type === 'CUSTOM') {
        return
      }
      if (item.link) {
        this.$router.push(item.link)
      }
    },

    mouseoutCustomNavigations(item) {
      this.mouseoveCustomNavigationId = ''
    },
    mouseoverCustomNavigations(item) {
      this.mouseoveCustomNavigationId = item.id
    },
    removeCustomNavigations(item) {
      const that = this
      removeDashboardCustom(item.id).then(res => {
        that.$opsMessage.success()
        that.loadDashboard()
      }).catch(er => {
        console.error('removeCustomNavigations', er)
      })
    },
    mouseoutRecentPreviews(item) {
      this.mouseoveRecentSeletedId = ''
    },
    mouseoverRecentPreviews(item) {
      this.mouseoveRecentSeletedId = item.id
    },
    removeRecentPreviews(item) {
      const that = this
      removeDashboardPreviews(item.id).then(res => {
        that.$opsMessage.success()
        that.loadDashboard()
      }).catch(er => {
        console.error('removeRecentPreviews', er)
      })
    },
    onSubmitCustomNavigationForm() {
      const that = this
      that.$refs.customNavigationForm.validate((valid) => {
        if (!valid) {
          console.error('error submit!!')
          return false
        }
        that.customNavigationFormLoading = true
        addDashboardCustom(that.customNavigationForm).then(res => {
          that.$opsMessage.success()
          that.customNavigationFormLoading = false
          that.customNavigationFormVisible = false
          that.loadDashboard()
        }).catch(er => {
          that.customNavigationFormLoading = false
          that.customNavigationFormVisible = false
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">
.my-native {
  .tag {
    width: 100%;
    height: 32px;
    line-height: 32px;
    padding: 0 12px;
    margin-bottom: 8px;
    font-size: 12px;
    overflow: hidden;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
    cursor: pointer;
    white-space: nowrap;
    text-decoration: none;
  }
}

</style>
