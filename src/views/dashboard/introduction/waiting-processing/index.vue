<template>
  <div class="waiting-processing region-card">
    <div class="card">
      <div class="title">每日一文</div>
      <div class="tips-text">{{ textKey }}</div>

      <!-- <div class="content">
        <el-row :gutter="10">
          <el-col :md="8">
            <div class="tag region-tag-color">
              <div class="tag-title">待处理</div>
              <div class="quantity">0</div>
            </div>
          </el-col>
          <el-col :md="8">
            <div class="tag region-tag-color">
              <div class="tag-title">待定</div>
              <div class="quantity">0</div>
            </div>
          </el-col>
          <el-col :md="8">
            <div class="tag region-tag-color">
              <div class="tag-title">已处理</div>
              <div class="quantity">0</div>
            </div>
          </el-col>
          <el-col :span="24">
            开发中, 敬请期待...
          </el-col>
        </el-row>
      </div> -->
    </div>

    <div class="card">
      <div class="title">系统时间</div>
      <div class="system-date">
        <div>时区: {{ nowDateTimeZone }}</div>
        <p>时间: {{ nowDateTime | dateFormat }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Cookies from 'js-cookie'

export default {
  name: 'AccountInfo',
  data() {
    return {
      nowDateTime: new Date(),
      nowDateTimeZone: new Intl.DateTimeFormat().resolvedOptions().timeZone,
      textKey: 'DailyText',
      toipsText: '',
      copywriting: [
        '得到的失去的在乎的忘掉的都让它随风而去。',
        '再多的不幸，都是曾经，都会过去，一如窗外的雨，淋过，湿过，走了，远了。曾经的美好，留于心底，曾经的悲伤，置于脑后，不恋，不恨。',
        '别人相信的事实，未必是你的。',
        '当你带着同情心去看时，你可以在你的心中创造一个美丽的空间。',
        '有时候，记忆是甜的，照片无论怎么老旧泛黄。',
        '当我回到我的中心，一个包裹着优雅的提醒等待着我：永远从温柔中流淌。',
        '漫长的人生，其实只是瞬间，一定学着，让自己在人生途中，不后悔。',
        '给事留一个机会;给人留一个空间，给己留一份尊严。有些事想不开别想，得不到别要;看不开，就背着;放不下，就记着;舍不得，就留着。晚安!',
        '过分的理性，令女人失去了女人的属性。',
        '强者创造事变，弱者受制于上帝给他铺排的事变。',
        '快乐来自于以感激的心情，去接受眼前的生活。',
        '少时浪费生命寻找金钱，老时浪费金钱寻找生命。',
        '生活需要微笑，是微笑让这个世界变得更加温暖，变得更加的美丽生动，变得丰富多彩了有了微笑河流才那么清澈，有了微笑天空才那么蔚蓝。',
        '大鸟是关不住的，它总要飞向天空。',
        '用%的灵感加上%的汗水，去换取%的辉煌成就。',
        '有信心就会有成功，有思索就会有思路，有努力就会有收获。',
        '中国式管理，讲起来就是水的管理。和美国式管理偏向火的管理颇为不同。我们主张以柔克刚，先礼后兵，继旧开新，生生不息，无一不和水有关。晚安!',
        '只有抵住最黑的暗，才能收获最光的亮。',
        '人到无求多自由，人品若山极崇峻。',
        '这个世界，我们第一次来，也是最后一次。',
        '你改变不了过去，但你可以改变现在。',
        '行不通的真理是假真理，说不通的道理是假道理。',
        '清晨的暖阳，浩瀚的夜空，过去的美好，余下的人生。',
        '有喜就有悲，有阴影也就有光，不悲观不乐观，活在当下最重要。',
        '在我的生命中，从未遭受过失败，我所遇到的，都是暂时的挫折罢了。',
        '清新的空气，快乐的气息，透过空气射入你的灵魂里，将阳光呼吸，将幸福抱起，泡一杯甜蜜的咖啡，品尝幸福的意义。',
        '不管忙或是闲，放飞心情就是休闲。不管近或是远，内心宁静路就不远。',
        '勇者，脚下都是路；智者，知道走哪一条路最好。',
        '日久不一定生情，但必定见人心。有时候也怕，时间会说出真话。',
        '你要让你的能力，配得上你的虚荣，让你的优秀配得上你的自尊，让你的视野配得上你的骄傲。',
        '世界上最勇敢的事情是微笑着听你说你们之间的爱情，你的回忆里没有我，而我还听得那么认真。',
        '女人对待感情常有惯性，有了爱，就希望能一直爱下去。',
        '人生至高无上的幸福，莫过于确信自己被人所爱。',
        '人生就是一个个选择，有对有错，选择了，就不要后悔。',
        '给自己一个机会，重新开始。',
        '有时候我们觉得累，是因为在人生的道路上，忘记了去哪。',
        '经历了那么那么多,才知道自己不过是一个供人娱乐的玩具而已',
        '有钱的人把自己的房子装饰得漂亮；有德的人把自己的身心修养得很好。',
        '追逐梦想，寻觅梦想的清香。',
        '能做自己的人非常少，但是必须努力呀。',
        '工作做得好，你会更自豪。',
        '既然选择了，就要咬牙坚持走下去。',
        '年后的年轻美貌，与你是否每一天坚持喝颜如玉有关，保养，是女人最好的修行!',
        '拼搏实现梦想，坚持铸就辉煌。',
        '人生路上，留一点遗憾在生活中，也许比完美更觉得美！天地本不全，人间便不可能完整，也算是对不完美的缺憾，一种自我安慰吧！佛且这么说，何况我们人呢！',
        '我们要学会在顺境中感恩和体会幸福，在逆境中成熟和坚强！',
        '你从来就不应该从一个时刻跳到另一个时刻，而应该停留在你脚下的那个时刻。',
        '当我们在责备别人时，岂不是也在间接宽容自己。',
        '你的生活，源于你的选择。',
        '我们的目的是什么?是胜利！不惜一切代价争取胜利！',
        '我在书上看到过这样一句话，说是倘若我们的生命中遭遇到了遮蔽阳光的云层，那么唯一的理由是，我们的心灵飞得还不够高。所以我就在想啊，如果我能不断地努力，不断地到达一个更高的地方，那么是不是就再没有那么多能够遮蔽我视线的障碍了。晚安!',
        '我与不属于我的东西和谐相处。我用爱让它自由自在。',
        '人活着首先是要对自己负责，负责让自己这辈子都幸福。',
        '我们很在乎得到，一旦拥有就难以放下。其实拥有只是短暂的，那些东西即使再好，和你再密不可分，到最后都会离你而去。',
        '当你开始依照习惯行事，你的进取精神就会因此而丧失。',
        '凡人看人是看你拥有多少钱财，上帝看人看的是你做了多少善事。',
        '我希望你有足够的自由来瞥见你心中的爱，并对它的深处感到惊奇。',
        '是你忘了带我走。我们就这样迷散在陌生的风雨里，从此天各一方，两两相望。',
        '过去终是过去，那人，那事，那情，任你留恋，都是云烟。',
        '如果学到的知识，却不能修养自己，帮助别人，那么学的知识又有什么用呢?',
        '人与人之间的距离，要保持好，太近了会扎人，太远了会伤人。',
        '不管见不见面，总有朋友把你惦念。早安，朋友！愿幸福一直在你身边！',
        '锲而不舍你能发觉，喜欢上的便是运动自身！',
        '如果我们一直消极的活着，那么这辈子就一点指望也没有了。',
        '男人要有地位，但不能高得吓人。稻微一努力，自己就有可能站到他们的身边，这样的男人，才最令女人期待！',
        '倒霉的时候要默默熬过去，不要声张否则留下的都是笑柄。',
        '十六岁的时候看六十岁还很遥远，六十岁的时候看十六岁仿佛就在昨天。',
        '赚别人的钱，越多越好，贫穷才是命运的最大的敌人。',
        '一个人，经得起挫折，受得起委屈，方能为自己赢得美好的人生。',
        '你没挽留，我没回头，如此余生各自安好，也没有谁不好，也许只是时间不凑巧吧。',
        '只有自己足够强大，才会有用的话语权。',
        '你不能控制他人，但你可以掌握自己。'
      ],
      timeIndexInterval: null
    }
  },
  computed: {
    ...mapGetters(['buttonPermissions', 'name', 'loginName'])
  },
  created() {
    const that = this
    that.textKey = this.getDailyText()

    if (that.timeIndexInterval) {
      clearInterval(that.timeIndexInterval)
    }

    that.timeIndexInterval = setInterval(() => {
      that.nowDateTime = new Date()
    }, 1000)
  },
  methods: {
    getDailyText() {
      try {
        const text = Cookies.get(this.textKey)
        if (!text) {
          const randomText = this.copywriting[Math.floor((Math.random() * this.copywriting.length))] || this.copywriting[0]
          const date = new Date()
          Cookies.set(this.textKey, `${date.getFullYear()}${date.getMonth()}${date.getDate()}#${randomText}`)
          return randomText
        }

        return text.split('#')[1]
      } catch (er) {
        return this.copywriting[Math.floor((Math.random() * this.copywriting.length))] || this.copywriting[0]
      }
    }
  }
}
</script>
<style lang="scss">

</style>
  <style scoped lang="scss">
  .waiting-processing {
    .content {
      font-size: 12px;
      line-height: 18px;
      .tag {
        width: 100%;
        line-height: 32px;
        padding: 5px 12px;
        margin-bottom: 8px;
        font-size: 12px;
        overflow: hidden;
        -webkit-border-radius: 2px;
        -moz-border-radius: 2px;
        border-radius: 2px;
        cursor: pointer;
        white-space: nowrap;
        text-decoration: none;
        .quantity {
            font-weight: bold;
            font-size: 24px;
        }
    }
    }
  }
  </style>

