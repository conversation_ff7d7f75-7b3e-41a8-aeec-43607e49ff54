<template>
  <div class="introduction">
    <el-row :gutter="20">
      <el-col :md="17">
        <user-navigation />
      </el-col>
      <el-col :md="7">
        <user-account-info />
        <waiting-processing />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import UserNavigation from './user-navigation'
import UserAccountInfo from './user-account-info'
import WaitingProcessing from './waiting-processing'

export default {
  name: 'Introduction',
  components: { UserNavigation, UserAccountInfo, WaitingProcessing },
  data() {
    return {}
  },
  computed: {
    ...mapGetters(['buttonPermissions', 'name'])
  },
  created() {
  },
  methods: {}
}
</script>
<style lang="scss">

</style>
