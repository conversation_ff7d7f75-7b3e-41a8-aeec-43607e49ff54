<template>
  <div class="account-info region-card">
    <div class="card">
      <div class="account-info flex-l">
        <div class="avatar flex-c" :style="userAvatarColor">{{ firstNameStr }}</div>
        <div class="info">
          <div class="nickname">{{ name }}</div>
          <div class="account">账号: {{ loginName }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { extractColorByText } from '@/utils'
export default {
  name: 'AccountInfo',
  data() {
    return {}
  },
  computed: {
    ...mapGetters(['buttonPermissions', 'name', 'loginName']),
    userAvatarColor() {
      return 'background-color:' + extractColorByText(this.name) + ';'
    },
    firstNameStr() {
      return this.name ? this.name.trim().charAt(0) : '?'
    }
  },
  created() {
  },
  methods: {

  }
}
</script>
<style lang="scss">
.account-info {
  .avatar {
    width: 55px;
    height: 55px;
    border-radius: 100%;
    color: #FFFFFF;
    font-weight: bold;
  }
  .info {
    font-size: 12px;
    line-height: 18px;
    padding: 0px 10px;
  }
}
</style>

