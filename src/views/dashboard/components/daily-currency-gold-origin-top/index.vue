<template>
  <div v-loading="loading" class="dily-register-user">
    <el-date-picker
      v-model="dateNumber"
      style="margin-bottom: 10px;"
      type="date"
      placeholder="选择日期"
      value-format="yyyyMMdd"
      size="mini"
      :picker-options="pickerOptions"
      :clearable="false"
      @change="changeDateNumber"
    />
    <div v-for="(item, index) in list" :key="index">
      <el-card v-if="sysOriginPlatforms.includes(item.sysOrigin)" class="box-card">
        <div slot="header" class="clearfix">
          <span>金币TOP榜({{ item.sysOriginName }})</span>
        </div>
        <div style="padding: 10px 0px">
          <el-row>
            <el-col class="card-col" :md="24" :xs="24">
              <bar-graph-charts
                ref="lineGraphCharts"
                :key="item.sysOrigin"
                :charts-data="item.list"
                :height="(item.list.length * 20)+'px'"
              />
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { latestStatisticsGoldOriginDetailsAll } from '@/api/statistics'
import BarGraphCharts from './bar-graph-charts'
import { currencyOrigins } from '@/constant/type'
import { mapGetters } from 'vuex'
import { formatDate, beforeDateObject } from '@/utils'

export default {
  name: 'DilyRegisterUser',
  components: { BarGraphCharts },
  data() {
    return {
      pickerOptions: {
        disabledDate(date) {
          return date.getTime() > beforeDateObject(1).getTime()
        }
      },
      dateNumber: formatDate(beforeDateObject(new Date().getHours() >= 8 ? 1 : 2), 'yyyyMMdd'),
      loading: false,
      list: [],
      selectOrigin: currencyOrigins[0].value,
      selectOriginName: currencyOrigins[0].name
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatformAlls']),
    sysOriginPlatforms() {
      if (!this.permissionsSysOriginPlatformAlls || this.permissionsSysOriginPlatformAlls.length <= 0) {
        return []
      }
      return this.permissionsSysOriginPlatformAlls.map(item => item.value)
    }
  },
  created() {
    this.loadOrigin()
  },
  methods: {
    loadOrigin() {
      const that = this
      that.loading = true
      latestStatisticsGoldOriginDetailsAll(this.dateNumber).then(res => {
        that.loading = false
        const { body } = res
        const list = body || []
        that.list = list.filter(item => !item.sysOrigin || that.permissionsSysOriginPlatformAlls.some(sys => sys.value === item.sysOrigin))
      }).catch(() => {
        that.loading = false
      })
    },
    querySearch(queryString, cb) {
      var restaurants = currencyOrigins
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
      cb(results)
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) >= 0) || (restaurant.name.toLowerCase().indexOf(queryString.toLowerCase()) >= 0)
      }
    },
    handleSelect(item) {
      this.selectOrigin = item.value
      this.selectOriginName = item.name
      this.loadOrigin()
    },
    clickAnalyze() {
      this.$router.push('/system/manager/tools/manager/gold-analyze')
    },
    changeDateNumber() {
      this.loadOrigin()
    }
  }
}
</script>

<style scoped lang="scss">
.box-card {
  margin-bottom: 10px;
  padding: 10px;
  .card-col {
    margin-bottom: 10px;
  }
}

.register-logout-count {
   .date {
     font-weight: bold;
     line-height: 30px;
   }
}
</style>
