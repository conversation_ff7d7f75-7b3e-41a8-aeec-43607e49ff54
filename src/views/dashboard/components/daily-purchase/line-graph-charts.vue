<template>
  <div class="daily-currency-charts">
    <el-tag v-for="(item ,index) in platformCounts" :key="index" style="margin-right: 10px;">{{ item.label }}({{ item.type }}): {{ item.val }}</el-tag>
    <div id="videoCharts" ref="videoCharts" :style="'width: 100%;height:'+ height +';'" />
  </div>
</template>

<script>
export default {
  name: 'DailyCurrencyCharts',
  props: {
    height: {
      type: String,
      default: '600px'
    },
    chartsData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      videoRealTimeCharts: null,
      platformCounts: []
    }
  },
  created() {
  },
  mounted() {
    const that = this
    that.videoRealTimeCharts = that.$echarts.init(that.$refs.videoCharts)
    that.renderCharts()
    window.addEventListener('resize', () => {
      that.videoRealTimeCharts.resize()
    })
  },
  methods: {
    count(list) {
      if (!list || list.length <= 0) {
        return []
      }

      let androidPurchaseCount = 0
      let iosPurchaseCount = 0
      let h5PurchaseCount = 0
      let totalCount = 0
      let refundCount = 0
      const size = list.length
      for (let index = 0; index < size; index++) {
        const item = list[index]
        totalCount += item.completeQuantity
        refundCount += item.reimburseQuantity
        androidPurchaseCount += item.androidQuantity
        iosPurchaseCount += item.iosQuantity
        h5PurchaseCount += item.h5Quantity
      }
      return [
        { type: 'avg', label: 'Android', val: this.calAvg(androidPurchaseCount, 30) },
        { type: 'avg', label: 'iOS', val: this.calAvg(iosPurchaseCount, 30) },
        { type: 'avg', label: 'H5', val: this.calAvg(h5PurchaseCount, 30) },
        { type: 'avg', label: 'Total', val: this.calAvg(totalCount, 30) },
        { type: 'count', label: 'Refund', val: refundCount }
      ]
    },
    calAvg(num, size) {
      return (num / size).toFixed(2) * 100 / 100
    },
    handleListToChartsData() {
      const that = this
      const dataCharts = {
        androidQuantity: [],
        iosQuantity: [],
        h5Quantity: [],
        completeQuantity: [],
        reimburseQuantity: [],
        offlineQuantity: []
      }
      if (that.chartsData.length > 0) {
        that.chartsData.forEach(item => {
          const date = item.dateNumber
          dataCharts.androidQuantity.push([date, (item.androidQuantity || 0)])
          dataCharts.iosQuantity.push([date, (item.iosQuantity || 0)])
          dataCharts.h5Quantity.push([date, (item.h5Quantity || 0)])
          dataCharts.completeQuantity.push([date, (item.completeQuantity || 0)])
          dataCharts.reimburseQuantity.push([date, (item.reimburseQuantity || 0)])
          if (that.areaCode === 'DAILY') {
            dataCharts.offlineQuantity.push([date, (item.offlineQuantity || 0)])
          }
        })
      }

      // that.platformCounts = that.count(that.chartsData)
      return dataCharts
    },
    renderCharts() {
      const that = this
      const thatDayData = that.handleListToChartsData()
      const series = [
        {
          name: 'Android',
          type: 'line',
          stack: 'Android',
          smooth: 0.6,
          symbol: 'none',
          symbolSize: 10,
          data: thatDayData.androidQuantity,
          areaStyle: {},
          itemStyle: {
            color: '#3eda86'
          }
        },
        {
          name: 'iOS',
          type: 'line',
          stack: 'iOS',
          smooth: 0.6,
          symbol: 'none',
          symbolSize: 10,
          data: thatDayData.iosQuantity,
          areaStyle: {},
          itemStyle: {
            color: '#303133'
          }
        },
        {
          name: 'H5',
          type: 'line',
          stack: 'H5',
          smooth: 0.6,
          symbol: 'none',
          symbolSize: 10,
          data: thatDayData.h5Quantity,
          areaStyle: {},
          itemStyle: {
            color: '#e9640e'
          }
        },
        {
          name: 'Offline',
          type: 'line',
          stack: 'Offline',
          smooth: 0.6,
          symbol: 'none',
          symbolSize: 10,
          data: thatDayData.offlineQuantity,
          areaStyle: {},
          itemStyle: {
            color: '#6c4c49'
          }
        },
        {
          name: '有效',
          type: 'line',
          stack: '有效',
          smooth: 0.6,
          symbol: 'none',
          symbolSize: 10,
          data: thatDayData.completeQuantity,
          areaStyle: {},
          itemStyle: {
            color: '#ce90e8'
          }
        },
        {
          name: '退款',
          type: 'line',
          stack: '退款',
          smooth: 0.6,
          symbol: 'none',
          symbolSize: 10,
          data: thatDayData.reimburseQuantity,
          areaStyle: {},
          itemStyle: {
            color: '#F56C6C'
          }
        }]
      that.videoRealTimeCharts.setOption({
        // color: ['#3eda86', '#303133', '#e9640e', '#6c4c49', '#66b3ff', '#ce90e8', '#ff9c6e', '#5cdbd3', '#E6A23C'],
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(50,50,50,0.5)',
          axisPointer: {
            type: 'cross',
            label: {
              lineStyle: { color: '#009688' },
              crossStyle: { color: '#008acd' },
              shadowStyle: { color: 'rgba(200,200,200,0.2)' }
            }
          }
        },
        grid: {
          x: 20,
          y: 50,
          x2: 20,
          y2: 0,
          containLabel: true,
          borderColor: '#eee'
        },
        toolbox: { color: ['#1e90ff', '#1e90ff', '#1e90ff', '#1e90ff'], effectiveColor: '#ff4500' },
        xAxis: [
          {
            show: false,
            type: 'category',
            boundaryGap: false,
            splitArea: {
              show: true,
              areaStyle: { color: ['rgba(250,250,250,0.1)', 'rgba(200,200,200,0.1)'] }
            },
            axisLabel: {
              rotate: 38
            },
            axisLine: { lineStyle: { color: '#b7bdc7' }},
            splitLine: { lineStyle: { color: ['#eee'] }},
            splitNumber: 24
          }
        ],
        yAxis: [{
          type: 'value',
          axisTick: { show: true, length: 0 },
          splitNumber: 5,
          splitLine: { lineStyle: { color: ['#eee'] }},
          axisLine: { lineStyle: { color: '#b7bdc7' }}
        }],
        legend: {
          y: 10,
          textStyle: { color: '#8e929b' }
        },
        series: series.filter(item => item.data && item.data.length > 0)
      }, true)
    }
  }
}
</script>
