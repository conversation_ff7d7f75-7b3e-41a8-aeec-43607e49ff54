<template>
  <div class="subsidy-integral-charts">
    <el-card v-loading="listLoading">
      <div slot="header" class="clearfix">
        <span>近30天内购(总揽)</span>
      </div>
      <el-tag v-for="(item ,index) in platformCounts" :key="index" style="margin-right: 10px;">{{ item.label }}({{ item.type }}): {{ item.val }}</el-tag>
      <div id="subsidyIntegralCharts" ref="subsidyIntegralCharts" :style="'width: 100%;height:'+ height +';'" />
    </el-card>
  </div>
</template>

<script>

import { formatDate } from '@/utils'
import { getDailyPurchaseAmountThirtyDays } from '@/api/statistics'

export default {
  name: 'LineGraphChartsGeneral',
  props: {
    height: {
      type: String,
      default: '600px'
    },
    areaCode: {
      type: String,
      default: 'DAILY'
    }
  },
  data() {
    return {
      listLoading: true,
      list: [],
      subsidyIntegralCharts: null,
      platformCounts: []
    }
  },
  watch: {
    areaCode: {
      handler(newVal) {
        if (newVal) {
          this.renderData()
        }
      },
      immediate: true
    }
  },
  mounted() {
    const that = this
    that.subsidyIntegralCharts = that.$echarts.init(that.$refs.subsidyIntegralCharts)
    window.addEventListener('resize', () => {
      that.subsidyIntegralCharts.resize()
    })
  },
  methods: {
    renderData() {
      const that = this
      that.listLoading = true
      getDailyPurchaseAmountThirtyDays(that.areaCode).then(res => {
        const { body } = res
        that.list = body || []
        that.renderCharts()
        that.listLoading = false
        // that.platformCounts = that.count(that.list)
      })
    },
    count(list) {
      if (!list || list.length <= 0) {
        return []
      }

      let androidPurchaseCount = 0
      let androidPurchaseIndex = 0
      let iosPurchaseCount = 0
      let iosPurchaseIndex = 0
      let h5PurchaseCount = 0
      let h5PurchaseCountIndex = 0
      let totalCount = 0
      let totalIndex = 0
      let refundCount = 0
      const size = list.length
      for (let index = 0; index < size; index++) {
        const item = list[index]
        totalCount += item.purchase
        refundCount += item.refund
        if (item && item.platform === 'Android') {
          androidPurchaseIndex += 1
          totalIndex += 1
          androidPurchaseCount += item.purchase
        }
        if (item && item.platform === 'iOS') {
          iosPurchaseIndex += 1
          totalIndex += 1
          iosPurchaseCount += item.purchase
        }
        if (item && item.platform === 'H5') {
          h5PurchaseCountIndex += 1
          totalIndex += 1
          h5PurchaseCount += item.purchase
        }
      }
      return [
        { type: 'avg', label: 'Android', val: this.calAvg(androidPurchaseCount, androidPurchaseIndex) },
        { type: 'avg', label: 'iOS', val: this.calAvg(iosPurchaseCount, iosPurchaseIndex) },
        { type: 'avg', label: 'H5', val: this.calAvg(h5PurchaseCount, h5PurchaseCountIndex) },
        { type: 'avg', label: 'Total2', val: this.calAvg(totalCount, totalIndex) },
        { type: 'count', label: 'Refund', val: refundCount }
      ]
    },
    calAvg(num, size) {
      if (!size || size <= 0) {
        return 0
      }
      return (num / size).toFixed(2) * 100 / 100
    },
    handleSearch() {
      this.renderData()
    },
    handleChartsData() {
      const that = this
      const iosPurchase = []
      const iosRefund = []
      const googlePurchase = []
      const googleRefund = []
      const h5Purchase = []
      const h5Refund = []
      const offline = []
      if (that.list.length > 0) {
        that.list.forEach(item => {
          const date = formatDate(item.statisticsTime, 'yyyyMMdd')
          if (item.platform === 'iOS') {
            iosPurchase.push([date, (item.purchase || 0)])
            iosRefund.push([date, (item.refund || 0)])
            return
          }
          if (item.platform === 'Android') {
            googlePurchase.push([date, (item.purchase || 0)])
            googleRefund.push([date, (item.refund || 0)])
          }
          if (item.platform === 'H5') {
            h5Purchase.push([date, (item.purchase || 0)])
            h5Refund.push([date, (item.refund || 0)])
          }
          if (item.platform === 'Offline' && that.areaCode === 'DAILY') {
            offline.push([date, (item.purchase || 0)])
          }
        })
      }
      return { iosPurchase, iosRefund, googlePurchase, googleRefund, h5Purchase, h5Refund }
    },
    renderCharts() {
      const that = this
      const thatDayData = that.handleChartsData()

      const series = [{
        name: 'Adnroid内购',
        type: 'line',
        stack: 'Adnroid内购',
        smooth: 0.6,
        symbol: 'none',
        symbolSize: 10,
        data: thatDayData.googlePurchase,
        areaStyle: {},
        itemStyle: {
          color: '#3eda86'
        }
      }, {
        name: 'Adnroid退款',
        type: 'line',
        stack: 'Adnroid退款',
        smooth: 0.6,
        symbol: 'none',
        symbolSize: 10,
        data: thatDayData.googleRefund,
        areaStyle: {},
        itemStyle: {
          color: '#55886d'
        }
      },
      {
        name: 'iOS内购',
        type: 'line',
        stack: 'iOS内购',
        smooth: 0.6,
        symbol: 'none',
        symbolSize: 10,
        data: thatDayData.iosPurchase,
        areaStyle: {},
        itemStyle: {
          color: '#303133'
        }
      }, {
        name: 'iOS退款',
        type: 'line',
        stack: 'iOS退款',
        smooth: 0.6,
        symbol: 'none',
        symbolSize: 10,
        data: thatDayData.iosRefund,
        areaStyle: {},
        itemStyle: {
          color: '#828486'
        }
      },
      {
        name: 'Offline',
        type: 'line',
        stack: 'Offline',
        smooth: 0.6,
        symbol: 'none',
        symbolSize: 10,
        data: thatDayData.offline,
        areaStyle: {},
        itemStyle: {
          color: '#6c4c49'
        }
      }, {
        name: 'H5内购',
        type: 'line',
        stack: 'H5内购',
        smooth: 0.6,
        symbol: 'none',
        symbolSize: 10,
        data: thatDayData.h5Purchase,
        areaStyle: {},
        itemStyle: {
          color: '#e9640e'
        }
      }, {
        name: 'H5退款',
        type: 'line',
        stack: 'H5退款',
        smooth: 0.6,
        symbol: 'none',
        symbolSize: 10,
        data: thatDayData.h5Refund,
        areaStyle: {},
        itemStyle: {
          color: '#ff9c6e'
        }
      }]
      that.subsidyIntegralCharts.setOption({
        // color: ['#3eda86', '#55886d', '#303133', '#828486', '#6c4c49', '#e9640e', '#ff9c6e', '#66b3ff', '#E6A23C', '#ce90e8', '#5cdbd3'],
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(50,50,50,0.5)',
          axisPointer: {
            type: 'cross',
            label: {
              lineStyle: { color: '#009688' },
              crossStyle: { color: '#008acd' },
              shadowStyle: { color: 'rgba(200,200,200,0.2)' }
            }
          }
        },
        grid: {
          x: 20,
          y: 50,
          x2: 20,
          y2: 0,
          containLabel: true,
          borderColor: '#eee'
        },
        toolbox: { color: ['#1e90ff', '#1e90ff', '#1e90ff', '#1e90ff'], effectiveColor: '#ff4500' },
        xAxis: [
          {
            show: false,
            type: 'category',
            boundaryGap: false,
            splitArea: {
              show: true,
              areaStyle: { color: ['rgba(250,250,250,0.1)', 'rgba(200,200,200,0.1)'] }
            },
            axisLabel: {
              rotate: 38
            },
            splitLine: { lineStyle: { color: ['#eee'] }},
            splitNumber: 24,
            axisLine: { lineStyle: { color: '#b7bdc7' }}
          }
        ],
        yAxis: [{
          type: 'value',
          axisTick: { show: true, length: 0 },
          splitNumber: 5,
          splitLine: { lineStyle: { color: ['#eee'] }},
          axisLine: { lineStyle: { color: '#b7bdc7' }}
        }],
        legend: { y: 10, textStyle: { color: '#8e929b' }},
        series: series.filter(item => item.data && item.data.length > 0)
      }, true)
    }
  }
}
</script>
