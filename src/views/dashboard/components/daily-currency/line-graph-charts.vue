<template>
  <div class="daily-currency-charts">
    <div id="videoCharts" ref="videoCharts" :style="'width: 100%;height:'+ height +';'" />
  </div>
</template>

<script>

import { formatDate } from '@/utils'
export default {
  name: 'DailyCurrencyCharts',
  props: {
    height: {
      type: String,
      default: '600px'
    },
    chartsData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      videoRealTimeCharts: null
    }
  },
  created() {
  },
  mounted() {
    const that = this
    that.videoRealTimeCharts = that.$echarts.init(that.$refs.videoCharts)
    that.renderCharts()
    window.addEventListener('resize', () => {
      that.videoRealTimeCharts.resize()
    })
  },
  methods: {
    handleListToChartsData() {
      const that = this
      const charts = {
        integralIncome: [],
        integralExpenditure: [],
        goldIncome: [],
        goldExpenditure: []
      }
      if (that.chartsData.length > 0) {
        that.chartsData.forEach(item => {
          const date = formatDate(item.statisticsTime, 'yyyy-MM-dd')
          if (item.currencyType === 'GOLD' && item.type === 0) {
            charts.goldIncome.push([date, (item.quantity || 0)])
          } else if (item.currencyType === 'GOLD' && item.type === 1) {
            charts.goldExpenditure.push([date, (item.quantity || 0)])
          } else if (item.currencyType === 'INTEGRAL' && item.type === 0) {
            charts.integralIncome.push([date, (item.quantity || 0)])
          } else if (item.currencyType === 'INTEGRAL' && item.type === 1) {
            charts.integralExpenditure.push([date, (item.quantity || 0)])
          }
        })
      }
      return charts
    },
    renderCharts() {
      const that = this
      const thatDayData = that.handleListToChartsData()
      that.videoRealTimeCharts.setOption({
        color: ['#66b3ff', '#ce90e8', '#ff9c6e', '#5cdbd3'],
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(50,50,50,0.5)',
          axisPointer: {
            type: 'cross',
            label: {
              lineStyle: { color: '#009688' },
              crossStyle: { color: '#008acd' },
              shadowStyle: { color: 'rgba(200,200,200,0.2)' }
            }
          }
        },
        grid: {
          x: 20,
          y: 50,
          x2: 20,
          y2: 0,
          containLabel: true,
          borderColor: '#eee'
        },
        toolbox: { color: ['#1e90ff', '#1e90ff', '#1e90ff', '#1e90ff'], effectiveColor: '#ff4500' },
        xAxis: [
          {
            show: false,
            type: 'category',
            boundaryGap: false,
            splitArea: {
              show: true,
              areaStyle: { color: ['rgba(250,250,250,0.1)', 'rgba(200,200,200,0.1)'] }
            },
            axisLabel: {
              rotate: 38
            },
            axisLine: { lineStyle: { color: '#b7bdc7' }},
            splitLine: { lineStyle: { color: ['#eee'] }},
            splitNumber: 24
          }
        ],
        yAxis: [{
          type: 'value',
          axisTick: { show: true, length: 0 },
          splitNumber: 5,
          splitLine: { lineStyle: { color: ['#eee'] }},
          axisLine: { lineStyle: { color: '#b7bdc7' }}
        }],
        legend: {
          y: 10,
          textStyle: { color: '#8e929b' }
        },
        series: [
          {
            name: '金币收入',
            type: 'line',
            stack: '金币收入',
            smooth: 0.6,
            symbol: 'none',
            symbolSize: 10,
            data: thatDayData.goldIncome,
            areaStyle: {}
          },
          {
            name: '金币支出',
            type: 'line',
            stack: '金币支出',
            smooth: 0.6,
            symbol: 'none',
            symbolSize: 10,
            data: thatDayData.goldExpenditure,
            areaStyle: {}
          }, {
            name: '积分收入',
            type: 'line',
            stack: '金币收入',
            smooth: 0.6,
            symbol: 'none',
            symbolSize: 10,
            data: thatDayData.integralIncome,
            areaStyle: {}
          },
          {
            name: '积分支出',
            type: 'line',
            stack: '金币支出',
            smooth: 0.6,
            symbol: 'none',
            symbolSize: 10,
            data: thatDayData.integralExpenditure,
            areaStyle: {}
          }]
      }, true)
    }
  }
}
</script>
