<template>
  <div class="daily-currency-charts">
    <el-select v-model="typeValue" placeholder="请选择" size="mini" @change="typeValueChange">
      <el-option
        v-for="item in types"
        :key="item.value"
        :label="item.name"
        :value="item.value"
      />
    </el-select>
    <div id="barCharts" ref="barCharts" :style="'width: 100%;height:'+ height +';'" />
  </div>
</template>

<script>

// import { currencyOrigins } from '@/constant/type'

export default {
  props: {
    height: {
      type: String,
      default: '600px'
    },
    chartsData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      types: [
        { value: 0, name: '收入' },
        { value: 1, name: '支出' }
      ],
      dateNumber: '',
      typeValue: 0,
      barCharts: null,
      groupChartsData: {
        income: [],
        expenditure: []
      }
    }
  },
  watch: {
    chartsData: {
      handler(newVal) {
        const that = this
        that.groupChartsData = that.groupChartsDataByType()
        that.$nextTick(() => {
          that.renderCharts()
        })
      },
      immediate: true
    }
  },
  created() {
  },
  mounted() {
    const that = this
    that.barCharts = that.$echarts.init(that.$refs.barCharts)
    that.renderCharts()
    window.addEventListener('resize', () => {
      that.barCharts.resize()
    })
  },
  methods: {
    groupChartsDataByType() {
      const that = this
      const obj = {
        income: [],
        expenditure: []
      }
      if (that.chartsData.length > 0) {
        that.chartsData.forEach(item => {
          if (item.type === 0) {
            obj.income.push(item)
          }
          if (item.type === 1) {
            obj.expenditure.push(item)
          }
        })
      }
      return obj
    },
    getBarData(list) {
      const that = this
      const dataKeys = []
      const dataList = []
      if (!list || list.length === 0) {
        return { keys: dataKeys, data: dataList }
      }
      const size = list.length - 1
      for (let index = size; index >= 0; index--) {
        const item = list[index]
        dataKeys.push(item.originName)
        dataList.push({
          groupId: item.originName,
          name: item.originName,
          value: item.quantity,
          itemStyle: {
            color: that.getRandomColor()
          }
        })
      }
      return { keys: dataKeys, data: dataList }
    },
    renderCharts() {
      const that = this
      const barData = that.getBarData(that.typeValue === 0 ? that.groupChartsData.income : that.groupChartsData.expenditure)
      that.barCharts.setOption({
        color: ['#66b3ff', '#ce90e8', '#ff9c6e', '#5cdbd3'],
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(50,50,50,0.5)',
          axisPointer: {
            type: 'cross',
            label: {
              lineStyle: { color: '#009688' },
              crossStyle: { color: '#008acd' },
              shadowStyle: { color: 'rgba(200,200,200,0.2)' }
            }
          }
        },
        grid: {
          x: 20,
          y: 50,
          x2: 20,
          y2: 0,
          containLabel: true,
          borderColor: '#eee'
        },
        xAxis: [
          {
            show: false
          }
        ],
        yAxis: [{
          type: 'category',
          data: barData.keys,
          splitLine: { lineStyle: { color: ['#eee'] }},
          axisLine: { lineStyle: { color: '#b7bdc7' }},
          axisLabel: {
            interval: 0,
            rotate: 38,
            width: 15,
            overflow: 'truncate',
            ellipsis: '...',
            formatter: function(value) {
              return value.length > 10 ? value.substring(0, 10) + '...' : value
            }
          }
        }],
        legend: {
          y: 10,
          textStyle: { color: '#8e929b' }
        },
        series: {
          type: 'bar',
          label: {
            show: true,
            position: 'right',
            valueAnimation: true
          },
          data: barData.data
        }
      }, true)
    },
    getRandomColor() {
      const r = Math.floor(Math.random() * 255)
      const g = Math.floor(Math.random() * 255)
      const b = Math.floor(Math.random() * 255)
      return 'rgba(' + r + ',' + g + ',' + b + ',0.8)'
    },
    typeValueChange() {
      const that = this
      that.$nextTick(() => {
        that.renderCharts()
      })
    }
  }
}
</script>
