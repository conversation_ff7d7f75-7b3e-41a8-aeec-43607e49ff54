<template>
  <div v-loading="latestActiveListLoading" class="active-index">
    <div class="title">第三方活跃指标
      <span class="desc">All {{ latestActiveIndex.date || '-' }}</span>
    </div>
    <div class=" flex-l flex-wrap">
      <div
        v-for="(item, index) in activeIndexs"
        :key="index"
        class="block-card"
        :class="{'block-card-selected': item.type === selectedIndex.data.type}"
        @click="clickQueryCharts('IM_ACTIVE',item)"
      >
        <div class="block-card-title nowrap-ellipsis"><a :title="item.name">{{ item.name }}</a></div>
        <div class="cite">{{ latestActiveIndex.imActiveIndex ? latestActiveIndex.imActiveIndex[item.field] || '0' : '-' }}</div>
      </div>
    </div>
    <div class="title">自定义活跃指标
      <span class="desc">{{ latestActiveIndex.date || '-' }}</span>
    </div>
    <div class="flex-l flex-wrap">
      <div
        v-for="(item, index) in customizeIndexs"
        :key="index"
        class="block-card"

        :class="{'block-card-selected': item.type === selectedIndex.data.type}"
        @click="clickQueryCharts('CUTOMIZE_ACTIVE',item)"
      >
        <div class="block-card-title nowrap-ellipsis"><a :title="item.name">{{ item.name }}</a></div>
        <div class="cite">{{ latestActiveIndex.customizeActiveIndex ? latestActiveIndex.customizeActiveIndex[item.field] || '0' : '-' }}</div>
      </div>
    </div>
    <div class="title">数据折线图
      <span class="desc">{{ chartsData.title }}</span>
    </div>
    <div class="charts">
      <line-graph-charts :charts-data="chartsData" height="300px" />
    </div>
  </div>
</template>

<script>

import { formatDate } from '@/utils'
import { listUserActiveIndex } from '@/api/statistics'
import LineGraphCharts from './line-graph-charts'
export default {
  components: { LineGraphCharts },
  data() {
    return {
      latestActiveListLoading: true,
      latestActiveList: [],
      latestActiveIndex: {},
      // 活跃指标
      activeIndexs: [
        { name: '活跃用户数', field: 'activeUserNum', type: 'ACTIVE_USER_NUM' },
        { name: '新增注册人数', field: 'registUserNumOneDay', type: 'REGIST_USER_NUM_ONE_DAY' },
        { name: '累计注册人数', field: 'registUserNumTotal', type: 'REGIST_USER_NUM_TOTAL' },
        { name: '登录次数', field: 'loginTimes', type: 'LOGIN_TIMES' },
        { name: '登录人数', field: 'loginUserNum', type: 'LOGIN_USER_NUM' },
        { name: '上行消息数', field: 'upMsgNum', type: 'UP_MSG_NUM' },
        { name: '发消息人数', field: 'sendMsgUserNum', type: 'SEND_MSG_USER_NUM' },
        { name: 'APNs 推送数', field: 'apnsMsgNum', type: 'APNS_MSG_NUM' },
        { name: '上行消息数（C2C）', field: 'c2cUpMsgNum', type: 'C2C_UP_MSG_NUM' },
        { name: '下行消息数（C2C）', field: 'c2cDownMsgNum', type: 'C2C_DOWN_MSG_NUM' },
        { name: '发消息人数（C2C）', field: 'c2cSendMsgUserNum', type: 'C2C_SEND_MSG_USER_NUM' },
        { name: 'APNs 推送数（C2C）', field: 'c2cAPNSMsgNum', type: 'C2C_APNS_MSG_NUM' },
        { name: '最高在线人数', field: 'maxOnlineNum', type: 'MAX_ONLINE_NUM' },
        { name: '下行消息总数（C2C和群）', field: 'downMsgNum', type: 'FOWN_MSG_NUM' },
        { name: '关系链对数增加量', field: 'chainIncrease', type: 'CHAIN_INCREASE' },
        { name: '关系链对数删除量', field: 'chainDecrease', type: 'CHAIN_DECREASE' },
        { name: '上行消息数（群）', field: 'groupUpMsgNum', type: 'GROUP_UP_MSG_NUM' },
        { name: '下行消息数（群）', field: 'groupDownMsgNum', type: 'GROUP_DOWN_MSG_NUM' },
        { name: '发消息人数（群）', field: 'groupSendMsgUserNum', type: 'GROUP_SEND_MSG_USER_NUM' },
        { name: 'APNs 推送数（群）', field: 'groupAPNSMsgNum', type: 'GROUP_APNS_MSG_NUM' },
        { name: '发消息群组数', field: 'groupSendMsgGroupNum', type: 'GROUP_SEND_MSG_GROUP_NUM' },
        { name: '入群总数', field: 'groupJoinGroupTimes', type: 'GROUP_JOIN_GROUP_TIMES' },
        { name: '退群总数', field: 'groupQuitGroupTimes', type: 'GROUP_QUIT_GROUP_TIMES' },
        { name: '新增群组数', field: 'groupNewGroupNum', type: 'GROUP_NEW_GROUP_NUM' },
        { name: '累计群组数', field: 'groupAllGroupNum', type: 'GROUP_ALL_GROUP_NUM' },
        { name: '解散群个数', field: 'groupDestroyGroupNum', type: 'GROUP_DESTORY_GROUP_NUM' },
        { name: '回调请求数', field: 'callbackReq', type: 'CALLBACK_REQ' },
        { name: '回调应答数', field: 'callbackRsp', type: 'CALLBACK_RSP' }
      ],
      // 自定义活跃指标
      customizeIndexs: [
        { name: '普通用户活跃数', field: 'ordinaryActiveNum', type: 'GENERAL_USER_ACTIVE_NUM' },
        { name: '主播活跃数', field: 'anchorActiveNum', type: 'HOST_USER_ACTIVE_NUM' }
      ],
      chartsData: {
        data: [],
        title: ''
      },
      selectedIndex: {
        type: 'IM_ACTIVE',
        data: {}
      }
    }
  },

  created() {
    this.selectedIndex = {
      type: 'IM_ACTIVE',
      data: this.activeIndexs[0]
    }
    this.renderData()
  },
  methods: {
    renderData() {
      const that = this
      that.latestActiveListLoading = true 
      listUserActiveIndex().then(res => {
        that.latestActiveListLoading = false
        const { body } = res
        that.latestActiveList = body || []
        if (that.latestActiveList.length > 0) {
          that.latestActiveIndex = that.latestActiveList[that.latestActiveList.length - 1]
        }
        that.proccessIndexChartsData()
        that.renderCharts()
      }).catch(er => {
        that.latestActiveListLoading = false
      })
    },
    proccessIndexChartsData() {
      const that = this
      const type = that.selectedIndex.type
      const data = that.selectedIndex.data
      const field = data.field
      that.chartsData.title = data.name
      that.chartsData.data = []
      if (!that.latestActiveList || that.latestActiveList.length <= 0 || !field) {
        return
      }
      const resultData = []
      that.latestActiveList.forEach(activeIndex => {
        if (type === 'IM_ACTIVE') {
          const value = activeIndex.imActiveIndex[field] || 0
          resultData.push([activeIndex.date, value])
        }
        if (type === 'CUTOMIZE_ACTIVE') {
          const value = activeIndex.customizeActiveIndex[field] || 0
          resultData.push([activeIndex.date, value])
        }
      })
      that.chartsData = {
        title: data.name,
        data: resultData
      }
    },
    clickQueryCharts(type, item) {
      this.selectedIndex = {
        type,
        data: item
      }
      this.proccessIndexChartsData()
    },
    handleSearch() {
      this.renderData()
    },
    handleListToChartsData() {
      const that = this
      const chartsData = []
      if (that.list.length > 0) {
        that.list.forEach(item => {
          const date = formatDate(item.statisticsTime, 'yyyy-MM-dd')
          chartsData.push([date, (item.quantity || 0)])
        })
      }
      return chartsData
    },
    renderCharts() {
      const that = this
      const thatDayData = that.handleListToChartsData()
      that.subsidyIntegralCharts.setOption({
        color: ['#66b3ff', '#ce90e8', '#ff9c6e', '#5cdbd3'],
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(50,50,50,0.5)',
          axisPointer: {
            type: 'cross',
            label: {
              lineStyle: { color: '#009688' },
              crossStyle: { color: '#008acd' },
              shadowStyle: { color: 'rgba(200,200,200,0.2)' }
            }
          }
        },
        grid: {
          x: 20,
          y: 50,
          x2: 20,
          y2: 0,
          containLabel: true,
          borderColor: '#eee'
        },
        toolbox: { color: ['#1e90ff', '#1e90ff', '#1e90ff', '#1e90ff'], effectiveColor: '#ff4500' },
        xAxis: [
          {
            show: false,
            type: 'category',
            boundaryGap: false,
            splitArea: {
              show: true,
              areaStyle: { color: ['rgba(250,250,250,0.1)', 'rgba(200,200,200,0.1)'] }
            },
            axisLabel: {
              rotate: 38
            },
            splitLine: { lineStyle: { color: ['#eee'] }},
            splitNumber: 24,
            axisLine: { lineStyle: { color: '#b7bdc7' }}
          }
        ],
        yAxis: [{
          type: 'value',
          axisTick: { show: true, length: 0 },
          splitNumber: 5,
          splitLine: { lineStyle: { color: ['#eee'] }},
          axisLine: { lineStyle: { color: '#b7bdc7' }}
        }],
        legend: { y: 10, textStyle: { color: '#8e929b' }},
        series: [
          {
            name: '补贴积分',
            type: 'line',
            stack: '补贴积分',
            smooth: 0.6,
            symbol: 'none',
            symbolSize: 10,
            data: thatDayData,
            areaStyle: {}
          }]
      }, true)
    }
  }
}
</script>
<style scoped lang="scss">
.active-index {
  .title {
    line-height: 60px;
    font-size: 20px;
    font-weight: bold;
    .desc {
      font-size: 14px;
      font-weight: 400;
      color: #999999;
    }
  }
  .block-card {
    width: 130px;
    cursor: pointer;
    border-radius: 2px;
    margin-right:5px;
    .date {
      font-weight: bold;
      line-height: 30px;
    }
  }
}
</style>
