<template>
  <div v-loading="loading" class="dily-register-user">
    <el-date-picker
      v-model="dateNumber"
      style="margin-bottom: 10px;"
      type="date"
      placeholder="选择日期"
      value-format="yyyyMMdd"
      size="mini"
      :picker-options="pickerOptions"
      :clearable="false"
      @change="changeDateNumber"
    />
    <div v-for="(item, index) in list" :key="index">
      <el-card v-if="sysOriginPlatforms.includes(item.sysOrigin)" class="box-card">
        <div slot="header" class="clearfix">
          <span>金币收支Top50({{ item.sysOriginName }})</span>
        </div>
        <div class="content">
          <div style="font-size: 12px;color: #666;margin-bottom: 10px;">当日金币收支抽取前TOP50, 不含外部游戏</div>
          <el-table
            :data="item.items"
            element-loading-text="Loading"
            fit
            highlight-current-row
          >
            <el-table-column label="No" width="50" align="center">
              <template slot-scope="scope">{{ scope.$index + 1 }}</template>
            </el-table-column>
            <el-table-column prop="roomProfile.roomName" label="用户" align="center" min-width="200">
              <template slot-scope="scope">
                <user-table-exhibit :user-profile="scope.row.userProfile" :query-details="true" />
              </template>
            </el-table-column>
            <el-table-column label="收入" width="100" align="center">
              <template slot-scope="scope">
                <div>{{ scope.row.groupTypeQuantity['0'] || 0 }}</div>
              </template>
            </el-table-column>
            <el-table-column label="支出" width="100" align="center">
              <template slot-scope="scope">
                <div>{{ scope.row.groupTypeQuantity['1'] || 0 }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="quantity" label="合计" align="center" width="100" />
            <el-table-column fixed="right" label="操作" align="center" width="80">
              <template slot-scope="scope">
                <el-button type="text" @click.native="clickQueryDetails(scope.row)">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>

    <div class="user-daily-currency-gold-top-drawer">
      <el-drawer
        title="详情"
        :before-close="() => queryDetailsVisible=false"
        :visible="queryDetailsVisible"
        :close-on-press-escape="false"
        :wrapper-closable="false"
        :modal-append-to-body="true"
        :append-to-body="true"
        custom-class="drawer-auto-layout"
      >

        <div class="bar-graph-charts" style="padding: 0px 10px">
          <bar-graph-charts
            ref="lineGraphCharts"
            :key="queryDetailsRow ? queryDetailsRow.userId : 'tmp'"
            :charts-data="queryDetailsRow.items"
            :height="(queryDetailsRow.items ? queryDetailsRow.items.length > 5 ? queryDetailsRow.items.length * 40 : queryDetailsRow.items.length * 80: 300)+'px'"
          />
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import { listDailyUserGoldTop } from '@/api/statistics'
import BarGraphCharts from './bar-graph-charts'
import { currencyOrigins } from '@/constant/type'
import { mapGetters } from 'vuex'
import { formatDate, beforeDateObject } from '@/utils'

export default {
  name: 'DilyRegisterUser',
  components: { BarGraphCharts },
  data() {
    return {
      queryDetailsVisible: false,
      pickerOptions: {
        disabledDate(date) {
          return date.getTime() > beforeDateObject(1).getTime()
        }
      },
      dateNumber: formatDate(beforeDateObject(new Date().getHours() > 8 ? 1 : 2), 'yyyyMMdd'),
      loading: false,
      list: [],
      queryDetailsRow: {},
      selectOrigin: currencyOrigins[0].value,
      selectOriginName: currencyOrigins[0].name
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatformAlls']),
    sysOriginPlatforms() {
      if (!this.permissionsSysOriginPlatformAlls || this.permissionsSysOriginPlatformAlls.length <= 0) {
        return []
      }
      return this.permissionsSysOriginPlatformAlls.map(item => item.value)
    }
  },
  created() {
    this.loadOrigin()
  },
  methods: {
    loadOrigin() {
      const that = this
      that.loading = true
      listDailyUserGoldTop(this.dateNumber).then(res => {
        that.loading = false
        const { body } = res
        const list = body || []
        that.list = list.filter(item => !item.sysOrigin || that.permissionsSysOriginPlatformAlls.some(sys => sys.value === item.sysOrigin))
      }).catch(() => {
        that.loading = false
      })
    },
    querySearch(queryString, cb) {
      var restaurants = currencyOrigins
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
      cb(results)
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) >= 0) || (restaurant.name.toLowerCase().indexOf(queryString.toLowerCase()) >= 0)
      }
    },
    handleSelect(item) {
      this.selectOrigin = item.value
      this.selectOriginName = item.name
      this.loadOrigin()
    },
    clickAnalyze() {
      this.$router.push('/system/manager/tools/manager/gold-analyze')
    },
    changeDateNumber() {
      this.loadOrigin()
    },
    clickQueryDetails(row) {
      this.queryDetailsRow = row
      this.queryDetailsVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
.box-card {
  margin-bottom: 10px;
  padding: 10px;
  .card-col {
    margin-bottom: 10px;
  }
}

.register-logout-count {
   .date {
     font-weight: bold;
     line-height: 30px;
   }
}
</style>
