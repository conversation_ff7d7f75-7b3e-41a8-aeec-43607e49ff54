<template>
  <div v-loading="loading" class="dily-register-user">
    <el-row :gutter="10">
      <el-col :span="24">
        <el-autocomplete
          v-model="selectOriginName"
          popper-class="my-autocomplete"
          :fetch-suggestions="querySearch"
          placeholder="请输选择查看来源类型"
          clearable
          style="width: 100%; padding-bottom: 10px;"
          @select="handleSelect"
          @clear="handleSelect"
        >
          <i
            slot="suffix"
            class="el-icon-edit el-input__icon"
          />
          <template slot-scope="{ item }">
            <span class="name">{{ item.name }}</span>
            <div class="value font-info" style="font-size:12px;line-height: 12px; padding-bottom: 10px;">{{ item.value }}</div>
          </template>
        </el-autocomplete>
      </el-col>
      <el-col :span="24">
        <div v-for="(item, index) in list" :key="index">
          <el-card v-if="sysOriginPlatforms.includes(item.sysOrigin)" class="box-card">
            <div slot="header" class="clearfix">
              <span>近30金币来源明细({{ item.sysOriginName }})&nbsp;<a class="font-blue" @click="clickAnalyze">实时分析</a></span>
            </div>
            <div style="padding: 10px 0px">
              <el-row>
                <el-col class="card-col" :md="24" :xs="24">
                  <line-graph-charts ref="lineGraphCharts" :key="item.sysOrigin" :charts-data="item.list" height="300px" />
                </el-col>
              </el-row>
            </div>
          </el-card>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { latestStatisticsGoldOriginDetails } from '@/api/statistics'
import LineGraphCharts from './line-graph-charts'
import { currencyOrigins } from '@/constant/type'
import { mapGetters } from 'vuex'

export default {
  name: 'DilyRegisterUser',
  components: { LineGraphCharts },
  data() {
    return {
      loading: false,
      list: [],
      selectOrigin: currencyOrigins[0].value,
      selectOriginName: currencyOrigins[0].name
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatformAlls']),
    sysOriginPlatforms() {
      if (!this.permissionsSysOriginPlatformAlls || this.permissionsSysOriginPlatformAlls.length <= 0) {
        return []
      }
      return this.permissionsSysOriginPlatformAlls.map(item => item.value)
    }
  },
  created() {
    this.loadOrigin()
  },
  methods: {
    loadOrigin() {
      const that = this
      that.loading = true
      latestStatisticsGoldOriginDetails(that.selectOrigin).then(res => {
        that.loading = false
        const { body } = res
        const list = body || []
        that.list = list.filter(item => !item.sysOrigin || that.permissionsSysOriginPlatformAlls.some(sys => sys.value === item.sysOrigin))
      }).catch(() => {
        that.loading = false
      })
    },
    querySearch(queryString, cb) {
      var restaurants = currencyOrigins
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
      cb(results)
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) >= 0) || (restaurant.name.toLowerCase().indexOf(queryString.toLowerCase()) >= 0)
      }
    },
    handleSelect(item) {
      this.selectOrigin = item.value
      this.selectOriginName = item.name
      this.loadOrigin()
    },
    clickAnalyze() {
      this.$router.push('/system/manager/tools/manager/gold-analyze')
    }
  }
}
</script>

<style scoped lang="scss">
.box-card {
  margin-bottom: 10px;
  padding: 10px;
  .card-col {
    margin-bottom: 10px;
  }
}

.register-logout-count {
   .date {
     font-weight: bold;
     line-height: 30px;
   }
}
</style>
