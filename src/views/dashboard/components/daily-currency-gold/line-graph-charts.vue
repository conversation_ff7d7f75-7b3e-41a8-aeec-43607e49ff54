<template>
  <div class="daily-currency-charts">
    <el-select v-model="typeValue" placeholder="请选择" size="mini" @change="typeValueChange">
      <el-option
        v-for="item in types"
        :key="item.value"
        :label="item.name"
        :value="item.value"
      />
    </el-select>
    <div id="videoCharts" ref="videoCharts" :style="'width: 100%;height:'+ height +';'" />
  </div>
</template>

<script>

// import { currencyOrigins } from '@/constant/type'
// import { toMap } from '@/utils'

export default {
  name: 'DailyCurrencyCharts',
  props: {
    height: {
      type: String,
      default: '600px'
    },
    chartsData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // currencyOriginMap: toMap(currencyOrigins, 'value'),
      types: [
        { value: 0, name: '收入' },
        { value: 1, name: '支出' }
      ],
      typeValue: 0,
      videoRealTimeCharts: null,
      groupChartsData: {
        income: [],
        expenditure: []
      }
    }
  },
  watch: {
    chartsData: {
      handler(newVal) {
        const that = this
        that.groupChartsData = that.groupChartsDataByType()
        that.$nextTick(() => {
          that.renderCharts()
        })
      },
      immediate: true
    }
  },
  created() {
  },
  mounted() {
    const that = this
    that.videoRealTimeCharts = that.$echarts.init(that.$refs.videoCharts)
    that.renderCharts()
    window.addEventListener('resize', () => {
      that.videoRealTimeCharts.resize()
    })
  },
  methods: {
    groupChartsDataByType() {
      const that = this
      const obj = {
        income: [],
        expenditure: []
      }
      if (that.chartsData.length > 0) {
        that.chartsData.forEach(item => {
          if (item.type === 0) {
            obj.income.push(item)
          }
          if (item.type === 1) {
            obj.expenditure.push(item)
          }
        })
      }
      return obj
    },
    createSeries(list) {
      if (!list || list.length === 0) {
        return []
      }
      // const that = this
      const groupOrigin = {}
      const size = list.length - 1
      for (let index = size; index >= 0; index--) {
        const item = list[index]
        const origin = groupOrigin[item.originName]
        const element = [item.dateNumber, item.quantity || 0]
        if (!origin) {
          groupOrigin[item.originName] = {
            origin: item.origin,
            originName: item.originName,
            data: [element]
          }
          continue
        }
        origin.data.push(element)
      }

      const resultSeries = []
      for (const key in groupOrigin) {
        const origin = groupOrigin[key]
        resultSeries.push({
          name: origin.originName,
          type: 'line',
          stack: origin.originName,
          smooth: 0.6,
          symbol: 'none',
          symbolSize: 10,
          data: origin.data || [],
          areaStyle: {}
        })
      }
      return resultSeries
    },
    renderCharts() {
      const that = this
      that.videoRealTimeCharts.setOption({
        color: ['#66b3ff', '#ce90e8', '#ff9c6e', '#5cdbd3'],
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(50,50,50,0.5)',
          axisPointer: {
            type: 'cross',
            label: {
              lineStyle: { color: '#009688' },
              crossStyle: { color: '#008acd' },
              shadowStyle: { color: 'rgba(200,200,200,0.2)' }
            }
          }
        },
        grid: {
          x: 20,
          y: 50,
          x2: 20,
          y2: 0,
          containLabel: true,
          borderColor: '#eee'
        },
        toolbox: { color: ['#1e90ff', '#1e90ff', '#1e90ff', '#1e90ff'], effectiveColor: '#ff4500' },
        xAxis: [
          {
            show: false,
            type: 'category',
            boundaryGap: false,
            splitArea: {
              show: true,
              areaStyle: { color: ['rgba(250,250,250,0.1)', 'rgba(200,200,200,0.1)'] }
            },
            axisLabel: {
              rotate: 38
            },
            axisLine: { lineStyle: { color: '#b7bdc7' }},
            splitLine: { lineStyle: { color: ['#eee'] }},
            splitNumber: 24
          }
        ],
        yAxis: [{
          type: 'value',
          axisTick: { show: true, length: 0 },
          splitNumber: 5,
          splitLine: { lineStyle: { color: ['#eee'] }},
          axisLine: { lineStyle: { color: '#b7bdc7' }}
        }],
        legend: {
          y: 10,
          textStyle: { color: '#8e929b' }
        },
        series: that.createSeries(that.typeValue === 0 ? that.groupChartsData.income : that.groupChartsData.expenditure)
      }, true)
    },
    typeValueChange() {
      const that = this
      that.$nextTick(() => {
        that.renderCharts()
      })
    }
  }
}
</script>
