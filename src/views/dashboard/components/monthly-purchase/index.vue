<template>
  <div class="dily-register-user">
    <el-row :gutter="10">
      <el-col :span="24">
        <div v-for="(item, index) in list" :key="index">
          <el-card v-if="sysOriginPlatforms.includes(item.sysOrigin)" class="box-card">
            <div slot="header" class="clearfix">
              <span>近12月内购({{ item.sysOriginName }})</span>
            </div>
            <div v-loading="loading" style="padding: 10px 0px">
              <el-row :gutter="10" class="daily-purchase">
                <el-col :span="24">
                  <div class="date">{{ item.last ? item.last.dateNumber : '' }}</div>
                </el-col>
                <el-col :md="6" :xs="24">
                  <div class="block-card">
                    <div class="block-card-title">Android</div>
                    <div class="block-card-content cite">{{ item.last ? item.last.androidQuantity || 0 : 0 }}</div>
                  </div>
                </el-col>
                <el-col :md="6" :xs="24">
                  <div class="block-card">
                    <div class="block-card-title">iOS</div>
                    <div class="block-card-content cite">{{ item.last ? item.last.iosQuantity || 0 : 0 }}</div>
                  </div>
                </el-col>
                <el-col :md="6" :xs="24">
                  <div class="block-card">
                    <div class="block-card-title">H5</div>
                    <div class="block-card-content cite">{{ item.last ? item.last.h5Quantity || 0 :0 }}</div>
                  </div>
                </el-col>
                <el-col :md="6" :xs="24">
                  <div class="block-card">
                    <div class="block-card-title">有效/退款</div>
                    <div class="block-card-content cite">{{ item.last ?item.last.completeQuantity || 0 :0 }} / {{ item.last ?item.last.reimburseQuantity || 0 :0 }}</div>
                  </div>
                </el-col>
                <el-col class="card-col" :md="24" :xs="24">
                  <line-graph-charts :key="item.sysOrigin" :charts-data="item.lastMonths" height="300px" />
                </el-col>
              </el-row>
            </div>
          </el-card>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { latestMonthlyPurchaseAmountPlatform } from '@/api/statistics'
import LineGraphCharts from './line-graph-charts'
import { mapGetters } from 'vuex'

export default {
  name: 'DilyRegisterUser',
  components: { LineGraphCharts },
  data() {
    return {
      loading: false,
      list: []
    }
  },
  computed: {
    ...mapGetters(['permissionsSysOriginPlatformAlls']),
    sysOriginPlatforms() {
      if (!this.permissionsSysOriginPlatformAlls || this.permissionsSysOriginPlatformAlls.length <= 0) {
        return []
      }
      return this.permissionsSysOriginPlatformAlls.map(item => item.value)
    }
  },
  created() {
    if (!this.permissionsSysOriginPlatformAlls || this.permissionsSysOriginPlatformAlls.length <= 0) {
      return
    }
    this.renderData()
  },
  methods: {
    renderData() {
      const that = this
      that.loading = true
      latestMonthlyPurchaseAmountPlatform().then(res => {
        that.loading = false
        const { body } = res
        const list = body || []
        that.list = list.filter(item => !item.sysOrigin || that.permissionsSysOriginPlatformAlls.some(sys => sys.value === item.sysOrigin))
      }).catch(() => {
        that.loading = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
.box-card {
  margin-bottom: 10px;
  padding: 10px;
  .card-col {
    margin-bottom: 10px;
  }
}

.daily-purchase {
   .date {
     font-weight: bold;
     line-height: 30px;
   }
}
</style>
