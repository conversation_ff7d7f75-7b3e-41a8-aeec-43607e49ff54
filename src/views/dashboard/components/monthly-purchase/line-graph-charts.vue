<template>
  <div class="daily-currency-charts">
    <div id="videoCharts" ref="videoCharts" :style="'width: 100%;height:'+ height +';'" />
  </div>
</template>

<script>

import { formatDate } from '@/utils'
export default {
  name: 'DailyCurrencyCharts',
  props: {
    height: {
      type: String,
      default: '600px'
    },
    chartsData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      videoRealTimeCharts: null
    }
  },
  created() {
  },
  mounted() {
    const that = this
    that.videoRealTimeCharts = that.$echarts.init(that.$refs.videoCharts)
    that.renderCharts()
    window.addEventListener('resize', () => {
      that.videoRealTimeCharts.resize()
    })
  },
  methods: {
    handleListToChartsData() {
      const that = this
      const dataCharts = {
        androidQuantity: [],
        iosQuantity: [],
        h5Quantity: [],
        completeQuantity: [],
        reimburseQuantity: [],
        offlineQuantity: []
      }
      if (that.chartsData.length > 0) {
        that.chartsData.forEach(item => {
          const date = item.dateNumber
          dataCharts.androidQuantity.push([date, (item.androidQuantity || 0)])
          dataCharts.iosQuantity.push([date, (item.iosQuantity || 0)])
          dataCharts.h5Quantity.push([date, (item.h5Quantity || 0)])
          dataCharts.completeQuantity.push([date, (item.completeQuantity || 0)])
          dataCharts.reimburseQuantity.push([date, (item.reimburseQuantity || 0)])
          dataCharts.offlineQuantity.push([date, (item.offlineQuantity || 0)])
        })
      }
      return dataCharts
    },
    renderCharts() {
      const that = this
      const thatDayData = that.handleListToChartsData()
      that.videoRealTimeCharts.setOption({
        color: ['#3eda86', '#303133', '#e9640e', '#6c4c49', '#66b3ff', '#ce90e8', '#ff9c6e', '#5cdbd3', '#E6A23C'],
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(50,50,50,0.5)',
          axisPointer: {
            type: 'cross',
            label: {
              lineStyle: { color: '#009688' },
              crossStyle: { color: '#008acd' },
              shadowStyle: { color: 'rgba(200,200,200,0.2)' }
            }
          }
        },
        grid: {
          x: 20,
          y: 50,
          x2: 20,
          y2: 0,
          containLabel: true,
          borderColor: '#eee'
        },
        toolbox: { color: ['#1e90ff', '#1e90ff', '#1e90ff', '#1e90ff'], effectiveColor: '#ff4500' },
        xAxis: [
          {
            show: false,
            type: 'category',
            boundaryGap: false,
            splitArea: {
              show: true,
              areaStyle: { color: ['rgba(250,250,250,0.1)', 'rgba(200,200,200,0.1)'] }
            },
            axisLabel: {
              rotate: 38
            },
            axisLine: { lineStyle: { color: '#b7bdc7' }},
            splitLine: { lineStyle: { color: ['#eee'] }},
            splitNumber: 24
          }
        ],
        yAxis: [{
          type: 'value',
          axisTick: { show: true, length: 0 },
          splitNumber: 5,
          splitLine: { lineStyle: { color: ['#eee'] }},
          axisLine: { lineStyle: { color: '#b7bdc7' }}
        }],
        legend: {
          y: 10,
          textStyle: { color: '#8e929b' }
        },
        series: [
          {
            name: 'Android',
            type: 'line',
            stack: 'Android',
            smooth: 0.6,
            symbol: 'none',
            symbolSize: 10,
            data: thatDayData.androidQuantity,
            areaStyle: {}
          },
          {
            name: 'iOS',
            type: 'line',
            stack: 'iOS',
            smooth: 0.6,
            symbol: 'none',
            symbolSize: 10,
            data: thatDayData.iosQuantity,
            areaStyle: {}
          },
          {
            name: 'H5',
            type: 'line',
            stack: 'H5',
            smooth: 0.6,
            symbol: 'none',
            symbolSize: 10,
            data: thatDayData.h5Quantity,
            areaStyle: {}
          },
          {
            name: 'Offline',
            type: 'line',
            stack: 'Offline',
            smooth: 0.6,
            symbol: 'none',
            symbolSize: 10,
            data: thatDayData.offlineQuantity,
            areaStyle: {}
          },
          {
            name: '有效',
            type: 'line',
            stack: '有效',
            smooth: 0.6,
            symbol: 'none',
            symbolSize: 10,
            data: thatDayData.completeQuantity,
            areaStyle: {}
          },
          {
            name: '退款',
            type: 'line',
            stack: '退款',
            smooth: 0.6,
            symbol: 'none',
            symbolSize: 10,
            data: thatDayData.reimburseQuantity,
            areaStyle: {}
          }]
      }, true)
    }
  }
}
</script>
