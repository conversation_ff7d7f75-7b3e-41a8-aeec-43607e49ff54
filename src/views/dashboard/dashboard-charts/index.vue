<template>
  <div class="customize-charts">
    <el-row :gutter="10">
      <el-col v-for="item in tablePermissions" :key="item.component" :md="4" :sm="8" :xs="12">
        <div class="charts-tag flex-c" :class="{'charts-tag-selected': activeName === item.component}" @click="clickTag(item)">
          <div class="nowrap-ellipsis">
            {{ item.title }}
          </div>
        </div>
      </el-col>
      <el-col :span="24">
        <component :is="activeName" />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import DailyRegisterUser from './../components/daily-register-user'
import DailyCurrency from './../components/daily-currency'
import DailyPurchase from './../components/daily-purchase'
import MonthlyPurchase from './../components/monthly-purchase'
import ActiveIndex from './../components/active-index'
import DailyCurrencyGold from './../components/daily-currency-gold'
import UserDailyCurrencyGoldTop from './../components/user-daily-currency-gold-top'
import UserDailyCurrencyRechargeTop from './../components/user-daily-currency-recharge-top'
import DailyCurrencyGoldOriginTop from './../components/daily-currency-gold-origin-top'
import PropsSalesOverviewCharts from '@/components/data/PropsSalesOverviewCharts'
import { mapGetters } from 'vuex'

export default {
  name: 'DashboardCharts',
  components: { UserDailyCurrencyRechargeTop, UserDailyCurrencyGoldTop, DailyCurrencyGoldOriginTop, DailyRegisterUser, DailyCurrency, DailyPurchase, MonthlyPurchase, ActiveIndex, PropsSalesOverviewCharts, DailyCurrencyGold },
  data() {
    return {
      activeName: '',
      tables: {
        'dashboard:added:daily': {
          title: '每日新增',
          component: 'DailyRegisterUser',
          sort: 1
        },
        'dashboard:active:situation': {
          title: '活跃情况',
          component: 'ActiveIndex',
          sort: 2
        },
        'dashboard:daily:inapp:purchases': {
          title: '每日内购',
          component: 'DailyPurchase',
          sort: 3
        },
        'dashboard:monthly:inapp:purchases': {
          title: '每月内购',
          component: 'MonthlyPurchase',
          sort: 4
        },
        'props:sales:overview:charts': {
          title: '道具销售情况',
          component: 'PropsSalesOverviewCharts',
          sort: 5
        },
        'dashboard:money:balance': {
          title: '货币收支',
          component: 'DailyCurrency',
          sort: 6
        },
        'dashboard:daily:gold': {
          title: '每日金币(分析)',
          component: 'DailyCurrencyGold',
          sort: 7
        },
        'dashboard:daily:gold:origin:top': {
          title: '每日金币(来源榜)',
          component: 'DailyCurrencyGoldOriginTop',
          sort: 8
        },
        'dashboard:daily:gold:user:top': {
          title: '用户金币(Top50)',
          component: 'UserDailyCurrencyGoldTop',
          sort: 9
        },
        'dashboard:daily:recharge:rank': {
          title: '每日充值(Top50)',
          component: 'UserDailyCurrencyRechargeTop',
          sort: 10
        }
      }
    }
  },
  computed: {
    ...mapGetters(['buttonPermissions']),
    tablePermissions() {
      if (!this.buttonPermissions || this.buttonPermissions.length <= 0) {
        return []
      }
      const tabMap = {}
      for (let index = 0; index < this.buttonPermissions.length; index++) {
        const permission = this.buttonPermissions[index]
        if (!permission) {
          continue
        }
        const item = this.tables[permission]
        if (item) {
          tabMap[item.sort] = item
        }
      }

      const sortKeys = Object.keys(tabMap).sort((a, b) => {
        return tabMap[a].sort - tabMap[b].sort
      })

      return sortKeys.map(index => {
        return tabMap[index]
      })
    }
  },
  created() {
    if (this.tablePermissions.length > 0) {
      this.activeName = this.tablePermissions[0].component
    }
  },
  methods: {
    clickTag(item) {
      this.activeName = item.component
    }
  }
}
</script>

<style lang="scss">
.customize-charts {
  .charts-tag {
    color: #333;
    background-color: #eaeff2;
  }
  .charts-tag:hover {
    background-color: #dfe9ee;
  }
  .charts-tag-selected {
    background-color: #cadce5;
  }
}
</style>
<style scoped lang="scss">
.customize-charts {
  .charts-tag {
    width: 100%;
    height: 32px;
    padding: 0 12px;
    margin-bottom: 8px;
    font-size: 12px;
    overflow: hidden;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    border-radius: 2px;
    cursor: pointer;
    white-space: nowrap;
    text-decoration: none;
  }
}

</style>
