import Vue from 'vue'
import Vuex from 'vuex'
// import createVuexAlong from 'vuex-along'
import getters from './getters'
import app from './modules/app'
import settings from './modules/settings'
import user from './modules/user'

Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,
    settings,
    user
  },
  getters
  // plugins: [createVuexAlong({
  //   name: 'sug-vx-al',
  //   local: {},
  //   session: {
  //     list: ['user']
  //   }
  // })]
})

export default store
