import Cookies from 'js-cookie'

const state = {
  sidebar: {
    opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,
    withoutAnimation: false
  },
  device: 'desktop',
  size: Cookies.get('size') || 'medium',
  runTask: []
}

const mutations = {
  TOGGLE_SIDEBAR: state => {
    state.sidebar.opened = !state.sidebar.opened
    state.sidebar.withoutAnimation = false
    if (state.sidebar.opened) {
      Cookies.set('sidebarStatus', 1)
    } else {
      Cookies.set('sidebarStatus', 0)
    }
  },
  CLOSE_SIDEBAR: (state, withoutAnimation) => {
    Cookies.set('sidebarStatus', 0)
    state.sidebar.opened = false
    state.sidebar.withoutAnimation = withoutAnimation
  },
  TOGGLE_DEVICE: (state, device) => {
    state.device = device
  },
  SET_SIZE: (state, size) => {
    state.size = size
    Cookies.set('size', size)
  },
  PUSH_RUN_TASK: (state, runTask) => {
    if (!runTask) {
      return
    }
    state.runTask.push(runTask)
  },
  CLEAN_ALL_RUN_TASK: (state) => {
    state.runTask.forEach(item => {
      try {
        clearInterval(item)
      } catch (er) {
        // ignore
      }
      try {
        clearTimeout(item)
      } catch (er) {
        // ignore
      }
    })
    state.runTask = []
  },
  REMOVE_RUN_TASK: (state, runTask) => {
    if (!runTask) {
      return
    }
    state.runTask = state.runTask.filter(item => item !== runTask)
  }
}

const actions = {
  toggleSideBar({ commit }) {
    commit('TOGGLE_SIDEBAR')
  },
  closeSideBar({ commit }, { withoutAnimation }) {
    commit('CLOSE_SIDEBAR', withoutAnimation)
  },
  toggleDevice({ commit }, device) {
    commit('TOGGLE_DEVICE', device)
  },
  setSize({ commit }, size) {
    commit('SET_SIZE', size)
  },
  pushRunTask({ commit }, runTask) {
    commit('PUSH_RUN_TASK', runTask)
  },
  cleanAllRunTask({ commit }) {
    commit('CLEAN_ALL_RUN_TASK')
  },
  removeRunTask({ commit }, runTask) {
    commit('REMOVE_RUN_TASK', runTask)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
