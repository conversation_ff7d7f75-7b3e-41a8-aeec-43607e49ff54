apiVersion: v1
kind: Namespace
metadata:
  name: rc-service-admin-dashboard
  labels:
    elbv2.k8s.aws/pod-readiness-gate-inject: enabled
---

apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: rc-service-admin-dashboard
  name: admin-dashboard-deployment
  labels:
    app: admin-dashboard
spec:
  replicas: 2
  revisionHistoryLimit:  3
  selector:
    matchLabels:
      app: admin-dashboard
  template:
    metadata:
      labels:
        app: admin-dashboard
    spec:
      containers:
        - name: admin-dashboard
          image: registry.ap-southeast-1.aliyuncs.com/rc1304/admin-dashboard:20230417v1 
          ports:
            - containerPort: 80
          resources:
            requests:
              cpu: ".5"
              memory: "200Mi"
            limits:
              cpu: "1"
              memory: "520Mi"
      imagePullSecrets:
        - name: rc-aliyun-secret

---
apiVersion: v1
kind: Service
metadata:
  namespace: rc-service-admin-dashboard
  name: admin-dashboard-service
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: external
    service.beta.kubernetes.io/aws-load-balancer-nlb-target-type: ip
    service.beta.kubernetes.io/aws-load-balancer-scheme: internet-facing
    service.beta.kubernetes.io/aws-load-balancer-target-group-attributes: deregistration_delay.timeout_seconds=30
    service.beta.kubernetes.io/aws-load-balancer-healthcheck-path: /
    service.beta.kubernetes.io/aws-load-balancer-healthcheck-healthy-threshold: '2'
    service.beta.kubernetes.io/aws-load-balancer-healthcheck-unhealthy-threshold: '2'
    service.beta.kubernetes.io/aws-load-balancer-healthcheck-interval: '10'
    #service.beta.kubernetes.io/aws-load-balancer-healthcheck-timeout: '2'

spec:
  selector:
    app: admin-dashboard
  type: LoadBalancer
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80

---
apiVersion: v1
data:
  .dockerconfigjson: eyJhdXRocyI6eyJyZWdpc3RyeS5hcC1zb3V0aGVhc3QtMS5hbGl5dW5jcy5jb20iOnsidXNlcm5hbWUiOiLnuqLlnIbnp5HmioDlhazlj7giLCJwYXNzd29yZCI6IjEzOTIyNDYwMDUwLiIsImF1dGgiOiI1N3FpNVp5RzU2ZVI1b3FBNVlXczVZKzRPakV6T1RJeU5EWXdNRFV3TGc9PSJ9fX0=
kind: Secret
metadata:
  namespace: rc-service-admin-dashboard
  creationTimestamp: "2023-04-12T08:35:59Z"
  name: rc-aliyun-secret
  resourceVersion: "2202094"
  uid: 0ea42058-a318-4ead-af1c-441cbbc5e649
type: kubernetes.io/dockerconfigjson