# just a flag
ENV = 'development'

# base api
VUE_APP_BASE_API = '/console'

# vue-cli uses the VUE_CLI_BABEL_TRANSPILE_MODULES environment variable,
# to control whether the babel-plugin-dynamic-import-node plugin is enabled.
# It only does one thing by converting all import() to require().
# This configuration can significantly increase the speed of hot updates,
# when you have a large number of pages.
# Detail:  https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/babel-preset-app/index.js

VUE_CLI_BABEL_TRANSPILE_MODULES = true

# application base url
# VUE_APP_BASE_URL = 'http://localhost:9528'
# VUE_APP_BASE_URL ='http://local.consolepage.yuyinfang168.com'
 # VUE_APP_BASE_URL ='http://127.0.0.1:2700'
 VUE_APP_BASE_URL ='http://**************:2700'
# oss
VUE_APP_OSS_BUCKET = 'dev-yuyin-1352144764'
VUE_APP_OSS_URL = 'https://dev-yuyin-1352144764.cos.ap-beijing.myqcloud.com'
# h5
VUE_APP_H5_BASE_URL = 'http://local.h5.yuyinfang168.com'
# start mock
VUE_APP_START_MOCK = false

