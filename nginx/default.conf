server {
    listen       80;
    listen       [::]:80;
    server_name  _;
    root         /usr/share/nginx/html;

    # Load configuration files for the default server block.
    include /etc/nginx/default.d/*.conf;
    location / {
        # 此处的 @router 实际上是引用下面的转发，否则在 Vue 路由刷新时可能会抛出 404
        try_files $uri $uri/ @router;
        # 请求指向的首页
        index index.html;
    }
    location /console {
       proxy_pass http://console.prod.svc.cluster.local/console;
       proxy_set_header Host $host;
       proxy_set_header X-Real-IP $remote_addr;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       proxy_connect_timeout 30s;
       proxy_read_timeout 60s;
       proxy_send_timeout 30s;
       proxy_http_version 1.1;
       proxy_set_header Upgrade $http_upgrade;
       proxy_set_header Connection "Upgrade";
    }
    error_page 404 /404.html;
    location = /404.html {
    }

    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
    }
}
