import Mock from 'mockjs'

export default [
  // 积分信息
  {
    url: '/user/integral/info/page',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          records: Mock.mock({ 'items|30': [
            {
              'userId': '12691914***********',
              'balance': 719300.00,
              'redeemed': 0.00,
              'lastBalanceTime': '2020-06-10 12:29:28',
              'integralCount': 719300.00,
              'userAvatar': 'http://dev.qiniu.sugartimeapp.com/8058D607-30D9-4492-95AD-C7413C177545.png',
              'userNickname': 'Clad',
              'userSex': 1,
              'userSexName': '男',
              'age': 20,
              'level': 0
            }
          ] }
          ).items,
          'total': 30,
          'size': 30,
          'current': 1,
          'searchCount': true,
          'pages': 3
        }
      }
    }
  },
  // 积分详情
  {
    url: '/user/integral/origin/page',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          'createTime': '2020-06-06 16:56:45',
          'updateTime': '2020-06-09 14:39:07',
          'id': '12691914***********',
          'userAvatar': 'http://dev.qiniu.sugartimeapp.com/8058D607-30D9-4492-95AD-C7413C177545.png',
          'userNickname': 'Clad',
          'liveUrl': '',
          'liveCover': '',
          'userSex': 1,
          'userSexName': '男',
          'userType': 0,
          'age': 20,
          'bornYear': 2000,
          'bornMonth': 1,
          'bornDay': 1,
          'countryName': 'United States',
          'countryId': '1231833389347123201',
          'countryCode': 'US',
          'accountStatus': 'NORMAL',
          'freezingTime': '2020-06-05 16:56:46'
        }
      }
    }
  },
  {
    url: '/user/base/info/level/\/*',
    type: 'get',
    response: () => {
      return {
        'status': 200
      }
    }
  },
  {
    url: '/team/billing/integral/record/page',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          records: Mock.mock({ 'items|30': [] }
          ).items,
          'total': 30,
          'size': 30,
          'current': 1,
          'searchCount': true,
          'pages': 3
        }
      }
    }
  },
  // 用户积分流水列表
  {
    url: '/user/integral/origin/stream/page',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          'createTime': '2020-06-06 16:56:45',
          'updateTime': '2020-06-09 14:39:07',
          'id': '12691914***********',
          'userAvatar': 'http://dev.qiniu.sugartimeapp.com/8058D607-30D9-4492-95AD-C7413C177545.png',
          'userNickname': 'Clad',
          'liveUrl': '',
          'liveCover': '',
          'userSex': 1,
          'userSexName': '男',
          'userType': 0,
          'age': 20,
          'bornYear': 2000,
          'bornMonth': 1,
          'bornDay': 1,
          'countryName': 'United States',
          'countryId': '1231833389347123201',
          'countryCode': 'US',
          'accountStatus': 'NORMAL',
          'freezingTime': '2020-06-05 16:56:46'
        }
      }
    }
  },
  // 用户积分历史流水列表
  {
    url: '/user/integral/origin/history/stream/page',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          'createTime': '2020-06-06 16:56:45',
          'updateTime': '2020-06-09 14:39:07',
          'id': '12691914***********',
          'userAvatar': 'http://dev.qiniu.sugartimeapp.com/8058D607-30D9-4492-95AD-C7413C177545.png',
          'userNickname': 'Clad',
          'liveUrl': '',
          'liveCover': '',
          'userSex': 1,
          'userSexName': '男',
          'userType': 0,
          'age': 20,
          'bornYear': 2000,
          'bornMonth': 1,
          'bornDay': 1,
          'countryName': 'United States',
          'countryId': '1231833389347123201',
          'countryCode': 'US',
          'accountStatus': 'NORMAL',
          'freezingTime': '2020-06-05 16:56:46'
        }
      }
    }
  }
]
