/**
 * 默认的mock请求声明数据
 */
import Mock from 'mockjs'

export function get(url, result) {
  return {
    url,
    type: 'get',
    response: config => {
      return {
        'status': 200,
        'result': result
      }
    }
  }
}

export function getPage(url, result) {
  return {
    url,
    type: 'get',
    response: config => {
      return {
        'status': 200,
        'result': {
          'records': Mock.mock({
            'items|30': [result]
          }).items,
          'total': 30,
          'size': 20,
          'current': 1,
          'searchCount': true,
          'pages': 1
        }
      }
    }
  }
}

export function post(url, result) {
  return {
    url,
    type: 'post',
    response: config => {
      return {
        'status': 200,
        'result': {}
      }
    }
  }
}

export function del(url, result) {
  return {
    url,
    type: 'delete',
    response: config => {
      return {
        'status': 200,
        result
      }
    }
  }
}

export function put(url, result) {
  return {
    url,
    type: 'put',
    response: config => {
      return {
        'status': 200,
        result
      }
    }
  }
}

