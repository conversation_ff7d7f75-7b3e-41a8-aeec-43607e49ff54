import Mock from 'mockjs'

export default [
  {
    url: '/user-special-id/flow',
    type: 'get',
    response: config => {
      return { 'status': 200, 'result': [{ 'userSpecialId': { 'id': 'TIM_CHAT_123321', 'timeId': '1493866636127289346', 'sysOrigin': 'TIM_CHAT', 'userId': '1493138116758827009', 'account': '123321', 'expiredTime': '2022-02-25 16:35:35', 'createTime': '2022-02-16 16:35:35', 'updateTime': '2022-02-16 16:35:35', 'customizeField': { 'tesrr': '1' }}, 'userBaseInfo': { 'createTime': '2022-02-14 16:20:42', 'updateTime': '2022-02-14 16:20:42', 'id': '1493138116758827009', 'userAvatar': '', 'originSys': 'TIM_CHAT', 'sysOriginChild': 'TIM_CHAT', 'userNickname': '啦啦啦2', 'userSex': 1, 'userSexName': '男', 'userType': 0, 'age': 2, 'bornYear': 2020, 'bornMonth': 1, 'bornDay': 1, 'userTypeName': '真实', 'countryName': 'China', 'countryId': '1231833262813360130', 'countryCode': 'CN', 'accountStatus': 'NORMAL', 'accountStatusName': '正常', 'freezingTime': '2022-02-13 16:20:43', 'del': false, 'account': '42032' }}] }
    }
  },
  {
    url: '/user-special-id/latest-log',
    type: 'get',
    response: config => {
      return {}
    }
  }]

