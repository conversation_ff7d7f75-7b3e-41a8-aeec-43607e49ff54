import Mock from 'mockjs'

export default [
  // 国家分页列表
  {
    url: '/sys/country/code/page',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          records: Mock.mock({ 'items|30': [
            {
              'createTime': '2021-03-08 15:25:33',
              'updateTime': '2021-03-08 15:52:43',
              'updateUser': '23',
              'id': '111111111111111111',
              'alphaTwo': 'ZZ',
              'alphaThree': 'ZZZ',
              'countryNumeric': 222,
              'enName': 'ce shis',
              'assignment': 'CESHI',
              'nationalFlag': 'http://dev.img.sugartimeapp.com/other/manager-59edd6ef-f06c-475c-b90c-ab5690810c6e/3b39690be0b2740770f323546bf499aa.jpg',
              'sort': 1000,
              'top': 1,
              'open': 0
            }
          ] }
          ).items,
          'total': 30,
          'size': 30,
          'current': 1,
          'searchCount': true,
          'pages': 3
        }
      }
    }
  },
  // 周星礼物分组列表
  {
    url: '/sys_week_star_group/page',
    type: 'get',
    response: config => {
      return { 'status': 200, 'result': { 'records': [{ 'createTime': '2021-04-20 16:30:58', 'updateTime': '2021-04-20 16:31:02', 'id': '2', 'sysOrigin': '', 'displayNumber': 1, 'sysGiftConfigs': [{ 'createTime': '2021-01-26 12:03:39', 'updateTime': '2021-04-16 18:18:55', 'updateUser': '33', 'id': '1269808328087785475', 'giftPhoto': 'http://app.qiniu.sugartimeapp.com/coffee.png', 'giftName': '咖啡', 'giftSourceUrl': 'http://images.prod.ricomapp.com/COFFEE.svga', 'giftCode': 'COFFEE', 'giftCandy': 100.00, 'giftIntegral': 10.00, 'special': 'ANIMATION,MUSIC,NOTICE,STAR', 'type': 'GOLD', 'sort': 98, 'del': false, 'giftTab': 'NATIONAL_FLAG' }, { 'createTime': '2021-01-26 12:03:39', 'updateTime': '2021-04-16 18:21:08', 'updateUser': '33', 'id': '1269808328087785478', 'giftPhoto': 'http://app.qiniu.sugartimeapp.com/perfume.png', 'giftName': '香水', 'giftSourceUrl': 'http://images.prod.ricomapp.com/PERFUME.svga', 'giftCode': 'PERFUME', 'giftCandy': 5000.00, 'giftIntegral': 500.00, 'special': 'ANIMATION', 'type': 'GOLD', 'sort': 95, 'del': false, 'giftTab': 'CP' }, { 'createTime': '2021-01-26 12:03:39', 'updateTime': '2021-03-03 09:35:54', 'updateUser': '33', 'id': '1269808328087785480', 'giftPhoto': 'http://app.qiniu.sugartimeapp.com/sports_car.png', 'giftName': '跑车', 'giftSourceUrl': 'http://images.prod.ricomapp.com/SPORTS_CAR.svga', 'giftCode': 'SPORTS_CAR', 'giftCandy': 20000.00, 'giftIntegral': 2000.00, 'special': 'ANIMATION', 'type': 'GOLD', 'sort': 93, 'del': false, 'giftTab': 'ORDINARY' }] }], 'total': 1, 'size': 20, 'current': 1, 'searchCount': true, 'pages': 1 }}
    }
  }
]
