
import Mock from 'mockjs'
import { getPage, post, get } from './default_request'
export default [
  // 获取审批信息列表
  {
    url: '/sys/video/call/censor/approval/page',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          records: Mock.mock({
            'items|30': [{
              'createTime': '2020-03-20 17:52:52',
              'updateTime': '2020-03-20 17:52:52',
              'id': '1240939356966854657',
              'userId': '1237225366122110977',
              'userNickname': 'Linda',
              'userSex': 1,
              'userSexName': '男',
              'userAvatar': 'http://dev.qiniu.sugartimeapp.com/*************.jpg',
              'age': 30,
              'accountStatus': 'NORMAL',
              'accountStatusName': '正常',
              'countryName': 'China',
              'labelNames': '色情/性感',
              'scores': '0.55/0.95/0.68/0.85',
              'approvalResult': '',
              'approvalResultName': '',
              'approvalStatus': 0,
              'approvalStatusName': '待审批',
              'censorViolationResources': Mock.mock({ 'items|1-6': [
                {
                  'id': '1240939358531330050',
                  'relatedId': '1240939356966854657',
                  'groupId': '1240939358489387010',
                  'rate': 0.55,
                  'label': 0,
                  'labelName': '色情',
                  'imgUrl': 'http://dev.qiniu.sugartimeapp.com/1240939357029769218_20200320.png',
                  'review': true,
                  'createTime': '2020-03-20 17:52:53',
                  'updateTime': '2020-03-20 17:52:53'
                }
              ] }).items,
              'coverPicture': 'http://dev.qiniu.sugartimeapp.com/1240939357029769218_20200320.png'
            }]
          }).items,
          total: 30,
          size: 30,
          current: 1,
          searchCount: true,
          pages: 3
        }
      }
    }
  },
  // 用户账号违规
  {
    url: '/user/data/violation/expand/approval/account',
    type: 'post',
    response: config => {
      return { 'status': 200 }
    }
  },
  // 获取视频违规记录
  {
    url: '/sys/video/call/censor/approval/page',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          records: Mock.mock({
            'items|30': [{
              'createTime': '2020-03-17 14:36:11',
              'updateTime': '2020-03-18 10:33:16',
              'updateUser': '23',
              'id': '@id',
              'userId': '1218488747672195074',
              'userNickname': 'view~',
              'userSex': 0,
              'userSexName': '女',
              'userAvatar': 'http://dev.qiniu.sugartimeapp.com/*************.jpg',
              'age': 26,
              'accountStatus': 'NORMAL',
              'accountStatusName': '正常',
              'countryName': 'Taiwan, Province of China',
              'labelNames': '男人',
              'scores': '0.62',
              'approvalResult': 'PRON,VIOLENT',
              'approvalResultName': '涉黄/涉爆',
              'approvalStatus': 2,
              'approvalStatusName': '违规',
              'censorViolationResources': Array[1],
              'coverPicture': 'http://dev.qiniu.sugartimeapp.com/1239802694169763842_20200317.png',
              'approvalNickname': 'admin'
            }]
          }).items,
          total: 30,
          size: 30,
          current: 1,
          searchCount: true,
          pages: 3
        }
      }
    }
  },
  // 审批违规审批图片
  {
    url: '/sys/video/call/censor/approval',
    type: 'post',
    response: config => {
      return { 'status': 200 }
    }
  },
  // 用户资料审批
  {
    url: '/user/data/violation/expand',
    type: 'get',
    response: config => {
      return {
        'status': 200,
        'result': [
          {
            'userId': '1210502604691271681',
            'nickname': 'Joyce ',
            'avatar': 'http://dev.qiniu.sugartimeapp.com/425C923B-75AE-4C8E-A632-475F767BBB19.png',
            'userPhotoWalls': Mock.mock({
              'items|1-6': [{
                'createTime': '2020-03-24 16:33:45',
                'updateTime': '2020-03-24 16:33:45',
                'id': '1242368996419805190',
                'userId': '1210502604691271681',
                'resourceUrl': 'http://dev.qiniu.sugartimeapp.com/912008AD-68AF-405F-BC66-62F303242A01.png',
                'sort': 5,
                'violation': 'NORMAL'
              }]
            }).items,
            'audioUrl': '',
            'accountStatus': '正常',
            'identityName': '会员',
            'violationTypes': [
              '待实现'
            ],
            'machineReviewSuggest': {
              'PHOTO_WALL': '正常',
              'AVATAR': ''
            },
            'avatarScore': 0,
            'photoWallScore': 0.58,
            'personReviewSuggest': {
              'SOUND': '',
              'PHOTO_WALL': '',
              'NICKNAME': '',
              'AVATAR': ''
            },
            'violationSize': 0,
            'updateDataTime': '2020-03-24 16:33:46',
            'userDataViolationLatest': Mock.mock({ 'items|1-6': [
              {
                'createTime': '2020-03-24 16:34:13',
                'updateTime': '2020-03-24 16:34:13',
                'id': '1242369115756142593',
                'userId': '1210502604691271681',
                'violationType': 'PHOTO_WALL',
                'suggestion': 'pass',
                'label': 'normal',
                'labelName': '正常',
                'politicianName': '',
                'score': 0,
                'contentId': '1242368996419805185',
                'content': 'http://dev.qiniu.sugartimeapp.com/AF0B5BA6-5468-41B5-B702-84AD1B8E7026.png'
              }
            ] }).items
          }
        ]
      }
    }
  },
  // 用户资料审批
  {
    url: '/user/data/violation/expand/approval',
    type: 'post',
    response: config => {
      return { 'status': 200 }
    }
  },
  // 获取违规照片墙
  {
    url: '/user/data/violation/latest/photo/wall/\.*',
    type: 'get',
    response: config => {
      return { 'status': 200,
        'result':
          Mock.mock({
            'items|6': [{
              'userPhotoWall': {
                'createTime': '2020-03-24 16:33:45',
                'updateTime': '2020-03-24 16:33:45',
                'id': '1242368996419805185',
                'userId': '1210502604691271681',
                'resourceUrl': 'http://dev.qiniu.sugartimeapp.com/AF0B5BA6-5468-41B5-B702-84AD1B8E7026.png',
                'sort': 0,
                'violation': 'NORMAL'
              },
              'userDataViolationLatest': {
                'createTime': '2020-03-24 16:34:13',
                'updateTime': '2020-03-24 16:34:13',
                'id': '1242369115756142593',
                'userId': '1210502604691271681',
                'violationType': 'PHOTO_WALL',
                'suggestion': 'pass',
                'label': 'normal',
                'labelName': '正常',
                'politicianName': '',
                'score': 0,
                'contentId': '1242368996419805185',
                'content': 'http://dev.qiniu.sugartimeapp.com/AF0B5BA6-5468-41B5-B702-84AD1B8E7026.png'
              }
            }]
          }).items
      }
    }
  },
  // 审批违规历史记录列表
  {
    url: '/approval/history/page',
    type: 'get',
    response: config => {
      return {
        'status': 200,
        'result': {
          'records':
            Mock.mock({ 'items|2': [{
              'id': '1255039948302983170',
              'userId': '1254987013716664322',
              'violationTypeName': '照片',
              'labelNames': '',
              'approvalResult': 'NOT_PASS',
              'approvalResultName': '',
              'contentId': '1254987013716664322',
              'content': '',
              'description': '',
              'createTime': '2020-04-28 07:43:36',
              'createUser': '23',
              'approvalNickname': 'admin'
            }]
            }).items,
          'total': 21,
          'size': 20,
          'current': 1,
          'searchCount': true,
          'pages': 2
        }
      }
    }
  },
  // 最近账号审批记录
  {
    url: '/approval/user/account/status/log/latest',
    type: 'get',
    response: config => {
      return {
        'status': 200,
        'result': []
      }
    }
  },
  // 照片墙审批-通过
  {
    url: '/approval/photo/wall/pass',
    type: 'post',
    response: config => {
      return { 'status': 200 }
    }
  },
  // 照片墙审批-不通过
  {
    url: '/approval/photo/wall/not/pass',
    type: 'post',
    response: config => {
      return { 'status': 200 }
    }
  },
  // 举报审批-通过
  {
    url: '/approval/reported/pass',
    type: 'post',
    response: config => {
      return { 'status': 200 }
    }
  },
  // 举报审批-不通过
  {
    url: '/approval/reported/not/pass',
    type: 'post',
    response: config => {
      return { 'status': 200 }
    }
  },
  // 内容审批
  post('/data/approval', ''),
  // 房间资料审批信息分页列表
  getPage('/room/profile-manager/approval/page', {}),
  // 房间资料审批信息分页列表
  getPage('/data/approval/user_profile/page', {
    'userId': '1336596463023435778',
    'userAvatar': 'http://app.qiniu.sugartimeapp.com/3A5C04A2-92AE-4EBE-B551-1BB8D3A62DE0.png',
    'userNickname': ' مي ع ',
    'age': 20,
    'userSex': 1,
    'userSexName': '男',
    'machineLabel': '鉴黄-正常:0.9313728,鉴暴恐-正常:0.7882,敏感人物识别-正常:0',
    'updateTime': '2020-12-10 09:51:06',
    'approvalUserName': '张三丰'
  }),
  // 用户个性签名审批列表
  getPage('/data/approval/user_profile_desc/page', {}),
  // 主题审批分页列表
  getPage('/room/user/theme/page', {
    'id': '324223',
    'userId': '1348513431641104386',
    'themeBack': 'http://dev.qiniu.sugartimeapp.com/19999.png',
    'themeStatus': 'PENDING',
    'themeMoney': 50.00,
    'userBaseInfo': {
      'createTime': '2021-01-11 14:13:50',
      'updateTime': '2021-01-14 11:12:29',
      'id': '1348513431641104386',
      'userAvatar': 'http://dev.img.sugartimeapp.com/avatar/*************.png',
      'originSys': 'TIM_CHAT',
      'userNickname': '我',
      'userSex': 1,
      'userSexName': '男',
      'userType': 0,
      'age': 120,
      'bornYear': 1900,
      'bornMonth': 1,
      'bornDay': 1,
      'userTypeName': '真实',
      'countryName': 'China',
      'countryId': '1231833262813360130',
      'countryCode': 'CN',
      'accountStatus': 'NORMAL',
      'freezingTime': '2021-01-10 14:13:51',
      'del': false,
      'account': '399697'
    }
  }),
  // 主题审批
  post('/room/user/theme/approve', {})
]
