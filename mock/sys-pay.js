// import Mock from 'mockjs'
import { get, getPage, del, post } from './default_request'
export default [
  // 支付渠道-分页
  getPage('/sys-pay-channel/page', {
    'channelCode': '@id',
    'channelName': '@string',
    'channelIcon': 'http://img.sugartimeapp.com/avatar/b6f7c4d0-c4c1-42cd-b7cb-968aa62d03a7.jpg',
    'channelType': 0,
    'createTime': '2021-11-15 12:12:32'
  }),
  // 支付渠道-列表
  get('/sys-pay-channel/list', {
    'channelCode': '@id',
    'channelName': '@string',
    'channelIcon': 'http://img.sugartimeapp.com/avatar/b6f7c4d0-c4c1-42cd-b7cb-968aa62d03a7.jpg',
    'channelType': 0,
    'createTime': '2021-11-15 12:12:32'
  }),
  // 支付厂商-分页
  getPage('/sys-pay-factory/page', {
    'factoryCode': '@id',
    'factoryName': '@string',
    'factoryIcon': 'http://img.sugartimeapp.com/avatar/b6f7c4d0-c4c1-42cd-b7cb-968aa62d03a7.jpg',
    'createTime': '2021-11-15 12:12:32'
  }),
  // 支付厂商-列表
  get('/sys-pay-factory/list', {
    'factoryCode': '@id',
    'factoryName': '@string',
    'factoryIcon': 'http://img.sugartimeapp.com/avatar/b6f7c4d0-c4c1-42cd-b7cb-968aa62d03a7.jpg',
    'createTime': '2021-11-15 12:12:32'
  }),
  // 支付厂商-列表
  get('/sys_pay_application/list', {
    'id': '@id',
    'appName': '@string',
    'appCode': 'TIM_CHAT',
    'androidLink': '',
    'iosLink': ''
  }),
  // 开放支付国家-分页列表
  getPage('/sys-pay-open-country/list/page', {
    'id': '@id',
    'countryId': '@id',
    'currency': '@id',
    'usdExchangeRate': '1',
    'shelf': true,
    'country': {
      'alphaTwo': 'CN',
      'enName': 'asdsa',
      'aliasName': 'aliasName'
    }
  }),
  // 开放支付国家-列表
  get('/sys-pay-open-country/list', {
    'id': '@id',
    'countryId': '@id',
    'currency': '@id',
    'usdExchangeRate': '1',
    'shelf': true,
    'country': {
      'alphaTwo': 'CN',
      'enName': 'asdsa',
      'aliasName': 'aliasName'
    }
  }),
  // 开放支付国家-已关联渠道列表
  get('/sys-pay-channel-factory/country', {
    'associated': {
      'id': '1468036919836319746',
      'payCountryId': '1467683339950387202',
      'channelCode': '123123',
      'shelf': false
    },
    'channel': {
      'id': '1465645906115207170',
      'channelCode': '123123',
      'channelName': '12312234234',
      'channelIcon': 'http://dev.img.sugartimeapp.com/svgasource/manager-d27b6be6-d29b-4fc5-8851-aa1ffb83e5e2.png',
      'channelType': 'ELECTRONIC_WALLET',
      'createTime': '2021-11-30 19:36:29'
    },
    'countryChannels': [
      {
        'payFactory': {
          'id': '1467745759100231682',
          'factoryCode': 'test2',
          'factoryName': '测试2',
          'factoryIcon': 'http://dev.img.sugartimeapp.com/svgasource/manager-bf5c7314-3e56-4380-89c7-61a2188ea16d.png',
          'createTime': '2021-12-06 14:40:33'
        },
        'countryChannelDetails': {
          'payCountryId': '1467683339950387202',
          'countryChannelId': '1468036919836319746',
          'channelCode': '123123',
          'factoryCode': 'test2',
          'shelf': false
        }
      }
    ]
  }),
  // 根据限额获取支付的付款渠道-列表
  get('/sys-pay-channel-factory/support_amount', {
    'computedAmount': 123,
    'amountUsd': 12,
    'channels': [{
      'associated': {
        'id': '1468036919836319746',
        'payCountryId': '1467683339950387202',
        'channelCode': '123123',
        'shelf': false
      },
      'channel': {
        'id': '1465645906115207170',
        'channelCode': '123123',
        'channelName': '12312234234',
        'channelIcon': 'http://dev.img.sugartimeapp.com/svgasource/manager-d27b6be6-d29b-4fc5-8851-aa1ffb83e5e2.png',
        'channelType': 'ELECTRONIC_WALLET',
        'createTime': '2021-11-30 19:36:29'
      },
      'countryChannels': [
        {
          'payFactory': {
            'id': '1467745759100231682',
            'factoryCode': 'test2',
            'factoryName': '测试2',
            'factoryIcon': 'http://dev.img.sugartimeapp.com/svgasource/manager-bf5c7314-3e56-4380-89c7-61a2188ea16d.png',
            'createTime': '2021-12-06 14:40:33'
          },
          'countryChannelDetails': {
            'payCountryId': '1467683339950387202',
            'countryChannelId': '1468036919836319746',
            'channelCode': '123123',
            'factoryCode': 'test2',
            'shelf': false
          }
        }
      ]
    }]
  }),
  // 渠道关联厂商列表-列表
  get('/sys-pay-channel-factory/associate', {
    'channel': {
      'channelCode': '@id',
      'channelName': '@string',
      'channelIcon': 'http://img.sugartimeapp.com/avatar/b6f7c4d0-c4c1-42cd-b7cb-968aa62d03a7.jpg',
      'channelType': 0,
      'createTime': '2021-11-15 12:12:32'
    },
    'payFactorys': {
      'factoryCode': '@id',
      'factoryName': '@string',
      'factoryIcon': 'http://img.sugartimeapp.com/avatar/b6f7c4d0-c4c1-42cd-b7cb-968aa62d03a7.jpg',
      'createTime': '2021-11-15 12:12:32'
    }
  })
]
