/**
 * 照片墙
 */
import Mock from 'mockjs'

export default [
  // 照片墙审核
  {
    url: '/user/photo/wall/page/all',
    type: 'get',
    response: config => {
      return {
        'status': 200,
        'result': {
          'records': Mock.mock({ 'items|30': [
            {
              'createTime': '2020-04-27 19:29:50',
              'updateTime': '2020-04-27 19:29:51',
              'id': '1254734492813185027',
              'userId': '1254677709117698049',
              'resourceUrl': 'http://dev.qiniu.sugartimeapp.com/1587974769775.jpg',
              'width': 360,
              'height': 780,
              'sort': 2,
              'violation': 'SUSPECTED',
              'labelNames': '鉴黄-正常:0.92142,鉴暴恐-特殊字符文字:0.42707,敏感人物识别-正常:0',
              'userNickname': '123456789101112',
              'age': 27,
              'userSexName': '男'
            }
          ] }
          ).items,
          'total': 30,
          'size': 30,
          'current': 1,
          'searchCount': true,
          'pages': 3
        }
      }
    }
  }
]
