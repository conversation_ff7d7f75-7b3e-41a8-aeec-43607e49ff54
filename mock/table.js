// import Mock from 'mockjs'
import { get, getPage, del, post } from './default_request'
export default [
  // 意见反馈
  getPage('/sys/feedback/page', {
    'id': '@id',
    'userId': '1198154451673640962',
    'content': 'ghjjjjhgg',
    'imageUrls': 'DA66D98A-1A0F-4FA3-A86D-DE2DC9C95DB9.png,023CAF95-58F5-4942-9606-A177B175CF2D.png',
    'approvalStatus': 0,
    'approvalStatusName': '未处理',
    'approvalRemarks': '132132132',
    'createTime': '2019-11-29 03:58:10',
    'updateTime': '2020-01-09 03:54:05',
    'updateUser': '23',
    'lastUpdateUser': {
      'id': 23,
      'loginName': 'admin',
      'nickname': 'admin',
      'ip': '127.0.0.1',
      'status': 0,
      'createUid': 1,
      'createTime': '2019-11-13 18:28:38',
      'updateTime': '2019-12-15 18:49:53'
    }
  }),
  // 举报管理
  getPage('/sys/reported/user/page', {
    'createTime': '2020-03-31 17:34:57',
    'updateTime': '2020-03-31 17:34:57',
    'id': '@id',
    'originName': '视频',
    'censorRemarks': '',
    'approvalStatusName': '系统已审批',
    'reportUser': {
      'createTime': '2020-03-31 10:51:08',
      'updateTime': '2020-03-31 17:28:50',
      'id': '1244819490287362050',
      'userAvatar': 'http://dev.qiniu.sugartimeapp.com/F83FAD34-96AA-467E-8581-2253005EB8D7.png',
      'userNickname': '松毛',
      'userSex': 1,
      'userSexName': '男',
      'userType': 0,
      'userTypeName': '真实',
      'countryName': 'China',
      'del': false
    },
    'reportedUser': {
      'createTime': '2019-12-27 10:47:25',
      'updateTime': '2020-02-24 17:01:14',
      'id': '1210169958483365892',
      'userAvatar': 'http://app.qiniu.sugartimeapp.com/1210169958483365893_20191226.jpg',
      'userNickname': 'Renee Keppel',
      'userSex': 0,
      'userSexName': '女',
      'userType': 1,
      'userTypeName': '马甲',
      'countryName': '',
      'del': false
    },
    'reportType': 0,
    'reportTypeName': '非法信息',
    'reportedContent': '',
    'approvalStatus': false
  }),
  // 参数配置
  getPage('/sys/enum/config/page', {
    'createTime': '2020-10-13 16:03:51',
    'updateTime': '2020-10-19 12:24:53',
    'createUser': '23',
    'updateUser': '26',
    'id': '1315926207577972737',
    'name': 'SHORT_VIDEO_WATCH_AD_INTEGRAL',
    'val': '0.1',
    'title': '短视频广告积分',
    'groupName': 'OTHER',
    'description': '短视频观看广告获得积分',
    'dataType': 'double',
    'sort': 1,
    'inoperable': false,
    'returnApp': false,
    'platform': 'ALL'
  }),
  // 获取系统枚举分组列表
  get('/sys/enum/config/list/\.', [{ 'createTime': '2020-07-28 19:01:41', 'updateTime': '2020-07-31 14:21:55', 'createUser': '23', 'updateUser': '23', 'id': '1288067094097670145', 'name': 'NORMAL_MATCH_ALL_AI_FEMALE_PROBABILITY', 'val': 'true', 'groupName': 'VIDEO_MATCH', 'title': '开关bool', 'description': '开关bool', 'dataType': 'bool', 'sort': 0, 'inoperable': false }, { 'createTime': '2020-07-29 10:33:24', 'updateTime': '2020-07-31 12:29:07', 'createUser': '23', 'updateUser': '23', 'id': '1288301568651632642', 'name': 'NORMAL_MATCH_ALL_AI_PROBABILITY', 'val': '55', 'groupName': 'VIDEO_MATCH', 'description': '视频匹配所有(正常用户)-AI概率', 'dataType': 'int', 'sort': 0, 'inoperable': false }, { 'createTime': '2020-07-28 19:01:18', 'updateTime': '2020-07-31 14:22:01', 'createUser': '23', 'updateUser': '23', 'id': '1288067001030258689', 'name': 'T0_MATCH_ALL_AI_FEMALE_PROBABILITY', 'val': '70', 'groupName': 'VIDEO_MATCH', 'description': 'T0匹配所有-AI女性概率', 'dataType': 'int', 'sort': 0, 'inoperable': false }, { 'createTime': '2020-07-28 19:00:49', 'updateTime': '2020-07-31 14:22:06', 'createUser': '23', 'updateUser': '23', 'id': '1288066876207771649', 'name': 'T0_MATCH_ALL_AI_PROBABILITY', 'val': '70', 'groupName': 'VIDEO_MATCH', 'description': 'T0匹配所有-AI概率', 'dataType': 'int', 'sort': 0, 'inoperable': false }, { 'createTime': '2020-07-28 19:02:09', 'updateTime': '2020-07-31 14:21:48', 'createUser': '23', 'updateUser': '23', 'id': '1288067215082369025', 'name': 'VIDEO_MATCH_ALL_FEMALE_PROBABILITY', 'val': '70', 'groupName': 'VIDEO_MATCH', 'description': '视频匹配所有(正常用户)-女性概率', 'dataType': 'int', 'sort': 0, 'inoperable': false }]),
  // 保存配置序号
  post('/sys/enum/config/sort', {}),
  // 获取app崩溃版本信息
  post('/sys/breakdown/info/flow', []),
  // 清空指定平台崩溃日志
  del('/sys/breakdown/info/\./delete'),
  // 清空指定平台日期崩溃日志
  del('/sys/breakdown/info/\./\./\./delete'),
  // 站内评分
  getPage('/sys/app/score/page', { 'id': '@id', 'userId': '@id', 'platform': 'iOS', 'score': 1, 'createTime': '2020-11-24 14:47:00', 'userBaseInfo': {}})
]
