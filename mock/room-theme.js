import Mock from 'mockjs'

export default [
  // 房间主题信息
  {
    url: '/room/theme/list',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          records: Mock.mock({ 'items|30': [
            {
              'createTime': '2020-12-09 17:15:00',
              'updateTime': '2020-12-15 17:56:50',
              'updateUser': '23',
              'id': '4232232422',
              'themeCode': 'DEFAULT_THEME',
              'themeBack': 'http://dev.qiniu.sugartimeapp.com/19999.png',
              'showcase': true,
              'themeMoney': 0.00
            }
          ] }
          ).items,
          'total': 30,
          'size': 30,
          'current': 1,
          'searchCount': true,
          'pages': 3
        }
      }
    }
  },
  // 修改房间主题信息
  {
    url: '/room/theme',
    type: 'put',
    response: () => {
      return {
        'status': 200
      }
    }
  },
  // 删除房间主题
  {
    url: '/room/theme/\/*',
    type: 'delete',
    response: () => {
      return {
        'status': 200
      }
    }
  }
]
