import Mock from 'mockjs'

export default [
  // 活跃用户分布国家
  {
    url: '/datav/active_user_country_code',
    type: 'get',
    response: config => {
      return { 'status': 200 }
    }
  },
  // 在线用户总数
  {
    url: '/datav/online/user/count',
    type: 'get',
    response: config => {
      return { 'status': 200, 'result': 100 }
    }
  },
  // 在线房间总数
  {
    url: '/datav/online/room/count',
    type: 'get',
    response: config => {
      return { 'status': 200, 'result': 100 }
    }
  }
]
