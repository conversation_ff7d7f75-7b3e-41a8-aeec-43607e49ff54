
export default [
  // 团列表
  {
    url: '/team/list',
    type: 'get',
    response: config => {
      return { 'status': 200, 'result': [{ 'teamProfile': { 'id': '1600424626181439489', 'region': 'shanghai', 'account': '', 'sysOrigin': 'ASWAT', 'nickname': 'hhhiii8', 'ownUserId': '1600013750836187138', 'remarks': [], 'status': 'AVAILABLE', 'setting': { 'maxMember': 1000, 'billType': 'MONTH', 'billCycle': 1 }, 'counter': { 'memberQuantity': '1', 'adminQuantity': '0' }, 'contacts': [{ 'type': 'FACEBOOK', 'contact': '12312', 'remarks': '12312' }], 'createTime': '2022-12-07 17:38:59', 'updateTime': '2022-12-07 17:38:59' }, 'ownUserProfile': { 'id': '1600013750836187138', 'account': '42155', 'userAvatar': 'http://dev.img.sugartimeapp.com/avatar/151e099f-8779-4219-9f33-cafe8db2cf0e.png', 'userNickname': 'hhhiii8', 'userSex': 1, 'age': 27, 'accountStatus': 'NORMAL', 'freezingTime': '2022-12-05 14:26:20', 'countryName': 'United States', 'countryCode': 'en', 'originSys': 'ASWAT', 'sysOriginChild': 'ASWAT', 'del': false, 'bornYear': 1995, 'bornMonth': 1, 'bornDay': 1, 'createTime': '2022-12-06 14:26:19', 'actualAccountStatus': 'NORMAL', 'actualAccount': '42155', 'accountStatusName': '正常', 'userSexName': '男' }}, { 'teamProfile': { 'id': '1600416069822275585', 'region': 'shanghai', 'account': '1234321', 'sysOrigin': 'ASWAT', 'nickname': 'kaka', 'ownUserId': '1600059496931332097', 'remarks': [{ 'remarkId': '1600416069860024321', 'remark': 'test', 'createTime': '2022-12-07 17:05:00' }], 'status': 'AVAILABLE', 'counter': { 'memberQuantity': '1', 'adminQuantity': '0' }, 'contacts': [], 'createTime': '2022-12-07 17:04:59', 'updateTime': '2022-12-07 17:04:59' }, 'ownUserProfile': { 'id': '1600059496931332097', 'account': '42156', 'userAvatar': '', 'userNickname': 'kaka', 'userSex': 1, 'age': 27, 'accountStatus': 'NORMAL', 'freezingTime': '2022-12-05 17:28:06', 'countryName': 'United States', 'countryCode': 'en', 'originSys': 'ASWAT', 'sysOriginChild': 'ASWAT', 'del': false, 'bornYear': 1995, 'bornMonth': 1, 'bornDay': 1, 'createTime': '2022-12-06 17:28:06', 'actualAccountStatus': 'NORMAL', 'actualAccount': '42156', 'accountStatusName': '正常', 'userSexName': '男' }}] }
    }
  },
  //  成员列表
  {
    url: '/team/member/list',
    type: 'get',
    response: config => {
      return { 'status': 200, 'result': [] }
    }
  },
  //  团队账单列表
  {
    url: '/team/bill/list',
    type: 'get',
    response: config => {
      return { 'status': 200, 'result': [] }
    }
  },
  //  系统区域政策
  {
    url: '/team/policy/manager/sys-region',
    type: 'get',
    response: config => {
      return { 'status': 200, 'result': [] }
    }
  },
  //  团队通过银行卡
  {
    url: '/team/bank-card/pass-list',
    type: 'get',
    response: config => {
      return { 'status': 200, 'result': [] }
    }
  }
]
