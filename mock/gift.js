import Mock from 'mockjs'

export default [
  // 获取指定平台礼物
  {
    url: '/sys/gift/config/sys_origin/\.',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: Mock.mock({ 'items|30': [
          {
            'createTime': '2020-06-08 11:56:53',
            'updateTime': '2020-06-08 11:56:53',
            'id': '@id',
            'giftPhoto': 'http://dev.qiniu.sugartimeapp.com/sports_car.png',
            'giftSourceUrl': '',
            'giftName': '跑车',
            'giftCode': 'SPORTS_CAR',
            'giftCandy': 100000.00,
            'giftIntegral': 100000.00,
            'sort': 0,
            'del': false
          }
        ] }
        ).items
      }
    }
  },
  // 礼物信息
  {
    url: '/sys/gift/config',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          records: Mock.mock({ 'items|30': [
            {
              'createTime': '2020-06-08 11:56:53',
              'updateTime': '2020-06-08 11:56:53',
              'id': '1269808328087785480',
              'giftPhoto': 'http://dev.qiniu.sugartimeapp.com/sports_car.png',
              'giftSourceUrl': '',
              'giftName': '跑车',
              'giftCode': 'SPORTS_CAR',
              'giftCandy': 100000.00,
              'giftIntegral': 100000.00,
              'sort': 0,
              'del': false
            }
          ] }
          ).items,
          'total': 30,
          'size': 30,
          'current': 1,
          'searchCount': true,
          'pages': 3
        }
      }
    }
  },
  // 修改礼物信息
  {
    url: '/sys/gift/config',
    type: 'put',
    response: () => {
      return {
        'status': 200
      }
    }
  },
  // 新增礼物信息
  {
    url: '/sys/gift/config',
    type: 'post',
    response: () => {
      return {
        'status': 200
      }
    }
  },
  // 礼物赠送记录列表
  {
    url: '/running-water-log/gift-give',
    type: 'get',
    response: config => {
      return { 'status': 200, 'result': [{ 'id': '1551860122988666881', 'trackId': '1551860117456379905', 'originId': '1407188479826173954', 'sysOrigin': 'ASWAT', 'requestPlatform': 'Android', 'userId': '1403635539530207234', 'anchor': true, 'giftId': '1524225254146154497', 'giftCover': 'http://dev.img.sugartimeapp.com/gifts/manager-abc56280-5d5f-4df9-9728-93394e5e676b.png', 'giftType': 'GOLD', 'giftValue': { 'currencyType': 'GOLD', 'giftType': 'ORDINARY', 'unitPrice': 1999.00, 'quantity': 1, 'userSize': 1, 'giftValue': 1999.00, 'actualAmount': 1999.00, 'percentage': 2, 'selfPercentage': 1 }, 'acceptUsers': [{ 'acceptUserId': '1403635539530207234', 'anchor': true, 'receiptId': '0', 'acceptAmount': 199, 'targetAmount': 1999.00, 'userProfile': { 'id': '1403635539530207234', 'account': '41640', 'userAvatar': 'http://dev.img.sugartimeapp.com/avatar/0aba61c0-bdfa-49b0-8b27-564a4cbe6ec9.jpg', 'userNickname': '推广的', 'userSex': 0, 'age': 66, 'accountStatus': 'NORMAL', 'freezingTime': '2021-08-20 15:44:59', 'countryId': '1231833262813360130', 'countryName': 'China', 'countryCode': 'CN', 'originSys': 'ASWAT', 'sysOriginChild': '', 'del': false, 'bornYear': 1955, 'bornMonth': 1, 'bornDay': 1, 'createTime': '2021-06-12 16:49:25', 'userSexName': '女', 'accountStatusName': '正常', 'actualAccountStatus': 'NORMAL', 'actualAccount': '41640' }}, { 'acceptUserId': '1406947844938510338', 'anchor': true, 'receiptId': '0', 'acceptAmount': 399, 'targetAmount': 1999.00, 'userProfile': { 'id': '1406947844938510338', 'account': '41680', 'userAvatar': 'http://dev.img.sugartimeapp.com/avatar/8f36b375-c943-4562-ae31-2d061ec3a9c4.png', 'userNickname': 'OnePlus ', 'userSex': 1, 'age': 122, 'accountStatus': 'NORMAL', 'freezingTime': '2021-06-20 20:11:21', 'countryId': '1231833262813360130', 'countryName': 'China', 'countryCode': 'CN', 'originSys': 'ASWAT', 'sysOriginChild': '', 'del': false, 'bornYear': 1900, 'bornMonth': 1, 'bornDay': 24, 'createTime': '2021-06-21 20:11:20', 'userSexName': '男', 'accountStatusName': '正常', 'actualAccountStatus': 'NORMAL', 'actualAccount': '41680' }}], 'logs': [{ 'content': '流水入库', 'createTime': '2022-07-26 17:21:00' }], 'roomProfile': { 'id': '1407188479826173954', 'roomAccount': '41680', 'userId': '1406947844938510338', 'roomCover': 'http://dev.img.sugartimeapp.com/avatar/4d73749d-209e-47c7-97db-dc0e99739527.jpg', 'roomName': 'oneplus', 'roomDesc': '欢迎大家！ 让我们一起聊天，一起玩吧。', 'event': 'AVAILABLE', 'sysOrigin': 'ASWAT', 'countryCode': 'CN', 'countryName': 'China', 'del': false, 'createTime': '2021-06-22 12:07:32', 'updateTime': '2022-07-26 17:02:22' }, 'userProfile': { 'id': '1403635539530207234', 'account': '41640', 'userAvatar': 'http://dev.img.sugartimeapp.com/avatar/0aba61c0-bdfa-49b0-8b27-564a4cbe6ec9.jpg', 'userNickname': '推广的', 'userSex': 0, 'age': 66, 'accountStatus': 'NORMAL', 'freezingTime': '2021-08-20 15:44:59', 'countryId': '1231833262813360130', 'countryName': 'China', 'countryCode': 'CN', 'originSys': 'ASWAT', 'sysOriginChild': '', 'del': false, 'bornYear': 1955, 'bornMonth': 1, 'bornDay': 1, 'createTime': '2021-06-12 16:49:25', 'userSexName': '女', 'accountStatusName': '正常', 'actualAccountStatus': 'NORMAL', 'actualAccount': '41640' }, 'createTime': '2022-07-26 17:20:58', 'updateTime': '2022-07-26 17:20:58', 'expiredTime': '2022-08-10 17:21:00' }] }
    }
  }
]
