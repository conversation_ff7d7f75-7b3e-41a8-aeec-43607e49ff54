import Mock from 'mockjs'

export default [
  // 系统字典信息
  {
    url: '/sys/dictionary/page',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          records: Mock.mock({ 'items|30': [
            {
              'createTime': '2020-12-04 10:25:48',
              'updateTime': '2020-12-04 10:25:48',
              'id': '6666',
              'type': 'ROOM_BLACK_TIME',
              'dicCode': 'ONE_MONTH',
              'dicParentCode': '',
              'dicVal': '43200',
              'dicDesc': '1 month',
              'typeName': '房间黑名单过期时长'
            }
          ] }
          ).items,
          'total': 30,
          'size': 30,
          'current': 1,
          'searchCount': true,
          'pages': 3
        }
      }
    }
  },
  // 修改字典信息
  {
    url: '/sys/dictionary',
    type: 'put',
    response: () => {
      return {
        'status': 200
      }
    }
  },
  // 增加字典信息
  {
    url: '/sys/dictionary',
    type: 'post',
    response: () => {
      return {
        'status': 200
      }
    }
  },
  // 删除字典
  {
    url: '/sys/dictionary/\/*',
    type: 'delete',
    response: () => {
      return {
        'status': 200
      }
    }
  }
]
