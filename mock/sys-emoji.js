import Mock from 'mockjs'

export default [
  // 表情列表-分组分页列表
  {
    url: '/emoji/config/group/page',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          records: Mock.mock({ 'items|30': [
            {
              'id': '1452527059287973890',
              'sysOrigin': 'TIM_CHAT',
              'groupCode': '123123',
              'groupName': '111',
              'cover': 'http://dev.img.sugartimeapp.com/svga_cover/manager-9d4d8fe5-4ae6-4279-8062-79a4d67b08fa.png',
              'shelfStatus': 1,
              'sort': 123,
              'createTime': '2021-10-25 14:46:52'
            }
          ] }
          ).items,
          'total': 30,
          'size': 30,
          'current': 1,
          'searchCount': true,
          'pages': 3
        }
      }
    }
  },
  // 表情列表-根据系统查找
  {
    url: '/emoji/config/group/sys_origin_list',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: Mock.mock({ 'items|30': [
          {
            'id': '1452527059287973890',
            'sysOrigin': 'TIM_CHAT',
            'groupCode': '123123',
            'groupName': '111',
            'cover': 'http://dev.img.sugartimeapp.com/svga_cover/manager-9d4d8fe5-4ae6-4279-8062-79a4d67b08fa.png',
            'shelfStatus': 1,
            'sort': 123,
            'createTime': '2021-10-25 14:46:52'
          }
        ] }
        ).items
      }
    }
  }
]
