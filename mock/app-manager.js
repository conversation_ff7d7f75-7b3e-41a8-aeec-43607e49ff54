import Mock from 'mockjs'

export default [
  // 设置buildVersion
  {
    url: '/sys/version/manage/add/build-version',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {}
      }
    }
  },
  // 获取当前设置buildVersion
  {
    url: '/sys/version/manage/get/build-version',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: 0
      }
    }
  },
  // app版本信息列表
  {
    url: '/sys/version/manage',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          records: Mock.mock({
            'items|30': [{
              'id': '@id',
              'clientType': 'iOS',
              'version': '1.5.7',
              'forceUpdate': 'true',
              'updateDescribe': '强制更新',
              'apkSize': '123M',
              'review': 'true',
              'del': 0,
              'downloadUrl': 'xxx',
              'publishTime': '2020-03-23 16:53:49'
            }]
          }).items, total: 0, size: 30, current: 1, searchCount: true, pages: 0
        }
      }
    }
  },
  // 添加版本信息
  {
    url: '/sys/version/manage',
    type: 'post',
    response: config => {
      return {
        status: 200,
        result: {}
      }
    }
  },
  // 修改版本信息
  {
    url: '/sys/version/manage/update',
    type: 'put',
    response: config => {
      return {
        status: 200,
        result: {}
      }
    }
  },
  // 删除版本信息
  {
    url: '/sys/version/manage/\.',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {}
      }
    }
  }
]
