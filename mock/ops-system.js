import Mock from 'mockjs'

export default [
  // user login
  {
    url: '/account/login',
    type: 'post',
    response: config => {
      return {
        status: true,
        errorCode: 0,
        body: {
          'uid': 1,
          'token': 'F6C849D9A7B2CBF5F7BEED86B0E8F094.djElM0EyMyUzQU9QUyUzQTE2ODQ1MDA3NzUzNzglM0ExNjg0NDkzNTc1Mzc4'
        }
      }
    }
  },

  // user logout
  {
    url: '/logout',
    type: 'post',
    response: _ => {
      return {
        status: true,
        errorCode: 0
      }
    }
  },

  // get user info
  {
    url: '/account/info',
    type: 'get',
    response: config => {
      return {
        status: true,
        errorCode: 0,
        body: {
          loginName: 'admin',
          nickname: 'admin'
        }
      }
    }
  },

  // 账号菜单
  {
    url: '/account/menus',
    type: 'get',
    response: config => {
      return { 'status': true, 'body': [] }
    }
  },

  // 权限按钮
  {
    url: '/account/buttons/aliases',
    type: 'get',
    response: config => {
      return { 'status': true, 'body': ['sys:user:add', 'user:table:edit:account:handler', 'user:table:edit', 'user:table:query:gold', 'user:table:query:list', 'dashboard:details', 'sys:user:edit', 'user:table:edit:deduction:gold', 'user:table:query:violation', 'user:table:query:account:handler', 'user:table:edit:reward:gold', 'user:table:query:details', 'sys:user:resetpwd'] }
    }
  },

  // 获取用户列表
  {
    url: '/users',
    type: 'get',
    response: config => {
      return {
        'status': true,
        'errorCode': 0,
        'body': Mock.mock({
          'items|30': [{
            'id': '@id',
            'loginName': '@sentence(5, 10)',
            'nickname': '@sentence(3, 8)',
            'email': '',
            'phone': '',
            'ip': '0:0:0:0:0:0:0:1',
            'status|1': ['published', 'draft', 'deleted'],
            createTime: '@datetime',
            updateTime: '@datetime',
            'userRoles': [
              {
                'roleId': '@id',
                'uid': '@id',
                'roleName': '管理员',
                'remark': '管理员'
              }
            ]
          }]
        }).items
      }
    }
  },
  // 角色列表
  {
    url: '/roles',
    type: 'get',
    response: config => {
      return {
        'status': true,
        'errorCode': 0,
        'body': {
          'records': Mock.mock({
            'items|30': [{
              'id': '@id',
              'roleName': '@sentence(5, 10)',
              'remark': '@sentence(5, 10)',
              'menuIds': [],
              'createTime': '@datetime',
              'updateTime': '@datetime'
            }]
          }).items,
          'total': 20,
          'size': 20,
          'current': 1
        }
      }
    }
  },
  // 所有菜单
  {
    url: '/menus',
    type: 'get',
    response: config => {
      return {
        'status': true,
        'errorCode': 0,
        'body': Mock.mock({
          'items|30': [{
            'id': '@id',
            'parentId': 1,
            'menuName': '@sentence(5, 10)',
            'path': '@sentence(5, 10)',
            'router': '@sentence(5, 10)',
            'menuType': 2,
            'icon': 'sys_manager_1',
            'alias': '@sentence(5, 10)',
            'status': 0,
            'sort': 99999
          }]
        }).items
      }
    }
  },
  // 资源列表
  {
    url: '/resources/list',
    type: 'get',
    response: config => {
      return {
        'status': true,
        'errorCode': 0,
        'body': Mock.mock({
          'id': '@id',
          'resourceName': '@sentence(5, 10)',
          'mapping': '/family/del/{familyId}',
          'method': 'GET',
          'authType': 1,
          'perm': 'GET:/family/del/{familyId}',
          'updateTime': [
            2023,
            5,
            19,
            8,
            58,
            47
          ]
        }).items
      }
    }
  },
  // 资源分页列表
  {
    url: '/resources',
    type: 'get',
    response: config => {
      return {
        'status': true,
        'errorCode': 0,
        'body': {
          'records': Mock.mock({
            'id': '@id',
            'resourceName': '@sentence(5, 10)',
            'mapping': '/family/del/{familyId}',
            'method': 'GET',
            'authType': 1,
            'perm': 'GET:/family/del/{familyId}',
            'updateTime': [
              2023,
              5,
              19,
              8,
              58,
              47
            ]
          }).items,
          'total': 20,
          'size': 20,
          'current': 1
        }
      }
    }
  }
]
