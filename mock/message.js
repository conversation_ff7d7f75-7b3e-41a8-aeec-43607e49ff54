/**
 * 消息相关
 */
import Mock from 'mockjs'

export default [
  // 新建push
  {
    url: '/push',
    type: 'post',
    response: config => {
      return {
        status: 200,
        result: { 'status': 200 }
      }
    }
  },
  // push文案库
  {
    url: '/message/copywriting/page',
    type: 'get',
    response: config => {
      return {
        'status': 200,
        'result': {
          'records': Mock.mock({ 'items|30': [
            {
              'createTime': '2020-03-11 20:30:19',
              'updateTime': '2020-03-11 20:30:19',
              'createUser': '23',
              'id': '1237717479216648193',
              'pushType': 'STRATEGY',
              'pushTypeName': '策略类',
              'businessScene': 'CARD_LIKE',
              'businessSceneName': '谁右滑了你',
              'pushUserType': 'BUY_CANDY',
              'pushUserTypeName': '购买过糖果',
              'sysPushTextContents': [
                {
                  'createTime': '2020-03-11 20:30:17',
                  'updateTime': '2020-03-11 20:30:17',
                  'updateUser': '23',
                  'id': '1237717479539609602',
                  'textTypeId': '1237717479216648193',
                  'language': 'en',
                  'languageName': '英语',
                  'title': 'English title',
                  'content': 'content',
                  'sort': 0
                },
                {
                  'createTime': '2020-03-11 20:30:17',
                  'updateTime': '2020-03-11 20:30:17',
                  'updateUser': '23',
                  'id': '1237717479539609603',
                  'textTypeId': '1237717479216648193',
                  'language': 'zh_CN',
                  'languageName': '中文',
                  'title': '中文标题',
                  'content': '内容',
                  'sort': 1
                }
              ],
              'user': {
                'id': 23,
                'loginName': 'admin',
                'password': '$apr1$admin$3NsFmc5jCaK5R97pgHRX21',
                'nickname': 'admin',
                'ip': '127.0.0.1',
                'status': 0,
                'createUid': 1,
                'createTime': '2019-11-13 18:28:38',
                'updateTime': '2019-12-15 18:49:53'
              }
            }
          ] }
          ).items,
          'total': 30,
          'size': 30,
          'current': 1,
          'searchCount': true,
          'pages': 3
        }
      }
    }
  },
  // push 日志
  {
    url: '/push/page',
    type: 'get',
    response: config => {
      return {
        'status': 200,
        'result': {
          'records': Mock.mock({ 'items|30': [
            {
              'id': '@id',
              'title': 'qe1',
              'content': '12312',
              'platform': 'Android',
              'pushType': '通知类',
              'pushBusinessScene': '版本更新',
              'pushUserType': '会员',
              'fixedUserIds': '12',
              'link': '123',
              'pushStatus': '成功',
              'sendUserName': 'admin',
              'createTime': '2020-03-30 15:32:32'
            }
          ] }
          ).items,
          'total': 30,
          'size': 30,
          'current': 1,
          'searchCount': true,
          'pages': 3
        }
      }
    }
  },
  // 添加push文案库
  {
    url: '/message/copywriting',
    type: 'post',
    response: config => {
      return {
        status: 200
      }
    }
  },
  // 删除push文案
  {
    url: '/message/copywriting/\.',
    type: 'delete',
    response: config => {
      return {
        status: 200
      }
    }
  },
  // 已录入文案内容
  {
    url: '/sys/push/text/content/\.*',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: Mock.mock({ 'items|30': [
          {
            'createTime': '2020-03-11 20:30:17',
            'updateTime': '2020-03-11 20:30:17',
            'updateUser': '23',
            'id': '@id',
            'textTypeId': '1237717479216648193',
            'language': 'en',
            'languageName': '英语',
            'title': '123',
            'content': '123',
            'sort': 0
          }
        ] }).items
      }
    }
  }
]
