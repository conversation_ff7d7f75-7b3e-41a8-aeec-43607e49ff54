import Mock from 'mockjs'

export default [
  {
    url: '/account/buttons/aliases',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: []
      }
    }
  },
  // user login
  {
    url: '/account/token',
    type: 'post',
    response: config => {
      return {
        status: 200,
        result: {
          token: 'admin-token',
          uid: 1
        }
      }
    }
  },

  // get user info
  {
    url: '/account/info',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          loginName: 'admin',
          nickname: 'admin'
        }
      }
    }
  },

  // user logout
  {
    url: '/mock/user/logout',
    type: 'post',
    response: _ => {
      return {
        code: 200,
        data: 'success'
      }
    }
  },
  // 账号菜单
  {
    url: '/account/menus',
    type: 'get',
    response: config => {
      return { 'status': 200, 'result': [] }
    }
  },
  // 用户道具流水
  {
    url: '/running/water/user/props/page',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          records: Mock.mock({
            'items|30': [{
              'id': '1403253727628857346',
              'propsCandy': 0,
              'propsId': '1402547029989617665',
              'propsCode': 'Aswat-vip3-ride',
              'propsName': 'Aswat-vip3-ride',
              'propsCover': 'http://dev.img.sugartimeapp.com/svga_cover/manager-4aeab7ae-7c0d-4406-bfd3-e50e058b8444.png',
              'buyerId': '1402831237773488130',
              'buyerNickname': 'xiaomi',
              'receiverId': '1402831237773488130',
              'receiverNickname': 'xiaomi',
              'createTime': '2021-06-11 15:32:14'
            },
            {
              'id': '1403253727314284545',
              'propsCandy': 14000,
              'propsId': '1402550414344155137',
              'propsCode': 'Aswat-vip3-badge',
              'propsName': 'MARQUIS',
              'propsCover': 'http://dev.img.sugartimeapp.com/svga_cover/manager-76e13b50-bff3-40ff-acb1-2cd1913f0336.png',
              'buyerId': '1392405692929675266',
              'buyerNickname': 'Samsung ',
              'receiverId': '1402831237773488130',
              'receiverNickname': 'xiaomi',
              'createTime': '2021-06-11 15:32:14'
            },
            {
              'id': '1403226439239589889',
              'propsCandy': 7,
              'propsId': '1377919753268461569',
              'propsCode': '1',
              'propsName': 'VISCOUNT',
              'propsCover': 'http://img.sugartimeapp.com/nobleVip/manager-3ed6ca82-6240-4fff-95c8-7ab29c875b8f.png',
              'buyerId': '1396765544959414273',
              'buyerNickname': 'Donkor',
              'receiverId': '1398094185093890049',
              'receiverNickname': '李易峰',
              'createTime': '2021-06-11 13:43:48'
            },
            {
              'id': '1402953820829376514',
              'propsCandy': 0,
              'propsId': '1357931892593101005',
              'propsCode': '1012',
              'propsName': '凤凰',
              'propsCover': 'http://img.sugartimeapp.com/theme/cars_1012.png',
              'buyerId': '1376452751777554434',
              'buyerNickname': 'st_1',
              'receiverId': '1376452751777554434',
              'receiverNickname': 'st_1',
              'createTime': '2021-06-10 19:40:31'
            }]
          }).items,
          total: 30,
          size: 30,
          current: 1,
          searchCount: true,
          pages: 3
        }
      }
    }
  },
  // 权限菜单
  {
    url: '/account/buttons/aliases',
    type: 'get',
    response: config => {
      return { 'status': 200, 'result': ['sys:user:add', 'user:table:edit:account:handler', 'user:table:edit', 'user:table:query:gold', 'user:table:query:list', 'dashboard:details', 'sys:user:edit', 'user:table:edit:deduction:gold', 'user:table:query:violation', 'user:table:query:account:handler', 'user:table:edit:reward:gold', 'user:table:query:details', 'sys:user:resetpwd'] }
    }
  }
]
