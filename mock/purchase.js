import Mock from 'mockjs'
import { get, getPage, post, put, del } from './default_request'
export default [
  // 金币收支 列表
  {
    url: '/user-wallet/list',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: Mock.mock({
          'items|30': [{
            userProfile: {
              'createTime': '2020-03-23 16:49:17',
              'utcCreateTime': '2020-03-23 16:49:17',
              'updateTime': '2020-03-23 16:49:23',
              'id': '1242010518727663617',
              'userAvatar': '',
              'userNickname': 'Dcvv ',
              'userSex': 0,
              'userSexName': '女',
              'userType': 0,
              'userTypeName': '真实',
              'countryName': 'China',
              'del': false
            },
            runningWater: {
              id: '@id',
              userId: '@id',
              trackId: '@sentence(2, 6)',
              quantity: 550,
              balance: 2550,
              type: 0,
              origin: 'TEST',
              originName: '糖果包 4.99',
              createTime: '2020-03-20 16:03:21'
            }

          }]
        }).items
      }
    }
  },
  // 购买记录
  {
    url: '/order/purchase/history/page',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          records: [],
          total: 30,
          size: 30,
          current: 1,
          searchCount: true,
          pages: 3
        }
      }
    }
  },
  // 每日糖果收回總和
  {
    url: '/statistics/candy/incomeRate/sum',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: '48462'
      }
    }
  },
  // 每日糖果收回
  {
    url: '/statistics/candy/incomeRate',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          records: Mock.mock({
            'items|30': [{
              'createTime': '2020-06-10 08:20:00',
              'updateTime': '2020-06-10 08:20:00',
              'id': '1270510992397119490',
              'incomeTotal': 691700.00,
              'incomePeopleTotal': '2',
              'femaleMatch': 10.00,
              'callSuccess': 50.00,
              'greetTask': 0.00,
              'imChat': 90.00,
              'callVideo': 0.00,
              'giftGiveAway': 691550.00,
              'toLikeMe': 0.00,
              'unblockAccount': 0.00,
              'other': 0.00,
              'handlerDate': '2020-06-09',
              'arppuCandy': 345850.00
            }]
          }).items,
          total: 30,
          size: 30,
          current: 1,
          searchCount: true,
          pages: 3
        }
      }
    }
  },
  // 今日内购总额
  {
    url: '/order/purchase/history/purchase/today/total',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: 10
      }
    }
  },
  //  获取异常订单日志列表
  getPage('/order/abnormal/log/page', {
    'id': '@id',
    'platform': 'iOS',
    'originalOrderId': '123',
    'orderId': '321',
    'productCode': '123',
    'status': 'IGNORE',
    'create_time': '2020-11-24 14:03:00'
  }),
  // 获取异常订单日志,凭证
  get('/order/abnormal/log/certificate', 'test'),
  // 获取异常订单日志,凭证
  put('/order/abnormal/log/status'),
  // 产品池列表
  get('/sys/product/pool', [{}]),
  // 添加指定产品信息到池子
  post('/sys/product/pool', ''),
  // 删除产品池中指定信息
  del('/sys/product/pool/\.', '')
]
