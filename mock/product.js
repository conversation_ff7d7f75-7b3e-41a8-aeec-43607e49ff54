/**
 * 产品信息
 */
import Mock from 'mockjs'

export default [
  // 内购产品配置
  {
    url: '/sys/product/config/list',
    type: 'get',
    response: config => {
      return { 'status': 200, 'result': [{ 'createTime': '2020-09-09 17:32:35', 'updateTime': '2020-09-09 18:15:13', 'createUser': '23', 'id': '1303627348529004546', 'productId': 'WASTE_MATERIAL_VIP', 'productPackage': 'com.hongyuan.1.99Vip', 'productType': 'AUTO_RENEWAL_SUBSCRIPTION', 'description': '废材VIP', 'groupName': 'VIP', 'unitPrice': 1.99, 'obtainCandy': 0.00, 'platform': 'iOS', 'sort': 6, 'discountRate': 0, 'showcase': true, 'recommend': false, 'tags': '', 'positionIndex': 0 }, { 'createTime': '2020-08-10 15:32:17', 'updateTime': '2020-09-09 18:15:13', 'updateUser': '23', 'id': '1292725083076419586', 'productId': 'VIP', 'productPackage': 'com.hongyuan.sugartimeVip', 'productType': 'AUTO_RENEWAL_SUBSCRIPTION', 'description': '订阅会员', 'groupName': 'VIP', 'unitPrice': 19.99, 'obtainCandy': 2500.00, 'platform': 'iOS', 'sort': 5, 'discountRate': 0, 'showcase': true, 'recommend': false, 'tags': '', 'positionIndex': 0 }, { 'createTime': '2020-08-31 11:51:39', 'updateTime': '2020-09-09 18:15:13', 'createUser': '23', 'updateUser': '23', 'id': '1300280062414716929', 'productId': 'THREE_DAYS_FREE', 'productPackage': 'com.hongyuan.sugartime19.99', 'productType': 'AUTO_RENEWAL_SUBSCRIPTION', 'description': '3天免費', 'groupName': 'VIP', 'unitPrice': 19.90, 'obtainCandy': 2500.00, 'platform': 'iOS', 'sort': 4, 'discountRate': 0, 'showcase': true, 'recommend': false, 'tags': '', 'positionIndex': 0 }, { 'createTime': '2020-08-25 16:43:20', 'updateTime': '2020-09-01 17:24:11', 'createUser': '23', 'id': '1298179138910146561', 'productId': 'SCRAP_IRON_VIP', 'productPackage': 'com.hongyuan.3.99vip', 'productType': 'AUTO_RENEWAL_SUBSCRIPTION', 'description': '废铁VIP', 'groupName': 'VIP', 'unitPrice': 3.99, 'obtainCandy': 0.00, 'platform': 'iOS', 'sort': 3, 'discountRate': 0, 'showcase': true, 'recommend': false, 'tags': '', 'positionIndex': 0 }, { 'createTime': '2020-08-10 15:33:46', 'updateTime': '2020-09-01 17:24:11', 'id': '1292725083076419588', 'productId': 'GOLD_VIP', 'productPackage': 'com.hongyuan.goldvip', 'productType': 'AUTO_RENEWAL_SUBSCRIPTION', 'description': '订阅会员', 'groupName': 'VIP', 'unitPrice': 89.99, 'obtainCandy': 14000.00, 'platform': 'iOS', 'sort': 2, 'discountRate': 0, 'showcase': true, 'recommend': false, 'tags': '', 'positionIndex': 0 }, { 'createTime': '2020-08-10 15:32:25', 'updateTime': '2020-09-01 12:20:56', 'id': '1292725083076419587', 'productId': 'SILVER_VIP', 'productPackage': 'com.hongyuan.silvervip', 'productType': 'AUTO_RENEWAL_SUBSCRIPTION', 'description': '订阅会员', 'groupName': 'VIP', 'unitPrice': 59.99, 'obtainCandy': 9000.00, 'platform': 'iOS', 'sort': 1, 'discountRate': 0, 'showcase': true, 'recommend': false, 'tags': '', 'positionIndex': 0 }] }
    }
  },
  // 内购产品配置信息
  {
    url: '/sys/product/config/\.',
    type: 'get',
    response: config => {
      return { 'status': 200, 'result': { 'createTime': '2020-09-09 17:32:35', 'updateTime': '2020-09-09 18:15:13', 'createUser': '23', 'id': '1303627348529004546', 'productId': 'WASTE_MATERIAL_VIP', 'productPackage': 'com.hongyuan.1.99Vip', 'productType': 'AUTO_RENEWAL_SUBSCRIPTION', 'description': '废材VIP', 'groupName': 'VIP', 'unitPrice': 1.99, 'obtainCandy': 0.00, 'platform': 'iOS', 'sort': 6, 'discountRate': 0, 'showcase': true, 'recommend': false, 'tags': '', 'positionIndex': 0 }}
    }
  },
  // 推荐商品,同步所以平台
  {
    url: '/sys/product/config/sync/all/recommend',
    type: 'post',
    response: config => {
      return {
        'status': 200,
        'result': {}
      }
    }
  },
  // 推荐商品
  {
    url: '/sys/product/config/single/recommend',
    type: 'post',
    response: config => {
      return {
        'status': 200,
        'result': {}
      }
    }
  },
  // 取消推荐
  {
    url: '/cancel/recommend/recommend',
    type: 'delete',
    response: config => {
      return {
        'status': 200,
        'result': {}
      }
    }
  },
  // 修改排序
  {
    url: '/cancel/recommend/sort',
    type: 'post',
    response: config => {
      return {
        'status': 200,
        'result': {}
      }
    }
  }
]
