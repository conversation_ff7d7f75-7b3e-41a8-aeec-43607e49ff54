import Mock from 'mockjs'

export default [
  // banner信息
  {
    url: '/sys/banner/config',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          records: Mock.mock({ 'items|30': [
            {
              'createTime': '2021-01-26 18:25:37',
              'updateTime': '2021-01-26 18:32:17',
              'updateUser': '23',
              'id': '1354012611532255233',
              'link': 'ddd',
              'cover': 'http://sugartime-dev.oss-accelerate.aliyuncs.com/manager-e22186b3-e630-4451-8816-daeecf6377b2.jpg',
              'showcase': true,
              'expiredTime': '2021-01-11 02:03:02',
              'depict': 'dee'
            }
          ] }
          ).items,
          'total': 30,
          'size': 30,
          'current': 1,
          'searchCount': true,
          'pages': 3
        }
      }
    }
  }
]
