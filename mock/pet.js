import { getPage } from './default_request'

export default [
  /**
   * 喂养分页列表.
   */
  getPage('/pet_pool/feeding/page', {}),
  /**
   *  宠物分页列表.
   */
  getPage('/pet_pool/page', {
    'petPool': {
      'id': '1448172780357816321',
      'sysOrigin': 'TIM_CHAT',
      'petCode': 'tuzi',
      'petName': '兔子',
      'level': 1,
      'rewardGroupId': '1402442046035218434',
      'shelf': false,
      'createTime': '2021-10-13 14:24:31',
      'updateTime': '2021-10-13 14:24:31',
      'createUser': '23',
      'updateUser': '23'
    },
    'petStages': [
      {
        'id': '1448172780722720769',
        'sysOrigin': 'TIM_CHAT',
        'petId': '1448172780357816321',
        'level': 1,
        'cover': 'http://dev.img.sugartimeapp.com/svga_cover/manager-4ac77ee7-9e25-4e27-8fef-f3396607308e.png',
        'sourceUrl': 'http://dev.img.sugartimeapp.com/other/manager-d89cd926-56ac-4487-8e9d-46f577c17281.svga',
        'upgradeFeedingNum': 0,
        'foodFeedingNum': 0,
        'revenue': 0,
        'revenueNum': 1,
        'revenueIntervalMinute': 1,
        'feedingIntervalMinute': 1,
        'freeFeedingNum': 1
      },
      {
        'id': '1448172780722720770',
        'sysOrigin': 'TIM_CHAT',
        'petId': '1448172780357816321',
        'level': 2,
        'cover': 'http://dev.img.sugartimeapp.com/svga_cover/manager-8a2207e8-2d9e-4373-8a77-506a20b8d97f.png',
        'sourceUrl': 'http://dev.img.sugartimeapp.com/other/manager-0bd41ca9-29e2-4249-9859-39d5223e21d3.svga',
        'upgradeFeedingNum': 0,
        'foodFeedingNum': 0,
        'revenue': 0,
        'revenueNum': 1,
        'revenueIntervalMinute': 1,
        'feedingIntervalMinute': 1,
        'freeFeedingNum': 1
      },
      {
        'id': '1448172780722720771',
        'sysOrigin': 'TIM_CHAT',
        'petId': '1448172780357816321',
        'level': 3,
        'cover': 'http://dev.img.sugartimeapp.com/svga_cover/manager-22c40b43-8f90-4902-8a89-1126fe962462.png',
        'sourceUrl': 'http://dev.img.sugartimeapp.com/other/manager-62c6977b-61a6-4873-8703-4d60e9a0f057.svga',
        'upgradeFeedingNum': 0,
        'foodFeedingNum': 0,
        'revenue': 0,
        'revenueNum': 1,
        'revenueIntervalMinute': 1,
        'feedingIntervalMinute': 1,
        'freeFeedingNum': 1
      },
      {
        'id': '1448172780722720772',
        'sysOrigin': 'TIM_CHAT',
        'petId': '1448172780357816321',
        'level': 4,
        'cover': 'http://dev.img.sugartimeapp.com/svga_cover/manager-5a91d0c1-178d-45d4-8bfb-4e50316fff79.png',
        'sourceUrl': 'http://dev.img.sugartimeapp.com/other/manager-15367223-e5f7-4ab2-bced-0305fa2ef123.svga',
        'upgradeFeedingNum': 0,
        'foodFeedingNum': 0,
        'revenue': 0,
        'revenueNum': 1,
        'revenueIntervalMinute': 1,
        'feedingIntervalMinute': 1,
        'freeFeedingNum': 1
      }
    ],
    'petUnlockConditions': [
      {
        'id': '1448172780865327105',
        'sysOrigin': 'TIM_CHAT',
        'petId': '1448172780357816321',
        'conditionType': 'WEALTH_LEVEL',
        'unit': 'LE',
        'quantity': 1
      }
    ],
    'rewards': {
      'createTime': '2021-06-09 09:46:54',
      'updateTime': '2021-08-22 19:37:54',
      'id': '1402442046035218434',
      'name': '312312',
      'sysOrigin': 'TIM_CHAT',
      'shelfStatus': true,
      'rewardConfigList': [
        {
          'createTime': '2021-06-23 11:50:43',
          'updateTime': '2021-06-23 11:50:43',
          'id': '1407546634501410818',
          'groupId': '1402442046035218434',
          'type': 'PROPS',
          'detailType': '',
          'content': '1398094409879130114',
          'quantity': 1,
          'cover': 'http://dev.img.sugartimeapp.com/other/manager-90e3a4aa-518b-450d-8ac7-593bceec71a4.png',
          'sourceUrl': 'http://dev.img.sugartimeapp.com/other/manager-28f6f187-5b95-4557-9f11-aa00869cccc7.svga',
          'amount': 1398094409879130114,
          'sort': 1
        },
        {
          'createTime': '2021-06-23 11:50:43',
          'updateTime': '2021-06-23 11:50:43',
          'id': '1407546634501410819',
          'groupId': '1402442046035218434',
          'type': 'GOLD',
          'detailType': '',
          'content': '12321',
          'quantity': 0,
          'amount': 12321,
          'sort': 2
        },
        {
          'createTime': '2021-06-23 11:50:43',
          'updateTime': '2021-06-23 11:50:43',
          'id': '1407546634501410820',
          'groupId': '1402442046035218434',
          'type': 'DIAMOND',
          'detailType': '',
          'content': '123',
          'quantity': 0,
          'amount': 123,
          'sort': 3
        }
      ]
    }
  })
]
