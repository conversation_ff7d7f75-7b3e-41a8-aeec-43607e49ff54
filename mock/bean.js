import { getPage } from "./default_request";

export default [
  /**
   * 用户豆子余额列表.
   */
  getPage("/user-bean-balance/page", {
    userId: "1443513452148449281",
    earnPoints: 123123,
    consumptionPoints: 12312,
    userBaseInfo: {
      createTime: "2021-09-30 17:50:00",
      updateTime: "2021-09-30 17:50:04",
      id: "1443513452148449281",
      userAvatar:
        "http://dev.img.sugartimeapp.com/avatar/B3894080-3EC8-486D-B402-6F5F6AF70716.png",
      originSys: "TIM_CHAT",
      userNickname: "Undo",
      userSex: 1,
      userSexName: "男",
      userType: 0,
      age: 21,
      bornYear: 2000,
      bornMonth: 1,
      bornDay: 1,
      userTypeName: "真实",
      countryName: "United States",
      countryId: "1231833389347123201",
      countryCode: "US",
      accountStatus: "NORMAL",
      accountStatusName: "正常",
      freezingTime: "2021-09-29 17:50:01",
      del: false,
      account: "41909"
    }
  }),

  /**
   * 用户豆子流水列表.
   */
  getPage("/user-bean-balance/running-water/page", {
    id: "1447763661450973186",
    sysOrigin: "TIM_CHAT",
    userId: "1443513452148449281",
    associateId: "1447763661325144066",
    quantity: 100,
    balance: 110911,
    origin: "REWARD_COINS",
    originName: "Reward coins",
    createTime: "2021-10-12 03:18:49",
    userBaseInfo: {
      createTime: "2021-09-30 17:50:00",
      updateTime: "2021-09-30 17:50:04",
      id: "1443513452148449281",
      userAvatar:
        "http://dev.img.sugartimeapp.com/avatar/B3894080-3EC8-486D-B402-6F5F6AF70716.png",
      originSys: "TIM_CHAT",
      userNickname: "Undo",
      userSex: 1,
      userSexName: "男",
      userType: 0,
      age: 21,
      bornYear: 2000,
      bornMonth: 1,
      bornDay: 1,
      userTypeName: "真实",
      countryName: "United States",
      countryId: "1231833389347123201",
      countryCode: "US",
      accountStatus: "NORMAL",
      accountStatusName: "正常",
      freezingTime: "2021-09-29 17:50:01",
      del: false,
      account: "41909"
    }
  })
];
