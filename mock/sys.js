import Mock from 'mockjs'

export default [
  // 获取抽奖配置数据
  {
    url: '/lottery/conf',
    type: 'get',
    response: config => {
      return { 'status': 200, 'result': { 'diameter': '300px', 'blocks': [{ 'padding': '13px', 'background': '#77D637', 'imgs': [], 'imageIndex': '0' }], 'prizes': [{ 'background': '#f8d384', 'fonts': [{ 'text': '1 元红包', 'top': '18%', 'fontColor': '', 'fontSize': '14px', 'fontStyle': 'sans-serif', 'fontWeight': '', 'lineHeight': '14px', 'wordWrap': true, 'lengthLimit': '' }], 'imgs': [{ 'src': 'http://img.sugartimeapp.com/svga_cover/manager-3c9372f3-1d15-482d-9cb7-e58e6852dbcb.png', 'top': '35%', 'width': '30%', 'height': '' }], 'fontIndex': '0', 'inStock': '2312', 'prize': { 'content': '', 'cover': '', 'propsType': '' }, 'probability': 0 }, { 'background': '#f9e3bb', 'fonts': [{ 'text': '2 元红包', 'top': '18%', 'fontColor': '', 'fontSize': '14px', 'fontStyle': 'sans-serif', 'fontWeight': '', 'lineHeight': '14px', 'wordWrap': true, 'lengthLimit': '' }], 'imgs': [{ 'src': 'http://img.sugartimeapp.com/svga_cover/manager-3c9372f3-1d15-482d-9cb7-e58e6852dbcb.png', 'top': '35%', 'width': '30%', 'height': '' }], 'fontIndex': '0', 'inStock': '0', 'prize': { 'content': '', 'cover': '', 'propsType': '' }, 'probability': 0 }, { 'background': '#f8d384', 'fonts': [{ 'text': '3 元红包', 'top': '18%', 'fontColor': '', 'fontSize': '14px', 'fontStyle': 'sans-serif', 'fontWeight': '', 'lineHeight': '14px', 'wordWrap': true, 'lengthLimit': '' }], 'imgs': [{ 'src': 'http://img.sugartimeapp.com/svga_cover/manager-3c9372f3-1d15-482d-9cb7-e58e6852dbcb.png', 'top': '35%', 'width': '30%', 'height': '' }], 'fontIndex': '0', 'inStock': '0', 'prize': { 'content': '', 'cover': '', 'propsType': '' }, 'probability': 0 }, { 'background': '#f9e3bb', 'fonts': [{ 'text': '4 元红包', 'top': '18%', 'fontColor': '', 'fontSize': '14px', 'fontStyle': 'sans-serif', 'fontWeight': '', 'lineHeight': '14px', 'wordWrap': true, 'lengthLimit': '' }], 'imgs': [{ 'src': 'http://img.sugartimeapp.com/other/manager-496c0d2a-6efc-404a-ae40-1d0557ccc53f.png', 'top': '35%', 'width': '30%', 'height': '' }], 'fontIndex': '0', 'inStock': '321', 'prize': { 'content': '1386570046155755521', 'cover': 'http://img.sugartimeapp.com/other/manager-496c0d2a-6efc-404a-ae40-1d0557ccc53f.png', 'propsType': 'GIFT' }, 'probability': 0 }, { 'background': '#f8d384', 'fonts': [{ 'text': '5 元红包', 'top': '18%', 'fontColor': '', 'fontSize': '14px', 'fontStyle': 'sans-serif', 'fontWeight': '', 'lineHeight': '14px', 'wordWrap': true, 'lengthLimit': '' }], 'imgs': [{ 'src': 'http://img.sugartimeapp.com/svga_cover/manager-3c9372f3-1d15-482d-9cb7-e58e6852dbcb.png', 'top': '35%', 'width': '30%', 'height': '' }], 'fontIndex': '0', 'inStock': '0', 'prize': { 'content': '', 'cover': '', 'propsType': '' }, 'probability': 0 }, { 'background': '#f9e3bb', 'fonts': [{ 'text': '6 元红包', 'top': '18%', 'fontColor': '', 'fontSize': '14px', 'fontStyle': 'sans-serif', 'fontWeight': '', 'lineHeight': '14px', 'wordWrap': true, 'lengthLimit': '' }], 'imgs': [{ 'src': 'http://img.sugartimeapp.com/svga_cover/manager-3c9372f3-1d15-482d-9cb7-e58e6852dbcb.png', 'top': '35%', 'width': '30%', 'height': '' }], 'fontIndex': '0', 'inStock': '0', 'prize': { 'content': '', 'cover': '', 'propsType': '' }, 'probability': 0 }], 'buttons': [{ 'radius': '50%', 'pointer': false, 'background': '#d64737', 'fonts': [], 'imgs': [] }, { 'radius': '45%', 'pointer': false, 'background': '#ffffff', 'fonts': [], 'imgs': [] }, { 'radius': '41%', 'pointer': true, 'background': '#f6c66f', 'fonts': [], 'imgs': [] }, { 'radius': '35%', 'pointer': false, 'background': '#ffdea0', 'fonts': [], 'imgs': [] }], 'defaultConfig': { 'gutter': '0', 'stopRange': 0, 'offsetDegree': 0, 'speed': 20, 'accelerationTime': 2500, 'decelerationTime': 2500 }, 'defaultStyle': { 'background': '', 'fontColor': '#000', 'fontSize': '14px', 'fontStyle': 'sans-serif', 'fontWeight': '400', 'lineHeight': '14px', 'wordWrap': true, 'lengthLimit': '90%' }}}
    }
  },
  // 用户登录日志
  {
    url: '/user/login/logger/page',
    type: 'get',
    response: config => {
      return { 'status': 200, 'result': {}}
    }
  },
  // 活动奖品预览
  {
    url: '/activity-conf/rank-reward-preview',
    type: 'get',
    response: config => {
      return { 'status': 200, 'result': { 'butOneRanks': [{ 'reward': { 'rankRange': '1', 'resourceGroupId': '1546775100905127937', 'rewards': [{ 'createTime': '2022-08-16 15:12:43', 'updateTime': '2022-08-16 15:12:43', 'createUser': '23', 'updateUser': '23', 'id': '1559437982926438401', 'groupId': '1546775100905127937', 'type': 'PROPS', 'detailType': 'NOBLE_VIP', 'content': '1386297763882045441', 'quantity': 1, 'cover': 'http://dev.img.sugartimeapp.com/svga_cover/manager-1428b2f3-ce1c-4b54-8e50-651cc55155ca.png', 'sourceUrl': 'http://dev.img.sugartimeapp.com/svgasource/manager-d1a9739f-ac73-40fb-a3ac-915f472c99a0.svga', 'amount': 1386297763882045441, 'sort': 1, 'remark': '' }, { 'createTime': '2022-08-16 15:12:43', 'updateTime': '2022-08-16 15:12:43', 'createUser': '23', 'updateUser': '23', 'id': '1559437982926438402', 'groupId': '1546775100905127937', 'type': 'GOLD', 'detailType': 'GOLD', 'content': '111', 'quantity': 0, 'amount': 111, 'sort': 2, 'remark': '' }, { 'createTime': '2022-08-16 15:12:43', 'updateTime': '2022-08-16 15:12:43', 'createUser': '23', 'updateUser': '23', 'id': '1559437982926438403', 'groupId': '1546775100905127937', 'type': 'PROPS', 'detailType': 'AVATAR_FRAME', 'content': '1435930845005176833', 'quantity': 1, 'cover': 'http://dev.img.sugartimeapp.com/svga_cover/manager-de6db8aa-01cb-4906-9dbd-c21e48b9ce1f.png', 'sourceUrl': 'http://dev.img.sugartimeapp.com/svgasource/manager-8398eb77-e65b-4344-87d2-8285670cad55.svga', 'amount': 1435930845005176833, 'sort': 3, 'remark': '' }, { 'createTime': '2022-08-16 15:12:43', 'updateTime': '2022-08-16 15:12:43', 'createUser': '23', 'updateUser': '23', 'id': '1559437982926438404', 'groupId': '1546775100905127937', 'type': 'BADGE', 'detailType': 'BADGE', 'content': '1432243445812940802', 'quantity': 1, 'cover': 'http://dev.img.sugartimeapp.com/other/manager-d7942cd5-c2bc-4af6-8e22-fa58c1f8ef63.png', 'sourceUrl': '', 'amount': 1432243445812940802, 'badgeName': 'cp_rank_top2', 'sort': 4, 'remark': '' }, { 'createTime': '2022-08-16 15:12:43', 'updateTime': '2022-08-16 15:12:43', 'createUser': '23', 'updateUser': '23', 'id': '1559437982926438405', 'groupId': '1546775100905127937', 'type': 'CUSTOMIZE', 'detailType': 'CUSTOMIZE', 'content': '1498615841441071105', 'quantity': 0, 'cover': 'http://dev.img.sugartimeapp.com/svga_cover/manager-4013fd35-930a-4d4a-98dd-8f71d0824de7.png', 'sourceUrl': '', 'amount': 1498615841441071105, 'sort': 5, 'remark': '请问去玩' }] }, 'userProfile': { 'id': '1557628769233387522', 'account': '42098', 'userAvatar': '', 'userNickname': 'Chris and I are ', 'userSex': 1, 'age': 27, 'accountStatus': 'NORMAL', 'freezingTime': '2022-08-10 15:23:33', 'countryId': '1231833232115249154', 'countryName': 'United Arab Emirates', 'countryCode': 'AE', 'originSys': 'ASWAT', 'sysOriginChild': 'ASWAT', 'del': false, 'bornYear': 1995, 'bornMonth': 1, 'bornDay': 1, 'createTime': '2022-08-11 15:23:32', 'actualAccountStatus': 'NORMAL', 'actualAccount': '42098', 'userSexName': '男', 'accountStatusName': '正常' }, 'quantity': '360000', 'quantityFormat': '360K' }], 'butTwoRanks': [{ 'reward': { 'rankRange': '1', 'resourceGroupId': '1546775100905127937', 'rewards': [{ 'createTime': '2022-08-16 15:12:43', 'updateTime': '2022-08-16 15:12:43', 'createUser': '23', 'updateUser': '23', 'id': '1559437982926438401', 'groupId': '1546775100905127937', 'type': 'PROPS', 'detailType': 'NOBLE_VIP', 'content': '1386297763882045441', 'quantity': 1, 'cover': 'http://dev.img.sugartimeapp.com/svga_cover/manager-1428b2f3-ce1c-4b54-8e50-651cc55155ca.png', 'sourceUrl': 'http://dev.img.sugartimeapp.com/svgasource/manager-d1a9739f-ac73-40fb-a3ac-915f472c99a0.svga', 'amount': 1386297763882045441, 'sort': 1, 'remark': '' }, { 'createTime': '2022-08-16 15:12:43', 'updateTime': '2022-08-16 15:12:43', 'createUser': '23', 'updateUser': '23', 'id': '1559437982926438402', 'groupId': '1546775100905127937', 'type': 'GOLD', 'detailType': 'GOLD', 'content': '111', 'quantity': 0, 'amount': 111, 'sort': 2, 'remark': '' }, { 'createTime': '2022-08-16 15:12:43', 'updateTime': '2022-08-16 15:12:43', 'createUser': '23', 'updateUser': '23', 'id': '1559437982926438403', 'groupId': '1546775100905127937', 'type': 'PROPS', 'detailType': 'AVATAR_FRAME', 'content': '1435930845005176833', 'quantity': 1, 'cover': 'http://dev.img.sugartimeapp.com/svga_cover/manager-de6db8aa-01cb-4906-9dbd-c21e48b9ce1f.png', 'sourceUrl': 'http://dev.img.sugartimeapp.com/svgasource/manager-8398eb77-e65b-4344-87d2-8285670cad55.svga', 'amount': 1435930845005176833, 'sort': 3, 'remark': '' }, { 'createTime': '2022-08-16 15:12:43', 'updateTime': '2022-08-16 15:12:43', 'createUser': '23', 'updateUser': '23', 'id': '1559437982926438404', 'groupId': '1546775100905127937', 'type': 'BADGE', 'detailType': 'BADGE', 'content': '1432243445812940802', 'quantity': 1, 'cover': 'http://dev.img.sugartimeapp.com/other/manager-d7942cd5-c2bc-4af6-8e22-fa58c1f8ef63.png', 'sourceUrl': '', 'amount': 1432243445812940802, 'badgeName': 'cp_rank_top2', 'sort': 4, 'remark': '' }, { 'createTime': '2022-08-16 15:12:43', 'updateTime': '2022-08-16 15:12:43', 'createUser': '23', 'updateUser': '23', 'id': '1559437982926438405', 'groupId': '1546775100905127937', 'type': 'CUSTOMIZE', 'detailType': 'CUSTOMIZE', 'content': '1498615841441071105', 'quantity': 0, 'cover': 'http://dev.img.sugartimeapp.com/svga_cover/manager-4013fd35-930a-4d4a-98dd-8f71d0824de7.png', 'sourceUrl': '', 'amount': 1498615841441071105, 'sort': 5, 'remark': '请问去玩' }] }, 'userProfile': { 'id': '1557626889618964481', 'account': '42096', 'userAvatar': '', 'userNickname': 'qwe', 'userSex': 1, 'age': 27, 'accountStatus': 'NORMAL', 'freezingTime': '2022-08-10 15:16:04', 'countryId': '1231833232115249154', 'countryName': 'United Arab Emirates', 'countryCode': 'AE', 'originSys': 'ASWAT', 'sysOriginChild': 'ASWAT', 'del': false, 'bornYear': 1995, 'bornMonth': 1, 'bornDay': 1, 'createTime': '2022-08-11 15:16:04', 'actualAccountStatus': 'NORMAL', 'actualAccount': '42096', 'userSexName': '男', 'accountStatusName': '正常' }, 'quantity': '360000', 'quantityFormat': '360K' }] }}
    }
  }
]
