/**
 * 统计相关
 */
import Mock from 'mockjs'
import { get, put } from './default_request'

export default [
  // 获取最新货币收支情况
  get('/count/currency/daily/platform', {}),
  // 最新平台内购情况
  get('/count/purchase/daily/list/sys-origin', []),

  // 变更工资
  put('/statistics/support/work/user/change/wag', ''),
  // 获取最近30天最订单购买退款统计数据
  get('/count/purchase/latest/order', [{ 'id': '1', 'platform': 'iOS', 'purchase': 1.00, 'refund': 4.00, 'statisticsTime': '2020-11-02 22:20:27' }]),
  // 推荐人信息
  {
    url: '/statistics/quality/users/details/page',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          records: Mock.mock({ 'items|30': [
            {
              'sysOrigin': 'TIM_CHAT',
              'userId': '1438693762037395457',
              'onlineQuantity': 0,
              'freightQuantity': 12312.31,
              'salaryQuantity': 0,
              'userBaseInfo': {
                'createTime': '2021-09-17 10:38:17',
                'updateTime': '2021-09-17 10:38:19',
                'id': '1438693762037395457',
                'userAvatar': 'http://dev.img.sugartimeapp.com/avatar/1A71A687-888B-49D4-902D-8E2BFA918426.png',
                'originSys': 'TIM_CHAT',
                'userNickname': 'KKK',
                'userSex': 0,
                'userSexName': '女',
                'userType': 0,
                'age': 21,
                'bornYear': 2000,
                'bornMonth': 1,
                'bornDay': 1,
                'userTypeName': '真实',
                'countryName': 'China',
                'countryId': '1231833262813360130',
                'countryCode': 'CN',
                'accountStatus': 'NORMAL',
                'accountStatusName': '正常',
                'freezingTime': '2021-09-16 10:38:17',
                'del': false,
                'account': '41902'
              },
              'totalQuantity': 12312
            }
          ] }
          ).items,
          'total': 30,
          'size': 30,
          'current': 1,
          'searchCount': true,
          'pages': 3
        }
      }
    }
  }

]
