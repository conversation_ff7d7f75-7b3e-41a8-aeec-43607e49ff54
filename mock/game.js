
/**
 * 游戏相关
 */
import Mock from 'mockjs'

export default [
  // 飞行棋游戏-列表
  {
    url: '/game-ludo/flow',
    type: 'get',
    response: config => {
      return {
        'status': 200,
        'result': [
          {
            'history': {
              'id': '1522848508305948674',
              'sysOrigin': 'ASWAT',
              'roomId': '1000',
              'gameStatus': 'END',
              'gameStatusEvent': 'NORMAL_SETTLEMENT',
              'joinAmount': 200,
              'playerSize': 2,
              'bonus': 280,
              'lockSeat': '',
              'victoryColor': 'GREEN',
              'createTime': '2022-05-07 15:59:12'
            },
            'players': [
              {
                'players': {
                  'createTime': '2022-05-07 15:59:12',
                  'updateTime': '2022-05-07 15:59:12',
                  'id': '1522848508373057538',
                  'gameId': '1522848508305948674',
                  'userId': '1497138368407797762',
                  'consumeJoinAmount': 200,
                  'checkerboardColor': 'RED',
                  'initiator': true,
                  'rankingIndex': 0
                },
                'userProfile': {
                  'id': '1497138368407797762',
                  'account': '42041',
                  'userAvatar': 'http://img.sugartimeapp.com/halla_default_avatar_2.png',
                  'userNickname': '233445',
                  'userSex': 1,
                  'age': 15,
                  'accountStatus': 'NORMAL',
                  'freezingTime': '2022-02-24 17:16:17',
                  'countryId': '1231833241778925570',
                  'countryName': 'Azerbaijan',
                  'countryCode': 'AZ',
                  'originSys': 'ASWAT',
                  'sysOriginChild': 'ASWAT_LITE',
                  'del': false,
                  'bornYear': 2006,
                  'bornMonth': 2,
                  'bornDay': 25,
                  'createTime': '2022-02-25 17:16:17',
                  'ownSpecialId': {
                    'account': '433435',
                    'customizeField': {

                    }
                  },
                  'actualAccountStatus': 'NORMAL',
                  'actualAccount': '433435',
                  'accountStatusName': '正常',
                  'userSexName': '男'
                }
              },
              {
                'players': {
                  'createTime': '2022-05-07 15:59:12',
                  'updateTime': '2022-05-07 15:59:12',
                  'id': '1522848508373057539',
                  'gameId': '1522848508305948674',
                  'userId': '1522441430756216833',
                  'consumeJoinAmount': 200,
                  'checkerboardColor': 'GREEN',
                  'initiator': false,
                  'rankingIndex': 0
                },
                'userProfile': {
                  'id': '1522441430756216833',
                  'account': '42054',
                  'userAvatar': 'http://dev.img.sugartimeapp.com/avatar/c48011dd-676f-4b75-953c-22389d1dde3c.jpg',
                  'userNickname': 'Dog',
                  'userSex': 1,
                  'age': 27,
                  'accountStatus': 'NORMAL',
                  'freezingTime': '2022-05-05 13:01:37',
                  'countryId': '1231833262813360130',
                  'countryName': 'China',
                  'countryCode': 'CN',
                  'originSys': 'ASWAT',
                  'sysOriginChild': 'ASWAT',
                  'del': false,
                  'bornYear': 1995,
                  'bornMonth': 1,
                  'bornDay': 1,
                  'createTime': '2022-05-06 13:01:37',
                  'actualAccountStatus': 'NORMAL',
                  'actualAccount': '42054',
                  'accountStatusName': '正常',
                  'userSexName': '男'
                }
              }
            ]
          }
        ]
      }
    }
  },
  // lucky box
  {
    url: '/game/lucky/box/list',
    type: 'get',
    response: config => {
      return {
        'status': 200,
        'result': []
      }
    }
  }
]
