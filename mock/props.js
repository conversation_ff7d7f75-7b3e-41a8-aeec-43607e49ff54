import Mock from 'mockjs'

export default [
  // 道具购买流水
  {
    url: '/room/props/receipt',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          records: Mock.mock({ 'items|30': [
            {
              'createTime': '2021-01-20 14:34:44',
              'updateTime': '2021-01-20 14:34:44',
              'id': '3243322',
              'userId': '1351021829418778626',
              'propsId': '1351727566616408065',
              'propsCandy': 50.00,
              'propsPhoto': 'http://sugartime-dev.oss-accelerate.aliyuncs.com/manager-80db3c54-4164-4f9b-a1d3-28ae96525c64.jpg',
              'propsName': 'yy',
              'typeName': '头像框',
              'userBaseInfo': {
                'createTime': '2021-01-18 12:21:19',
                'updateTime': '2021-01-18 12:21:20',
                'id': '1351021829418778626',
                'userAvatar': 'http://dev.img.sugartimeapp.com/avatar/2211A700-6413-4064-9810-197306DDEE20.png',
                'originSys': 'ASWAT',
                'userNickname': '呢你',
                'userSex': 1,
                'userSexName': '男',
                'userType': 0,
                'age': 20,
                'bornYear': 2000,
                'bornMonth': 1,
                'bornDay': 1,
                'userTypeName': '真实',
                'countryName': 'China',
                'countryId': '1231833262813360130',
                'countryCode': 'CN',
                'accountStatus': 'NORMAL',
                'accountStatusName': '正常',
                'freezingTime': '2021-01-17 12:21:20',
                'del': false,
                'account': '399823'
              }
            }
          ] }
          ).items,
          'total': 30,
          'size': 30,
          'current': 1,
          'searchCount': true,
          'pages': 3
        }
      }
    }
  },
  // 道具资源分页列表
  {
    url: '/props/source/record/page',
    type: 'get',
    response: config => {
      return {
        status: 200,
        body: []
      }
    }
  },

  // 活动道具奖励配置-分页列表
  {
    url: '/props/activity/reward/group/page',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          records: Mock.mock({ 'items|30': [
            {
              'createTime': '2021-06-09 09:46:54',
              'updateTime': '2021-06-09 09:46:54',
              'id': '1402442046035218434',
              'code': '12312',
              'sysOrigin': 'ASWAT',
              'name': '312312',
              'shelfStatus': true,
              'rewardConfigList': [
                {
                  'createTime': '2021-06-09 09:46:55',
                  'updateTime': '2021-06-09 09:46:55',
                  'id': '1402442046827941889',
                  'groupId': '1402442046035218434',
                  'type': 'PROPS',
                  'content': '1352931737504518145',
                  'quantity': 123123
                }
              ]
            }
          ] }
          ).items,
          'total': 30,
          'size': 30,
          'current': 1,
          'searchCount': true,
          'pages': 3
        }
      }
    }
  },
  // 道具商店-分页
  {
    url: '/props/store/page',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {}
      }
    }
  }
]
