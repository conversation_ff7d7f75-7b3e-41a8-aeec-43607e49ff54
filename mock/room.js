import Mock from 'mockjs'

export default [
  // 房间贡献余额列表
  {
    url: '/room/contribution/balance/page',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          records: Mock.mock({ 'items|30': [
            {
              'createTime': '2021-03-01 20:05:20',
              'updateTime': '2021-03-01 20:05:20',
              'roomId': '1364518645829402625',
              'totalQuantity': 70.00,
              'withdrawQuantity': 0.00,
              'roomProfile': {
                'createTime': '2021-02-24 18:12:51',
                'updateTime': '2021-02-24 18:12:51',
                'id': '1364518645829402625',
                'roomAccount': '400374',
                'userId': '1364396133145575425',
                'roomCover': 'http://dev.img.sugartimeapp.com/avatar/10DEFAD7-A188-4115-A31C-1ACF2422A706.png',
                'roomName': '恶魔哦ok',
                'roomDesc': "Welcome everyone! Let's chat and have fun together.",
                'roomTag': 'CHAT'
              }
            }
          ] }
          ).items,
          'total': 30,
          'size': 30,
          'current': 1,
          'searchCount': true,
          'pages': 3
        }
      }
    }
  },
  // 老虎机开奖记录
  {
    url: '/game/slot/machine/lottery/record/page',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          records: Mock.mock({ 'items|30': [
            {
              'createTime': '2021-03-18 18:04:52',
              'updateTime': '2021-03-18 18:04:52',
              'id': '1372489167350652930',
              'userId': '1369599569717846018',
              'consumeGolds': 123.00,
              'prizeType': 'NONE',
              'prizeTypeName': '没有中奖',
              'multiple': 0,
              'obtainGolds': 0.00,
              'prize': false,
              'userBaseInfo': {
                'createTime': '2021-02-19 19:38:21',
                'updateTime': '2021-02-19 19:38:31',
                'id': '1362728225851977730',
                'userAvatar': 'http://dev.img.sugartimeapp.com/avatar/B55EF836-D632-4FD2-8347-92099E29DF65.png',
                'originSys': 'TIM_CHAT',
                'userNickname': '不见了',
                'userSex': 1,
                'userSexName': '男',
                'userType': 0,
                'age': 21,
                'bornYear': 2000,
                'bornMonth': 1,
                'bornDay': 1,
                'userTypeName': '真实',
                'countryName': 'China',
                'countryId': '1231833262813360130',
                'countryCode': 'CN',
                'accountStatus': 'NORMAL',
                'accountStatusName': '正常',
                'freezingTime': '2021-02-18 19:38:22',
                'del': false,
                'account': '400315'
              }
            }
          ] }
          ).items,
          'total': 30,
          'size': 30,
          'current': 1,
          'searchCount': true,
          'pages': 3
        }
      }
    }
  },
  // 红包发送记录
  {
    url: '/game-red-packet/flow',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: Mock.mock({ 'items|30': [
          {
            'id': '1394536136562552833',
            'sysOrigin': 'TIM_CHAT',
            'roomId': '1',
            'userId': '1394493636222013442',
            'amount': 290,
            'quantity': 6,
            'status': 'REIMBURSE',
            'createTime': '2021-05-18 14:11:40',
            'available': false
          },
          {
            'id': '1394535868311646210',
            'sysOrigin': 'TIM_CHAT',
            'roomId': '1',
            'userId': '1394493636222013442',
            'amount': 290,
            'quantity': 6,
            'status': 'REIMBURSE',
            'createTime': '2021-05-18 14:10:36',
            'available': false
          },
          {
            'id': '1394535667182186498',
            'sysOrigin': 'TIM_CHAT',
            'roomId': '1',
            'userId': '1394493636222013442',
            'amount': 290,
            'quantity': 6,
            'status': 'REIMBURSE',
            'createTime': '2021-05-18 14:09:48',
            'available': false
          }
        ] }
        ).items
      }
    }
  }

]
