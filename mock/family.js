import Mock from 'mockjs'

export default [
  {
    url: '/family/page',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          records: Mock.mock({
            'items|30': [
              {
                'createTime': '2021-07-30 10:01:08',
                'updateTime': '2021-07-30 14:04:17',
                'createUser': '1412983576920535042',
                'id': '1420927408446341122',
                'sysOrigin': 'ASWAT',
                'familyAccount': '1005',
                'familyAvatar': 'http://dev.img.sugartimeapp.com/avatar/8CB2584D-74D1-42AE-890B-************.png',
                'familyName': '把我啦',
                'familyLevelId': '1419951513519673345',
                'familyStatus': 'NORMAL',
                'familyNotice': '考虑图哇路',
                'memberCount': '1',
                'levelKey': 'BLACK_IRON',
                'familyExp': 1998,
                'userBaseInfo': {
                  'createTime': '2021-07-08 11:55:11',
                  'updateTime': '2021-07-23 15:44:04',
                  'id': '1412983576920535042',
                  'userAvatar': 'http://dev.img.sugartimeapp.com/avatar/72E33441-0AD8-47F9-80E4-A3115D3AC068.png',
                  'originSys': 'ASWAT',
                  'userNickname': '考虑一下',
                  'userSex': 1,
                  'userSexName': '男',
                  'userType': 0,
                  'age': 21,
                  'bornYear': 2000,
                  'bornMonth': 1,
                  'bornDay': 1,
                  'userTypeName': '真实',
                  'countryName': 'United Kingdom',
                  'countryId': '1231833285743620097',
                  'countryCode': 'GB',
                  'accountStatus': 'NORMAL',
                  'accountStatusName': '正常',
                  'freezingTime': '2021-07-07 11:55:11',
                  'del': false,
                  'account': '41758'
                }
              },
              {
                'createTime': '2021-07-29 16:01:59',
                'createUser': '1403599281328107522',
                'id': '1420656112526315521',
                'sysOrigin': 'ASWAT',
                'familyAccount': '1004',
                'familyAvatar': 'http://dev.img.sugartimeapp.com/avatar/ca82dbad-00fe-4d5f-9317-34581eaf4828.jpg',
                'familyName': 'psg',
                'familyLevelId': '1419951513519673345',
                'familyStatus': 'NORMAsssL',
                'familyNotice': 'hgjdjdhdb ',
                'memberCount': '1',
                'levelKey': 'BLACK_IRON',
                'familyExp': 99,
                'userBaseInfo': {
                  'createTime': '2021-06-12 14:25:21',
                  'updateTime': '2021-06-17 12:09:23',
                  'id': '1403599281328107522',
                  'userAvatar': 'http://dev.img.sugartimeapp.com/avatar/8d9ac8af-149c-409b-9689-da6b30fab64a.jpg',
                  'originSys': 'ASWAT',
                  'userNickname': 'Samsung',
                  'userSex': 1,
                  'userSexName': '男',
                  'userType': 0,
                  'age': 26,
                  'bornYear': 1995,
                  'bornMonth': 1,
                  'bornDay': 1,
                  'userTypeName': '真实',
                  'countryName': 'China',
                  'countryId': '1231833262813360130',
                  'countryCode': 'CN',
                  'accountStatus': 'NORMAL',
                  'accountStatusName': '正常',
                  'freezingTime': '2021-06-11 14:25:21',
                  'del': false,
                  'account': '41634'
                }
              },
              {
                'createTime': '2021-07-29 10:44:13',
                'updateTime': '2021-07-29 15:25:49',
                'createUser': '1407187450447503361',
                'id': '1420575864802758658',
                'sysOrigin': 'ASWAT',
                'familyAccount': '1003',
                'familyAvatar': '',
                'familyName': 'st_3327445017982',
                'familyLevelId': '1419951513519673345',
                'familyStatus': 'NORMAL',
                'familyNotice': '',
                'memberCount': '2',
                'levelKey': 'BLACK_IRON',
                'familyExp': 0,
                'userBaseInfo': {
                  'createTime': '2021-06-22 12:03:27',
                  'updateTime': '2021-06-22 12:04:09',
                  'id': '1407187450447503361',
                  'userAvatar': 'http://dev.img.sugartimeapp.com/avatar/5a5cdb55-322c-4400-bc06-21e99cf26fc1.jpg',
                  'originSys': 'ASWAT',
                  'userNickname': 'HONOR 9x',
                  'userSex': 1,
                  'userSexName': '男',
                  'userType': 0,
                  'age': 26,
                  'bornYear': 1995,
                  'bornMonth': 1,
                  'bornDay': 1,
                  'userTypeName': '真实',
                  'countryName': 'Azerbaijan',
                  'countryId': '1231833241778925570',
                  'countryCode': 'AZ',
                  'accountStatus': 'NORMAL',
                  'accountStatusName': '正常',
                  'freezingTime': '2021-06-21 12:03:27',
                  'del': false,
                  'account': '41681'
                }
              }
            ]
          }).items,
          total: 30,
          size: 30,
          current: 1,
          searchCount: true,
          pages: 3
        }
      }
    }
  },
  {
    url: '/family/level/config/page',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          records: Mock.mock({
            'items|30': [
              {
                'createTime': '2021-07-27 17:22:12',
                'updateTime': '2021-07-28 15:06:24',
                'id': '1419951513519673345',
                'sysOrigin': 'ASWAT',
                'levelKey': 'BLACK_IRON',
                'avatarFrameId': '1419923318128943106',
                'badgeId': '1419919035165745154',
                'giftId': '1419936886396178433',
                'levelExp': 20000,
                'maxMember': 150,
                'maxManager': 5,
                'levelBackgroundPicture': 'http://dev.img.sugartimeapp.com/svga_cover/manager-58578f0d-72fd-410d-8ac3-b49ad29c9473.jpg',
                'sort': 0
              },
              {
                'createTime': '2021-07-27 17:25:41',
                'updateTime': '2021-07-28 15:06:38',
                'id': '1419952388564733953',
                'sysOrigin': 'ASWAT',
                'levelKey': 'BRONZE_I',
                'avatarFrameId': '1419923459619594242',
                'badgeId': '1419919170159419394',
                'giftId': '1419937069750177793',
                'levelExp': 50000,
                'maxMember': 160,
                'maxManager': 6,
                'levelBackgroundPicture': 'http://dev.img.sugartimeapp.com/svga_cover/manager-bb499970-95d8-4f53-8a2e-7c77fa64f9ee.jpg',
                'sort': 1
              },
              {
                'createTime': '2021-07-28 15:11:59',
                'updateTime': '2021-07-28 15:11:59',
                'id': '1420281134152867842',
                'sysOrigin': 'ASWAT',
                'levelKey': 'BRONZE_II',
                'avatarFrameId': '1419923318128943106',
                'badgeId': '1419919035165745154',
                'giftId': '1419936886396178433',
                'levelExp': 25000,
                'maxMember': 170,
                'maxManager': 6,
                'levelBackgroundPicture': 'http://dev.img.sugartimeapp.com/svga_cover/manager-ac57a8b1-3042-4d0b-9295-cb17b2d00800.jpg',
                'sort': 2
              }
            ]
          }).items,
          total: 30,
          size: 30,
          current: 1,
          searchCount: true,
          pages: 3
        }
      }
    }
  },
  {
    url: '/family/create/rule/page',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          records: Mock.mock({
            'items|30': [
              {
                'createTime': '2021-07-27 17:29:06',
                'updateTime': '2021-07-28 14:25:07',
                'id': '1419951513519673341',
                'sysOrigin': 'ASWAT',
                'payCandy': 30000
              },
              {
                'createTime': '2021-07-28 14:23:33',
                'updateTime': '2021-07-28 14:23:33',
                'id': '1420268947057336322',
                'sysOrigin': 'TIM_CHAT',
                'payCandy': 20000
              }
            ]
          }).items,
          total: 30,
          size: 30,
          current: 1,
          searchCount: true,
          pages: 3
        }
      }
    }
  }
]
