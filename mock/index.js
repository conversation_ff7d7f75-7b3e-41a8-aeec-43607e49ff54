import Mock from 'mockjs'
import { param2Obj } from '../src/utils'

import user from './user'
import table from './table'
import appUser from './app-user'
import purchase from './purchase'
import statistics from './statistics'
import approval from './approval'
import message from './message'
import gift from './gift'
import integral from './integral'
import appManager from './app-manager'
import product from './product'
import datav from './datav'
import imAccount from './im-account'
import props from './props'
import room from './room'
import game from './game'
import family from './family'
import tootls from './tootls'
import bean from './bean'
import sysEmoji from './sys-emoji'
import sys from './sys'
import userSpecialId from './user-special-id'
import activity from './activity'
import team from './team'
import opsSystem from './ops-system'

const mocks = [
  ...opsSystem,
  ...team,
  ...activity,
  ...userSpecialId,
  ...sys,
  ...sysEmoji,
  ...user,
  ...table,
  ...appUser,
  ...purchase,
  ...statistics,
  ...approval,
  ...message,
  ...gift,
  ...integral,
  ...appManager,
  ...product,
  ...datav,
  ...imAccount,
  ...props,
  ...room,
  ...room,
  ...game,
  ...family,
  ...tootls,
  ...bean
]

// for front mock
// please use it cautiously, it will redefine XMLHttpRequest,
// which will cause many of your third-party libraries to be invalidated(like progress event).
export function mockXHR() {
  // mock patch
  // https://github.com/nuysoft/Mock/issues/300
  Mock.XHR.prototype.proxy_send = Mock.XHR.prototype.send
  Mock.XHR.prototype.send = function() {
    if (this.custom.xhr) {
      this.custom.xhr.withCredentials = this.withCredentials || false

      if (this.responseType) {
        this.custom.xhr.responseType = this.responseType
      }
    }
    this.proxy_send(...arguments)
  }

  function XHR2ExpressReqWrap(respond) {
    return function(options) {
      let result = null
      if (respond instanceof Function) {
        const { body, type, url } = options
        // https://expressjs.com/en/4x/api.html#req
        result = respond({
          method: type,
          body: JSON.parse(body),
          query: param2Obj(url)
        })
      } else {
        result = respond
      }
      return Mock.mock(result)
    }
  }

  for (const i of mocks) {
    Mock.mock(new RegExp(i.url), i.type || 'get', XHR2ExpressReqWrap(i.response))
  }
}

// for mock server
const responseFake = (url, type, respond) => {
  return {
    url: new RegExp(`${process.env.VUE_APP_BASE_API}${url}`),
    type: type || 'get',
    response(req, res) {
      console.log('request invoke:' + req.path)
      res.json(Mock.mock(respond instanceof Function ? respond(req, res) : respond))
    }
  }
}

export default mocks.map(route => {
  return responseFake(route.url, route.type, route.response)
})
