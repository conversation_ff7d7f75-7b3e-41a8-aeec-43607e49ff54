import Mock from 'mockjs'

export default [
  // 用户扩展资料
  {
    url: '/user/expand',
    type: 'get',
    response: () => {
      return { 'status': 200, 'result': { 'createTime': '2022-05-24 18:55:46', 'updateTime': '2022-05-24 19:15:03', 'userId': '1529053537530519554', 'language': 'en', 'lastZoneId': 'UTC', 'lastActiveTime': '2022-05-24 19:15:03', 'unlockArchiveSize': 0, 'purchasing': false, 'signature': '', 'registerCountryCode': 'US' }}
    }
  },
  // 货运代理账户分页列表
  {
    url: '/freight/page',
    type: 'get',
    response: () => {
      return { 'status': 200, 'result': { 'records': [{ 'id': '123', 'sysOrigin': 'ASWAT', 'userId': '1408342064156770305', 'earnPoints': 10000.00, 'consumptionPoints': 4000.00, 'createTime': '2021-07-06 16:41:09', 'updateTime': '2021-07-06 17:21:58', 'userBaseInfo': { 'createTime': '2021-06-25 16:31:28', 'updateTime': '2021-06-25 16:31:28', 'id': '1408342064156770305', 'userAvatar': '', 'originSys': 'ASWAT', 'userNickname': '啦啦啦2', 'userSex': 1, 'userSexName': '男', 'userType': 0, 'age': 1, 'bornYear': 2020, 'bornMonth': 1, 'bornDay': 1, 'userTypeName': '真实', 'countryName': 'China', 'countryId': '1231833262813360130', 'countryCode': 'CN', 'accountStatus': 'NORMAL', 'accountStatusName': '正常', 'freezingTime': '2021-06-24 16:31:28', 'del': false, 'account': '41712' }, 'balance': 6000.00 }], 'total': 1, 'size': 1000, 'current': 1, 'searchCount': true, 'pages': 1 }}
    }
  },
  // 用户糖果余额
  {
    url: '/user/base/info/account/status/\.',
    type: 'get',
    response: () => {
      return {
        'status': 200,
        'result': {
          'key': 'ARCHIVE',
          'value': '封存'
        }
      }
    }
  },
  // 账户余额信息
  {
    url: '/user/subscription/balance/\./name',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: '-'
      }
    }
  },
  // 认证信息
  {
    url: '/user/auth/type/\.*',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          'createTime': '2020-03-23 16:49:17',
          'updateTime': '2020-03-23 16:49:17',
          'userId': '1242010518727663617',
          'openId': '123456',
          'type': 'FACEBOOK',
          'del': false
        }
      }
    }
  },
  // 注册信息
  {
    url: '/user/register/info/\.*',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: {
          'createTime': '2020-03-23 16:49:17',
          'updateTime': '2020-03-23 16:49:17',
          'userId': '1242010518727663617',
          'residentialAddress': '深圳市 软件产业基地 xxxx',
          'authType': 'FACEBOOK',
          'originPlatform': 'iOS',
          'origin_phone_model': 'iphone 12'
        }
      }
    }
  },
  // 正常照片墙
  {
    url: '/user/photo/wall/normal/\.*',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: Mock.mock({
          'items|6': [{
            'id': '@id',
            'resourceUrl': 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
            'sort': '0',
            'violation': 'NORMAL',
            'createTime': '2020-03-23 16:53:49',
            'updateTime': '2020-03-23 16:53:49'
          }]
        }).items
      }
    }
  },
  // 所有照片墙
  {
    url: '/user/photo/wall/all/\.*',
    type: 'get',
    response: config => {
      return {
        status: 200,
        result: Mock.mock({
          'items|6': [{
            'id': '@id',
            'resourceUrl': 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
            'sort': '0',
            'violation': 'NORMAL',
            'createTime': '2020-03-23 16:53:49',
            'updateTime': '2020-03-23 16:53:49'
          }]
        }).items
      }
    }
  },
  // app用户列表
  {
    url: '/user/base/info/page',
    type: 'post',
    response: config => {
      return {
        status: 200,
        result: Mock.mock({
          'items|30': [
            {
              'userProfile': {
                'id': '@id',
                'sysOrigin': 'SUGARTIME',
                'account': '@id',
                'candyNumber': '12',
                'userAvatar': '',
                'userNickname': '@sentence(2, 6)',
                'userSex': 1,
                'userSexName': '男',
                'userType': 0,
                'userTypeName': '真实',
                'countryName': 'Qatar',
                'countryCode': 'QA',
                'countryId': '1231833357432664065',
                'createTime': '2021-07-25 10:36:17',
                'vipStatusName': '-',
                'authType': 'APPLE',
                'level': 0,
                'accountStatus': 'NORMAL',
                'accountStatusName': '正常',
                'age': 25,
                'bornYear': 1996,
                'bornMonth': 4,
                'bornDay': 1
              },
              'userRegisterInfo': {
                'originPlatform': 'iOS',
                'originPhoneModel': 'iPhone 11 Pro'
              },
              'lastActiveTime': '2021-07-25 10:36:19',
              'goldBalance': 123123
            }]
        }).items
      }
    }
  },
  // app用户列表
  {
    url: '/user/base/info/\.*',
    type: 'get',
    response: () => {
      return {
        'status': 200,
        'result': {
          'createTime': '2020-03-23 14:43:50',
          'updateTime': '2020-03-23 14:45:10',
          'id': '1241978948486668290',
          'userAvatar': 'http://dev.qiniu.sugartimeapp.com/807E56D2-4E24-4048-BDCE-8C72BB39C723.png',
          'userNickname': 'Betty Aldbhiidacbdi Lauberg',
          'userSex': 1,
          'userSexName': '男',
          'userType': 0,
          'userTypeName': '真实',
          'countryName': 'China',
          'del': false
        }
      }
    }
  },
  // 所有照片墙
  {
    url: '/user/photo/wall/page',
    type: 'get',
    response: () => {
      return {
        'status': 200,
        result: {
          records: Mock.mock({
            'items|30': [{
              'createTime': '2020-04-03 18:13:13',
              'updateTime': '2020-04-03 18:13:13',
              'id': '1246017905511866369',
              'userId': '1245960976894763009',
              'resourceUrl': 'http://dev.qiniu.sugartimeapp.com/3BBD3DBF-6086-444B-9AF0-77F16BF901F2.png',
              'sort': 0,
              'violation': 'NORMAL'
            }]
          }).items,
          total: 30,
          size: 30,
          current: 1,
          searchCount: true,
          pages: 3
        }
      }
    }
  },
  {
    url: '/user/photo/wall/del/\.*',
    type: 'get',
    response: () => {
      return {
        'status': 200
      }
    }
  },
  // 修改用户信息
  {
    url: '/user/base/info',
    type: 'put',
    response: () => {
      return {
        'status': 200
      }
    }
  },
  // 修改用户等级
  {
    url: '/user/level/score',
    type: 'post',
    response: () => {
      return {
        'status': 200
      }
    }
  },
  // 用户糖果余额
  {
    url: '/user/candy/balance/\.',
    type: 'get',
    response: () => {
      return {
        'status': 200,
        'result': {
          'balance': 100
        }
      }
    }
  },
  // 用户糖果余额-top表
  {
    url: '/user/candy/balance/top',
    type: 'get',
    response: () => {
      return {
        'status': 200,
        'result': []
      }
    }
  }
]
