<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="icon" href="<%= BASE_URL %>favicon_new.ico">
    <title><%= webpackConfig.name %></title>
    <style>
    html,
    body,
    #app {
      height: 100%;
      margin: 0;
      padding: 0;
    }
    .main-loading {
      background-color: #23232b;
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .main-loading__main {
      user-select: none;
      width: 100%;
      flex-grow: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }

    .main-loading__footer {
      width: 100%;
      flex-grow: 0;
      text-align: center;
      padding: 1em 0;
    }

    .main-loading__footer>a {
      font-size: 12px;
      color: #FFF;
      text-decoration: none;
    }

    .main-loading__loading {
      height: 32px;
      width: 32px;
      margin-bottom: 20px;
    }

    .main-loading__title {
      color: #FFF;
      font-size: 14px;
      margin-bottom: 10px;
    }

    .main-loading__sub-title {
      color: #FFF;
      font-size: 12px;
    }
    </style>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but <%= webpackConfig.name %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app">
      <div class="main-loading">
        <div class="main-loading__main">
          <img class="main-loading__loading" src="./loading.svg" alt="loading">
          <div class="main-loading__title">
            正在加载资源
          </div>
          <div class="main-loading__sub-title">
            初次加载资源可能需要较多时间 请耐心等待
          </div>
        </div>
        <div class="main-loading__footer">
          <a href="javascript:void(0)" target="_blank"></a>
        </div>
    </div>
    </div>
    <!-- built files will be auto injected -->
  </body>
</html>
